'use client'

import { Package, Users, DollarSign, AlertTriangle } from 'lucide-react'
import { useTheme } from 'next-themes'

import type { DashboardStats } from '@/types'

interface DashboardStatsProps {
  stats: DashboardStats
}

export default function DashboardStats({ stats }: DashboardStatsProps) {
  const { resolvedTheme } = useTheme()

  const statCards = [
    {
      title: 'Products in List',
      value: stats.totalProducts,
      icon: Package,
      color: 'bg-blue-500',
      textColor: 'text-blue-600',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Customer Debts',
      value: stats.totalDebts,
      icon: Users,
      color: 'bg-green-500',
      textColor: 'text-green-600',
      bgColor: 'bg-green-50',
    },
    {
      title: 'Total Debt Amount',
      value: `₱${stats.totalDebtAmount.toFixed(2)}`,
      icon: DollarSign,
      color: 'bg-yellow-500',
      textColor: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
    },
    {
      title: 'Low Stock Items',
      value: stats.lowStockItems,
      icon: AlertTriangle,
      color: 'bg-red-500',
      textColor: 'text-red-600',
      bgColor: 'bg-red-50',
    },
  ]

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {statCards.map((card, index) => {
          const Icon = card.icon
          return (
            <div
              key={index}
              className="rounded-lg shadow-md p-6 transition-all duration-300 hover:shadow-lg"
              style={{
                backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
                border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'
              }}
            >
              <div className="flex items-center">
                <div className={`p-3 rounded-lg ${card.bgColor} ${resolvedTheme === 'dark' ? 'opacity-90' : ''}`}>
                  <Icon className={`h-6 w-6 ${card.textColor}`} />
                </div>
                <div className="ml-4">
                  <p
                    className="text-sm font-medium transition-colors duration-300"
                    style={{
                      color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                    }}
                  >
                    {card.title}
                  </p>
                  <p
                    className="text-2xl font-semibold transition-colors duration-300"
                    style={{
                      color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                    }}
                  >
                    {card.value}
                  </p>
                </div>
              </div>
            </div>
          )
        })}
      </div>

      {/* Quick Actions */}
      <div
        className="rounded-lg shadow-md p-6 transition-all duration-300"
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
          border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'
        }}
      >
        <h3
          className="text-lg font-semibold mb-4 transition-colors duration-300"
          style={{
            color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
          }}
        >
          Quick Actions
        </h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            className="flex items-center p-4 rounded-lg transition-all duration-300 hover:scale-[1.02] hover:shadow-md"
            style={{
              border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #d1d5db',
              backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? '#475569' : '#f3f4f6'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? '#334155' : '#f9fafb'
            }}
          >
            <Package className="h-8 w-8 text-blue-600 mr-3" />
            <div className="text-left">
              <p
                className="font-medium transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                }}
              >
                Add to Product List
              </p>
              <p
                className="text-sm transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                }}
              >
                Add a new product to your list
              </p>
            </div>
          </button>
          <button
            className="flex items-center p-4 rounded-lg transition-all duration-300 hover:scale-[1.02] hover:shadow-md"
            style={{
              border: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #d1d5db',
              backgroundColor: resolvedTheme === 'dark' ? '#334155' : '#f9fafb'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? '#475569' : '#f3f4f6'
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = resolvedTheme === 'dark' ? '#334155' : '#f9fafb'
            }}
          >
            <Users className="h-8 w-8 text-green-600 mr-3" />
            <div className="text-left">
              <p
                className="font-medium transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
                }}
              >
                Record New Debt
              </p>
              <p
                className="text-sm transition-colors duration-300"
                style={{
                  color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
                }}
              >
                Add a new customer debt record
              </p>
            </div>
          </button>
        </div>
      </div>

      {/* Store Overview */}
      <div
        className="rounded-lg shadow-md p-6 transition-all duration-300"
        style={{
          backgroundColor: resolvedTheme === 'dark' ? '#1e293b' : '#ffffff',
          border: resolvedTheme === 'dark' ? '1px solid #334155' : '1px solid #e5e7eb'
        }}
      >
        <h3
          className="text-lg font-semibold mb-4 transition-colors duration-300"
          style={{
            color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
          }}
        >
          Store Overview
        </h3>
        <div className="space-y-4">
          <div
            className="flex justify-between items-center py-2 transition-colors duration-300"
            style={{
              borderBottom: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #f3f4f6'
            }}
          >
            <span
              className="transition-colors duration-300"
              style={{
                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
              }}
            >
              Products in List
            </span>
            <span
              className="font-semibold transition-colors duration-300"
              style={{
                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
              }}
            >
              {stats.totalProducts}
            </span>
          </div>
          <div
            className="flex justify-between items-center py-2 transition-colors duration-300"
            style={{
              borderBottom: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #f3f4f6'
            }}
          >
            <span
              className="transition-colors duration-300"
              style={{
                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
              }}
            >
              Outstanding Debts
            </span>
            <span
              className="font-semibold transition-colors duration-300"
              style={{
                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
              }}
            >
              {stats.totalDebts}
            </span>
          </div>
          <div
            className="flex justify-between items-center py-2 transition-colors duration-300"
            style={{
              borderBottom: resolvedTheme === 'dark' ? '1px solid #475569' : '1px solid #f3f4f6'
            }}
          >
            <span
              className="transition-colors duration-300"
              style={{
                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
              }}
            >
              Total Amount Owed
            </span>
            <span
              className="font-semibold transition-colors duration-300"
              style={{
                color: resolvedTheme === 'dark' ? '#f8fafc' : '#111827'
              }}
            >
              ₱{stats.totalDebtAmount.toFixed(2)}
            </span>
          </div>
          <div className="flex justify-between items-center py-2">
            <span
              className="transition-colors duration-300"
              style={{
                color: resolvedTheme === 'dark' ? '#cbd5e1' : '#6b7280'
              }}
            >
              Items Need Restocking
            </span>
            <span className={`font-semibold ${stats.lowStockItems > 0 ? 'text-red-600' : 'text-green-600'}`}>
              {stats.lowStockItems}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
