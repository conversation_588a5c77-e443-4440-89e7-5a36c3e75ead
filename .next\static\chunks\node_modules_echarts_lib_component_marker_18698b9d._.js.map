{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/marker/checkMarkerInSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isArray } from 'zrender/lib/core/util.js';\nexport default function checkMarkerInSeries(seriesOpts, markerType) {\n  if (!seriesOpts) {\n    return false;\n  }\n  var seriesOptArr = isArray(seriesOpts) ? seriesOpts : [seriesOpts];\n  for (var idx = 0; idx < seriesOptArr.length; idx++) {\n    if (seriesOptArr[idx] && seriesOptArr[idx][markerType]) {\n      return true;\n    }\n  }\n  return false;\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;;AACe,SAAS,oBAAoB,UAAU,EAAE,UAAU;IAChE,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IACA,IAAI,eAAe,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,cAAc,aAAa;QAAC;KAAW;IAClE,IAAK,IAAI,MAAM,GAAG,MAAM,aAAa,MAAM,EAAE,MAAO;QAClD,IAAI,YAAY,CAAC,IAAI,IAAI,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE;YACtD,OAAO;QACT;IACF;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/marker/MarkerModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport { DataFormatMixin } from '../../model/mixin/dataFormat.js';\nimport ComponentModel from '../../model/Component.js';\nimport { makeInner, defaultEmphasis } from '../../util/model.js';\nimport { createTooltipMarkup } from '../tooltip/tooltipMarkup.js';\nfunction fillLabel(opt) {\n  defaultEmphasis(opt, 'label', ['show']);\n}\n// { [componentType]: MarkerModel }\nvar inner = makeInner();\nvar MarkerModel = /** @class */function (_super) {\n  __extends(MarkerModel, _super);\n  function MarkerModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkerModel.type;\n    /**\r\n     * If marker model is created by self from series\r\n     */\n    _this.createdBySelf = false;\n    return _this;\n  }\n  /**\r\n   * @overrite\r\n   */\n  MarkerModel.prototype.init = function (option, parentModel, ecModel) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (this.type === 'marker') {\n        throw new Error('Marker component is abstract component. Use markLine, markPoint, markArea instead.');\n      }\n    }\n    this.mergeDefaultAndTheme(option, ecModel);\n    this._mergeOption(option, ecModel, false, true);\n  };\n  MarkerModel.prototype.isAnimationEnabled = function () {\n    if (env.node) {\n      return false;\n    }\n    var hostSeries = this.__hostSeries;\n    return this.getShallow('animation') && hostSeries && hostSeries.isAnimationEnabled();\n  };\n  /**\r\n   * @overrite\r\n   */\n  MarkerModel.prototype.mergeOption = function (newOpt, ecModel) {\n    this._mergeOption(newOpt, ecModel, false, false);\n  };\n  MarkerModel.prototype._mergeOption = function (newOpt, ecModel, createdBySelf, isInit) {\n    var componentType = this.mainType;\n    if (!createdBySelf) {\n      ecModel.eachSeries(function (seriesModel) {\n        // mainType can be markPoint, markLine, markArea\n        var markerOpt = seriesModel.get(this.mainType, true);\n        var markerModel = inner(seriesModel)[componentType];\n        if (!markerOpt || !markerOpt.data) {\n          inner(seriesModel)[componentType] = null;\n          return;\n        }\n        if (!markerModel) {\n          if (isInit) {\n            // Default label emphasis `position` and `show`\n            fillLabel(markerOpt);\n          }\n          zrUtil.each(markerOpt.data, function (item) {\n            // FIXME Overwrite fillLabel method ?\n            if (item instanceof Array) {\n              fillLabel(item[0]);\n              fillLabel(item[1]);\n            } else {\n              fillLabel(item);\n            }\n          });\n          markerModel = this.createMarkerModelFromSeries(markerOpt, this, ecModel);\n          // markerModel = new ImplementedMarkerModel(\n          //     markerOpt, this, ecModel\n          // );\n          zrUtil.extend(markerModel, {\n            mainType: this.mainType,\n            // Use the same series index and name\n            seriesIndex: seriesModel.seriesIndex,\n            name: seriesModel.name,\n            createdBySelf: true\n          });\n          markerModel.__hostSeries = seriesModel;\n        } else {\n          markerModel._mergeOption(markerOpt, ecModel, true);\n        }\n        inner(seriesModel)[componentType] = markerModel;\n      }, this);\n    }\n  };\n  MarkerModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    var data = this.getData();\n    var value = this.getRawValue(dataIndex);\n    var itemName = data.getName(dataIndex);\n    return createTooltipMarkup('section', {\n      header: this.name,\n      blocks: [createTooltipMarkup('nameValue', {\n        name: itemName,\n        value: value,\n        noName: !itemName,\n        noValue: value == null\n      })]\n    });\n  };\n  MarkerModel.prototype.getData = function () {\n    return this._data;\n  };\n  MarkerModel.prototype.setData = function (data) {\n    this._data = data;\n  };\n  MarkerModel.prototype.getDataParams = function (dataIndex, dataType) {\n    var params = DataFormatMixin.prototype.getDataParams.call(this, dataIndex, dataType);\n    var hostSeries = this.__hostSeries;\n    if (hostSeries) {\n      params.seriesId = hostSeries.id;\n      params.seriesName = hostSeries.name;\n      params.seriesType = hostSeries.subType;\n    }\n    return params;\n  };\n  MarkerModel.getMarkerModelFromSeries = function (seriesModel,\n  // Support three types of markers. Strict check.\n  componentType) {\n    return inner(seriesModel)[componentType];\n  };\n  MarkerModel.type = 'marker';\n  MarkerModel.dependencies = ['series', 'grid', 'polar', 'geo'];\n  return MarkerModel;\n}(ComponentModel);\nzrUtil.mixin(MarkerModel, DataFormatMixin.prototype);\nexport default MarkerModel;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AA4BQ;AA3BR;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACA,SAAS,UAAU,GAAG;IACpB,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD,EAAE,KAAK,SAAS;QAAC;KAAO;AACxC;AACA,mCAAmC;AACnC,IAAI,QAAQ,CAAA,GAAA,kJAAA,CAAA,YAAS,AAAD;AACpB,IAAI,cAAc,WAAW,GAAE,SAAU,MAAM;IAC7C,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,aAAa;IACvB,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,YAAY,IAAI;QAC7B;;KAEC,GACD,MAAM,aAAa,GAAG;QACtB,OAAO;IACT;IACA;;GAEC,GACD,YAAY,SAAS,CAAC,IAAI,GAAG,SAAU,MAAM,EAAE,WAAW,EAAE,OAAO;QACjE,wCAA2C;YACzC,IAAI,IAAI,CAAC,IAAI,KAAK,UAAU;gBAC1B,MAAM,IAAI,MAAM;YAClB;QACF;QACA,IAAI,CAAC,oBAAoB,CAAC,QAAQ;QAClC,IAAI,CAAC,YAAY,CAAC,QAAQ,SAAS,OAAO;IAC5C;IACA,YAAY,SAAS,CAAC,kBAAkB,GAAG;QACzC,IAAI,gJAAA,CAAA,UAAG,CAAC,IAAI,EAAE;YACZ,OAAO;QACT;QACA,IAAI,aAAa,IAAI,CAAC,YAAY;QAClC,OAAO,IAAI,CAAC,UAAU,CAAC,gBAAgB,cAAc,WAAW,kBAAkB;IACpF;IACA;;GAEC,GACD,YAAY,SAAS,CAAC,WAAW,GAAG,SAAU,MAAM,EAAE,OAAO;QAC3D,IAAI,CAAC,YAAY,CAAC,QAAQ,SAAS,OAAO;IAC5C;IACA,YAAY,SAAS,CAAC,YAAY,GAAG,SAAU,MAAM,EAAE,OAAO,EAAE,aAAa,EAAE,MAAM;QACnF,IAAI,gBAAgB,IAAI,CAAC,QAAQ;QACjC,IAAI,CAAC,eAAe;YAClB,QAAQ,UAAU,CAAC,SAAU,WAAW;gBACtC,gDAAgD;gBAChD,IAAI,YAAY,YAAY,GAAG,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAC/C,IAAI,cAAc,MAAM,YAAY,CAAC,cAAc;gBACnD,IAAI,CAAC,aAAa,CAAC,UAAU,IAAI,EAAE;oBACjC,MAAM,YAAY,CAAC,cAAc,GAAG;oBACpC;gBACF;gBACA,IAAI,CAAC,aAAa;oBAChB,IAAI,QAAQ;wBACV,+CAA+C;wBAC/C,UAAU;oBACZ;oBACA,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,UAAU,IAAI,EAAE,SAAU,IAAI;wBACxC,qCAAqC;wBACrC,IAAI,gBAAgB,OAAO;4BACzB,UAAU,IAAI,CAAC,EAAE;4BACjB,UAAU,IAAI,CAAC,EAAE;wBACnB,OAAO;4BACL,UAAU;wBACZ;oBACF;oBACA,cAAc,IAAI,CAAC,2BAA2B,CAAC,WAAW,IAAI,EAAE;oBAChE,4CAA4C;oBAC5C,+BAA+B;oBAC/B,KAAK;oBACL,CAAA,GAAA,iJAAA,CAAA,SAAa,AAAD,EAAE,aAAa;wBACzB,UAAU,IAAI,CAAC,QAAQ;wBACvB,qCAAqC;wBACrC,aAAa,YAAY,WAAW;wBACpC,MAAM,YAAY,IAAI;wBACtB,eAAe;oBACjB;oBACA,YAAY,YAAY,GAAG;gBAC7B,OAAO;oBACL,YAAY,YAAY,CAAC,WAAW,SAAS;gBAC/C;gBACA,MAAM,YAAY,CAAC,cAAc,GAAG;YACtC,GAAG,IAAI;QACT;IACF;IACA,YAAY,SAAS,CAAC,aAAa,GAAG,SAAU,SAAS,EAAE,cAAc,EAAE,QAAQ;QACjF,IAAI,OAAO,IAAI,CAAC,OAAO;QACvB,IAAI,QAAQ,IAAI,CAAC,WAAW,CAAC;QAC7B,IAAI,WAAW,KAAK,OAAO,CAAC;QAC5B,OAAO,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW;YACpC,QAAQ,IAAI,CAAC,IAAI;YACjB,QAAQ;gBAAC,CAAA,GAAA,0KAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;oBACxC,MAAM;oBACN,OAAO;oBACP,QAAQ,CAAC;oBACT,SAAS,SAAS;gBACpB;aAAG;QACL;IACF;IACA,YAAY,SAAS,CAAC,OAAO,GAAG;QAC9B,OAAO,IAAI,CAAC,KAAK;IACnB;IACA,YAAY,SAAS,CAAC,OAAO,GAAG,SAAU,IAAI;QAC5C,IAAI,CAAC,KAAK,GAAG;IACf;IACA,YAAY,SAAS,CAAC,aAAa,GAAG,SAAU,SAAS,EAAE,QAAQ;QACjE,IAAI,SAAS,iKAAA,CAAA,kBAAe,CAAC,SAAS,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW;QAC3E,IAAI,aAAa,IAAI,CAAC,YAAY;QAClC,IAAI,YAAY;YACd,OAAO,QAAQ,GAAG,WAAW,EAAE;YAC/B,OAAO,UAAU,GAAG,WAAW,IAAI;YACnC,OAAO,UAAU,GAAG,WAAW,OAAO;QACxC;QACA,OAAO;IACT;IACA,YAAY,wBAAwB,GAAG,SAAU,WAAW,EAC5D,gDAAgD;IAChD,aAAa;QACX,OAAO,MAAM,YAAY,CAAC,cAAc;IAC1C;IACA,YAAY,IAAI,GAAG;IACnB,YAAY,YAAY,GAAG;QAAC;QAAU;QAAQ;QAAS;KAAM;IAC7D,OAAO;AACT,EAAE,uJAAA,CAAA,UAAc;AAChB,CAAA,GAAA,iJAAA,CAAA,QAAY,AAAD,EAAE,aAAa,iKAAA,CAAA,kBAAe,CAAC,SAAS;uCACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/marker/MarkPointModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport MarkerModel from './MarkerModel.js';\nvar MarkPointModel = /** @class */function (_super) {\n  __extends(MarkPointModel, _super);\n  function MarkPointModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkPointModel.type;\n    return _this;\n  }\n  MarkPointModel.prototype.createMarkerModelFromSeries = function (markerOpt, masterMarkerModel, ecModel) {\n    return new MarkPointModel(markerOpt, masterMarkerModel, ecModel);\n  };\n  MarkPointModel.type = 'markPoint';\n  MarkPointModel.defaultOption = {\n    // zlevel: 0,\n    z: 5,\n    symbol: 'pin',\n    symbolSize: 50,\n    // symbolRotate: 0,\n    // symbolOffset: [0, 0]\n    tooltip: {\n      trigger: 'item'\n    },\n    label: {\n      show: true,\n      position: 'inside'\n    },\n    itemStyle: {\n      borderWidth: 2\n    },\n    emphasis: {\n      label: {\n        show: true\n      }\n    }\n  };\n  return MarkPointModel;\n}(MarkerModel);\nexport default MarkPointModel;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;;;AACA,IAAI,iBAAiB,WAAW,GAAE,SAAU,MAAM;IAChD,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;IAC1B,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,eAAe,IAAI;QAChC,OAAO;IACT;IACA,eAAe,SAAS,CAAC,2BAA2B,GAAG,SAAU,SAAS,EAAE,iBAAiB,EAAE,OAAO;QACpG,OAAO,IAAI,eAAe,WAAW,mBAAmB;IAC1D;IACA,eAAe,IAAI,GAAG;IACtB,eAAe,aAAa,GAAG;QAC7B,aAAa;QACb,GAAG;QACH,QAAQ;QACR,YAAY;QACZ,mBAAmB;QACnB,uBAAuB;QACvB,SAAS;YACP,SAAS;QACX;QACA,OAAO;YACL,MAAM;YACN,UAAU;QACZ;QACA,WAAW;YACT,aAAa;QACf;QACA,UAAU;YACR,OAAO;gBACL,MAAM;YACR;QACF;IACF;IACA,OAAO;AACT,EAAE,uKAAA,CAAA,UAAW;uCACE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/marker/markerHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as numberUtil from '../../util/number.js';\nimport { isDimensionStacked } from '../../data/helper/dataStackHelper.js';\nimport { indexOf, curry, clone, isArray } from 'zrender/lib/core/util.js';\nimport { parseDataValue } from '../../data/helper/dataValueHelper.js';\nfunction hasXOrY(item) {\n  return !(isNaN(parseFloat(item.x)) && isNaN(parseFloat(item.y)));\n}\nfunction hasXAndY(item) {\n  return !isNaN(parseFloat(item.x)) && !isNaN(parseFloat(item.y));\n}\nfunction markerTypeCalculatorWithExtent(markerType, data, otherDataDim, targetDataDim, otherCoordIndex, targetCoordIndex) {\n  var coordArr = [];\n  var stacked = isDimensionStacked(data, targetDataDim /* , otherDataDim */);\n  var calcDataDim = stacked ? data.getCalculationInfo('stackResultDimension') : targetDataDim;\n  var value = numCalculate(data, calcDataDim, markerType);\n  var dataIndex = data.indicesOfNearest(calcDataDim, value)[0];\n  coordArr[otherCoordIndex] = data.get(otherDataDim, dataIndex);\n  coordArr[targetCoordIndex] = data.get(calcDataDim, dataIndex);\n  var coordArrValue = data.get(targetDataDim, dataIndex);\n  // Make it simple, do not visit all stacked value to count precision.\n  var precision = numberUtil.getPrecision(data.get(targetDataDim, dataIndex));\n  precision = Math.min(precision, 20);\n  if (precision >= 0) {\n    coordArr[targetCoordIndex] = +coordArr[targetCoordIndex].toFixed(precision);\n  }\n  return [coordArr, coordArrValue];\n}\n// TODO Specified percent\nvar markerTypeCalculator = {\n  min: curry(markerTypeCalculatorWithExtent, 'min'),\n  max: curry(markerTypeCalculatorWithExtent, 'max'),\n  average: curry(markerTypeCalculatorWithExtent, 'average'),\n  median: curry(markerTypeCalculatorWithExtent, 'median')\n};\n/**\r\n * Transform markPoint data item to format used in List by do the following\r\n * 1. Calculate statistic like `max`, `min`, `average`\r\n * 2. Convert `item.xAxis`, `item.yAxis` to `item.coord` array\r\n */\nexport function dataTransform(seriesModel, item) {\n  if (!item) {\n    return;\n  }\n  var data = seriesModel.getData();\n  var coordSys = seriesModel.coordinateSystem;\n  var dims = coordSys && coordSys.dimensions;\n  // 1. If not specify the position with pixel directly\n  // 2. If `coord` is not a data array. Which uses `xAxis`,\n  // `yAxis` to specify the coord on each dimension\n  // parseFloat first because item.x and item.y can be percent string like '20%'\n  if (!hasXAndY(item) && !isArray(item.coord) && isArray(dims)) {\n    var axisInfo = getAxisInfo(item, data, coordSys, seriesModel);\n    // Clone the option\n    // Transform the properties xAxis, yAxis, radiusAxis, angleAxis, geoCoord to value\n    item = clone(item);\n    if (item.type && markerTypeCalculator[item.type] && axisInfo.baseAxis && axisInfo.valueAxis) {\n      var otherCoordIndex = indexOf(dims, axisInfo.baseAxis.dim);\n      var targetCoordIndex = indexOf(dims, axisInfo.valueAxis.dim);\n      var coordInfo = markerTypeCalculator[item.type](data, axisInfo.baseDataDim, axisInfo.valueDataDim, otherCoordIndex, targetCoordIndex);\n      item.coord = coordInfo[0];\n      // Force to use the value of calculated value.\n      // let item use the value without stack.\n      item.value = coordInfo[1];\n    } else {\n      // FIXME Only has one of xAxis and yAxis.\n      item.coord = [item.xAxis != null ? item.xAxis : item.radiusAxis, item.yAxis != null ? item.yAxis : item.angleAxis];\n    }\n  }\n  // x y is provided\n  if (item.coord == null || !isArray(dims)) {\n    item.coord = [];\n  } else {\n    // Each coord support max, min, average\n    var coord = item.coord;\n    for (var i = 0; i < 2; i++) {\n      if (markerTypeCalculator[coord[i]]) {\n        coord[i] = numCalculate(data, data.mapDimension(dims[i]), coord[i]);\n      }\n    }\n  }\n  return item;\n}\nexport function getAxisInfo(item, data, coordSys, seriesModel) {\n  var ret = {};\n  if (item.valueIndex != null || item.valueDim != null) {\n    ret.valueDataDim = item.valueIndex != null ? data.getDimension(item.valueIndex) : item.valueDim;\n    ret.valueAxis = coordSys.getAxis(dataDimToCoordDim(seriesModel, ret.valueDataDim));\n    ret.baseAxis = coordSys.getOtherAxis(ret.valueAxis);\n    ret.baseDataDim = data.mapDimension(ret.baseAxis.dim);\n  } else {\n    ret.baseAxis = seriesModel.getBaseAxis();\n    ret.valueAxis = coordSys.getOtherAxis(ret.baseAxis);\n    ret.baseDataDim = data.mapDimension(ret.baseAxis.dim);\n    ret.valueDataDim = data.mapDimension(ret.valueAxis.dim);\n  }\n  return ret;\n}\nfunction dataDimToCoordDim(seriesModel, dataDim) {\n  var dimItem = seriesModel.getData().getDimensionInfo(dataDim);\n  return dimItem && dimItem.coordDim;\n}\n/**\r\n * Filter data which is out of coordinateSystem range\r\n * [dataFilter description]\r\n */\nexport function dataFilter(\n// Currently only polar and cartesian has containData.\ncoordSys, item) {\n  // Always return true if there is no coordSys\n  return coordSys && coordSys.containData && item.coord && !hasXOrY(item) ? coordSys.containData(item.coord) : true;\n}\nexport function zoneFilter(\n// Currently only polar and cartesian has containData.\ncoordSys, item1, item2) {\n  // Always return true if there is no coordSys\n  return coordSys && coordSys.containZone && item1.coord && item2.coord && !hasXOrY(item1) && !hasXOrY(item2) ? coordSys.containZone(item1.coord, item2.coord) : true;\n}\nexport function createMarkerDimValueGetter(inCoordSys, dims) {\n  return inCoordSys ? function (item, dimName, dataIndex, dimIndex) {\n    var rawVal = dimIndex < 2\n    // x, y, radius, angle\n    ? item.coord && item.coord[dimIndex] : item.value;\n    return parseDataValue(rawVal, dims[dimIndex]);\n  } : function (item, dimName, dataIndex, dimIndex) {\n    return parseDataValue(item.value, dims[dimIndex]);\n  };\n}\nexport function numCalculate(data, valueDataDim, type) {\n  if (type === 'average') {\n    var sum_1 = 0;\n    var count_1 = 0;\n    data.each(valueDataDim, function (val, idx) {\n      if (!isNaN(val)) {\n        sum_1 += val;\n        count_1++;\n      }\n    });\n    return sum_1 / count_1;\n  } else if (type === 'median') {\n    return data.getMedian(valueDataDim);\n  } else {\n    // max & min\n    return data.getDataExtent(valueDataDim)[type === 'max' ? 1 : 0];\n  }\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;;;;;AACA;AACA;AACA;AACA;;;;;AACA,SAAS,QAAQ,IAAI;IACnB,OAAO,CAAC,CAAC,MAAM,WAAW,KAAK,CAAC,MAAM,MAAM,WAAW,KAAK,CAAC,EAAE;AACjE;AACA,SAAS,SAAS,IAAI;IACpB,OAAO,CAAC,MAAM,WAAW,KAAK,CAAC,MAAM,CAAC,MAAM,WAAW,KAAK,CAAC;AAC/D;AACA,SAAS,+BAA+B,UAAU,EAAE,IAAI,EAAE,YAAY,EAAE,aAAa,EAAE,eAAe,EAAE,gBAAgB;IACtH,IAAI,WAAW,EAAE;IACjB,IAAI,UAAU,CAAA,GAAA,sKAAA,CAAA,qBAAkB,AAAD,EAAE,MAAM,cAAc,kBAAkB;IACvE,IAAI,cAAc,UAAU,KAAK,kBAAkB,CAAC,0BAA0B;IAC9E,IAAI,QAAQ,aAAa,MAAM,aAAa;IAC5C,IAAI,YAAY,KAAK,gBAAgB,CAAC,aAAa,MAAM,CAAC,EAAE;IAC5D,QAAQ,CAAC,gBAAgB,GAAG,KAAK,GAAG,CAAC,cAAc;IACnD,QAAQ,CAAC,iBAAiB,GAAG,KAAK,GAAG,CAAC,aAAa;IACnD,IAAI,gBAAgB,KAAK,GAAG,CAAC,eAAe;IAC5C,qEAAqE;IACrE,IAAI,YAAY,CAAA,GAAA,mJAAA,CAAA,eAAuB,AAAD,EAAE,KAAK,GAAG,CAAC,eAAe;IAChE,YAAY,KAAK,GAAG,CAAC,WAAW;IAChC,IAAI,aAAa,GAAG;QAClB,QAAQ,CAAC,iBAAiB,GAAG,CAAC,QAAQ,CAAC,iBAAiB,CAAC,OAAO,CAAC;IACnE;IACA,OAAO;QAAC;QAAU;KAAc;AAClC;AACA,yBAAyB;AACzB,IAAI,uBAAuB;IACzB,KAAK,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,gCAAgC;IAC3C,KAAK,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,gCAAgC;IAC3C,SAAS,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,gCAAgC;IAC/C,QAAQ,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,gCAAgC;AAChD;AAMO,SAAS,cAAc,WAAW,EAAE,IAAI;IAC7C,IAAI,CAAC,MAAM;QACT;IACF;IACA,IAAI,OAAO,YAAY,OAAO;IAC9B,IAAI,WAAW,YAAY,gBAAgB;IAC3C,IAAI,OAAO,YAAY,SAAS,UAAU;IAC1C,qDAAqD;IACrD,yDAAyD;IACzD,iDAAiD;IACjD,8EAA8E;IAC9E,IAAI,CAAC,SAAS,SAAS,CAAC,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,KAAK,KAAK,KAAK,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,OAAO;QAC5D,IAAI,WAAW,YAAY,MAAM,MAAM,UAAU;QACjD,mBAAmB;QACnB,kFAAkF;QAClF,OAAO,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;QACb,IAAI,KAAK,IAAI,IAAI,oBAAoB,CAAC,KAAK,IAAI,CAAC,IAAI,SAAS,QAAQ,IAAI,SAAS,SAAS,EAAE;YAC3F,IAAI,kBAAkB,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,MAAM,SAAS,QAAQ,CAAC,GAAG;YACzD,IAAI,mBAAmB,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,MAAM,SAAS,SAAS,CAAC,GAAG;YAC3D,IAAI,YAAY,oBAAoB,CAAC,KAAK,IAAI,CAAC,CAAC,MAAM,SAAS,WAAW,EAAE,SAAS,YAAY,EAAE,iBAAiB;YACpH,KAAK,KAAK,GAAG,SAAS,CAAC,EAAE;YACzB,8CAA8C;YAC9C,wCAAwC;YACxC,KAAK,KAAK,GAAG,SAAS,CAAC,EAAE;QAC3B,OAAO;YACL,yCAAyC;YACzC,KAAK,KAAK,GAAG;gBAAC,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,GAAG,KAAK,UAAU;gBAAE,KAAK,KAAK,IAAI,OAAO,KAAK,KAAK,GAAG,KAAK,SAAS;aAAC;QACpH;IACF;IACA,kBAAkB;IAClB,IAAI,KAAK,KAAK,IAAI,QAAQ,CAAC,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,OAAO;QACxC,KAAK,KAAK,GAAG,EAAE;IACjB,OAAO;QACL,uCAAuC;QACvC,IAAI,QAAQ,KAAK,KAAK;QACtB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;YAC1B,IAAI,oBAAoB,CAAC,KAAK,CAAC,EAAE,CAAC,EAAE;gBAClC,KAAK,CAAC,EAAE,GAAG,aAAa,MAAM,KAAK,YAAY,CAAC,IAAI,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;YACpE;QACF;IACF;IACA,OAAO;AACT;AACO,SAAS,YAAY,IAAI,EAAE,IAAI,EAAE,QAAQ,EAAE,WAAW;IAC3D,IAAI,MAAM,CAAC;IACX,IAAI,KAAK,UAAU,IAAI,QAAQ,KAAK,QAAQ,IAAI,MAAM;QACpD,IAAI,YAAY,GAAG,KAAK,UAAU,IAAI,OAAO,KAAK,YAAY,CAAC,KAAK,UAAU,IAAI,KAAK,QAAQ;QAC/F,IAAI,SAAS,GAAG,SAAS,OAAO,CAAC,kBAAkB,aAAa,IAAI,YAAY;QAChF,IAAI,QAAQ,GAAG,SAAS,YAAY,CAAC,IAAI,SAAS;QAClD,IAAI,WAAW,GAAG,KAAK,YAAY,CAAC,IAAI,QAAQ,CAAC,GAAG;IACtD,OAAO;QACL,IAAI,QAAQ,GAAG,YAAY,WAAW;QACtC,IAAI,SAAS,GAAG,SAAS,YAAY,CAAC,IAAI,QAAQ;QAClD,IAAI,WAAW,GAAG,KAAK,YAAY,CAAC,IAAI,QAAQ,CAAC,GAAG;QACpD,IAAI,YAAY,GAAG,KAAK,YAAY,CAAC,IAAI,SAAS,CAAC,GAAG;IACxD;IACA,OAAO;AACT;AACA,SAAS,kBAAkB,WAAW,EAAE,OAAO;IAC7C,IAAI,UAAU,YAAY,OAAO,GAAG,gBAAgB,CAAC;IACrD,OAAO,WAAW,QAAQ,QAAQ;AACpC;AAKO,SAAS,WAChB,sDAAsD;AACtD,QAAQ,EAAE,IAAI;IACZ,6CAA6C;IAC7C,OAAO,YAAY,SAAS,WAAW,IAAI,KAAK,KAAK,IAAI,CAAC,QAAQ,QAAQ,SAAS,WAAW,CAAC,KAAK,KAAK,IAAI;AAC/G;AACO,SAAS,WAChB,sDAAsD;AACtD,QAAQ,EAAE,KAAK,EAAE,KAAK;IACpB,6CAA6C;IAC7C,OAAO,YAAY,SAAS,WAAW,IAAI,MAAM,KAAK,IAAI,MAAM,KAAK,IAAI,CAAC,QAAQ,UAAU,CAAC,QAAQ,SAAS,SAAS,WAAW,CAAC,MAAM,KAAK,EAAE,MAAM,KAAK,IAAI;AACjK;AACO,SAAS,2BAA2B,UAAU,EAAE,IAAI;IACzD,OAAO,aAAa,SAAU,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ;QAC9D,IAAI,SAAS,WAAW,IAEtB,KAAK,KAAK,IAAI,KAAK,KAAK,CAAC,SAAS,GAAG,KAAK,KAAK;QACjD,OAAO,CAAA,GAAA,sKAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,IAAI,CAAC,SAAS;IAC9C,IAAI,SAAU,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ;QAC9C,OAAO,CAAA,GAAA,sKAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,KAAK,EAAE,IAAI,CAAC,SAAS;IAClD;AACF;AACO,SAAS,aAAa,IAAI,EAAE,YAAY,EAAE,IAAI;IACnD,IAAI,SAAS,WAAW;QACtB,IAAI,QAAQ;QACZ,IAAI,UAAU;QACd,KAAK,IAAI,CAAC,cAAc,SAAU,GAAG,EAAE,GAAG;YACxC,IAAI,CAAC,MAAM,MAAM;gBACf,SAAS;gBACT;YACF;QACF;QACA,OAAO,QAAQ;IACjB,OAAO,IAAI,SAAS,UAAU;QAC5B,OAAO,KAAK,SAAS,CAAC;IACxB,OAAO;QACL,YAAY;QACZ,OAAO,KAAK,aAAa,CAAC,aAAa,CAAC,SAAS,QAAQ,IAAI,EAAE;IACjE;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 537, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/marker/MarkerView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport ComponentView from '../../view/Component.js';\nimport { createHashMap, each } from 'zrender/lib/core/util.js';\nimport MarkerModel from './MarkerModel.js';\nimport { makeInner } from '../../util/model.js';\nimport { enterBlur, leaveBlur } from '../../util/states.js';\nvar inner = makeInner();\nvar MarkerView = /** @class */function (_super) {\n  __extends(MarkerView, _super);\n  function MarkerView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkerView.type;\n    return _this;\n  }\n  MarkerView.prototype.init = function () {\n    this.markerGroupMap = createHashMap();\n  };\n  MarkerView.prototype.render = function (markerModel, ecModel, api) {\n    var _this = this;\n    var markerGroupMap = this.markerGroupMap;\n    markerGroupMap.each(function (item) {\n      inner(item).keep = false;\n    });\n    ecModel.eachSeries(function (seriesModel) {\n      var markerModel = MarkerModel.getMarkerModelFromSeries(seriesModel, _this.type);\n      markerModel && _this.renderSeries(seriesModel, markerModel, ecModel, api);\n    });\n    markerGroupMap.each(function (item) {\n      !inner(item).keep && _this.group.remove(item.group);\n    });\n  };\n  MarkerView.prototype.markKeep = function (drawGroup) {\n    inner(drawGroup).keep = true;\n  };\n  MarkerView.prototype.toggleBlurSeries = function (seriesModelList, isBlur) {\n    var _this = this;\n    each(seriesModelList, function (seriesModel) {\n      var markerModel = MarkerModel.getMarkerModelFromSeries(seriesModel, _this.type);\n      if (markerModel) {\n        var data = markerModel.getData();\n        data.eachItemGraphicEl(function (el) {\n          if (el) {\n            isBlur ? enterBlur(el) : leaveBlur(el);\n          }\n        });\n      }\n    });\n  };\n  MarkerView.type = 'marker';\n  return MarkerView;\n}(ComponentView);\nexport default MarkerView;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACA,IAAI,QAAQ,CAAA,GAAA,kJAAA,CAAA,YAAS,AAAD;AACpB,IAAI,aAAa,WAAW,GAAE,SAAU,MAAM;IAC5C,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,YAAY;IACtB,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,WAAW,IAAI;QAC5B,OAAO;IACT;IACA,WAAW,SAAS,CAAC,IAAI,GAAG;QAC1B,IAAI,CAAC,cAAc,GAAG,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;IACpC;IACA,WAAW,SAAS,CAAC,MAAM,GAAG,SAAU,WAAW,EAAE,OAAO,EAAE,GAAG;QAC/D,IAAI,QAAQ,IAAI;QAChB,IAAI,iBAAiB,IAAI,CAAC,cAAc;QACxC,eAAe,IAAI,CAAC,SAAU,IAAI;YAChC,MAAM,MAAM,IAAI,GAAG;QACrB;QACA,QAAQ,UAAU,CAAC,SAAU,WAAW;YACtC,IAAI,cAAc,uKAAA,CAAA,UAAW,CAAC,wBAAwB,CAAC,aAAa,MAAM,IAAI;YAC9E,eAAe,MAAM,YAAY,CAAC,aAAa,aAAa,SAAS;QACvE;QACA,eAAe,IAAI,CAAC,SAAU,IAAI;YAChC,CAAC,MAAM,MAAM,IAAI,IAAI,MAAM,KAAK,CAAC,MAAM,CAAC,KAAK,KAAK;QACpD;IACF;IACA,WAAW,SAAS,CAAC,QAAQ,GAAG,SAAU,SAAS;QACjD,MAAM,WAAW,IAAI,GAAG;IAC1B;IACA,WAAW,SAAS,CAAC,gBAAgB,GAAG,SAAU,eAAe,EAAE,MAAM;QACvE,IAAI,QAAQ,IAAI;QAChB,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,iBAAiB,SAAU,WAAW;YACzC,IAAI,cAAc,uKAAA,CAAA,UAAW,CAAC,wBAAwB,CAAC,aAAa,MAAM,IAAI;YAC9E,IAAI,aAAa;gBACf,IAAI,OAAO,YAAY,OAAO;gBAC9B,KAAK,iBAAiB,CAAC,SAAU,EAAE;oBACjC,IAAI,IAAI;wBACN,SAAS,CAAA,GAAA,mJAAA,CAAA,YAAS,AAAD,EAAE,MAAM,CAAA,GAAA,mJAAA,CAAA,YAAS,AAAD,EAAE;oBACrC;gBACF;YACF;QACF;IACF;IACA,WAAW,IAAI,GAAG;IAClB,OAAO;AACT,EAAE,sJAAA,CAAA,UAAa;uCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 640, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/marker/MarkPointView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SymbolDraw from '../../chart/helper/SymbolDraw.js';\nimport * as numberUtil from '../../util/number.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport * as markerHelper from './markerHelper.js';\nimport MarkerView from './MarkerView.js';\nimport MarkerModel from './MarkerModel.js';\nimport { isFunction, map, filter, curry, extend } from 'zrender/lib/core/util.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { getVisualFromData } from '../../visual/helper.js';\nfunction updateMarkerLayout(mpData, seriesModel, api) {\n  var coordSys = seriesModel.coordinateSystem;\n  mpData.each(function (idx) {\n    var itemModel = mpData.getItemModel(idx);\n    var point;\n    var xPx = numberUtil.parsePercent(itemModel.get('x'), api.getWidth());\n    var yPx = numberUtil.parsePercent(itemModel.get('y'), api.getHeight());\n    if (!isNaN(xPx) && !isNaN(yPx)) {\n      point = [xPx, yPx];\n    }\n    // Chart like bar may have there own marker positioning logic\n    else if (seriesModel.getMarkerPosition) {\n      // Use the getMarkerPosition\n      point = seriesModel.getMarkerPosition(mpData.getValues(mpData.dimensions, idx));\n    } else if (coordSys) {\n      var x = mpData.get(coordSys.dimensions[0], idx);\n      var y = mpData.get(coordSys.dimensions[1], idx);\n      point = coordSys.dataToPoint([x, y]);\n    }\n    // Use x, y if has any\n    if (!isNaN(xPx)) {\n      point[0] = xPx;\n    }\n    if (!isNaN(yPx)) {\n      point[1] = yPx;\n    }\n    mpData.setItemLayout(idx, point);\n  });\n}\nvar MarkPointView = /** @class */function (_super) {\n  __extends(MarkPointView, _super);\n  function MarkPointView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkPointView.type;\n    return _this;\n  }\n  MarkPointView.prototype.updateTransform = function (markPointModel, ecModel, api) {\n    ecModel.eachSeries(function (seriesModel) {\n      var mpModel = MarkerModel.getMarkerModelFromSeries(seriesModel, 'markPoint');\n      if (mpModel) {\n        updateMarkerLayout(mpModel.getData(), seriesModel, api);\n        this.markerGroupMap.get(seriesModel.id).updateLayout();\n      }\n    }, this);\n  };\n  MarkPointView.prototype.renderSeries = function (seriesModel, mpModel, ecModel, api) {\n    var coordSys = seriesModel.coordinateSystem;\n    var seriesId = seriesModel.id;\n    var seriesData = seriesModel.getData();\n    var symbolDrawMap = this.markerGroupMap;\n    var symbolDraw = symbolDrawMap.get(seriesId) || symbolDrawMap.set(seriesId, new SymbolDraw());\n    var mpData = createData(coordSys, seriesModel, mpModel);\n    // FIXME\n    mpModel.setData(mpData);\n    updateMarkerLayout(mpModel.getData(), seriesModel, api);\n    mpData.each(function (idx) {\n      var itemModel = mpData.getItemModel(idx);\n      var symbol = itemModel.getShallow('symbol');\n      var symbolSize = itemModel.getShallow('symbolSize');\n      var symbolRotate = itemModel.getShallow('symbolRotate');\n      var symbolOffset = itemModel.getShallow('symbolOffset');\n      var symbolKeepAspect = itemModel.getShallow('symbolKeepAspect');\n      // TODO: refactor needed: single data item should not support callback function\n      if (isFunction(symbol) || isFunction(symbolSize) || isFunction(symbolRotate) || isFunction(symbolOffset)) {\n        var rawIdx = mpModel.getRawValue(idx);\n        var dataParams = mpModel.getDataParams(idx);\n        if (isFunction(symbol)) {\n          symbol = symbol(rawIdx, dataParams);\n        }\n        if (isFunction(symbolSize)) {\n          // FIXME 这里不兼容 ECharts 2.x，2.x 貌似参数是整个数据？\n          symbolSize = symbolSize(rawIdx, dataParams);\n        }\n        if (isFunction(symbolRotate)) {\n          symbolRotate = symbolRotate(rawIdx, dataParams);\n        }\n        if (isFunction(symbolOffset)) {\n          symbolOffset = symbolOffset(rawIdx, dataParams);\n        }\n      }\n      var style = itemModel.getModel('itemStyle').getItemStyle();\n      var color = getVisualFromData(seriesData, 'color');\n      if (!style.fill) {\n        style.fill = color;\n      }\n      mpData.setItemVisual(idx, {\n        symbol: symbol,\n        symbolSize: symbolSize,\n        symbolRotate: symbolRotate,\n        symbolOffset: symbolOffset,\n        symbolKeepAspect: symbolKeepAspect,\n        style: style\n      });\n    });\n    // TODO Text are wrong\n    symbolDraw.updateData(mpData);\n    this.group.add(symbolDraw.group);\n    // Set host model for tooltip\n    // FIXME\n    mpData.eachItemGraphicEl(function (el) {\n      el.traverse(function (child) {\n        getECData(child).dataModel = mpModel;\n      });\n    });\n    this.markKeep(symbolDraw);\n    symbolDraw.group.silent = mpModel.get('silent') || seriesModel.get('silent');\n  };\n  MarkPointView.type = 'markPoint';\n  return MarkPointView;\n}(MarkerView);\nfunction createData(coordSys, seriesModel, mpModel) {\n  var coordDimsInfos;\n  if (coordSys) {\n    coordDimsInfos = map(coordSys && coordSys.dimensions, function (coordDim) {\n      var info = seriesModel.getData().getDimensionInfo(seriesModel.getData().mapDimension(coordDim)) || {};\n      // In map series data don't have lng and lat dimension. Fallback to same with coordSys\n      return extend(extend({}, info), {\n        name: coordDim,\n        // DON'T use ordinalMeta to parse and collect ordinal.\n        ordinalMeta: null\n      });\n    });\n  } else {\n    coordDimsInfos = [{\n      name: 'value',\n      type: 'float'\n    }];\n  }\n  var mpData = new SeriesData(coordDimsInfos, mpModel);\n  var dataOpt = map(mpModel.get('data'), curry(markerHelper.dataTransform, seriesModel));\n  if (coordSys) {\n    dataOpt = filter(dataOpt, curry(markerHelper.dataFilter, coordSys));\n  }\n  var dimValueGetter = markerHelper.createMarkerDimValueGetter(!!coordSys, coordDimsInfos);\n  mpData.initData(dataOpt, null, dimValueGetter);\n  return mpData;\n}\nexport default MarkPointView;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AACA,SAAS,mBAAmB,MAAM,EAAE,WAAW,EAAE,GAAG;IAClD,IAAI,WAAW,YAAY,gBAAgB;IAC3C,OAAO,IAAI,CAAC,SAAU,GAAG;QACvB,IAAI,YAAY,OAAO,YAAY,CAAC;QACpC,IAAI;QACJ,IAAI,MAAM,CAAA,GAAA,mJAAA,CAAA,eAAuB,AAAD,EAAE,UAAU,GAAG,CAAC,MAAM,IAAI,QAAQ;QAClE,IAAI,MAAM,CAAA,GAAA,mJAAA,CAAA,eAAuB,AAAD,EAAE,UAAU,GAAG,CAAC,MAAM,IAAI,SAAS;QACnE,IAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,MAAM;YAC9B,QAAQ;gBAAC;gBAAK;aAAI;QACpB,OAEK,IAAI,YAAY,iBAAiB,EAAE;YACtC,4BAA4B;YAC5B,QAAQ,YAAY,iBAAiB,CAAC,OAAO,SAAS,CAAC,OAAO,UAAU,EAAE;QAC5E,OAAO,IAAI,UAAU;YACnB,IAAI,IAAI,OAAO,GAAG,CAAC,SAAS,UAAU,CAAC,EAAE,EAAE;YAC3C,IAAI,IAAI,OAAO,GAAG,CAAC,SAAS,UAAU,CAAC,EAAE,EAAE;YAC3C,QAAQ,SAAS,WAAW,CAAC;gBAAC;gBAAG;aAAE;QACrC;QACA,sBAAsB;QACtB,IAAI,CAAC,MAAM,MAAM;YACf,KAAK,CAAC,EAAE,GAAG;QACb;QACA,IAAI,CAAC,MAAM,MAAM;YACf,KAAK,CAAC,EAAE,GAAG;QACb;QACA,OAAO,aAAa,CAAC,KAAK;IAC5B;AACF;AACA,IAAI,gBAAgB,WAAW,GAAE,SAAU,MAAM;IAC/C,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,eAAe;IACzB,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,cAAc,IAAI;QAC/B,OAAO;IACT;IACA,cAAc,SAAS,CAAC,eAAe,GAAG,SAAU,cAAc,EAAE,OAAO,EAAE,GAAG;QAC9E,QAAQ,UAAU,CAAC,SAAU,WAAW;YACtC,IAAI,UAAU,uKAAA,CAAA,UAAW,CAAC,wBAAwB,CAAC,aAAa;YAChE,IAAI,SAAS;gBACX,mBAAmB,QAAQ,OAAO,IAAI,aAAa;gBACnD,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,EAAE,EAAE,YAAY;YACtD;QACF,GAAG,IAAI;IACT;IACA,cAAc,SAAS,CAAC,YAAY,GAAG,SAAU,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;QACjF,IAAI,WAAW,YAAY,gBAAgB;QAC3C,IAAI,WAAW,YAAY,EAAE;QAC7B,IAAI,aAAa,YAAY,OAAO;QACpC,IAAI,gBAAgB,IAAI,CAAC,cAAc;QACvC,IAAI,aAAa,cAAc,GAAG,CAAC,aAAa,cAAc,GAAG,CAAC,UAAU,IAAI,kKAAA,CAAA,UAAU;QAC1F,IAAI,SAAS,WAAW,UAAU,aAAa;QAC/C,QAAQ;QACR,QAAQ,OAAO,CAAC;QAChB,mBAAmB,QAAQ,OAAO,IAAI,aAAa;QACnD,OAAO,IAAI,CAAC,SAAU,GAAG;YACvB,IAAI,YAAY,OAAO,YAAY,CAAC;YACpC,IAAI,SAAS,UAAU,UAAU,CAAC;YAClC,IAAI,aAAa,UAAU,UAAU,CAAC;YACtC,IAAI,eAAe,UAAU,UAAU,CAAC;YACxC,IAAI,eAAe,UAAU,UAAU,CAAC;YACxC,IAAI,mBAAmB,UAAU,UAAU,CAAC;YAC5C,+EAA+E;YAC/E,IAAI,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE,WAAW,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE,eAAe,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE,iBAAiB,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE,eAAe;gBACxG,IAAI,SAAS,QAAQ,WAAW,CAAC;gBACjC,IAAI,aAAa,QAAQ,aAAa,CAAC;gBACvC,IAAI,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE,SAAS;oBACtB,SAAS,OAAO,QAAQ;gBAC1B;gBACA,IAAI,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE,aAAa;oBAC1B,yCAAyC;oBACzC,aAAa,WAAW,QAAQ;gBAClC;gBACA,IAAI,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE,eAAe;oBAC5B,eAAe,aAAa,QAAQ;gBACtC;gBACA,IAAI,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE,eAAe;oBAC5B,eAAe,aAAa,QAAQ;gBACtC;YACF;YACA,IAAI,QAAQ,UAAU,QAAQ,CAAC,aAAa,YAAY;YACxD,IAAI,QAAQ,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;YAC1C,IAAI,CAAC,MAAM,IAAI,EAAE;gBACf,MAAM,IAAI,GAAG;YACf;YACA,OAAO,aAAa,CAAC,KAAK;gBACxB,QAAQ;gBACR,YAAY;gBACZ,cAAc;gBACd,cAAc;gBACd,kBAAkB;gBAClB,OAAO;YACT;QACF;QACA,sBAAsB;QACtB,WAAW,UAAU,CAAC;QACtB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,WAAW,KAAK;QAC/B,6BAA6B;QAC7B,QAAQ;QACR,OAAO,iBAAiB,CAAC,SAAU,EAAE;YACnC,GAAG,QAAQ,CAAC,SAAU,KAAK;gBACzB,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE,OAAO,SAAS,GAAG;YAC/B;QACF;QACA,IAAI,CAAC,QAAQ,CAAC;QACd,WAAW,KAAK,CAAC,MAAM,GAAG,QAAQ,GAAG,CAAC,aAAa,YAAY,GAAG,CAAC;IACrE;IACA,cAAc,IAAI,GAAG;IACrB,OAAO;AACT,EAAE,sKAAA,CAAA,UAAU;AACZ,SAAS,WAAW,QAAQ,EAAE,WAAW,EAAE,OAAO;IAChD,IAAI;IACJ,IAAI,UAAU;QACZ,iBAAiB,CAAA,GAAA,iJAAA,CAAA,MAAG,AAAD,EAAE,YAAY,SAAS,UAAU,EAAE,SAAU,QAAQ;YACtE,IAAI,OAAO,YAAY,OAAO,GAAG,gBAAgB,CAAC,YAAY,OAAO,GAAG,YAAY,CAAC,cAAc,CAAC;YACpG,sFAAsF;YACtF,OAAO,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,OAAO;gBAC9B,MAAM;gBACN,sDAAsD;gBACtD,aAAa;YACf;QACF;IACF,OAAO;QACL,iBAAiB;YAAC;gBAChB,MAAM;gBACN,MAAM;YACR;SAAE;IACJ;IACA,IAAI,SAAS,IAAI,uJAAA,CAAA,UAAU,CAAC,gBAAgB;IAC5C,IAAI,UAAU,CAAA,GAAA,iJAAA,CAAA,MAAG,AAAD,EAAE,QAAQ,GAAG,CAAC,SAAS,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,wKAAA,CAAA,gBAA0B,EAAE;IACzE,IAAI,UAAU;QACZ,UAAU,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,wKAAA,CAAA,aAAuB,EAAE;IAC3D;IACA,IAAI,iBAAiB,CAAA,GAAA,wKAAA,CAAA,6BAAuC,AAAD,EAAE,CAAC,CAAC,UAAU;IACzE,OAAO,QAAQ,CAAC,SAAS,MAAM;IAC/B,OAAO;AACT;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 849, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/marker/installMarkPoint.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nimport checkMarkerInSeries from './checkMarkerInSeries.js';\nimport MarkPointModel from './MarkPointModel.js';\nimport MarkPointView from './MarkPointView.js';\nexport function install(registers) {\n  registers.registerComponentModel(MarkPointModel);\n  registers.registerComponentView(MarkPointView);\n  registers.registerPreprocessor(function (opt) {\n    if (checkMarkerInSeries(opt.series, 'markPoint')) {\n      // Make sure markPoint component is enabled\n      opt.markPoint = opt.markPoint || {};\n    }\n  });\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC;;;AAED;AACA;AACA;;;;AACO,SAAS,QAAQ,SAAS;IAC/B,UAAU,sBAAsB,CAAC,0KAAA,CAAA,UAAc;IAC/C,UAAU,qBAAqB,CAAC,yKAAA,CAAA,UAAa;IAC7C,UAAU,oBAAoB,CAAC,SAAU,GAAG;QAC1C,IAAI,CAAA,GAAA,+KAAA,CAAA,UAAmB,AAAD,EAAE,IAAI,MAAM,EAAE,cAAc;YAChD,2CAA2C;YAC3C,IAAI,SAAS,GAAG,IAAI,SAAS,IAAI,CAAC;QACpC;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 903, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/marker/MarkLineModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport MarkerModel from './MarkerModel.js';\nvar MarkLineModel = /** @class */function (_super) {\n  __extends(MarkLineModel, _super);\n  function MarkLineModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkLineModel.type;\n    return _this;\n  }\n  MarkLineModel.prototype.createMarkerModelFromSeries = function (markerOpt, masterMarkerModel, ecModel) {\n    return new MarkLineModel(markerOpt, masterMarkerModel, ecModel);\n  };\n  MarkLineModel.type = 'markLine';\n  MarkLineModel.defaultOption = {\n    // zlevel: 0,\n    z: 5,\n    symbol: ['circle', 'arrow'],\n    symbolSize: [8, 16],\n    // symbolRotate: 0,\n    symbolOffset: 0,\n    precision: 2,\n    tooltip: {\n      trigger: 'item'\n    },\n    label: {\n      show: true,\n      position: 'end',\n      distance: 5\n    },\n    lineStyle: {\n      type: 'dashed'\n    },\n    emphasis: {\n      label: {\n        show: true\n      },\n      lineStyle: {\n        width: 3\n      }\n    },\n    animationEasing: 'linear'\n  };\n  return MarkLineModel;\n}(MarkerModel);\nexport default MarkLineModel;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;;;AACA,IAAI,gBAAgB,WAAW,GAAE,SAAU,MAAM;IAC/C,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,eAAe;IACzB,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,cAAc,IAAI;QAC/B,OAAO;IACT;IACA,cAAc,SAAS,CAAC,2BAA2B,GAAG,SAAU,SAAS,EAAE,iBAAiB,EAAE,OAAO;QACnG,OAAO,IAAI,cAAc,WAAW,mBAAmB;IACzD;IACA,cAAc,IAAI,GAAG;IACrB,cAAc,aAAa,GAAG;QAC5B,aAAa;QACb,GAAG;QACH,QAAQ;YAAC;YAAU;SAAQ;QAC3B,YAAY;YAAC;YAAG;SAAG;QACnB,mBAAmB;QACnB,cAAc;QACd,WAAW;QACX,SAAS;YACP,SAAS;QACX;QACA,OAAO;YACL,MAAM;YACN,UAAU;YACV,UAAU;QACZ;QACA,WAAW;YACT,MAAM;QACR;QACA,UAAU;YACR,OAAO;gBACL,MAAM;YACR;YACA,WAAW;gBACT,OAAO;YACT;QACF;QACA,iBAAiB;IACnB;IACA,OAAO;AACT,EAAE,uKAAA,CAAA,UAAW;uCACE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1001, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/marker/MarkLineView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesData from '../../data/SeriesData.js';\nimport * as numberUtil from '../../util/number.js';\nimport * as markerHelper from './markerHelper.js';\nimport LineDraw from '../../chart/helper/LineDraw.js';\nimport MarkerView from './MarkerView.js';\nimport { getStackedDimension } from '../../data/helper/dataStackHelper.js';\nimport { isCoordinateSystemType } from '../../coord/CoordinateSystem.js';\nimport { getECData } from '../../util/innerStore.js';\nimport MarkerModel from './MarkerModel.js';\nimport { isArray, retrieve, retrieve2, clone, extend, logError, merge, map, curry, filter, isNumber } from 'zrender/lib/core/util.js';\nimport { makeInner } from '../../util/model.js';\nimport { getVisualFromData } from '../../visual/helper.js';\nvar inner = makeInner();\nvar markLineTransform = function (seriesModel, coordSys, mlModel, item) {\n  var data = seriesModel.getData();\n  var itemArray;\n  if (!isArray(item)) {\n    // Special type markLine like 'min', 'max', 'average', 'median'\n    var mlType = item.type;\n    if (mlType === 'min' || mlType === 'max' || mlType === 'average' || mlType === 'median'\n    // In case\n    // data: [{\n    //   yAxis: 10\n    // }]\n    || item.xAxis != null || item.yAxis != null) {\n      var valueAxis = void 0;\n      var value = void 0;\n      if (item.yAxis != null || item.xAxis != null) {\n        valueAxis = coordSys.getAxis(item.yAxis != null ? 'y' : 'x');\n        value = retrieve(item.yAxis, item.xAxis);\n      } else {\n        var axisInfo = markerHelper.getAxisInfo(item, data, coordSys, seriesModel);\n        valueAxis = axisInfo.valueAxis;\n        var valueDataDim = getStackedDimension(data, axisInfo.valueDataDim);\n        value = markerHelper.numCalculate(data, valueDataDim, mlType);\n      }\n      var valueIndex = valueAxis.dim === 'x' ? 0 : 1;\n      var baseIndex = 1 - valueIndex;\n      // Normized to 2d data with start and end point\n      var mlFrom = clone(item);\n      var mlTo = {\n        coord: []\n      };\n      mlFrom.type = null;\n      mlFrom.coord = [];\n      mlFrom.coord[baseIndex] = -Infinity;\n      mlTo.coord[baseIndex] = Infinity;\n      var precision = mlModel.get('precision');\n      if (precision >= 0 && isNumber(value)) {\n        value = +value.toFixed(Math.min(precision, 20));\n      }\n      mlFrom.coord[valueIndex] = mlTo.coord[valueIndex] = value;\n      itemArray = [mlFrom, mlTo, {\n        type: mlType,\n        valueIndex: item.valueIndex,\n        // Force to use the value of calculated value.\n        value: value\n      }];\n    } else {\n      // Invalid data\n      if (process.env.NODE_ENV !== 'production') {\n        logError('Invalid markLine data.');\n      }\n      itemArray = [];\n    }\n  } else {\n    itemArray = item;\n  }\n  var normalizedItem = [markerHelper.dataTransform(seriesModel, itemArray[0]), markerHelper.dataTransform(seriesModel, itemArray[1]), extend({}, itemArray[2])];\n  // Avoid line data type is extended by from(to) data type\n  normalizedItem[2].type = normalizedItem[2].type || null;\n  // Merge from option and to option into line option\n  merge(normalizedItem[2], normalizedItem[0]);\n  merge(normalizedItem[2], normalizedItem[1]);\n  return normalizedItem;\n};\nfunction isInfinity(val) {\n  return !isNaN(val) && !isFinite(val);\n}\n// If a markLine has one dim\nfunction ifMarkLineHasOnlyDim(dimIndex, fromCoord, toCoord, coordSys) {\n  var otherDimIndex = 1 - dimIndex;\n  var dimName = coordSys.dimensions[dimIndex];\n  return isInfinity(fromCoord[otherDimIndex]) && isInfinity(toCoord[otherDimIndex]) && fromCoord[dimIndex] === toCoord[dimIndex] && coordSys.getAxis(dimName).containData(fromCoord[dimIndex]);\n}\nfunction markLineFilter(coordSys, item) {\n  if (coordSys.type === 'cartesian2d') {\n    var fromCoord = item[0].coord;\n    var toCoord = item[1].coord;\n    // In case\n    // {\n    //  markLine: {\n    //    data: [{ yAxis: 2 }]\n    //  }\n    // }\n    if (fromCoord && toCoord && (ifMarkLineHasOnlyDim(1, fromCoord, toCoord, coordSys) || ifMarkLineHasOnlyDim(0, fromCoord, toCoord, coordSys))) {\n      return true;\n    }\n  }\n  return markerHelper.dataFilter(coordSys, item[0]) && markerHelper.dataFilter(coordSys, item[1]);\n}\nfunction updateSingleMarkerEndLayout(data, idx, isFrom, seriesModel, api) {\n  var coordSys = seriesModel.coordinateSystem;\n  var itemModel = data.getItemModel(idx);\n  var point;\n  var xPx = numberUtil.parsePercent(itemModel.get('x'), api.getWidth());\n  var yPx = numberUtil.parsePercent(itemModel.get('y'), api.getHeight());\n  if (!isNaN(xPx) && !isNaN(yPx)) {\n    point = [xPx, yPx];\n  } else {\n    // Chart like bar may have there own marker positioning logic\n    if (seriesModel.getMarkerPosition) {\n      // Use the getMarkerPosition\n      point = seriesModel.getMarkerPosition(data.getValues(data.dimensions, idx));\n    } else {\n      var dims = coordSys.dimensions;\n      var x = data.get(dims[0], idx);\n      var y = data.get(dims[1], idx);\n      point = coordSys.dataToPoint([x, y]);\n    }\n    // Expand line to the edge of grid if value on one axis is Inifnity\n    // In case\n    //  markLine: {\n    //    data: [{\n    //      yAxis: 2\n    //      // or\n    //      type: 'average'\n    //    }]\n    //  }\n    if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n      // TODO: TYPE ts@4.1 may still infer it as Axis instead of Axis2D. Not sure if it's a bug\n      var xAxis = coordSys.getAxis('x');\n      var yAxis = coordSys.getAxis('y');\n      var dims = coordSys.dimensions;\n      if (isInfinity(data.get(dims[0], idx))) {\n        point[0] = xAxis.toGlobalCoord(xAxis.getExtent()[isFrom ? 0 : 1]);\n      } else if (isInfinity(data.get(dims[1], idx))) {\n        point[1] = yAxis.toGlobalCoord(yAxis.getExtent()[isFrom ? 0 : 1]);\n      }\n    }\n    // Use x, y if has any\n    if (!isNaN(xPx)) {\n      point[0] = xPx;\n    }\n    if (!isNaN(yPx)) {\n      point[1] = yPx;\n    }\n  }\n  data.setItemLayout(idx, point);\n}\nvar MarkLineView = /** @class */function (_super) {\n  __extends(MarkLineView, _super);\n  function MarkLineView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkLineView.type;\n    return _this;\n  }\n  MarkLineView.prototype.updateTransform = function (markLineModel, ecModel, api) {\n    ecModel.eachSeries(function (seriesModel) {\n      var mlModel = MarkerModel.getMarkerModelFromSeries(seriesModel, 'markLine');\n      if (mlModel) {\n        var mlData_1 = mlModel.getData();\n        var fromData_1 = inner(mlModel).from;\n        var toData_1 = inner(mlModel).to;\n        // Update visual and layout of from symbol and to symbol\n        fromData_1.each(function (idx) {\n          updateSingleMarkerEndLayout(fromData_1, idx, true, seriesModel, api);\n          updateSingleMarkerEndLayout(toData_1, idx, false, seriesModel, api);\n        });\n        // Update layout of line\n        mlData_1.each(function (idx) {\n          mlData_1.setItemLayout(idx, [fromData_1.getItemLayout(idx), toData_1.getItemLayout(idx)]);\n        });\n        this.markerGroupMap.get(seriesModel.id).updateLayout();\n      }\n    }, this);\n  };\n  MarkLineView.prototype.renderSeries = function (seriesModel, mlModel, ecModel, api) {\n    var coordSys = seriesModel.coordinateSystem;\n    var seriesId = seriesModel.id;\n    var seriesData = seriesModel.getData();\n    var lineDrawMap = this.markerGroupMap;\n    var lineDraw = lineDrawMap.get(seriesId) || lineDrawMap.set(seriesId, new LineDraw());\n    this.group.add(lineDraw.group);\n    var mlData = createList(coordSys, seriesModel, mlModel);\n    var fromData = mlData.from;\n    var toData = mlData.to;\n    var lineData = mlData.line;\n    inner(mlModel).from = fromData;\n    inner(mlModel).to = toData;\n    // Line data for tooltip and formatter\n    mlModel.setData(lineData);\n    // TODO\n    // Functionally, `symbolSize` & `symbolOffset` can also be 2D array now.\n    // But the related logic and type definition are not finished yet.\n    // Finish it if required\n    var symbolType = mlModel.get('symbol');\n    var symbolSize = mlModel.get('symbolSize');\n    var symbolRotate = mlModel.get('symbolRotate');\n    var symbolOffset = mlModel.get('symbolOffset');\n    // TODO: support callback function like markPoint\n    if (!isArray(symbolType)) {\n      symbolType = [symbolType, symbolType];\n    }\n    if (!isArray(symbolSize)) {\n      symbolSize = [symbolSize, symbolSize];\n    }\n    if (!isArray(symbolRotate)) {\n      symbolRotate = [symbolRotate, symbolRotate];\n    }\n    if (!isArray(symbolOffset)) {\n      symbolOffset = [symbolOffset, symbolOffset];\n    }\n    // Update visual and layout of from symbol and to symbol\n    mlData.from.each(function (idx) {\n      updateDataVisualAndLayout(fromData, idx, true);\n      updateDataVisualAndLayout(toData, idx, false);\n    });\n    // Update visual and layout of line\n    lineData.each(function (idx) {\n      var lineStyle = lineData.getItemModel(idx).getModel('lineStyle').getLineStyle();\n      // lineData.setItemVisual(idx, {\n      //     color: lineColor || fromData.getItemVisual(idx, 'color')\n      // });\n      lineData.setItemLayout(idx, [fromData.getItemLayout(idx), toData.getItemLayout(idx)]);\n      if (lineStyle.stroke == null) {\n        lineStyle.stroke = fromData.getItemVisual(idx, 'style').fill;\n      }\n      lineData.setItemVisual(idx, {\n        fromSymbolKeepAspect: fromData.getItemVisual(idx, 'symbolKeepAspect'),\n        fromSymbolOffset: fromData.getItemVisual(idx, 'symbolOffset'),\n        fromSymbolRotate: fromData.getItemVisual(idx, 'symbolRotate'),\n        fromSymbolSize: fromData.getItemVisual(idx, 'symbolSize'),\n        fromSymbol: fromData.getItemVisual(idx, 'symbol'),\n        toSymbolKeepAspect: toData.getItemVisual(idx, 'symbolKeepAspect'),\n        toSymbolOffset: toData.getItemVisual(idx, 'symbolOffset'),\n        toSymbolRotate: toData.getItemVisual(idx, 'symbolRotate'),\n        toSymbolSize: toData.getItemVisual(idx, 'symbolSize'),\n        toSymbol: toData.getItemVisual(idx, 'symbol'),\n        style: lineStyle\n      });\n    });\n    lineDraw.updateData(lineData);\n    // Set host model for tooltip\n    // FIXME\n    mlData.line.eachItemGraphicEl(function (el) {\n      getECData(el).dataModel = mlModel;\n      el.traverse(function (child) {\n        getECData(child).dataModel = mlModel;\n      });\n    });\n    function updateDataVisualAndLayout(data, idx, isFrom) {\n      var itemModel = data.getItemModel(idx);\n      updateSingleMarkerEndLayout(data, idx, isFrom, seriesModel, api);\n      var style = itemModel.getModel('itemStyle').getItemStyle();\n      if (style.fill == null) {\n        style.fill = getVisualFromData(seriesData, 'color');\n      }\n      data.setItemVisual(idx, {\n        symbolKeepAspect: itemModel.get('symbolKeepAspect'),\n        // `0` should be considered as a valid value, so use `retrieve2` instead of `||`\n        symbolOffset: retrieve2(itemModel.get('symbolOffset', true), symbolOffset[isFrom ? 0 : 1]),\n        symbolRotate: retrieve2(itemModel.get('symbolRotate', true), symbolRotate[isFrom ? 0 : 1]),\n        // TODO: when 2d array is supported, it should ignore parent\n        symbolSize: retrieve2(itemModel.get('symbolSize'), symbolSize[isFrom ? 0 : 1]),\n        symbol: retrieve2(itemModel.get('symbol', true), symbolType[isFrom ? 0 : 1]),\n        style: style\n      });\n    }\n    this.markKeep(lineDraw);\n    lineDraw.group.silent = mlModel.get('silent') || seriesModel.get('silent');\n  };\n  MarkLineView.type = 'markLine';\n  return MarkLineView;\n}(MarkerView);\nfunction createList(coordSys, seriesModel, mlModel) {\n  var coordDimsInfos;\n  if (coordSys) {\n    coordDimsInfos = map(coordSys && coordSys.dimensions, function (coordDim) {\n      var info = seriesModel.getData().getDimensionInfo(seriesModel.getData().mapDimension(coordDim)) || {};\n      // In map series data don't have lng and lat dimension. Fallback to same with coordSys\n      return extend(extend({}, info), {\n        name: coordDim,\n        // DON'T use ordinalMeta to parse and collect ordinal.\n        ordinalMeta: null\n      });\n    });\n  } else {\n    coordDimsInfos = [{\n      name: 'value',\n      type: 'float'\n    }];\n  }\n  var fromData = new SeriesData(coordDimsInfos, mlModel);\n  var toData = new SeriesData(coordDimsInfos, mlModel);\n  // No dimensions\n  var lineData = new SeriesData([], mlModel);\n  var optData = map(mlModel.get('data'), curry(markLineTransform, seriesModel, coordSys, mlModel));\n  if (coordSys) {\n    optData = filter(optData, curry(markLineFilter, coordSys));\n  }\n  var dimValueGetter = markerHelper.createMarkerDimValueGetter(!!coordSys, coordDimsInfos);\n  fromData.initData(map(optData, function (item) {\n    return item[0];\n  }), null, dimValueGetter);\n  toData.initData(map(optData, function (item) {\n    return item[1];\n  }), null, dimValueGetter);\n  lineData.initData(map(optData, function (item) {\n    return item[2];\n  }));\n  lineData.hasItemOption = true;\n  return {\n    from: fromData,\n    to: toData,\n    line: lineData\n  };\n}\nexport default MarkLineView;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AA8DU;AA7DV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AACA,IAAI,QAAQ,CAAA,GAAA,kJAAA,CAAA,YAAS,AAAD;AACpB,IAAI,oBAAoB,SAAU,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI;IACpE,IAAI,OAAO,YAAY,OAAO;IAC9B,IAAI;IACJ,IAAI,CAAC,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,OAAO;QAClB,+DAA+D;QAC/D,IAAI,SAAS,KAAK,IAAI;QACtB,IAAI,WAAW,SAAS,WAAW,SAAS,WAAW,aAAa,WAAW,YAK5E,KAAK,KAAK,IAAI,QAAQ,KAAK,KAAK,IAAI,MAAM;YAC3C,IAAI,YAAY,KAAK;YACrB,IAAI,QAAQ,KAAK;YACjB,IAAI,KAAK,KAAK,IAAI,QAAQ,KAAK,KAAK,IAAI,MAAM;gBAC5C,YAAY,SAAS,OAAO,CAAC,KAAK,KAAK,IAAI,OAAO,MAAM;gBACxD,QAAQ,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,KAAK,EAAE,KAAK,KAAK;YACzC,OAAO;gBACL,IAAI,WAAW,CAAA,GAAA,wKAAA,CAAA,cAAwB,AAAD,EAAE,MAAM,MAAM,UAAU;gBAC9D,YAAY,SAAS,SAAS;gBAC9B,IAAI,eAAe,CAAA,GAAA,sKAAA,CAAA,sBAAmB,AAAD,EAAE,MAAM,SAAS,YAAY;gBAClE,QAAQ,CAAA,GAAA,wKAAA,CAAA,eAAyB,AAAD,EAAE,MAAM,cAAc;YACxD;YACA,IAAI,aAAa,UAAU,GAAG,KAAK,MAAM,IAAI;YAC7C,IAAI,YAAY,IAAI;YACpB,+CAA+C;YAC/C,IAAI,SAAS,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;YACnB,IAAI,OAAO;gBACT,OAAO,EAAE;YACX;YACA,OAAO,IAAI,GAAG;YACd,OAAO,KAAK,GAAG,EAAE;YACjB,OAAO,KAAK,CAAC,UAAU,GAAG,CAAC;YAC3B,KAAK,KAAK,CAAC,UAAU,GAAG;YACxB,IAAI,YAAY,QAAQ,GAAG,CAAC;YAC5B,IAAI,aAAa,KAAK,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,QAAQ;gBACrC,QAAQ,CAAC,MAAM,OAAO,CAAC,KAAK,GAAG,CAAC,WAAW;YAC7C;YACA,OAAO,KAAK,CAAC,WAAW,GAAG,KAAK,KAAK,CAAC,WAAW,GAAG;YACpD,YAAY;gBAAC;gBAAQ;gBAAM;oBACzB,MAAM;oBACN,YAAY,KAAK,UAAU;oBAC3B,8CAA8C;oBAC9C,OAAO;gBACT;aAAE;QACJ,OAAO;YACL,eAAe;YACf,wCAA2C;gBACzC,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE;YACX;YACA,YAAY,EAAE;QAChB;IACF,OAAO;QACL,YAAY;IACd;IACA,IAAI,iBAAiB;QAAC,CAAA,GAAA,wKAAA,CAAA,gBAA0B,AAAD,EAAE,aAAa,SAAS,CAAC,EAAE;QAAG,CAAA,GAAA,wKAAA,CAAA,gBAA0B,AAAD,EAAE,aAAa,SAAS,CAAC,EAAE;QAAG,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE;KAAE;IAC7J,yDAAyD;IACzD,cAAc,CAAC,EAAE,CAAC,IAAI,GAAG,cAAc,CAAC,EAAE,CAAC,IAAI,IAAI;IACnD,mDAAmD;IACnD,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,cAAc,CAAC,EAAE,EAAE,cAAc,CAAC,EAAE;IAC1C,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,cAAc,CAAC,EAAE,EAAE,cAAc,CAAC,EAAE;IAC1C,OAAO;AACT;AACA,SAAS,WAAW,GAAG;IACrB,OAAO,CAAC,MAAM,QAAQ,CAAC,SAAS;AAClC;AACA,4BAA4B;AAC5B,SAAS,qBAAqB,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ;IAClE,IAAI,gBAAgB,IAAI;IACxB,IAAI,UAAU,SAAS,UAAU,CAAC,SAAS;IAC3C,OAAO,WAAW,SAAS,CAAC,cAAc,KAAK,WAAW,OAAO,CAAC,cAAc,KAAK,SAAS,CAAC,SAAS,KAAK,OAAO,CAAC,SAAS,IAAI,SAAS,OAAO,CAAC,SAAS,WAAW,CAAC,SAAS,CAAC,SAAS;AAC7L;AACA,SAAS,eAAe,QAAQ,EAAE,IAAI;IACpC,IAAI,SAAS,IAAI,KAAK,eAAe;QACnC,IAAI,YAAY,IAAI,CAAC,EAAE,CAAC,KAAK;QAC7B,IAAI,UAAU,IAAI,CAAC,EAAE,CAAC,KAAK;QAC3B,UAAU;QACV,IAAI;QACJ,eAAe;QACf,0BAA0B;QAC1B,KAAK;QACL,IAAI;QACJ,IAAI,aAAa,WAAW,CAAC,qBAAqB,GAAG,WAAW,SAAS,aAAa,qBAAqB,GAAG,WAAW,SAAS,SAAS,GAAG;YAC5I,OAAO;QACT;IACF;IACA,OAAO,CAAA,GAAA,wKAAA,CAAA,aAAuB,AAAD,EAAE,UAAU,IAAI,CAAC,EAAE,KAAK,CAAA,GAAA,wKAAA,CAAA,aAAuB,AAAD,EAAE,UAAU,IAAI,CAAC,EAAE;AAChG;AACA,SAAS,4BAA4B,IAAI,EAAE,GAAG,EAAE,MAAM,EAAE,WAAW,EAAE,GAAG;IACtE,IAAI,WAAW,YAAY,gBAAgB;IAC3C,IAAI,YAAY,KAAK,YAAY,CAAC;IAClC,IAAI;IACJ,IAAI,MAAM,CAAA,GAAA,mJAAA,CAAA,eAAuB,AAAD,EAAE,UAAU,GAAG,CAAC,MAAM,IAAI,QAAQ;IAClE,IAAI,MAAM,CAAA,GAAA,mJAAA,CAAA,eAAuB,AAAD,EAAE,UAAU,GAAG,CAAC,MAAM,IAAI,SAAS;IACnE,IAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,MAAM;QAC9B,QAAQ;YAAC;YAAK;SAAI;IACpB,OAAO;QACL,6DAA6D;QAC7D,IAAI,YAAY,iBAAiB,EAAE;YACjC,4BAA4B;YAC5B,QAAQ,YAAY,iBAAiB,CAAC,KAAK,SAAS,CAAC,KAAK,UAAU,EAAE;QACxE,OAAO;YACL,IAAI,OAAO,SAAS,UAAU;YAC9B,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE;YAC1B,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE;YAC1B,QAAQ,SAAS,WAAW,CAAC;gBAAC;gBAAG;aAAE;QACrC;QACA,mEAAmE;QACnE,UAAU;QACV,eAAe;QACf,cAAc;QACd,gBAAgB;QAChB,aAAa;QACb,uBAAuB;QACvB,QAAQ;QACR,KAAK;QACL,IAAI,CAAA,GAAA,8JAAA,CAAA,yBAAsB,AAAD,EAAE,UAAU,gBAAgB;YACnD,yFAAyF;YACzF,IAAI,QAAQ,SAAS,OAAO,CAAC;YAC7B,IAAI,QAAQ,SAAS,OAAO,CAAC;YAC7B,IAAI,OAAO,SAAS,UAAU;YAC9B,IAAI,WAAW,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO;gBACtC,KAAK,CAAC,EAAE,GAAG,MAAM,aAAa,CAAC,MAAM,SAAS,EAAE,CAAC,SAAS,IAAI,EAAE;YAClE,OAAO,IAAI,WAAW,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE,OAAO;gBAC7C,KAAK,CAAC,EAAE,GAAG,MAAM,aAAa,CAAC,MAAM,SAAS,EAAE,CAAC,SAAS,IAAI,EAAE;YAClE;QACF;QACA,sBAAsB;QACtB,IAAI,CAAC,MAAM,MAAM;YACf,KAAK,CAAC,EAAE,GAAG;QACb;QACA,IAAI,CAAC,MAAM,MAAM;YACf,KAAK,CAAC,EAAE,GAAG;QACb;IACF;IACA,KAAK,aAAa,CAAC,KAAK;AAC1B;AACA,IAAI,eAAe,WAAW,GAAE,SAAU,MAAM;IAC9C,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;IACxB,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,aAAa,IAAI;QAC9B,OAAO;IACT;IACA,aAAa,SAAS,CAAC,eAAe,GAAG,SAAU,aAAa,EAAE,OAAO,EAAE,GAAG;QAC5E,QAAQ,UAAU,CAAC,SAAU,WAAW;YACtC,IAAI,UAAU,uKAAA,CAAA,UAAW,CAAC,wBAAwB,CAAC,aAAa;YAChE,IAAI,SAAS;gBACX,IAAI,WAAW,QAAQ,OAAO;gBAC9B,IAAI,aAAa,MAAM,SAAS,IAAI;gBACpC,IAAI,WAAW,MAAM,SAAS,EAAE;gBAChC,wDAAwD;gBACxD,WAAW,IAAI,CAAC,SAAU,GAAG;oBAC3B,4BAA4B,YAAY,KAAK,MAAM,aAAa;oBAChE,4BAA4B,UAAU,KAAK,OAAO,aAAa;gBACjE;gBACA,wBAAwB;gBACxB,SAAS,IAAI,CAAC,SAAU,GAAG;oBACzB,SAAS,aAAa,CAAC,KAAK;wBAAC,WAAW,aAAa,CAAC;wBAAM,SAAS,aAAa,CAAC;qBAAK;gBAC1F;gBACA,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,YAAY,EAAE,EAAE,YAAY;YACtD;QACF,GAAG,IAAI;IACT;IACA,aAAa,SAAS,CAAC,YAAY,GAAG,SAAU,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;QAChF,IAAI,WAAW,YAAY,gBAAgB;QAC3C,IAAI,WAAW,YAAY,EAAE;QAC7B,IAAI,aAAa,YAAY,OAAO;QACpC,IAAI,cAAc,IAAI,CAAC,cAAc;QACrC,IAAI,WAAW,YAAY,GAAG,CAAC,aAAa,YAAY,GAAG,CAAC,UAAU,IAAI,gKAAA,CAAA,UAAQ;QAClF,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,SAAS,KAAK;QAC7B,IAAI,SAAS,WAAW,UAAU,aAAa;QAC/C,IAAI,WAAW,OAAO,IAAI;QAC1B,IAAI,SAAS,OAAO,EAAE;QACtB,IAAI,WAAW,OAAO,IAAI;QAC1B,MAAM,SAAS,IAAI,GAAG;QACtB,MAAM,SAAS,EAAE,GAAG;QACpB,sCAAsC;QACtC,QAAQ,OAAO,CAAC;QAChB,OAAO;QACP,wEAAwE;QACxE,kEAAkE;QAClE,wBAAwB;QACxB,IAAI,aAAa,QAAQ,GAAG,CAAC;QAC7B,IAAI,aAAa,QAAQ,GAAG,CAAC;QAC7B,IAAI,eAAe,QAAQ,GAAG,CAAC;QAC/B,IAAI,eAAe,QAAQ,GAAG,CAAC;QAC/B,iDAAiD;QACjD,IAAI,CAAC,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,aAAa;YACxB,aAAa;gBAAC;gBAAY;aAAW;QACvC;QACA,IAAI,CAAC,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,aAAa;YACxB,aAAa;gBAAC;gBAAY;aAAW;QACvC;QACA,IAAI,CAAC,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,eAAe;YAC1B,eAAe;gBAAC;gBAAc;aAAa;QAC7C;QACA,IAAI,CAAC,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,eAAe;YAC1B,eAAe;gBAAC;gBAAc;aAAa;QAC7C;QACA,wDAAwD;QACxD,OAAO,IAAI,CAAC,IAAI,CAAC,SAAU,GAAG;YAC5B,0BAA0B,UAAU,KAAK;YACzC,0BAA0B,QAAQ,KAAK;QACzC;QACA,mCAAmC;QACnC,SAAS,IAAI,CAAC,SAAU,GAAG;YACzB,IAAI,YAAY,SAAS,YAAY,CAAC,KAAK,QAAQ,CAAC,aAAa,YAAY;YAC7E,gCAAgC;YAChC,+DAA+D;YAC/D,MAAM;YACN,SAAS,aAAa,CAAC,KAAK;gBAAC,SAAS,aAAa,CAAC;gBAAM,OAAO,aAAa,CAAC;aAAK;YACpF,IAAI,UAAU,MAAM,IAAI,MAAM;gBAC5B,UAAU,MAAM,GAAG,SAAS,aAAa,CAAC,KAAK,SAAS,IAAI;YAC9D;YACA,SAAS,aAAa,CAAC,KAAK;gBAC1B,sBAAsB,SAAS,aAAa,CAAC,KAAK;gBAClD,kBAAkB,SAAS,aAAa,CAAC,KAAK;gBAC9C,kBAAkB,SAAS,aAAa,CAAC,KAAK;gBAC9C,gBAAgB,SAAS,aAAa,CAAC,KAAK;gBAC5C,YAAY,SAAS,aAAa,CAAC,KAAK;gBACxC,oBAAoB,OAAO,aAAa,CAAC,KAAK;gBAC9C,gBAAgB,OAAO,aAAa,CAAC,KAAK;gBAC1C,gBAAgB,OAAO,aAAa,CAAC,KAAK;gBAC1C,cAAc,OAAO,aAAa,CAAC,KAAK;gBACxC,UAAU,OAAO,aAAa,CAAC,KAAK;gBACpC,OAAO;YACT;QACF;QACA,SAAS,UAAU,CAAC;QACpB,6BAA6B;QAC7B,QAAQ;QACR,OAAO,IAAI,CAAC,iBAAiB,CAAC,SAAU,EAAE;YACxC,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE,IAAI,SAAS,GAAG;YAC1B,GAAG,QAAQ,CAAC,SAAU,KAAK;gBACzB,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE,OAAO,SAAS,GAAG;YAC/B;QACF;QACA,SAAS,0BAA0B,IAAI,EAAE,GAAG,EAAE,MAAM;YAClD,IAAI,YAAY,KAAK,YAAY,CAAC;YAClC,4BAA4B,MAAM,KAAK,QAAQ,aAAa;YAC5D,IAAI,QAAQ,UAAU,QAAQ,CAAC,aAAa,YAAY;YACxD,IAAI,MAAM,IAAI,IAAI,MAAM;gBACtB,MAAM,IAAI,GAAG,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;YAC7C;YACA,KAAK,aAAa,CAAC,KAAK;gBACtB,kBAAkB,UAAU,GAAG,CAAC;gBAChC,gFAAgF;gBAChF,cAAc,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE,UAAU,GAAG,CAAC,gBAAgB,OAAO,YAAY,CAAC,SAAS,IAAI,EAAE;gBACzF,cAAc,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE,UAAU,GAAG,CAAC,gBAAgB,OAAO,YAAY,CAAC,SAAS,IAAI,EAAE;gBACzF,4DAA4D;gBAC5D,YAAY,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE,UAAU,GAAG,CAAC,eAAe,UAAU,CAAC,SAAS,IAAI,EAAE;gBAC7E,QAAQ,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE,UAAU,GAAG,CAAC,UAAU,OAAO,UAAU,CAAC,SAAS,IAAI,EAAE;gBAC3E,OAAO;YACT;QACF;QACA,IAAI,CAAC,QAAQ,CAAC;QACd,SAAS,KAAK,CAAC,MAAM,GAAG,QAAQ,GAAG,CAAC,aAAa,YAAY,GAAG,CAAC;IACnE;IACA,aAAa,IAAI,GAAG;IACpB,OAAO;AACT,EAAE,sKAAA,CAAA,UAAU;AACZ,SAAS,WAAW,QAAQ,EAAE,WAAW,EAAE,OAAO;IAChD,IAAI;IACJ,IAAI,UAAU;QACZ,iBAAiB,CAAA,GAAA,iJAAA,CAAA,MAAG,AAAD,EAAE,YAAY,SAAS,UAAU,EAAE,SAAU,QAAQ;YACtE,IAAI,OAAO,YAAY,OAAO,GAAG,gBAAgB,CAAC,YAAY,OAAO,GAAG,YAAY,CAAC,cAAc,CAAC;YACpG,sFAAsF;YACtF,OAAO,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,OAAO;gBAC9B,MAAM;gBACN,sDAAsD;gBACtD,aAAa;YACf;QACF;IACF,OAAO;QACL,iBAAiB;YAAC;gBAChB,MAAM;gBACN,MAAM;YACR;SAAE;IACJ;IACA,IAAI,WAAW,IAAI,uJAAA,CAAA,UAAU,CAAC,gBAAgB;IAC9C,IAAI,SAAS,IAAI,uJAAA,CAAA,UAAU,CAAC,gBAAgB;IAC5C,gBAAgB;IAChB,IAAI,WAAW,IAAI,uJAAA,CAAA,UAAU,CAAC,EAAE,EAAE;IAClC,IAAI,UAAU,CAAA,GAAA,iJAAA,CAAA,MAAG,AAAD,EAAE,QAAQ,GAAG,CAAC,SAAS,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,mBAAmB,aAAa,UAAU;IACvF,IAAI,UAAU;QACZ,UAAU,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,gBAAgB;IAClD;IACA,IAAI,iBAAiB,CAAA,GAAA,wKAAA,CAAA,6BAAuC,AAAD,EAAE,CAAC,CAAC,UAAU;IACzE,SAAS,QAAQ,CAAC,CAAA,GAAA,iJAAA,CAAA,MAAG,AAAD,EAAE,SAAS,SAAU,IAAI;QAC3C,OAAO,IAAI,CAAC,EAAE;IAChB,IAAI,MAAM;IACV,OAAO,QAAQ,CAAC,CAAA,GAAA,iJAAA,CAAA,MAAG,AAAD,EAAE,SAAS,SAAU,IAAI;QACzC,OAAO,IAAI,CAAC,EAAE;IAChB,IAAI,MAAM;IACV,SAAS,QAAQ,CAAC,CAAA,GAAA,iJAAA,CAAA,MAAG,AAAD,EAAE,SAAS,SAAU,IAAI;QAC3C,OAAO,IAAI,CAAC,EAAE;IAChB;IACA,SAAS,aAAa,GAAG;IACzB,OAAO;QACL,MAAM;QACN,IAAI;QACJ,MAAM;IACR;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1409, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/marker/installMarkLine.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nimport checkMarkerInSeries from './checkMarkerInSeries.js';\nimport MarkLineModel from './MarkLineModel.js';\nimport MarkLineView from './MarkLineView.js';\nexport function install(registers) {\n  registers.registerComponentModel(MarkLineModel);\n  registers.registerComponentView(MarkLineView);\n  registers.registerPreprocessor(function (opt) {\n    if (checkMarkerInSeries(opt.series, 'markLine')) {\n      // Make sure markLine component is enabled\n      opt.markLine = opt.markLine || {};\n    }\n  });\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC;;;AAED;AACA;AACA;;;;AACO,SAAS,QAAQ,SAAS;IAC/B,UAAU,sBAAsB,CAAC,yKAAA,CAAA,UAAa;IAC9C,UAAU,qBAAqB,CAAC,wKAAA,CAAA,UAAY;IAC5C,UAAU,oBAAoB,CAAC,SAAU,GAAG;QAC1C,IAAI,CAAA,GAAA,+KAAA,CAAA,UAAmB,AAAD,EAAE,IAAI,MAAM,EAAE,aAAa;YAC/C,0CAA0C;YAC1C,IAAI,QAAQ,GAAG,IAAI,QAAQ,IAAI,CAAC;QAClC;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1463, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/marker/MarkAreaModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport MarkerModel from './MarkerModel.js';\nvar MarkAreaModel = /** @class */function (_super) {\n  __extends(MarkAreaModel, _super);\n  function MarkAreaModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkAreaModel.type;\n    return _this;\n  }\n  MarkAreaModel.prototype.createMarkerModelFromSeries = function (markerOpt, masterMarkerModel, ecModel) {\n    return new MarkAreaModel(markerOpt, masterMarkerModel, ecModel);\n  };\n  MarkAreaModel.type = 'markArea';\n  MarkAreaModel.defaultOption = {\n    // zlevel: 0,\n    // PENDING\n    z: 1,\n    tooltip: {\n      trigger: 'item'\n    },\n    // markArea should fixed on the coordinate system\n    animation: false,\n    label: {\n      show: true,\n      position: 'top'\n    },\n    itemStyle: {\n      // color and borderColor default to use color from series\n      // color: 'auto'\n      // borderColor: 'auto'\n      borderWidth: 0\n    },\n    emphasis: {\n      label: {\n        show: true,\n        position: 'top'\n      }\n    }\n  };\n  return MarkAreaModel;\n}(MarkerModel);\nexport default MarkAreaModel;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;;;AACA,IAAI,gBAAgB,WAAW,GAAE,SAAU,MAAM;IAC/C,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,eAAe;IACzB,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,cAAc,IAAI;QAC/B,OAAO;IACT;IACA,cAAc,SAAS,CAAC,2BAA2B,GAAG,SAAU,SAAS,EAAE,iBAAiB,EAAE,OAAO;QACnG,OAAO,IAAI,cAAc,WAAW,mBAAmB;IACzD;IACA,cAAc,IAAI,GAAG;IACrB,cAAc,aAAa,GAAG;QAC5B,aAAa;QACb,UAAU;QACV,GAAG;QACH,SAAS;YACP,SAAS;QACX;QACA,iDAAiD;QACjD,WAAW;QACX,OAAO;YACL,MAAM;YACN,UAAU;QACZ;QACA,WAAW;YACT,yDAAyD;YACzD,gBAAgB;YAChB,sBAAsB;YACtB,aAAa;QACf;QACA,UAAU;YACR,OAAO;gBACL,MAAM;gBACN,UAAU;YACZ;QACF;IACF;IACA,OAAO;AACT,EAAE,uKAAA,CAAA,UAAW;uCACE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1552, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/marker/MarkAreaView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n// TODO Optimize on polar\nimport * as colorUtil from 'zrender/lib/tool/color.js';\nimport SeriesData from '../../data/SeriesData.js';\nimport * as numberUtil from '../../util/number.js';\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis, setStatesStylesFromModel } from '../../util/states.js';\nimport * as markerHelper from './markerHelper.js';\nimport MarkerView from './MarkerView.js';\nimport { retrieve, mergeAll, map, curry, filter, extend, isString } from 'zrender/lib/core/util.js';\nimport { isCoordinateSystemType } from '../../coord/CoordinateSystem.js';\nimport MarkerModel from './MarkerModel.js';\nimport { makeInner } from '../../util/model.js';\nimport { getVisualFromData } from '../../visual/helper.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { parseDataValue } from '../../data/helper/dataValueHelper.js';\nvar inner = makeInner();\nvar markAreaTransform = function (seriesModel, coordSys, maModel, item) {\n  // item may be null\n  var item0 = item[0];\n  var item1 = item[1];\n  if (!item0 || !item1) {\n    return;\n  }\n  var lt = markerHelper.dataTransform(seriesModel, item0);\n  var rb = markerHelper.dataTransform(seriesModel, item1);\n  // FIXME make sure lt is less than rb\n  var ltCoord = lt.coord;\n  var rbCoord = rb.coord;\n  ltCoord[0] = retrieve(ltCoord[0], -Infinity);\n  ltCoord[1] = retrieve(ltCoord[1], -Infinity);\n  rbCoord[0] = retrieve(rbCoord[0], Infinity);\n  rbCoord[1] = retrieve(rbCoord[1], Infinity);\n  // Merge option into one\n  var result = mergeAll([{}, lt, rb]);\n  result.coord = [lt.coord, rb.coord];\n  result.x0 = lt.x;\n  result.y0 = lt.y;\n  result.x1 = rb.x;\n  result.y1 = rb.y;\n  return result;\n};\nfunction isInfinity(val) {\n  return !isNaN(val) && !isFinite(val);\n}\n// If a markArea has one dim\nfunction ifMarkAreaHasOnlyDim(dimIndex, fromCoord, toCoord, coordSys) {\n  var otherDimIndex = 1 - dimIndex;\n  return isInfinity(fromCoord[otherDimIndex]) && isInfinity(toCoord[otherDimIndex]);\n}\nfunction markAreaFilter(coordSys, item) {\n  var fromCoord = item.coord[0];\n  var toCoord = item.coord[1];\n  var item0 = {\n    coord: fromCoord,\n    x: item.x0,\n    y: item.y0\n  };\n  var item1 = {\n    coord: toCoord,\n    x: item.x1,\n    y: item.y1\n  };\n  if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n    // In case\n    // {\n    //  markArea: {\n    //    data: [{ yAxis: 2 }]\n    //  }\n    // }\n    if (fromCoord && toCoord && (ifMarkAreaHasOnlyDim(1, fromCoord, toCoord, coordSys) || ifMarkAreaHasOnlyDim(0, fromCoord, toCoord, coordSys))) {\n      return true;\n    }\n    // Directly returning true may also do the work,\n    // because markArea will not be shown automatically\n    // when it's not included in coordinate system.\n    // But filtering ahead can avoid keeping rendering markArea\n    // when there are too many of them.\n    return markerHelper.zoneFilter(coordSys, item0, item1);\n  }\n  return markerHelper.dataFilter(coordSys, item0) || markerHelper.dataFilter(coordSys, item1);\n}\n// dims can be ['x0', 'y0'], ['x1', 'y1'], ['x0', 'y1'], ['x1', 'y0']\nfunction getSingleMarkerEndPoint(data, idx, dims, seriesModel, api) {\n  var coordSys = seriesModel.coordinateSystem;\n  var itemModel = data.getItemModel(idx);\n  var point;\n  var xPx = numberUtil.parsePercent(itemModel.get(dims[0]), api.getWidth());\n  var yPx = numberUtil.parsePercent(itemModel.get(dims[1]), api.getHeight());\n  if (!isNaN(xPx) && !isNaN(yPx)) {\n    point = [xPx, yPx];\n  } else {\n    // Chart like bar may have there own marker positioning logic\n    if (seriesModel.getMarkerPosition) {\n      // Consider the case that user input the right-bottom point first\n      // Pick the larger x and y as 'x1' and 'y1'\n      var pointValue0 = data.getValues(['x0', 'y0'], idx);\n      var pointValue1 = data.getValues(['x1', 'y1'], idx);\n      var clampPointValue0 = coordSys.clampData(pointValue0);\n      var clampPointValue1 = coordSys.clampData(pointValue1);\n      var pointValue = [];\n      if (dims[0] === 'x0') {\n        pointValue[0] = clampPointValue0[0] > clampPointValue1[0] ? pointValue1[0] : pointValue0[0];\n      } else {\n        pointValue[0] = clampPointValue0[0] > clampPointValue1[0] ? pointValue0[0] : pointValue1[0];\n      }\n      if (dims[1] === 'y0') {\n        pointValue[1] = clampPointValue0[1] > clampPointValue1[1] ? pointValue1[1] : pointValue0[1];\n      } else {\n        pointValue[1] = clampPointValue0[1] > clampPointValue1[1] ? pointValue0[1] : pointValue1[1];\n      }\n      // Use the getMarkerPosition\n      point = seriesModel.getMarkerPosition(pointValue, dims, true);\n    } else {\n      var x = data.get(dims[0], idx);\n      var y = data.get(dims[1], idx);\n      var pt = [x, y];\n      coordSys.clampData && coordSys.clampData(pt, pt);\n      point = coordSys.dataToPoint(pt, true);\n    }\n    if (isCoordinateSystemType(coordSys, 'cartesian2d')) {\n      // TODO: TYPE ts@4.1 may still infer it as Axis instead of Axis2D. Not sure if it's a bug\n      var xAxis = coordSys.getAxis('x');\n      var yAxis = coordSys.getAxis('y');\n      var x = data.get(dims[0], idx);\n      var y = data.get(dims[1], idx);\n      if (isInfinity(x)) {\n        point[0] = xAxis.toGlobalCoord(xAxis.getExtent()[dims[0] === 'x0' ? 0 : 1]);\n      } else if (isInfinity(y)) {\n        point[1] = yAxis.toGlobalCoord(yAxis.getExtent()[dims[1] === 'y0' ? 0 : 1]);\n      }\n    }\n    // Use x, y if has any\n    if (!isNaN(xPx)) {\n      point[0] = xPx;\n    }\n    if (!isNaN(yPx)) {\n      point[1] = yPx;\n    }\n  }\n  return point;\n}\nexport var dimPermutations = [['x0', 'y0'], ['x1', 'y0'], ['x1', 'y1'], ['x0', 'y1']];\nvar MarkAreaView = /** @class */function (_super) {\n  __extends(MarkAreaView, _super);\n  function MarkAreaView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = MarkAreaView.type;\n    return _this;\n  }\n  MarkAreaView.prototype.updateTransform = function (markAreaModel, ecModel, api) {\n    ecModel.eachSeries(function (seriesModel) {\n      var maModel = MarkerModel.getMarkerModelFromSeries(seriesModel, 'markArea');\n      if (maModel) {\n        var areaData_1 = maModel.getData();\n        areaData_1.each(function (idx) {\n          var points = map(dimPermutations, function (dim) {\n            return getSingleMarkerEndPoint(areaData_1, idx, dim, seriesModel, api);\n          });\n          // Layout\n          areaData_1.setItemLayout(idx, points);\n          var el = areaData_1.getItemGraphicEl(idx);\n          el.setShape('points', points);\n        });\n      }\n    }, this);\n  };\n  MarkAreaView.prototype.renderSeries = function (seriesModel, maModel, ecModel, api) {\n    var coordSys = seriesModel.coordinateSystem;\n    var seriesId = seriesModel.id;\n    var seriesData = seriesModel.getData();\n    var areaGroupMap = this.markerGroupMap;\n    var polygonGroup = areaGroupMap.get(seriesId) || areaGroupMap.set(seriesId, {\n      group: new graphic.Group()\n    });\n    this.group.add(polygonGroup.group);\n    this.markKeep(polygonGroup);\n    var areaData = createList(coordSys, seriesModel, maModel);\n    // Line data for tooltip and formatter\n    maModel.setData(areaData);\n    // Update visual and layout of line\n    areaData.each(function (idx) {\n      // Layout\n      var points = map(dimPermutations, function (dim) {\n        return getSingleMarkerEndPoint(areaData, idx, dim, seriesModel, api);\n      });\n      var xAxisScale = coordSys.getAxis('x').scale;\n      var yAxisScale = coordSys.getAxis('y').scale;\n      var xAxisExtent = xAxisScale.getExtent();\n      var yAxisExtent = yAxisScale.getExtent();\n      var xPointExtent = [xAxisScale.parse(areaData.get('x0', idx)), xAxisScale.parse(areaData.get('x1', idx))];\n      var yPointExtent = [yAxisScale.parse(areaData.get('y0', idx)), yAxisScale.parse(areaData.get('y1', idx))];\n      numberUtil.asc(xPointExtent);\n      numberUtil.asc(yPointExtent);\n      var overlapped = !(xAxisExtent[0] > xPointExtent[1] || xAxisExtent[1] < xPointExtent[0] || yAxisExtent[0] > yPointExtent[1] || yAxisExtent[1] < yPointExtent[0]);\n      // If none of the area is inside coordSys, allClipped is set to be true\n      // in layout so that label will not be displayed. See #12591\n      var allClipped = !overlapped;\n      areaData.setItemLayout(idx, {\n        points: points,\n        allClipped: allClipped\n      });\n      var style = areaData.getItemModel(idx).getModel('itemStyle').getItemStyle();\n      var color = getVisualFromData(seriesData, 'color');\n      if (!style.fill) {\n        style.fill = color;\n        if (isString(style.fill)) {\n          style.fill = colorUtil.modifyAlpha(style.fill, 0.4);\n        }\n      }\n      if (!style.stroke) {\n        style.stroke = color;\n      }\n      // Visual\n      areaData.setItemVisual(idx, 'style', style);\n    });\n    areaData.diff(inner(polygonGroup).data).add(function (idx) {\n      var layout = areaData.getItemLayout(idx);\n      if (!layout.allClipped) {\n        var polygon = new graphic.Polygon({\n          shape: {\n            points: layout.points\n          }\n        });\n        areaData.setItemGraphicEl(idx, polygon);\n        polygonGroup.group.add(polygon);\n      }\n    }).update(function (newIdx, oldIdx) {\n      var polygon = inner(polygonGroup).data.getItemGraphicEl(oldIdx);\n      var layout = areaData.getItemLayout(newIdx);\n      if (!layout.allClipped) {\n        if (polygon) {\n          graphic.updateProps(polygon, {\n            shape: {\n              points: layout.points\n            }\n          }, maModel, newIdx);\n        } else {\n          polygon = new graphic.Polygon({\n            shape: {\n              points: layout.points\n            }\n          });\n        }\n        areaData.setItemGraphicEl(newIdx, polygon);\n        polygonGroup.group.add(polygon);\n      } else if (polygon) {\n        polygonGroup.group.remove(polygon);\n      }\n    }).remove(function (idx) {\n      var polygon = inner(polygonGroup).data.getItemGraphicEl(idx);\n      polygonGroup.group.remove(polygon);\n    }).execute();\n    areaData.eachItemGraphicEl(function (polygon, idx) {\n      var itemModel = areaData.getItemModel(idx);\n      var style = areaData.getItemVisual(idx, 'style');\n      polygon.useStyle(areaData.getItemVisual(idx, 'style'));\n      setLabelStyle(polygon, getLabelStatesModels(itemModel), {\n        labelFetcher: maModel,\n        labelDataIndex: idx,\n        defaultText: areaData.getName(idx) || '',\n        inheritColor: isString(style.fill) ? colorUtil.modifyAlpha(style.fill, 1) : '#000'\n      });\n      setStatesStylesFromModel(polygon, itemModel);\n      toggleHoverEmphasis(polygon, null, null, itemModel.get(['emphasis', 'disabled']));\n      getECData(polygon).dataModel = maModel;\n    });\n    inner(polygonGroup).data = areaData;\n    polygonGroup.group.silent = maModel.get('silent') || seriesModel.get('silent');\n  };\n  MarkAreaView.type = 'markArea';\n  return MarkAreaView;\n}(MarkerView);\nfunction createList(coordSys, seriesModel, maModel) {\n  var areaData;\n  var dataDims;\n  var dims = ['x0', 'y0', 'x1', 'y1'];\n  if (coordSys) {\n    var coordDimsInfos_1 = map(coordSys && coordSys.dimensions, function (coordDim) {\n      var data = seriesModel.getData();\n      var info = data.getDimensionInfo(data.mapDimension(coordDim)) || {};\n      // In map series data don't have lng and lat dimension. Fallback to same with coordSys\n      return extend(extend({}, info), {\n        name: coordDim,\n        // DON'T use ordinalMeta to parse and collect ordinal.\n        ordinalMeta: null\n      });\n    });\n    dataDims = map(dims, function (dim, idx) {\n      return {\n        name: dim,\n        type: coordDimsInfos_1[idx % 2].type\n      };\n    });\n    areaData = new SeriesData(dataDims, maModel);\n  } else {\n    dataDims = [{\n      name: 'value',\n      type: 'float'\n    }];\n    areaData = new SeriesData(dataDims, maModel);\n  }\n  var optData = map(maModel.get('data'), curry(markAreaTransform, seriesModel, coordSys, maModel));\n  if (coordSys) {\n    optData = filter(optData, curry(markAreaFilter, coordSys));\n  }\n  var dimValueGetter = coordSys ? function (item, dimName, dataIndex, dimIndex) {\n    // TODO should convert to ParsedValue?\n    var rawVal = item.coord[Math.floor(dimIndex / 2)][dimIndex % 2];\n    return parseDataValue(rawVal, dataDims[dimIndex]);\n  } : function (item, dimName, dataIndex, dimIndex) {\n    return parseDataValue(item.value, dataDims[dimIndex]);\n  };\n  areaData.initData(optData, null, dimValueGetter);\n  areaData.hasItemOption = true;\n  return areaData;\n}\nexport default MarkAreaView;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;AACA;AACA,yBAAyB;AACzB;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;AACA,IAAI,QAAQ,CAAA,GAAA,kJAAA,CAAA,YAAS,AAAD;AACpB,IAAI,oBAAoB,SAAU,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE,IAAI;IACpE,mBAAmB;IACnB,IAAI,QAAQ,IAAI,CAAC,EAAE;IACnB,IAAI,QAAQ,IAAI,CAAC,EAAE;IACnB,IAAI,CAAC,SAAS,CAAC,OAAO;QACpB;IACF;IACA,IAAI,KAAK,CAAA,GAAA,wKAAA,CAAA,gBAA0B,AAAD,EAAE,aAAa;IACjD,IAAI,KAAK,CAAA,GAAA,wKAAA,CAAA,gBAA0B,AAAD,EAAE,aAAa;IACjD,qCAAqC;IACrC,IAAI,UAAU,GAAG,KAAK;IACtB,IAAI,UAAU,GAAG,KAAK;IACtB,OAAO,CAAC,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC;IACnC,OAAO,CAAC,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC;IACnC,OAAO,CAAC,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,EAAE,EAAE;IAClC,OAAO,CAAC,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,CAAC,EAAE,EAAE;IAClC,wBAAwB;IACxB,IAAI,SAAS,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE;QAAC,CAAC;QAAG;QAAI;KAAG;IAClC,OAAO,KAAK,GAAG;QAAC,GAAG,KAAK;QAAE,GAAG,KAAK;KAAC;IACnC,OAAO,EAAE,GAAG,GAAG,CAAC;IAChB,OAAO,EAAE,GAAG,GAAG,CAAC;IAChB,OAAO,EAAE,GAAG,GAAG,CAAC;IAChB,OAAO,EAAE,GAAG,GAAG,CAAC;IAChB,OAAO;AACT;AACA,SAAS,WAAW,GAAG;IACrB,OAAO,CAAC,MAAM,QAAQ,CAAC,SAAS;AAClC;AACA,4BAA4B;AAC5B,SAAS,qBAAqB,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,QAAQ;IAClE,IAAI,gBAAgB,IAAI;IACxB,OAAO,WAAW,SAAS,CAAC,cAAc,KAAK,WAAW,OAAO,CAAC,cAAc;AAClF;AACA,SAAS,eAAe,QAAQ,EAAE,IAAI;IACpC,IAAI,YAAY,KAAK,KAAK,CAAC,EAAE;IAC7B,IAAI,UAAU,KAAK,KAAK,CAAC,EAAE;IAC3B,IAAI,QAAQ;QACV,OAAO;QACP,GAAG,KAAK,EAAE;QACV,GAAG,KAAK,EAAE;IACZ;IACA,IAAI,QAAQ;QACV,OAAO;QACP,GAAG,KAAK,EAAE;QACV,GAAG,KAAK,EAAE;IACZ;IACA,IAAI,CAAA,GAAA,8JAAA,CAAA,yBAAsB,AAAD,EAAE,UAAU,gBAAgB;QACnD,UAAU;QACV,IAAI;QACJ,eAAe;QACf,0BAA0B;QAC1B,KAAK;QACL,IAAI;QACJ,IAAI,aAAa,WAAW,CAAC,qBAAqB,GAAG,WAAW,SAAS,aAAa,qBAAqB,GAAG,WAAW,SAAS,SAAS,GAAG;YAC5I,OAAO;QACT;QACA,gDAAgD;QAChD,mDAAmD;QACnD,+CAA+C;QAC/C,2DAA2D;QAC3D,mCAAmC;QACnC,OAAO,CAAA,GAAA,wKAAA,CAAA,aAAuB,AAAD,EAAE,UAAU,OAAO;IAClD;IACA,OAAO,CAAA,GAAA,wKAAA,CAAA,aAAuB,AAAD,EAAE,UAAU,UAAU,CAAA,GAAA,wKAAA,CAAA,aAAuB,AAAD,EAAE,UAAU;AACvF;AACA,qEAAqE;AACrE,SAAS,wBAAwB,IAAI,EAAE,GAAG,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG;IAChE,IAAI,WAAW,YAAY,gBAAgB;IAC3C,IAAI,YAAY,KAAK,YAAY,CAAC;IAClC,IAAI;IACJ,IAAI,MAAM,CAAA,GAAA,mJAAA,CAAA,eAAuB,AAAD,EAAE,UAAU,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,QAAQ;IACtE,IAAI,MAAM,CAAA,GAAA,mJAAA,CAAA,eAAuB,AAAD,EAAE,UAAU,GAAG,CAAC,IAAI,CAAC,EAAE,GAAG,IAAI,SAAS;IACvE,IAAI,CAAC,MAAM,QAAQ,CAAC,MAAM,MAAM;QAC9B,QAAQ;YAAC;YAAK;SAAI;IACpB,OAAO;QACL,6DAA6D;QAC7D,IAAI,YAAY,iBAAiB,EAAE;YACjC,iEAAiE;YACjE,2CAA2C;YAC3C,IAAI,cAAc,KAAK,SAAS,CAAC;gBAAC;gBAAM;aAAK,EAAE;YAC/C,IAAI,cAAc,KAAK,SAAS,CAAC;gBAAC;gBAAM;aAAK,EAAE;YAC/C,IAAI,mBAAmB,SAAS,SAAS,CAAC;YAC1C,IAAI,mBAAmB,SAAS,SAAS,CAAC;YAC1C,IAAI,aAAa,EAAE;YACnB,IAAI,IAAI,CAAC,EAAE,KAAK,MAAM;gBACpB,UAAU,CAAC,EAAE,GAAG,gBAAgB,CAAC,EAAE,GAAG,gBAAgB,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE;YAC7F,OAAO;gBACL,UAAU,CAAC,EAAE,GAAG,gBAAgB,CAAC,EAAE,GAAG,gBAAgB,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE;YAC7F;YACA,IAAI,IAAI,CAAC,EAAE,KAAK,MAAM;gBACpB,UAAU,CAAC,EAAE,GAAG,gBAAgB,CAAC,EAAE,GAAG,gBAAgB,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE;YAC7F,OAAO;gBACL,UAAU,CAAC,EAAE,GAAG,gBAAgB,CAAC,EAAE,GAAG,gBAAgB,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE;YAC7F;YACA,4BAA4B;YAC5B,QAAQ,YAAY,iBAAiB,CAAC,YAAY,MAAM;QAC1D,OAAO;YACL,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE;YAC1B,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE;YAC1B,IAAI,KAAK;gBAAC;gBAAG;aAAE;YACf,SAAS,SAAS,IAAI,SAAS,SAAS,CAAC,IAAI;YAC7C,QAAQ,SAAS,WAAW,CAAC,IAAI;QACnC;QACA,IAAI,CAAA,GAAA,8JAAA,CAAA,yBAAsB,AAAD,EAAE,UAAU,gBAAgB;YACnD,yFAAyF;YACzF,IAAI,QAAQ,SAAS,OAAO,CAAC;YAC7B,IAAI,QAAQ,SAAS,OAAO,CAAC;YAC7B,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE;YAC1B,IAAI,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC,EAAE,EAAE;YAC1B,IAAI,WAAW,IAAI;gBACjB,KAAK,CAAC,EAAE,GAAG,MAAM,aAAa,CAAC,MAAM,SAAS,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,OAAO,IAAI,EAAE;YAC5E,OAAO,IAAI,WAAW,IAAI;gBACxB,KAAK,CAAC,EAAE,GAAG,MAAM,aAAa,CAAC,MAAM,SAAS,EAAE,CAAC,IAAI,CAAC,EAAE,KAAK,OAAO,IAAI,EAAE;YAC5E;QACF;QACA,sBAAsB;QACtB,IAAI,CAAC,MAAM,MAAM;YACf,KAAK,CAAC,EAAE,GAAG;QACb;QACA,IAAI,CAAC,MAAM,MAAM;YACf,KAAK,CAAC,EAAE,GAAG;QACb;IACF;IACA,OAAO;AACT;AACO,IAAI,kBAAkB;IAAC;QAAC;QAAM;KAAK;IAAE;QAAC;QAAM;KAAK;IAAE;QAAC;QAAM;KAAK;IAAE;QAAC;QAAM;KAAK;CAAC;AACrF,IAAI,eAAe,WAAW,GAAE,SAAU,MAAM;IAC9C,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;IACxB,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,aAAa,IAAI;QAC9B,OAAO;IACT;IACA,aAAa,SAAS,CAAC,eAAe,GAAG,SAAU,aAAa,EAAE,OAAO,EAAE,GAAG;QAC5E,QAAQ,UAAU,CAAC,SAAU,WAAW;YACtC,IAAI,UAAU,uKAAA,CAAA,UAAW,CAAC,wBAAwB,CAAC,aAAa;YAChE,IAAI,SAAS;gBACX,IAAI,aAAa,QAAQ,OAAO;gBAChC,WAAW,IAAI,CAAC,SAAU,GAAG;oBAC3B,IAAI,SAAS,CAAA,GAAA,iJAAA,CAAA,MAAG,AAAD,EAAE,iBAAiB,SAAU,GAAG;wBAC7C,OAAO,wBAAwB,YAAY,KAAK,KAAK,aAAa;oBACpE;oBACA,SAAS;oBACT,WAAW,aAAa,CAAC,KAAK;oBAC9B,IAAI,KAAK,WAAW,gBAAgB,CAAC;oBACrC,GAAG,QAAQ,CAAC,UAAU;gBACxB;YACF;QACF,GAAG,IAAI;IACT;IACA,aAAa,SAAS,CAAC,YAAY,GAAG,SAAU,WAAW,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG;QAChF,IAAI,WAAW,YAAY,gBAAgB;QAC3C,IAAI,WAAW,YAAY,EAAE;QAC7B,IAAI,aAAa,YAAY,OAAO;QACpC,IAAI,eAAe,IAAI,CAAC,cAAc;QACtC,IAAI,eAAe,aAAa,GAAG,CAAC,aAAa,aAAa,GAAG,CAAC,UAAU;YAC1E,OAAO,IAAI,yLAAA,CAAA,QAAa;QAC1B;QACA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,aAAa,KAAK;QACjC,IAAI,CAAC,QAAQ,CAAC;QACd,IAAI,WAAW,WAAW,UAAU,aAAa;QACjD,sCAAsC;QACtC,QAAQ,OAAO,CAAC;QAChB,mCAAmC;QACnC,SAAS,IAAI,CAAC,SAAU,GAAG;YACzB,SAAS;YACT,IAAI,SAAS,CAAA,GAAA,iJAAA,CAAA,MAAG,AAAD,EAAE,iBAAiB,SAAU,GAAG;gBAC7C,OAAO,wBAAwB,UAAU,KAAK,KAAK,aAAa;YAClE;YACA,IAAI,aAAa,SAAS,OAAO,CAAC,KAAK,KAAK;YAC5C,IAAI,aAAa,SAAS,OAAO,CAAC,KAAK,KAAK;YAC5C,IAAI,cAAc,WAAW,SAAS;YACtC,IAAI,cAAc,WAAW,SAAS;YACtC,IAAI,eAAe;gBAAC,WAAW,KAAK,CAAC,SAAS,GAAG,CAAC,MAAM;gBAAO,WAAW,KAAK,CAAC,SAAS,GAAG,CAAC,MAAM;aAAM;YACzG,IAAI,eAAe;gBAAC,WAAW,KAAK,CAAC,SAAS,GAAG,CAAC,MAAM;gBAAO,WAAW,KAAK,CAAC,SAAS,GAAG,CAAC,MAAM;aAAM;YACzG,CAAA,GAAA,mJAAA,CAAA,MAAc,AAAD,EAAE;YACf,CAAA,GAAA,mJAAA,CAAA,MAAc,AAAD,EAAE;YACf,IAAI,aAAa,CAAC,CAAC,WAAW,CAAC,EAAE,GAAG,YAAY,CAAC,EAAE,IAAI,WAAW,CAAC,EAAE,GAAG,YAAY,CAAC,EAAE,IAAI,WAAW,CAAC,EAAE,GAAG,YAAY,CAAC,EAAE,IAAI,WAAW,CAAC,EAAE,GAAG,YAAY,CAAC,EAAE;YAC/J,uEAAuE;YACvE,4DAA4D;YAC5D,IAAI,aAAa,CAAC;YAClB,SAAS,aAAa,CAAC,KAAK;gBAC1B,QAAQ;gBACR,YAAY;YACd;YACA,IAAI,QAAQ,SAAS,YAAY,CAAC,KAAK,QAAQ,CAAC,aAAa,YAAY;YACzE,IAAI,QAAQ,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;YAC1C,IAAI,CAAC,MAAM,IAAI,EAAE;gBACf,MAAM,IAAI,GAAG;gBACb,IAAI,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,IAAI,GAAG;oBACxB,MAAM,IAAI,GAAG,CAAA,GAAA,kJAAA,CAAA,cAAqB,AAAD,EAAE,MAAM,IAAI,EAAE;gBACjD;YACF;YACA,IAAI,CAAC,MAAM,MAAM,EAAE;gBACjB,MAAM,MAAM,GAAG;YACjB;YACA,SAAS;YACT,SAAS,aAAa,CAAC,KAAK,SAAS;QACvC;QACA,SAAS,IAAI,CAAC,MAAM,cAAc,IAAI,EAAE,GAAG,CAAC,SAAU,GAAG;YACvD,IAAI,SAAS,SAAS,aAAa,CAAC;YACpC,IAAI,CAAC,OAAO,UAAU,EAAE;gBACtB,IAAI,UAAU,IAAI,sMAAA,CAAA,UAAe,CAAC;oBAChC,OAAO;wBACL,QAAQ,OAAO,MAAM;oBACvB;gBACF;gBACA,SAAS,gBAAgB,CAAC,KAAK;gBAC/B,aAAa,KAAK,CAAC,GAAG,CAAC;YACzB;QACF,GAAG,MAAM,CAAC,SAAU,MAAM,EAAE,MAAM;YAChC,IAAI,UAAU,MAAM,cAAc,IAAI,CAAC,gBAAgB,CAAC;YACxD,IAAI,SAAS,SAAS,aAAa,CAAC;YACpC,IAAI,CAAC,OAAO,UAAU,EAAE;gBACtB,IAAI,SAAS;oBACX,CAAA,GAAA,iKAAA,CAAA,cAAmB,AAAD,EAAE,SAAS;wBAC3B,OAAO;4BACL,QAAQ,OAAO,MAAM;wBACvB;oBACF,GAAG,SAAS;gBACd,OAAO;oBACL,UAAU,IAAI,sMAAA,CAAA,UAAe,CAAC;wBAC5B,OAAO;4BACL,QAAQ,OAAO,MAAM;wBACvB;oBACF;gBACF;gBACA,SAAS,gBAAgB,CAAC,QAAQ;gBAClC,aAAa,KAAK,CAAC,GAAG,CAAC;YACzB,OAAO,IAAI,SAAS;gBAClB,aAAa,KAAK,CAAC,MAAM,CAAC;YAC5B;QACF,GAAG,MAAM,CAAC,SAAU,GAAG;YACrB,IAAI,UAAU,MAAM,cAAc,IAAI,CAAC,gBAAgB,CAAC;YACxD,aAAa,KAAK,CAAC,MAAM,CAAC;QAC5B,GAAG,OAAO;QACV,SAAS,iBAAiB,CAAC,SAAU,OAAO,EAAE,GAAG;YAC/C,IAAI,YAAY,SAAS,YAAY,CAAC;YACtC,IAAI,QAAQ,SAAS,aAAa,CAAC,KAAK;YACxC,QAAQ,QAAQ,CAAC,SAAS,aAAa,CAAC,KAAK;YAC7C,CAAA,GAAA,wJAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,CAAA,GAAA,wJAAA,CAAA,uBAAoB,AAAD,EAAE,YAAY;gBACtD,cAAc;gBACd,gBAAgB;gBAChB,aAAa,SAAS,OAAO,CAAC,QAAQ;gBACtC,cAAc,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,MAAM,IAAI,IAAI,CAAA,GAAA,kJAAA,CAAA,cAAqB,AAAD,EAAE,MAAM,IAAI,EAAE,KAAK;YAC9E;YACA,CAAA,GAAA,mJAAA,CAAA,2BAAwB,AAAD,EAAE,SAAS;YAClC,CAAA,GAAA,mJAAA,CAAA,sBAAmB,AAAD,EAAE,SAAS,MAAM,MAAM,UAAU,GAAG,CAAC;gBAAC;gBAAY;aAAW;YAC/E,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE,SAAS,SAAS,GAAG;QACjC;QACA,MAAM,cAAc,IAAI,GAAG;QAC3B,aAAa,KAAK,CAAC,MAAM,GAAG,QAAQ,GAAG,CAAC,aAAa,YAAY,GAAG,CAAC;IACvE;IACA,aAAa,IAAI,GAAG;IACpB,OAAO;AACT,EAAE,sKAAA,CAAA,UAAU;AACZ,SAAS,WAAW,QAAQ,EAAE,WAAW,EAAE,OAAO;IAChD,IAAI;IACJ,IAAI;IACJ,IAAI,OAAO;QAAC;QAAM;QAAM;QAAM;KAAK;IACnC,IAAI,UAAU;QACZ,IAAI,mBAAmB,CAAA,GAAA,iJAAA,CAAA,MAAG,AAAD,EAAE,YAAY,SAAS,UAAU,EAAE,SAAU,QAAQ;YAC5E,IAAI,OAAO,YAAY,OAAO;YAC9B,IAAI,OAAO,KAAK,gBAAgB,CAAC,KAAK,YAAY,CAAC,cAAc,CAAC;YAClE,sFAAsF;YACtF,OAAO,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,OAAO;gBAC9B,MAAM;gBACN,sDAAsD;gBACtD,aAAa;YACf;QACF;QACA,WAAW,CAAA,GAAA,iJAAA,CAAA,MAAG,AAAD,EAAE,MAAM,SAAU,GAAG,EAAE,GAAG;YACrC,OAAO;gBACL,MAAM;gBACN,MAAM,gBAAgB,CAAC,MAAM,EAAE,CAAC,IAAI;YACtC;QACF;QACA,WAAW,IAAI,uJAAA,CAAA,UAAU,CAAC,UAAU;IACtC,OAAO;QACL,WAAW;YAAC;gBACV,MAAM;gBACN,MAAM;YACR;SAAE;QACF,WAAW,IAAI,uJAAA,CAAA,UAAU,CAAC,UAAU;IACtC;IACA,IAAI,UAAU,CAAA,GAAA,iJAAA,CAAA,MAAG,AAAD,EAAE,QAAQ,GAAG,CAAC,SAAS,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,mBAAmB,aAAa,UAAU;IACvF,IAAI,UAAU;QACZ,UAAU,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,SAAS,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,gBAAgB;IAClD;IACA,IAAI,iBAAiB,WAAW,SAAU,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ;QAC1E,sCAAsC;QACtC,IAAI,SAAS,KAAK,KAAK,CAAC,KAAK,KAAK,CAAC,WAAW,GAAG,CAAC,WAAW,EAAE;QAC/D,OAAO,CAAA,GAAA,sKAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,QAAQ,CAAC,SAAS;IAClD,IAAI,SAAU,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ;QAC9C,OAAO,CAAA,GAAA,sKAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,KAAK,EAAE,QAAQ,CAAC,SAAS;IACtD;IACA,SAAS,QAAQ,CAAC,SAAS,MAAM;IACjC,SAAS,aAAa,GAAG;IACzB,OAAO;AACT;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1987, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/marker/installMarkArea.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport checkMarkerInSeries from './checkMarkerInSeries.js';\nimport MarkAreaModel from './MarkAreaModel.js';\nimport MarkAreaView from './MarkAreaView.js';\nexport function install(registers) {\n  registers.registerComponentModel(MarkAreaModel);\n  registers.registerComponentView(MarkAreaView);\n  registers.registerPreprocessor(function (opt) {\n    if (checkMarkerInSeries(opt.series, 'markArea')) {\n      // Make sure markArea component is enabled\n      opt.markArea = opt.markArea || {};\n    }\n  });\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;;;;AACO,SAAS,QAAQ,SAAS;IAC/B,UAAU,sBAAsB,CAAC,yKAAA,CAAA,UAAa;IAC9C,UAAU,qBAAqB,CAAC,wKAAA,CAAA,UAAY;IAC5C,UAAU,oBAAoB,CAAC,SAAU,GAAG;QAC1C,IAAI,CAAA,GAAA,+KAAA,CAAA,UAAmB,AAAD,EAAE,IAAI,MAAM,EAAE,aAAa;YAC/C,0CAA0C;YAC1C,IAAI,QAAQ,GAAG,IAAI,QAAQ,IAAI,CAAC;QAClC;IACF;AACF", "ignoreList": [0], "debugId": null}}]}