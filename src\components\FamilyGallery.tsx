'use client'

import { useState } from 'react'
import { Upload, Heart, Share2, Download, Trash2, Plus, Image as ImageIcon } from 'lucide-react'

interface Photo {
  id: string
  url: string
  title: string
  description: string
  date: string
  likes: number
  isLiked: boolean
}

export default function FamilyGallery() {
  const [photos, setPhotos] = useState<Photo[]>([
    {
      id: '1',
      url: '/api/placeholder/400/300',
      title: 'Family Store Opening',
      description: 'Grand opening of our Revantad Store with the whole family',
      date: '2024-01-15',
      likes: 12,
      isLiked: true,
    },
    {
      id: '2',
      url: '/api/placeholder/400/300',
      title: 'Store Anniversary',
      description: 'Celebrating our first year in business',
      date: '2024-02-20',
      likes: 8,
      isLiked: false,
    },
    {
      id: '3',
      url: '/api/placeholder/400/300',
      title: 'Community Event',
      description: 'Participating in the local community festival',
      date: '2024-03-10',
      likes: 15,
      isLiked: true,
    },
  ])

  const [isUploadModalOpen, setIsUploadModalOpen] = useState(false)
  const [selectedPhoto, setSelectedPhoto] = useState<Photo | null>(null)
  const [uploadForm, setUploadForm] = useState({
    title: '',
    description: '',
    file: null as File | null,
  })

  const handleLike = (photoId: string) => {
    setPhotos(photos.map(photo => 
      photo.id === photoId 
        ? { 
            ...photo, 
            likes: photo.isLiked ? photo.likes - 1 : photo.likes + 1,
            isLiked: !photo.isLiked 
          }
        : photo
    ))
  }

  const handleDelete = (photoId: string) => {
    if (confirm('Are you sure you want to delete this photo?')) {
      setPhotos(photos.filter(photo => photo.id !== photoId))
    }
  }

  const handleUpload = (e: React.FormEvent) => {
    e.preventDefault()
    if (uploadForm.file && uploadForm.title) {
      const newPhoto: Photo = {
        id: Date.now().toString(),
        url: URL.createObjectURL(uploadForm.file),
        title: uploadForm.title,
        description: uploadForm.description,
        date: new Date().toISOString().split('T')[0] || '',
        likes: 0,
        isLiked: false,
      }
      setPhotos([newPhoto, ...photos])
      setUploadForm({ title: '', description: '', file: null })
      setIsUploadModalOpen(false)
    }
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      setUploadForm({ ...uploadForm, file })
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Family Gallery</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Preserve your family memories and store moments
          </p>
        </div>
        <button
          onClick={() => setIsUploadModalOpen(true)}
          className="btn-primary flex items-center"
        >
          <Plus className="h-4 w-4 mr-2" />
          Add Photo
        </button>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="card p-6 text-center">
          <ImageIcon className="h-8 w-8 text-green-500 mx-auto mb-2" />
          <p className="text-2xl font-bold text-gray-900 dark:text-white">{photos.length}</p>
          <p className="text-sm text-gray-600 dark:text-gray-400">Total Photos</p>
        </div>
        <div className="card p-6 text-center">
          <Heart className="h-8 w-8 text-red-500 mx-auto mb-2" />
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {photos.reduce((sum, photo) => sum + photo.likes, 0)}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">Total Likes</p>
        </div>
        <div className="card p-6 text-center">
          <Upload className="h-8 w-8 text-blue-500 mx-auto mb-2" />
          <p className="text-2xl font-bold text-gray-900 dark:text-white">
            {new Date().getFullYear()}
          </p>
          <p className="text-sm text-gray-600 dark:text-gray-400">Year Started</p>
        </div>
      </div>

      {/* Photo Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {photos.map((photo) => (
          <div key={photo.id} className="card overflow-hidden group">
            <div className="relative aspect-video bg-gray-200 dark:bg-gray-700">
              <div className="w-full h-full bg-gradient-to-br from-green-100 to-yellow-100 dark:from-green-900 dark:to-yellow-900 flex items-center justify-center">
                <ImageIcon className="h-16 w-16 text-gray-400" />
              </div>
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100">
                <button
                  onClick={() => setSelectedPhoto(photo)}
                  className="bg-white text-gray-900 px-4 py-2 rounded-lg font-medium"
                >
                  View Details
                </button>
              </div>
            </div>
            
            <div className="p-4">
              <h3 className="font-semibold text-gray-900 dark:text-white mb-1">
                {photo.title}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2">
                {photo.description}
              </p>
              
              <div className="flex items-center justify-between">
                <span className="text-xs text-gray-500 dark:text-gray-400">
                  {new Date(photo.date).toLocaleDateString()}
                </span>
                
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => handleLike(photo.id)}
                    className={`flex items-center space-x-1 text-sm ${
                      photo.isLiked ? 'text-red-500' : 'text-gray-500 dark:text-gray-400'
                    }`}
                  >
                    <Heart className={`h-4 w-4 ${photo.isLiked ? 'fill-current' : ''}`} />
                    <span>{photo.likes}</span>
                  </button>
                  
                  <button className="text-gray-500 dark:text-gray-400 hover:text-blue-500">
                    <Share2 className="h-4 w-4" />
                  </button>
                  
                  <button 
                    onClick={() => handleDelete(photo.id)}
                    className="text-gray-500 dark:text-gray-400 hover:text-red-500"
                  >
                    <Trash2 className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Upload Modal */}
      {isUploadModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-slate-800 rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Add New Photo
            </h3>
            
            <form onSubmit={handleUpload} className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Photo File
                </label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleFileChange}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Title
                </label>
                <input
                  type="text"
                  value={uploadForm.title}
                  onChange={(e) => setUploadForm({ ...uploadForm, title: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"
                  required
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Description
                </label>
                <textarea
                  value={uploadForm.description}
                  onChange={(e) => setUploadForm({ ...uploadForm, description: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"
                />
              </div>
              
              <div className="flex space-x-3 pt-4">
                <button
                  type="button"
                  onClick={() => setIsUploadModalOpen(false)}
                  className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  className="flex-1 btn-primary"
                >
                  Upload Photo
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      {/* Photo Detail Modal */}
      {selectedPhoto && (
        <div className="fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-slate-800 rounded-lg p-6 w-full max-w-2xl">
            <div className="flex justify-between items-start mb-4">
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                {selectedPhoto.title}
              </h3>
              <button
                onClick={() => setSelectedPhoto(null)}
                className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              >
                ✕
              </button>
            </div>
            
            <div className="aspect-video bg-gradient-to-br from-green-100 to-yellow-100 dark:from-green-900 dark:to-yellow-900 rounded-lg mb-4 flex items-center justify-center">
              <ImageIcon className="h-24 w-24 text-gray-400" />
            </div>
            
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              {selectedPhoto.description}
            </p>
            
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500 dark:text-gray-400">
                {new Date(selectedPhoto.date).toLocaleDateString()}
              </span>
              
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => handleLike(selectedPhoto.id)}
                  className={`flex items-center space-x-1 ${
                    selectedPhoto.isLiked ? 'text-red-500' : 'text-gray-500 dark:text-gray-400'
                  }`}
                >
                  <Heart className={`h-5 w-5 ${selectedPhoto.isLiked ? 'fill-current' : ''}`} />
                  <span>{selectedPhoto.likes}</span>
                </button>
                
                <button className="text-gray-500 dark:text-gray-400 hover:text-blue-500">
                  <Download className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
