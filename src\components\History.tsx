'use client'

import { useState } from 'react'
import { Search, Download, Clock, User, Package, DollarSign } from 'lucide-react'

interface HistoryItem {
  id: string
  type: 'product' | 'debt' | 'payment' | 'login' | 'system'
  action: string
  description: string
  user: string
  timestamp: string
  details?: any
}

export default function History() {
  const [searchTerm, setSearchTerm] = useState('')
  const [filterType, setFilterType] = useState('all')
  const [dateRange, setDateRange] = useState('7days')

  const historyData: HistoryItem[] = [
    {
      id: '1',
      type: 'product',
      action: 'Product Added',
      description: 'Added "Lucky Me Pancit Canton" to product list',
      user: 'Admin',
      timestamp: '2024-01-20T10:30:00Z',
      details: { productName: 'Lucky Me Pancit Canton', price: 15.00 }
    },
    {
      id: '2',
      type: 'debt',
      action: 'Debt Recorded',
      description: 'New debt record for Juan Dela Cruz',
      user: 'Admin',
      timestamp: '2024-01-20T09:15:00Z',
      details: { customer: 'Juan <PERSON>', amount: 45.00 }
    },
    {
      id: '3',
      type: 'payment',
      action: 'Payment Received',
      description: 'Payment received from <PERSON>',
      user: 'Admin',
      timestamp: '2024-01-19T16:45:00Z',
      details: { customer: 'Maria Santos', amount: 120.00 }
    },
    {
      id: '4',
      type: 'product',
      action: 'Stock Updated',
      description: 'Updated stock quantity for Coca-Cola',
      user: 'Admin',
      timestamp: '2024-01-19T14:20:00Z',
      details: { productName: 'Coca-Cola', oldStock: 25, newStock: 50 }
    },
    {
      id: '5',
      type: 'login',
      action: 'User Login',
      description: 'Admin user logged into the system',
      user: 'Admin',
      timestamp: '2024-01-19T08:00:00Z',
      details: { ipAddress: '*************' }
    },
    {
      id: '6',
      type: 'system',
      action: 'Backup Created',
      description: 'Automatic database backup completed',
      user: 'System',
      timestamp: '2024-01-19T02:00:00Z',
      details: { backupSize: '2.5MB' }
    },
    {
      id: '7',
      type: 'debt',
      action: 'Debt Updated',
      description: 'Updated debt record for Ana Reyes',
      user: 'Admin',
      timestamp: '2024-01-18T15:30:00Z',
      details: { customer: 'Ana Reyes', oldAmount: 75.00, newAmount: 100.00 }
    },
    {
      id: '8',
      type: 'product',
      action: 'Product Deleted',
      description: 'Removed "Expired Milk" from product list',
      user: 'Admin',
      timestamp: '2024-01-18T11:10:00Z',
      details: { productName: 'Expired Milk' }
    },
  ]

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'product':
        return <Package className="h-4 w-4" />
      case 'debt':
      case 'payment':
        return <DollarSign className="h-4 w-4" />
      case 'login':
        return <User className="h-4 w-4" />
      case 'system':
        return <Clock className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'product':
        return 'bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400'
      case 'debt':
        return 'bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400'
      case 'payment':
        return 'bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400'
      case 'login':
        return 'bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400'
      case 'system':
        return 'bg-gray-100 text-gray-600 dark:bg-gray-900/30 dark:text-gray-400'
      default:
        return 'bg-gray-100 text-gray-600 dark:bg-gray-900/30 dark:text-gray-400'
    }
  }

  const filteredHistory = historyData.filter(item => {
    const matchesSearch = item.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.action.toLowerCase().includes(searchTerm.toLowerCase())
    const matchesType = filterType === 'all' || item.type === filterType
    return matchesSearch && matchesType
  })

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp)
    const now = new Date()
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) {
      return 'Just now'
    } else if (diffInHours < 24) {
      return `${diffInHours} hours ago`
    } else {
      return date.toLocaleDateString() + ' ' + date.toLocaleTimeString()
    }
  }

  const exportHistory = () => {
    const csvContent = [
      ['Timestamp', 'Type', 'Action', 'Description', 'User'],
      ...filteredHistory.map(item => [
        item.timestamp,
        item.type,
        item.action,
        item.description,
        item.user
      ])
    ].map(row => row.join(',')).join('\n')

    const blob = new Blob([csvContent], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `revantad-store-history-${new Date().toISOString().split('T')[0]}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">Activity History</h2>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Track all activities and changes in your store
          </p>
        </div>
        <button
          onClick={exportHistory}
          className="btn-primary flex items-center"
        >
          <Download className="h-4 w-4 mr-2" />
          Export History
        </button>
      </div>

      {/* Filters */}
      <div className="card p-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <input
              type="text"
              placeholder="Search activities..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"
            />
          </div>

          {/* Type Filter */}
          <select
            value={filterType}
            onChange={(e) => setFilterType(e.target.value)}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"
          >
            <option value="all">All Types</option>
            <option value="product">Product Activities</option>
            <option value="debt">Debt Activities</option>
            <option value="payment">Payment Activities</option>
            <option value="login">Login Activities</option>
            <option value="system">System Activities</option>
          </select>

          {/* Date Range */}
          <select
            value={dateRange}
            onChange={(e) => setDateRange(e.target.value)}
            className="px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"
          >
            <option value="7days">Last 7 days</option>
            <option value="30days">Last 30 days</option>
            <option value="90days">Last 90 days</option>
            <option value="all">All time</option>
          </select>
        </div>
      </div>

      {/* Activity Stats */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        {[
          { type: 'product', label: 'Product Activities', count: historyData.filter(h => h.type === 'product').length },
          { type: 'debt', label: 'Debt Activities', count: historyData.filter(h => h.type === 'debt').length },
          { type: 'payment', label: 'Payment Activities', count: historyData.filter(h => h.type === 'payment').length },
          { type: 'login', label: 'Login Activities', count: historyData.filter(h => h.type === 'login').length },
          { type: 'system', label: 'System Activities', count: historyData.filter(h => h.type === 'system').length },
        ].map((stat) => (
          <div key={stat.type} className="card p-4 text-center">
            <div className={`inline-flex p-2 rounded-lg mb-2 ${getTypeColor(stat.type)}`}>
              {getTypeIcon(stat.type)}
            </div>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">{stat.count}</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">{stat.label}</p>
          </div>
        ))}
      </div>

      {/* History List */}
      <div className="card">
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
            Recent Activities ({filteredHistory.length})
          </h3>
        </div>
        
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          {filteredHistory.map((item) => (
            <div key={item.id} className="p-6 hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors">
              <div className="flex items-start space-x-4">
                <div className={`p-2 rounded-lg ${getTypeColor(item.type)}`}>
                  {getTypeIcon(item.type)}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center justify-between">
                    <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                      {item.action}
                    </h4>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {formatTimestamp(item.timestamp)}
                    </span>
                  </div>
                  
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {item.description}
                  </p>
                  
                  <div className="flex items-center justify-between mt-2">
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      by {item.user}
                    </span>
                    
                    {item.details && (
                      <button className="text-xs text-green-600 dark:text-green-400 hover:underline">
                        View Details
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
        
        {filteredHistory.length === 0 && (
          <div className="p-12 text-center">
            <Clock className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No activities found
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              Try adjusting your search or filter criteria
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
