{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/chart/bar/BaseBarSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport SeriesModel from '../../model/Series.js';\nimport createSeriesData from '../helper/createSeriesData.js';\nimport { each } from 'zrender/lib/core/util.js';\nvar BaseBarSeriesModel = /** @class */function (_super) {\n  __extends(BaseBarSeriesModel, _super);\n  function BaseBarSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = BaseBarSeriesModel.type;\n    return _this;\n  }\n  BaseBarSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: true\n    });\n  };\n  BaseBarSeriesModel.prototype.getMarkerPosition = function (value, dims, startingAtTick) {\n    var coordSys = this.coordinateSystem;\n    if (coordSys && coordSys.clampData) {\n      // PENDING if clamp ?\n      var clampData_1 = coordSys.clampData(value);\n      var pt_1 = coordSys.dataToPoint(clampData_1);\n      if (startingAtTick) {\n        each(coordSys.getAxes(), function (axis, idx) {\n          // If axis type is category, use tick coords instead\n          if (axis.type === 'category' && dims != null) {\n            var tickCoords = axis.getTicksCoords();\n            var alignTicksWithLabel = axis.getTickModel().get('alignWithLabel');\n            var targetTickId = clampData_1[idx];\n            // The index of rightmost tick of markArea is 1 larger than x1/y1 index\n            var isEnd = dims[idx] === 'x1' || dims[idx] === 'y1';\n            if (isEnd && !alignTicksWithLabel) {\n              targetTickId += 1;\n            }\n            // The only contains one tick, tickCoords is\n            // like [{coord: 0, tickValue: 0}, {coord: 0}]\n            // to the length should always be larger than 1\n            if (tickCoords.length < 2) {\n              return;\n            } else if (tickCoords.length === 2) {\n              // The left value and right value of the axis are\n              // the same. coord is 0 in both items. Use the max\n              // value of the axis as the coord\n              pt_1[idx] = axis.toGlobalCoord(axis.getExtent()[isEnd ? 1 : 0]);\n              return;\n            }\n            var leftCoord = void 0;\n            var coord = void 0;\n            var stepTickValue = 1;\n            for (var i = 0; i < tickCoords.length; i++) {\n              var tickCoord = tickCoords[i].coord;\n              // The last item of tickCoords doesn't contain\n              // tickValue\n              var tickValue = i === tickCoords.length - 1 ? tickCoords[i - 1].tickValue + stepTickValue : tickCoords[i].tickValue;\n              if (tickValue === targetTickId) {\n                coord = tickCoord;\n                break;\n              } else if (tickValue < targetTickId) {\n                leftCoord = tickCoord;\n              } else if (leftCoord != null && tickValue > targetTickId) {\n                coord = (tickCoord + leftCoord) / 2;\n                break;\n              }\n              if (i === 1) {\n                // Here we assume the step of category axes is\n                // the same\n                stepTickValue = tickValue - tickCoords[0].tickValue;\n              }\n            }\n            if (coord == null) {\n              if (!leftCoord) {\n                // targetTickId is smaller than all tick ids in the\n                // visible area, use the leftmost tick coord\n                coord = tickCoords[0].coord;\n              } else if (leftCoord) {\n                // targetTickId is larger than all tick ids in the\n                // visible area, use the rightmost tick coord\n                coord = tickCoords[tickCoords.length - 1].coord;\n              }\n            }\n            pt_1[idx] = axis.toGlobalCoord(coord);\n          }\n        });\n      } else {\n        var data = this.getData();\n        var offset = data.getLayout('offset');\n        var size = data.getLayout('size');\n        var offsetIndex = coordSys.getBaseAxis().isHorizontal() ? 0 : 1;\n        pt_1[offsetIndex] += offset + size / 2;\n      }\n      return pt_1;\n    }\n    return [NaN, NaN];\n  };\n  BaseBarSeriesModel.type = 'series.__base_bar__';\n  BaseBarSeriesModel.defaultOption = {\n    // zlevel: 0,\n    z: 2,\n    coordinateSystem: 'cartesian2d',\n    legendHoverLink: true,\n    // stack: null\n    // Cartesian coordinate system\n    // xAxisIndex: 0,\n    // yAxisIndex: 0,\n    barMinHeight: 0,\n    barMinAngle: 0,\n    // cursor: null,\n    large: false,\n    largeThreshold: 400,\n    progressive: 3e3,\n    progressiveChunkMode: 'mod'\n  };\n  return BaseBarSeriesModel;\n}(SeriesModel);\nSeriesModel.registerClass(BaseBarSeriesModel);\nexport default BaseBarSeriesModel;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;;;;;AACA,IAAI,qBAAqB,WAAW,GAAE,SAAU,MAAM;IACpD,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE,oBAAoB;IAC9B,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,mBAAmB,IAAI;QACpC,OAAO;IACT;IACA,mBAAmB,SAAS,CAAC,cAAc,GAAG,SAAU,MAAM,EAAE,OAAO;QACrE,OAAO,CAAA,GAAA,qKAAA,CAAA,UAAgB,AAAD,EAAE,MAAM,IAAI,EAAE;YAClC,oBAAoB;QACtB;IACF;IACA,mBAAmB,SAAS,CAAC,iBAAiB,GAAG,SAAU,KAAK,EAAE,IAAI,EAAE,cAAc;QACpF,IAAI,WAAW,IAAI,CAAC,gBAAgB;QACpC,IAAI,YAAY,SAAS,SAAS,EAAE;YAClC,qBAAqB;YACrB,IAAI,cAAc,SAAS,SAAS,CAAC;YACrC,IAAI,OAAO,SAAS,WAAW,CAAC;YAChC,IAAI,gBAAgB;gBAClB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO,IAAI,SAAU,IAAI,EAAE,GAAG;oBAC1C,oDAAoD;oBACpD,IAAI,KAAK,IAAI,KAAK,cAAc,QAAQ,MAAM;wBAC5C,IAAI,aAAa,KAAK,cAAc;wBACpC,IAAI,sBAAsB,KAAK,YAAY,GAAG,GAAG,CAAC;wBAClD,IAAI,eAAe,WAAW,CAAC,IAAI;wBACnC,uEAAuE;wBACvE,IAAI,QAAQ,IAAI,CAAC,IAAI,KAAK,QAAQ,IAAI,CAAC,IAAI,KAAK;wBAChD,IAAI,SAAS,CAAC,qBAAqB;4BACjC,gBAAgB;wBAClB;wBACA,4CAA4C;wBAC5C,8CAA8C;wBAC9C,+CAA+C;wBAC/C,IAAI,WAAW,MAAM,GAAG,GAAG;4BACzB;wBACF,OAAO,IAAI,WAAW,MAAM,KAAK,GAAG;4BAClC,iDAAiD;4BACjD,kDAAkD;4BAClD,iCAAiC;4BACjC,IAAI,CAAC,IAAI,GAAG,KAAK,aAAa,CAAC,KAAK,SAAS,EAAE,CAAC,QAAQ,IAAI,EAAE;4BAC9D;wBACF;wBACA,IAAI,YAAY,KAAK;wBACrB,IAAI,QAAQ,KAAK;wBACjB,IAAI,gBAAgB;wBACpB,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;4BAC1C,IAAI,YAAY,UAAU,CAAC,EAAE,CAAC,KAAK;4BACnC,8CAA8C;4BAC9C,YAAY;4BACZ,IAAI,YAAY,MAAM,WAAW,MAAM,GAAG,IAAI,UAAU,CAAC,IAAI,EAAE,CAAC,SAAS,GAAG,gBAAgB,UAAU,CAAC,EAAE,CAAC,SAAS;4BACnH,IAAI,cAAc,cAAc;gCAC9B,QAAQ;gCACR;4BACF,OAAO,IAAI,YAAY,cAAc;gCACnC,YAAY;4BACd,OAAO,IAAI,aAAa,QAAQ,YAAY,cAAc;gCACxD,QAAQ,CAAC,YAAY,SAAS,IAAI;gCAClC;4BACF;4BACA,IAAI,MAAM,GAAG;gCACX,8CAA8C;gCAC9C,WAAW;gCACX,gBAAgB,YAAY,UAAU,CAAC,EAAE,CAAC,SAAS;4BACrD;wBACF;wBACA,IAAI,SAAS,MAAM;4BACjB,IAAI,CAAC,WAAW;gCACd,mDAAmD;gCACnD,4CAA4C;gCAC5C,QAAQ,UAAU,CAAC,EAAE,CAAC,KAAK;4BAC7B,OAAO,IAAI,WAAW;gCACpB,kDAAkD;gCAClD,6CAA6C;gCAC7C,QAAQ,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,KAAK;4BACjD;wBACF;wBACA,IAAI,CAAC,IAAI,GAAG,KAAK,aAAa,CAAC;oBACjC;gBACF;YACF,OAAO;gBACL,IAAI,OAAO,IAAI,CAAC,OAAO;gBACvB,IAAI,SAAS,KAAK,SAAS,CAAC;gBAC5B,IAAI,OAAO,KAAK,SAAS,CAAC;gBAC1B,IAAI,cAAc,SAAS,WAAW,GAAG,YAAY,KAAK,IAAI;gBAC9D,IAAI,CAAC,YAAY,IAAI,SAAS,OAAO;YACvC;YACA,OAAO;QACT;QACA,OAAO;YAAC;YAAK;SAAI;IACnB;IACA,mBAAmB,IAAI,GAAG;IAC1B,mBAAmB,aAAa,GAAG;QACjC,aAAa;QACb,GAAG;QACH,kBAAkB;QAClB,iBAAiB;QACjB,cAAc;QACd,8BAA8B;QAC9B,iBAAiB;QACjB,iBAAiB;QACjB,cAAc;QACd,aAAa;QACb,gBAAgB;QAChB,OAAO;QACP,gBAAgB;QAChB,aAAa;QACb,sBAAsB;IACxB;IACA,OAAO;AACT,EAAE,iJAAA,CAAA,UAAW;AACb,iJAAA,CAAA,UAAW,CAAC,aAAa,CAAC;uCACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/chart/bar/BarSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport BaseBarSeriesModel from './BaseBarSeries.js';\nimport createSeriesData from '../helper/createSeriesData.js';\nimport { inheritDefaultOption } from '../../util/component.js';\nvar BarSeriesModel = /** @class */function (_super) {\n  __extends(BarSeriesModel, _super);\n  function BarSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = BarSeriesModel.type;\n    return _this;\n  }\n  BarSeriesModel.prototype.getInitialData = function () {\n    return createSeriesData(null, this, {\n      useEncodeDefaulter: true,\n      createInvertedIndices: !!this.get('realtimeSort', true) || null\n    });\n  };\n  /**\r\n   * @override\r\n   */\n  BarSeriesModel.prototype.getProgressive = function () {\n    // Do not support progressive in normal mode.\n    return this.get('large') ? this.get('progressive') : false;\n  };\n  /**\r\n   * @override\r\n   */\n  BarSeriesModel.prototype.getProgressiveThreshold = function () {\n    // Do not support progressive in normal mode.\n    var progressiveThreshold = this.get('progressiveThreshold');\n    var largeThreshold = this.get('largeThreshold');\n    if (largeThreshold > progressiveThreshold) {\n      progressiveThreshold = largeThreshold;\n    }\n    return progressiveThreshold;\n  };\n  BarSeriesModel.prototype.brushSelector = function (dataIndex, data, selectors) {\n    return selectors.rect(data.getItemLayout(dataIndex));\n  };\n  BarSeriesModel.type = 'series.bar';\n  BarSeriesModel.dependencies = ['grid', 'polar'];\n  BarSeriesModel.defaultOption = inheritDefaultOption(BaseBarSeriesModel.defaultOption, {\n    // If clipped\n    // Only available on cartesian2d\n    clip: true,\n    roundCap: false,\n    showBackground: false,\n    backgroundStyle: {\n      color: 'rgba(180, 180, 180, 0.2)',\n      borderColor: null,\n      borderWidth: 0,\n      borderType: 'solid',\n      borderRadius: 0,\n      shadowBlur: 0,\n      shadowColor: null,\n      shadowOffsetX: 0,\n      shadowOffsetY: 0,\n      opacity: 1\n    },\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    },\n    realtimeSort: false\n  });\n  return BarSeriesModel;\n}(BaseBarSeriesModel);\nexport default BarSeriesModel;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;;;;;AACA,IAAI,iBAAiB,WAAW,GAAE,SAAU,MAAM;IAChD,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;IAC1B,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,eAAe,IAAI;QAChC,OAAO;IACT;IACA,eAAe,SAAS,CAAC,cAAc,GAAG;QACxC,OAAO,CAAA,GAAA,qKAAA,CAAA,UAAgB,AAAD,EAAE,MAAM,IAAI,EAAE;YAClC,oBAAoB;YACpB,uBAAuB,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,gBAAgB,SAAS;QAC7D;IACF;IACA;;GAEC,GACD,eAAe,SAAS,CAAC,cAAc,GAAG;QACxC,6CAA6C;QAC7C,OAAO,IAAI,CAAC,GAAG,CAAC,WAAW,IAAI,CAAC,GAAG,CAAC,iBAAiB;IACvD;IACA;;GAEC,GACD,eAAe,SAAS,CAAC,uBAAuB,GAAG;QACjD,6CAA6C;QAC7C,IAAI,uBAAuB,IAAI,CAAC,GAAG,CAAC;QACpC,IAAI,iBAAiB,IAAI,CAAC,GAAG,CAAC;QAC9B,IAAI,iBAAiB,sBAAsB;YACzC,uBAAuB;QACzB;QACA,OAAO;IACT;IACA,eAAe,SAAS,CAAC,aAAa,GAAG,SAAU,SAAS,EAAE,IAAI,EAAE,SAAS;QAC3E,OAAO,UAAU,IAAI,CAAC,KAAK,aAAa,CAAC;IAC3C;IACA,eAAe,IAAI,GAAG;IACtB,eAAe,YAAY,GAAG;QAAC;QAAQ;KAAQ;IAC/C,eAAe,aAAa,GAAG,CAAA,GAAA,mJAAA,CAAA,uBAAoB,AAAD,EAAE,+JAAA,CAAA,UAAkB,CAAC,aAAa,EAAE;QACpF,aAAa;QACb,gCAAgC;QAChC,MAAM;QACN,UAAU;QACV,gBAAgB;QAChB,iBAAiB;YACf,OAAO;YACP,aAAa;YACb,aAAa;YACb,YAAY;YACZ,cAAc;YACd,YAAY;YACZ,aAAa;YACb,eAAe;YACf,eAAe;YACf,SAAS;QACX;QACA,QAAQ;YACN,WAAW;gBACT,aAAa;YACf;QACF;QACA,cAAc;IAChB;IACA,OAAO;AACT,EAAE,+JAAA,CAAA,UAAkB;uCACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 294, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/chart/bar/BarView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport Path from 'zrender/lib/graphic/Path.js';\nimport Group from 'zrender/lib/graphic/Group.js';\nimport { extend, each, map } from 'zrender/lib/core/util.js';\nimport { Rect, Sector, updateProps, initProps, removeElementWithFadeOut, traverseElements } from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { setStatesStylesFromModel, toggleHoverEmphasis } from '../../util/states.js';\nimport { setLabelStyle, getLabelStatesModels, setLabelValueAnimation, labelInner } from '../../label/labelStyle.js';\nimport { throttle } from '../../util/throttle.js';\nimport { createClipPath } from '../helper/createClipPathFromCoordSys.js';\nimport Sausage from '../../util/shape/sausage.js';\nimport ChartView from '../../view/Chart.js';\nimport { isCoordinateSystemType } from '../../coord/CoordinateSystem.js';\nimport { getDefaultLabel, getDefaultInterpolatedLabel } from '../helper/labelHelper.js';\nimport { warn } from '../../util/log.js';\nimport { createSectorCalculateTextPosition, setSectorTextRotation } from '../../label/sectorLabel.js';\nimport { saveOldStyle } from '../../animation/basicTransition.js';\nimport { getSectorCornerRadius } from '../helper/sectorHelper.js';\nvar mathMax = Math.max;\nvar mathMin = Math.min;\nfunction getClipArea(coord, data) {\n  var coordSysClipArea = coord.getArea && coord.getArea();\n  if (isCoordinateSystemType(coord, 'cartesian2d')) {\n    var baseAxis = coord.getBaseAxis();\n    // When boundaryGap is false or using time axis. bar may exceed the grid.\n    // We should not clip this part.\n    // See test/bar2.html\n    if (baseAxis.type !== 'category' || !baseAxis.onBand) {\n      var expandWidth = data.getLayout('bandWidth');\n      if (baseAxis.isHorizontal()) {\n        coordSysClipArea.x -= expandWidth;\n        coordSysClipArea.width += expandWidth * 2;\n      } else {\n        coordSysClipArea.y -= expandWidth;\n        coordSysClipArea.height += expandWidth * 2;\n      }\n    }\n  }\n  return coordSysClipArea;\n}\nvar BarView = /** @class */function (_super) {\n  __extends(BarView, _super);\n  function BarView() {\n    var _this = _super.call(this) || this;\n    _this.type = BarView.type;\n    _this._isFirstFrame = true;\n    return _this;\n  }\n  BarView.prototype.render = function (seriesModel, ecModel, api, payload) {\n    this._model = seriesModel;\n    this._removeOnRenderedListener(api);\n    this._updateDrawMode(seriesModel);\n    var coordinateSystemType = seriesModel.get('coordinateSystem');\n    if (coordinateSystemType === 'cartesian2d' || coordinateSystemType === 'polar') {\n      // Clear previously rendered progressive elements.\n      this._progressiveEls = null;\n      this._isLargeDraw ? this._renderLarge(seriesModel, ecModel, api) : this._renderNormal(seriesModel, ecModel, api, payload);\n    } else if (process.env.NODE_ENV !== 'production') {\n      warn('Only cartesian2d and polar supported for bar.');\n    }\n  };\n  BarView.prototype.incrementalPrepareRender = function (seriesModel) {\n    this._clear();\n    this._updateDrawMode(seriesModel);\n    // incremental also need to clip, otherwise might be overlow.\n    // But must not set clip in each frame, otherwise all of the children will be marked redraw.\n    this._updateLargeClip(seriesModel);\n  };\n  BarView.prototype.incrementalRender = function (params, seriesModel) {\n    // Reset\n    this._progressiveEls = [];\n    // Do not support progressive in normal mode.\n    this._incrementalRenderLarge(params, seriesModel);\n  };\n  BarView.prototype.eachRendered = function (cb) {\n    traverseElements(this._progressiveEls || this.group, cb);\n  };\n  BarView.prototype._updateDrawMode = function (seriesModel) {\n    var isLargeDraw = seriesModel.pipelineContext.large;\n    if (this._isLargeDraw == null || isLargeDraw !== this._isLargeDraw) {\n      this._isLargeDraw = isLargeDraw;\n      this._clear();\n    }\n  };\n  BarView.prototype._renderNormal = function (seriesModel, ecModel, api, payload) {\n    var group = this.group;\n    var data = seriesModel.getData();\n    var oldData = this._data;\n    var coord = seriesModel.coordinateSystem;\n    var baseAxis = coord.getBaseAxis();\n    var isHorizontalOrRadial;\n    if (coord.type === 'cartesian2d') {\n      isHorizontalOrRadial = baseAxis.isHorizontal();\n    } else if (coord.type === 'polar') {\n      isHorizontalOrRadial = baseAxis.dim === 'angle';\n    }\n    var animationModel = seriesModel.isAnimationEnabled() ? seriesModel : null;\n    var realtimeSortCfg = shouldRealtimeSort(seriesModel, coord);\n    if (realtimeSortCfg) {\n      this._enableRealtimeSort(realtimeSortCfg, data, api);\n    }\n    var needsClip = seriesModel.get('clip', true) || realtimeSortCfg;\n    var coordSysClipArea = getClipArea(coord, data);\n    // If there is clipPath created in large mode. Remove it.\n    group.removeClipPath();\n    // We don't use clipPath in normal mode because we needs a perfect animation\n    // And don't want the label are clipped.\n    var roundCap = seriesModel.get('roundCap', true);\n    var drawBackground = seriesModel.get('showBackground', true);\n    var backgroundModel = seriesModel.getModel('backgroundStyle');\n    var barBorderRadius = backgroundModel.get('borderRadius') || 0;\n    var bgEls = [];\n    var oldBgEls = this._backgroundEls;\n    var isInitSort = payload && payload.isInitSort;\n    var isChangeOrder = payload && payload.type === 'changeAxisOrder';\n    function createBackground(dataIndex) {\n      var bgLayout = getLayout[coord.type](data, dataIndex);\n      var bgEl = createBackgroundEl(coord, isHorizontalOrRadial, bgLayout);\n      bgEl.useStyle(backgroundModel.getItemStyle());\n      // Only cartesian2d support borderRadius.\n      if (coord.type === 'cartesian2d') {\n        bgEl.setShape('r', barBorderRadius);\n      } else {\n        bgEl.setShape('cornerRadius', barBorderRadius);\n      }\n      bgEls[dataIndex] = bgEl;\n      return bgEl;\n    }\n    ;\n    data.diff(oldData).add(function (dataIndex) {\n      var itemModel = data.getItemModel(dataIndex);\n      var layout = getLayout[coord.type](data, dataIndex, itemModel);\n      if (drawBackground) {\n        createBackground(dataIndex);\n      }\n      // If dataZoom in filteMode: 'empty', the baseValue can be set as NaN in \"axisProxy\".\n      if (!data.hasValue(dataIndex) || !isValidLayout[coord.type](layout)) {\n        return;\n      }\n      var isClipped = false;\n      if (needsClip) {\n        // Clip will modify the layout params.\n        // And return a boolean to determine if the shape are fully clipped.\n        isClipped = clip[coord.type](coordSysClipArea, layout);\n      }\n      var el = elementCreator[coord.type](seriesModel, data, dataIndex, layout, isHorizontalOrRadial, animationModel, baseAxis.model, false, roundCap);\n      if (realtimeSortCfg) {\n        /**\r\n         * Force label animation because even if the element is\r\n         * ignored because it's clipped, it may not be clipped after\r\n         * changing order. Then, if not using forceLabelAnimation,\r\n         * the label animation was never started, in which case,\r\n         * the label will be the final value and doesn't have label\r\n         * animation.\r\n         */\n        el.forceLabelAnimation = true;\n      }\n      updateStyle(el, data, dataIndex, itemModel, layout, seriesModel, isHorizontalOrRadial, coord.type === 'polar');\n      if (isInitSort) {\n        el.attr({\n          shape: layout\n        });\n      } else if (realtimeSortCfg) {\n        updateRealtimeAnimation(realtimeSortCfg, animationModel, el, layout, dataIndex, isHorizontalOrRadial, false, false);\n      } else {\n        initProps(el, {\n          shape: layout\n        }, seriesModel, dataIndex);\n      }\n      data.setItemGraphicEl(dataIndex, el);\n      group.add(el);\n      el.ignore = isClipped;\n    }).update(function (newIndex, oldIndex) {\n      var itemModel = data.getItemModel(newIndex);\n      var layout = getLayout[coord.type](data, newIndex, itemModel);\n      if (drawBackground) {\n        var bgEl = void 0;\n        if (oldBgEls.length === 0) {\n          bgEl = createBackground(oldIndex);\n        } else {\n          bgEl = oldBgEls[oldIndex];\n          bgEl.useStyle(backgroundModel.getItemStyle());\n          // Only cartesian2d support borderRadius.\n          if (coord.type === 'cartesian2d') {\n            bgEl.setShape('r', barBorderRadius);\n          } else {\n            bgEl.setShape('cornerRadius', barBorderRadius);\n          }\n          bgEls[newIndex] = bgEl;\n        }\n        var bgLayout = getLayout[coord.type](data, newIndex);\n        var shape = createBackgroundShape(isHorizontalOrRadial, bgLayout, coord);\n        updateProps(bgEl, {\n          shape: shape\n        }, animationModel, newIndex);\n      }\n      var el = oldData.getItemGraphicEl(oldIndex);\n      if (!data.hasValue(newIndex) || !isValidLayout[coord.type](layout)) {\n        group.remove(el);\n        return;\n      }\n      var isClipped = false;\n      if (needsClip) {\n        isClipped = clip[coord.type](coordSysClipArea, layout);\n        if (isClipped) {\n          group.remove(el);\n        }\n      }\n      if (!el) {\n        el = elementCreator[coord.type](seriesModel, data, newIndex, layout, isHorizontalOrRadial, animationModel, baseAxis.model, !!el, roundCap);\n      } else {\n        saveOldStyle(el);\n      }\n      if (realtimeSortCfg) {\n        el.forceLabelAnimation = true;\n      }\n      if (isChangeOrder) {\n        var textEl = el.getTextContent();\n        if (textEl) {\n          var labelInnerStore = labelInner(textEl);\n          if (labelInnerStore.prevValue != null) {\n            /**\r\n             * Set preValue to be value so that no new label\r\n             * should be started, otherwise, it will take a full\r\n             * `animationDurationUpdate` time to finish the\r\n             * animation, which is not expected.\r\n             */\n            labelInnerStore.prevValue = labelInnerStore.value;\n          }\n        }\n      }\n      // Not change anything if only order changed.\n      // Especially not change label.\n      else {\n        updateStyle(el, data, newIndex, itemModel, layout, seriesModel, isHorizontalOrRadial, coord.type === 'polar');\n      }\n      if (isInitSort) {\n        el.attr({\n          shape: layout\n        });\n      } else if (realtimeSortCfg) {\n        updateRealtimeAnimation(realtimeSortCfg, animationModel, el, layout, newIndex, isHorizontalOrRadial, true, isChangeOrder);\n      } else {\n        updateProps(el, {\n          shape: layout\n        }, seriesModel, newIndex, null);\n      }\n      data.setItemGraphicEl(newIndex, el);\n      el.ignore = isClipped;\n      group.add(el);\n    }).remove(function (dataIndex) {\n      var el = oldData.getItemGraphicEl(dataIndex);\n      el && removeElementWithFadeOut(el, seriesModel, dataIndex);\n    }).execute();\n    var bgGroup = this._backgroundGroup || (this._backgroundGroup = new Group());\n    bgGroup.removeAll();\n    for (var i = 0; i < bgEls.length; ++i) {\n      bgGroup.add(bgEls[i]);\n    }\n    group.add(bgGroup);\n    this._backgroundEls = bgEls;\n    this._data = data;\n  };\n  BarView.prototype._renderLarge = function (seriesModel, ecModel, api) {\n    this._clear();\n    createLarge(seriesModel, this.group);\n    this._updateLargeClip(seriesModel);\n  };\n  BarView.prototype._incrementalRenderLarge = function (params, seriesModel) {\n    this._removeBackground();\n    createLarge(seriesModel, this.group, this._progressiveEls, true);\n  };\n  BarView.prototype._updateLargeClip = function (seriesModel) {\n    // Use clipPath in large mode.\n    var clipPath = seriesModel.get('clip', true) && createClipPath(seriesModel.coordinateSystem, false, seriesModel);\n    var group = this.group;\n    if (clipPath) {\n      group.setClipPath(clipPath);\n    } else {\n      group.removeClipPath();\n    }\n  };\n  BarView.prototype._enableRealtimeSort = function (realtimeSortCfg, data, api) {\n    var _this = this;\n    // If no data in the first frame, wait for data to initSort\n    if (!data.count()) {\n      return;\n    }\n    var baseAxis = realtimeSortCfg.baseAxis;\n    if (this._isFirstFrame) {\n      this._dispatchInitSort(data, realtimeSortCfg, api);\n      this._isFirstFrame = false;\n    } else {\n      var orderMapping_1 = function (idx) {\n        var el = data.getItemGraphicEl(idx);\n        var shape = el && el.shape;\n        return shape &&\n        // The result should be consistent with the initial sort by data value.\n        // Do not support the case that both positive and negative exist.\n        Math.abs(baseAxis.isHorizontal() ? shape.height : shape.width)\n        // If data is NaN, shape.xxx may be NaN, so use || 0 here in case\n        || 0;\n      };\n      this._onRendered = function () {\n        _this._updateSortWithinSameData(data, orderMapping_1, baseAxis, api);\n      };\n      api.getZr().on('rendered', this._onRendered);\n    }\n  };\n  BarView.prototype._dataSort = function (data, baseAxis, orderMapping) {\n    var info = [];\n    data.each(data.mapDimension(baseAxis.dim), function (ordinalNumber, dataIdx) {\n      var mappedValue = orderMapping(dataIdx);\n      mappedValue = mappedValue == null ? NaN : mappedValue;\n      info.push({\n        dataIndex: dataIdx,\n        mappedValue: mappedValue,\n        ordinalNumber: ordinalNumber\n      });\n    });\n    info.sort(function (a, b) {\n      // If NaN, it will be treated as min val.\n      return b.mappedValue - a.mappedValue;\n    });\n    return {\n      ordinalNumbers: map(info, function (item) {\n        return item.ordinalNumber;\n      })\n    };\n  };\n  BarView.prototype._isOrderChangedWithinSameData = function (data, orderMapping, baseAxis) {\n    var scale = baseAxis.scale;\n    var ordinalDataDim = data.mapDimension(baseAxis.dim);\n    var lastValue = Number.MAX_VALUE;\n    for (var tickNum = 0, len = scale.getOrdinalMeta().categories.length; tickNum < len; ++tickNum) {\n      var rawIdx = data.rawIndexOf(ordinalDataDim, scale.getRawOrdinalNumber(tickNum));\n      var value = rawIdx < 0\n      // If some tick have no bar, the tick will be treated as min.\n      ? Number.MIN_VALUE\n      // PENDING: if dataZoom on baseAxis exits, is it a performance issue?\n      : orderMapping(data.indexOfRawIndex(rawIdx));\n      if (value > lastValue) {\n        return true;\n      }\n      lastValue = value;\n    }\n    return false;\n  };\n  /*\r\n   * Consider the case when A and B changed order, whose representing\r\n   * bars are both out of sight, we don't wish to trigger reorder action\r\n   * as long as the order in the view doesn't change.\r\n   */\n  BarView.prototype._isOrderDifferentInView = function (orderInfo, baseAxis) {\n    var scale = baseAxis.scale;\n    var extent = scale.getExtent();\n    var tickNum = Math.max(0, extent[0]);\n    var tickMax = Math.min(extent[1], scale.getOrdinalMeta().categories.length - 1);\n    for (; tickNum <= tickMax; ++tickNum) {\n      if (orderInfo.ordinalNumbers[tickNum] !== scale.getRawOrdinalNumber(tickNum)) {\n        return true;\n      }\n    }\n  };\n  BarView.prototype._updateSortWithinSameData = function (data, orderMapping, baseAxis, api) {\n    if (!this._isOrderChangedWithinSameData(data, orderMapping, baseAxis)) {\n      return;\n    }\n    var sortInfo = this._dataSort(data, baseAxis, orderMapping);\n    if (this._isOrderDifferentInView(sortInfo, baseAxis)) {\n      this._removeOnRenderedListener(api);\n      api.dispatchAction({\n        type: 'changeAxisOrder',\n        componentType: baseAxis.dim + 'Axis',\n        axisId: baseAxis.index,\n        sortInfo: sortInfo\n      });\n    }\n  };\n  BarView.prototype._dispatchInitSort = function (data, realtimeSortCfg, api) {\n    var baseAxis = realtimeSortCfg.baseAxis;\n    var sortResult = this._dataSort(data, baseAxis, function (dataIdx) {\n      return data.get(data.mapDimension(realtimeSortCfg.otherAxis.dim), dataIdx);\n    });\n    api.dispatchAction({\n      type: 'changeAxisOrder',\n      componentType: baseAxis.dim + 'Axis',\n      isInitSort: true,\n      axisId: baseAxis.index,\n      sortInfo: sortResult\n    });\n  };\n  BarView.prototype.remove = function (ecModel, api) {\n    this._clear(this._model);\n    this._removeOnRenderedListener(api);\n  };\n  BarView.prototype.dispose = function (ecModel, api) {\n    this._removeOnRenderedListener(api);\n  };\n  BarView.prototype._removeOnRenderedListener = function (api) {\n    if (this._onRendered) {\n      api.getZr().off('rendered', this._onRendered);\n      this._onRendered = null;\n    }\n  };\n  BarView.prototype._clear = function (model) {\n    var group = this.group;\n    var data = this._data;\n    if (model && model.isAnimationEnabled() && data && !this._isLargeDraw) {\n      this._removeBackground();\n      this._backgroundEls = [];\n      data.eachItemGraphicEl(function (el) {\n        removeElementWithFadeOut(el, model, getECData(el).dataIndex);\n      });\n    } else {\n      group.removeAll();\n    }\n    this._data = null;\n    this._isFirstFrame = true;\n  };\n  BarView.prototype._removeBackground = function () {\n    this.group.remove(this._backgroundGroup);\n    this._backgroundGroup = null;\n  };\n  BarView.type = 'bar';\n  return BarView;\n}(ChartView);\nvar clip = {\n  cartesian2d: function (coordSysBoundingRect, layout) {\n    var signWidth = layout.width < 0 ? -1 : 1;\n    var signHeight = layout.height < 0 ? -1 : 1;\n    // Needs positive width and height\n    if (signWidth < 0) {\n      layout.x += layout.width;\n      layout.width = -layout.width;\n    }\n    if (signHeight < 0) {\n      layout.y += layout.height;\n      layout.height = -layout.height;\n    }\n    var coordSysX2 = coordSysBoundingRect.x + coordSysBoundingRect.width;\n    var coordSysY2 = coordSysBoundingRect.y + coordSysBoundingRect.height;\n    var x = mathMax(layout.x, coordSysBoundingRect.x);\n    var x2 = mathMin(layout.x + layout.width, coordSysX2);\n    var y = mathMax(layout.y, coordSysBoundingRect.y);\n    var y2 = mathMin(layout.y + layout.height, coordSysY2);\n    var xClipped = x2 < x;\n    var yClipped = y2 < y;\n    // When xClipped or yClipped, the element will be marked as `ignore`.\n    // But we should also place the element at the edge of the coord sys bounding rect.\n    // Because if data changed and the bar shows again, its transition animation\n    // will begin at this place.\n    layout.x = xClipped && x > coordSysX2 ? x2 : x;\n    layout.y = yClipped && y > coordSysY2 ? y2 : y;\n    layout.width = xClipped ? 0 : x2 - x;\n    layout.height = yClipped ? 0 : y2 - y;\n    // Reverse back\n    if (signWidth < 0) {\n      layout.x += layout.width;\n      layout.width = -layout.width;\n    }\n    if (signHeight < 0) {\n      layout.y += layout.height;\n      layout.height = -layout.height;\n    }\n    return xClipped || yClipped;\n  },\n  polar: function (coordSysClipArea, layout) {\n    var signR = layout.r0 <= layout.r ? 1 : -1;\n    // Make sure r is larger than r0\n    if (signR < 0) {\n      var tmp = layout.r;\n      layout.r = layout.r0;\n      layout.r0 = tmp;\n    }\n    var r = mathMin(layout.r, coordSysClipArea.r);\n    var r0 = mathMax(layout.r0, coordSysClipArea.r0);\n    layout.r = r;\n    layout.r0 = r0;\n    var clipped = r - r0 < 0;\n    // Reverse back\n    if (signR < 0) {\n      var tmp = layout.r;\n      layout.r = layout.r0;\n      layout.r0 = tmp;\n    }\n    return clipped;\n  }\n};\nvar elementCreator = {\n  cartesian2d: function (seriesModel, data, newIndex, layout, isHorizontal, animationModel, axisModel, isUpdate, roundCap) {\n    var rect = new Rect({\n      shape: extend({}, layout),\n      z2: 1\n    });\n    rect.__dataIndex = newIndex;\n    rect.name = 'item';\n    if (animationModel) {\n      var rectShape = rect.shape;\n      var animateProperty = isHorizontal ? 'height' : 'width';\n      rectShape[animateProperty] = 0;\n    }\n    return rect;\n  },\n  polar: function (seriesModel, data, newIndex, layout, isRadial, animationModel, axisModel, isUpdate, roundCap) {\n    var ShapeClass = !isRadial && roundCap ? Sausage : Sector;\n    var sector = new ShapeClass({\n      shape: layout,\n      z2: 1\n    });\n    sector.name = 'item';\n    var positionMap = createPolarPositionMapping(isRadial);\n    sector.calculateTextPosition = createSectorCalculateTextPosition(positionMap, {\n      isRoundCap: ShapeClass === Sausage\n    });\n    // Animation\n    if (animationModel) {\n      var sectorShape = sector.shape;\n      var animateProperty = isRadial ? 'r' : 'endAngle';\n      var animateTarget = {};\n      sectorShape[animateProperty] = isRadial ? layout.r0 : layout.startAngle;\n      animateTarget[animateProperty] = layout[animateProperty];\n      (isUpdate ? updateProps : initProps)(sector, {\n        shape: animateTarget\n        // __value: typeof dataValue === 'string' ? parseInt(dataValue, 10) : dataValue\n      }, animationModel);\n    }\n    return sector;\n  }\n};\nfunction shouldRealtimeSort(seriesModel, coordSys) {\n  var realtimeSortOption = seriesModel.get('realtimeSort', true);\n  var baseAxis = coordSys.getBaseAxis();\n  if (process.env.NODE_ENV !== 'production') {\n    if (realtimeSortOption) {\n      if (baseAxis.type !== 'category') {\n        warn('`realtimeSort` will not work because this bar series is not based on a category axis.');\n      }\n      if (coordSys.type !== 'cartesian2d') {\n        warn('`realtimeSort` will not work because this bar series is not on cartesian2d.');\n      }\n    }\n  }\n  if (realtimeSortOption && baseAxis.type === 'category' && coordSys.type === 'cartesian2d') {\n    return {\n      baseAxis: baseAxis,\n      otherAxis: coordSys.getOtherAxis(baseAxis)\n    };\n  }\n}\nfunction updateRealtimeAnimation(realtimeSortCfg, seriesAnimationModel, el, layout, newIndex, isHorizontal, isUpdate, isChangeOrder) {\n  var seriesTarget;\n  var axisTarget;\n  if (isHorizontal) {\n    axisTarget = {\n      x: layout.x,\n      width: layout.width\n    };\n    seriesTarget = {\n      y: layout.y,\n      height: layout.height\n    };\n  } else {\n    axisTarget = {\n      y: layout.y,\n      height: layout.height\n    };\n    seriesTarget = {\n      x: layout.x,\n      width: layout.width\n    };\n  }\n  if (!isChangeOrder) {\n    // Keep the original growth animation if only axis order changed.\n    // Not start a new animation.\n    (isUpdate ? updateProps : initProps)(el, {\n      shape: seriesTarget\n    }, seriesAnimationModel, newIndex, null);\n  }\n  var axisAnimationModel = seriesAnimationModel ? realtimeSortCfg.baseAxis.model : null;\n  (isUpdate ? updateProps : initProps)(el, {\n    shape: axisTarget\n  }, axisAnimationModel, newIndex);\n}\nfunction checkPropertiesNotValid(obj, props) {\n  for (var i = 0; i < props.length; i++) {\n    if (!isFinite(obj[props[i]])) {\n      return true;\n    }\n  }\n  return false;\n}\nvar rectPropties = ['x', 'y', 'width', 'height'];\nvar polarPropties = ['cx', 'cy', 'r', 'startAngle', 'endAngle'];\nvar isValidLayout = {\n  cartesian2d: function (layout) {\n    return !checkPropertiesNotValid(layout, rectPropties);\n  },\n  polar: function (layout) {\n    return !checkPropertiesNotValid(layout, polarPropties);\n  }\n};\nvar getLayout = {\n  // itemModel is only used to get borderWidth, which is not needed\n  // when calculating bar background layout.\n  cartesian2d: function (data, dataIndex, itemModel) {\n    var layout = data.getItemLayout(dataIndex);\n    var fixedLineWidth = itemModel ? getLineWidth(itemModel, layout) : 0;\n    // fix layout with lineWidth\n    var signX = layout.width > 0 ? 1 : -1;\n    var signY = layout.height > 0 ? 1 : -1;\n    return {\n      x: layout.x + signX * fixedLineWidth / 2,\n      y: layout.y + signY * fixedLineWidth / 2,\n      width: layout.width - signX * fixedLineWidth,\n      height: layout.height - signY * fixedLineWidth\n    };\n  },\n  polar: function (data, dataIndex, itemModel) {\n    var layout = data.getItemLayout(dataIndex);\n    return {\n      cx: layout.cx,\n      cy: layout.cy,\n      r0: layout.r0,\n      r: layout.r,\n      startAngle: layout.startAngle,\n      endAngle: layout.endAngle,\n      clockwise: layout.clockwise\n    };\n  }\n};\nfunction isZeroOnPolar(layout) {\n  return layout.startAngle != null && layout.endAngle != null && layout.startAngle === layout.endAngle;\n}\nfunction createPolarPositionMapping(isRadial) {\n  return function (isRadial) {\n    var arcOrAngle = isRadial ? 'Arc' : 'Angle';\n    return function (position) {\n      switch (position) {\n        case 'start':\n        case 'insideStart':\n        case 'end':\n        case 'insideEnd':\n          return position + arcOrAngle;\n        default:\n          return position;\n      }\n    };\n  }(isRadial);\n}\nfunction updateStyle(el, data, dataIndex, itemModel, layout, seriesModel, isHorizontalOrRadial, isPolar) {\n  var style = data.getItemVisual(dataIndex, 'style');\n  if (!isPolar) {\n    var borderRadius = itemModel.get(['itemStyle', 'borderRadius']) || 0;\n    el.setShape('r', borderRadius);\n  } else if (!seriesModel.get('roundCap')) {\n    var sectorShape = el.shape;\n    var cornerRadius = getSectorCornerRadius(itemModel.getModel('itemStyle'), sectorShape, true);\n    extend(sectorShape, cornerRadius);\n    el.setShape(sectorShape);\n  }\n  el.useStyle(style);\n  var cursorStyle = itemModel.getShallow('cursor');\n  cursorStyle && el.attr('cursor', cursorStyle);\n  var labelPositionOutside = isPolar ? isHorizontalOrRadial ? layout.r >= layout.r0 ? 'endArc' : 'startArc' : layout.endAngle >= layout.startAngle ? 'endAngle' : 'startAngle' : isHorizontalOrRadial ? layout.height >= 0 ? 'bottom' : 'top' : layout.width >= 0 ? 'right' : 'left';\n  var labelStatesModels = getLabelStatesModels(itemModel);\n  setLabelStyle(el, labelStatesModels, {\n    labelFetcher: seriesModel,\n    labelDataIndex: dataIndex,\n    defaultText: getDefaultLabel(seriesModel.getData(), dataIndex),\n    inheritColor: style.fill,\n    defaultOpacity: style.opacity,\n    defaultOutsidePosition: labelPositionOutside\n  });\n  var label = el.getTextContent();\n  if (isPolar && label) {\n    var position = itemModel.get(['label', 'position']);\n    el.textConfig.inside = position === 'middle' ? true : null;\n    setSectorTextRotation(el, position === 'outside' ? labelPositionOutside : position, createPolarPositionMapping(isHorizontalOrRadial), itemModel.get(['label', 'rotate']));\n  }\n  setLabelValueAnimation(label, labelStatesModels, seriesModel.getRawValue(dataIndex), function (value) {\n    return getDefaultInterpolatedLabel(data, value);\n  });\n  var emphasisModel = itemModel.getModel(['emphasis']);\n  toggleHoverEmphasis(el, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n  setStatesStylesFromModel(el, itemModel);\n  if (isZeroOnPolar(layout)) {\n    el.style.fill = 'none';\n    el.style.stroke = 'none';\n    each(el.states, function (state) {\n      if (state.style) {\n        state.style.fill = state.style.stroke = 'none';\n      }\n    });\n  }\n}\n// In case width or height are too small.\nfunction getLineWidth(itemModel, rawLayout) {\n  // Has no border.\n  var borderColor = itemModel.get(['itemStyle', 'borderColor']);\n  if (!borderColor || borderColor === 'none') {\n    return 0;\n  }\n  var lineWidth = itemModel.get(['itemStyle', 'borderWidth']) || 0;\n  // width or height may be NaN for empty data\n  var width = isNaN(rawLayout.width) ? Number.MAX_VALUE : Math.abs(rawLayout.width);\n  var height = isNaN(rawLayout.height) ? Number.MAX_VALUE : Math.abs(rawLayout.height);\n  return Math.min(lineWidth, width, height);\n}\nvar LagePathShape = /** @class */function () {\n  function LagePathShape() {}\n  return LagePathShape;\n}();\nvar LargePath = /** @class */function (_super) {\n  __extends(LargePath, _super);\n  function LargePath(opts) {\n    var _this = _super.call(this, opts) || this;\n    _this.type = 'largeBar';\n    return _this;\n  }\n  LargePath.prototype.getDefaultShape = function () {\n    return new LagePathShape();\n  };\n  LargePath.prototype.buildPath = function (ctx, shape) {\n    // Drawing lines is more efficient than drawing\n    // a whole line or drawing rects.\n    var points = shape.points;\n    var baseDimIdx = this.baseDimIdx;\n    var valueDimIdx = 1 - this.baseDimIdx;\n    var startPoint = [];\n    var size = [];\n    var barWidth = this.barWidth;\n    for (var i = 0; i < points.length; i += 3) {\n      size[baseDimIdx] = barWidth;\n      size[valueDimIdx] = points[i + 2];\n      startPoint[baseDimIdx] = points[i + baseDimIdx];\n      startPoint[valueDimIdx] = points[i + valueDimIdx];\n      ctx.rect(startPoint[0], startPoint[1], size[0], size[1]);\n    }\n  };\n  return LargePath;\n}(Path);\nfunction createLarge(seriesModel, group, progressiveEls, incremental) {\n  // TODO support polar\n  var data = seriesModel.getData();\n  var baseDimIdx = data.getLayout('valueAxisHorizontal') ? 1 : 0;\n  var largeDataIndices = data.getLayout('largeDataIndices');\n  var barWidth = data.getLayout('size');\n  var backgroundModel = seriesModel.getModel('backgroundStyle');\n  var bgPoints = data.getLayout('largeBackgroundPoints');\n  if (bgPoints) {\n    var bgEl = new LargePath({\n      shape: {\n        points: bgPoints\n      },\n      incremental: !!incremental,\n      silent: true,\n      z2: 0\n    });\n    bgEl.baseDimIdx = baseDimIdx;\n    bgEl.largeDataIndices = largeDataIndices;\n    bgEl.barWidth = barWidth;\n    bgEl.useStyle(backgroundModel.getItemStyle());\n    group.add(bgEl);\n    progressiveEls && progressiveEls.push(bgEl);\n  }\n  var el = new LargePath({\n    shape: {\n      points: data.getLayout('largePoints')\n    },\n    incremental: !!incremental,\n    ignoreCoarsePointer: true,\n    z2: 1\n  });\n  el.baseDimIdx = baseDimIdx;\n  el.largeDataIndices = largeDataIndices;\n  el.barWidth = barWidth;\n  group.add(el);\n  el.useStyle(data.getVisual('style'));\n  // Stroke is rendered first to avoid overlapping with fill\n  el.style.stroke = null;\n  // Enable tooltip and user mouse/touch event handlers.\n  getECData(el).seriesIndex = seriesModel.seriesIndex;\n  if (!seriesModel.get('silent')) {\n    el.on('mousedown', largePathUpdateDataIndex);\n    el.on('mousemove', largePathUpdateDataIndex);\n  }\n  progressiveEls && progressiveEls.push(el);\n}\n// Use throttle to avoid frequently traverse to find dataIndex.\nvar largePathUpdateDataIndex = throttle(function (event) {\n  var largePath = this;\n  var dataIndex = largePathFindDataIndex(largePath, event.offsetX, event.offsetY);\n  getECData(largePath).dataIndex = dataIndex >= 0 ? dataIndex : null;\n}, 30, false);\nfunction largePathFindDataIndex(largePath, x, y) {\n  var baseDimIdx = largePath.baseDimIdx;\n  var valueDimIdx = 1 - baseDimIdx;\n  var points = largePath.shape.points;\n  var largeDataIndices = largePath.largeDataIndices;\n  var startPoint = [];\n  var size = [];\n  var barWidth = largePath.barWidth;\n  for (var i = 0, len = points.length / 3; i < len; i++) {\n    var ii = i * 3;\n    size[baseDimIdx] = barWidth;\n    size[valueDimIdx] = points[ii + 2];\n    startPoint[baseDimIdx] = points[ii + baseDimIdx];\n    startPoint[valueDimIdx] = points[ii + valueDimIdx];\n    if (size[valueDimIdx] < 0) {\n      startPoint[valueDimIdx] += size[valueDimIdx];\n      size[valueDimIdx] = -size[valueDimIdx];\n    }\n    if (x >= startPoint[0] && x <= startPoint[0] + size[0] && y >= startPoint[1] && y <= startPoint[1] + size[1]) {\n      return largeDataIndices[i];\n    }\n  }\n  return -1;\n}\nfunction createBackgroundShape(isHorizontalOrRadial, layout, coord) {\n  if (isCoordinateSystemType(coord, 'cartesian2d')) {\n    var rectShape = layout;\n    var coordLayout = coord.getArea();\n    return {\n      x: isHorizontalOrRadial ? rectShape.x : coordLayout.x,\n      y: isHorizontalOrRadial ? coordLayout.y : rectShape.y,\n      width: isHorizontalOrRadial ? rectShape.width : coordLayout.width,\n      height: isHorizontalOrRadial ? coordLayout.height : rectShape.height\n    };\n  } else {\n    var coordLayout = coord.getArea();\n    var sectorShape = layout;\n    return {\n      cx: coordLayout.cx,\n      cy: coordLayout.cy,\n      r0: isHorizontalOrRadial ? coordLayout.r0 : sectorShape.r0,\n      r: isHorizontalOrRadial ? coordLayout.r : sectorShape.r,\n      startAngle: isHorizontalOrRadial ? sectorShape.startAngle : 0,\n      endAngle: isHorizontalOrRadial ? sectorShape.endAngle : Math.PI * 2\n    };\n  }\n}\nfunction createBackgroundEl(coord, isHorizontalOrRadial, layout) {\n  var ElementClz = coord.type === 'polar' ? Sector : Rect;\n  return new ElementClz({\n    shape: createBackgroundShape(isHorizontalOrRadial, layout, coord),\n    silent: true,\n    z2: 0\n  });\n}\nexport default BarView;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;;;;;;;;;;;;;;;;;;;AACA,IAAI,UAAU,KAAK,GAAG;AACtB,IAAI,UAAU,KAAK,GAAG;AACtB,SAAS,YAAY,KAAK,EAAE,IAAI;IAC9B,IAAI,mBAAmB,MAAM,OAAO,IAAI,MAAM,OAAO;IACrD,IAAI,CAAA,GAAA,2JAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO,gBAAgB;QAChD,IAAI,WAAW,MAAM,WAAW;QAChC,yEAAyE;QACzE,gCAAgC;QAChC,qBAAqB;QACrB,IAAI,SAAS,IAAI,KAAK,cAAc,CAAC,SAAS,MAAM,EAAE;YACpD,IAAI,cAAc,KAAK,SAAS,CAAC;YACjC,IAAI,SAAS,YAAY,IAAI;gBAC3B,iBAAiB,CAAC,IAAI;gBACtB,iBAAiB,KAAK,IAAI,cAAc;YAC1C,OAAO;gBACL,iBAAiB,CAAC,IAAI;gBACtB,iBAAiB,MAAM,IAAI,cAAc;YAC3C;QACF;IACF;IACA,OAAO;AACT;AACA,IAAI,UAAU,WAAW,GAAE,SAAU,MAAM;IACzC,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE,SAAS;IACnB,SAAS;QACP,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,KAAK,IAAI;QACrC,MAAM,IAAI,GAAG,QAAQ,IAAI;QACzB,MAAM,aAAa,GAAG;QACtB,OAAO;IACT;IACA,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAU,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO;QACrE,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,yBAAyB,CAAC;QAC/B,IAAI,CAAC,eAAe,CAAC;QACrB,IAAI,uBAAuB,YAAY,GAAG,CAAC;QAC3C,IAAI,yBAAyB,iBAAiB,yBAAyB,SAAS;YAC9E,kDAAkD;YAClD,IAAI,CAAC,eAAe,GAAG;YACvB,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,SAAS,OAAO,IAAI,CAAC,aAAa,CAAC,aAAa,SAAS,KAAK;QACnH,OAAO,wCAA2C;YAChD,CAAA,GAAA,6IAAA,CAAA,OAAI,AAAD,EAAE;QACP;IACF;IACA,QAAQ,SAAS,CAAC,wBAAwB,GAAG,SAAU,WAAW;QAChE,IAAI,CAAC,MAAM;QACX,IAAI,CAAC,eAAe,CAAC;QACrB,6DAA6D;QAC7D,4FAA4F;QAC5F,IAAI,CAAC,gBAAgB,CAAC;IACxB;IACA,QAAQ,SAAS,CAAC,iBAAiB,GAAG,SAAU,MAAM,EAAE,WAAW;QACjE,QAAQ;QACR,IAAI,CAAC,eAAe,GAAG,EAAE;QACzB,6CAA6C;QAC7C,IAAI,CAAC,uBAAuB,CAAC,QAAQ;IACvC;IACA,QAAQ,SAAS,CAAC,YAAY,GAAG,SAAU,EAAE;QAC3C,CAAA,GAAA,iKAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,KAAK,EAAE;IACvD;IACA,QAAQ,SAAS,CAAC,eAAe,GAAG,SAAU,WAAW;QACvD,IAAI,cAAc,YAAY,eAAe,CAAC,KAAK;QACnD,IAAI,IAAI,CAAC,YAAY,IAAI,QAAQ,gBAAgB,IAAI,CAAC,YAAY,EAAE;YAClE,IAAI,CAAC,YAAY,GAAG;YACpB,IAAI,CAAC,MAAM;QACb;IACF;IACA,QAAQ,SAAS,CAAC,aAAa,GAAG,SAAU,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO;QAC5E,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,OAAO,YAAY,OAAO;QAC9B,IAAI,UAAU,IAAI,CAAC,KAAK;QACxB,IAAI,QAAQ,YAAY,gBAAgB;QACxC,IAAI,WAAW,MAAM,WAAW;QAChC,IAAI;QACJ,IAAI,MAAM,IAAI,KAAK,eAAe;YAChC,uBAAuB,SAAS,YAAY;QAC9C,OAAO,IAAI,MAAM,IAAI,KAAK,SAAS;YACjC,uBAAuB,SAAS,GAAG,KAAK;QAC1C;QACA,IAAI,iBAAiB,YAAY,kBAAkB,KAAK,cAAc;QACtE,IAAI,kBAAkB,mBAAmB,aAAa;QACtD,IAAI,iBAAiB;YACnB,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,MAAM;QAClD;QACA,IAAI,YAAY,YAAY,GAAG,CAAC,QAAQ,SAAS;QACjD,IAAI,mBAAmB,YAAY,OAAO;QAC1C,yDAAyD;QACzD,MAAM,cAAc;QACpB,4EAA4E;QAC5E,wCAAwC;QACxC,IAAI,WAAW,YAAY,GAAG,CAAC,YAAY;QAC3C,IAAI,iBAAiB,YAAY,GAAG,CAAC,kBAAkB;QACvD,IAAI,kBAAkB,YAAY,QAAQ,CAAC;QAC3C,IAAI,kBAAkB,gBAAgB,GAAG,CAAC,mBAAmB;QAC7D,IAAI,QAAQ,EAAE;QACd,IAAI,WAAW,IAAI,CAAC,cAAc;QAClC,IAAI,aAAa,WAAW,QAAQ,UAAU;QAC9C,IAAI,gBAAgB,WAAW,QAAQ,IAAI,KAAK;QAChD,SAAS,iBAAiB,SAAS;YACjC,IAAI,WAAW,SAAS,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM;YAC3C,IAAI,OAAO,mBAAmB,OAAO,sBAAsB;YAC3D,KAAK,QAAQ,CAAC,gBAAgB,YAAY;YAC1C,yCAAyC;YACzC,IAAI,MAAM,IAAI,KAAK,eAAe;gBAChC,KAAK,QAAQ,CAAC,KAAK;YACrB,OAAO;gBACL,KAAK,QAAQ,CAAC,gBAAgB;YAChC;YACA,KAAK,CAAC,UAAU,GAAG;YACnB,OAAO;QACT;;QAEA,KAAK,IAAI,CAAC,SAAS,GAAG,CAAC,SAAU,SAAS;YACxC,IAAI,YAAY,KAAK,YAAY,CAAC;YAClC,IAAI,SAAS,SAAS,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,WAAW;YACpD,IAAI,gBAAgB;gBAClB,iBAAiB;YACnB;YACA,qFAAqF;YACrF,IAAI,CAAC,KAAK,QAAQ,CAAC,cAAc,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,CAAC,SAAS;gBACnE;YACF;YACA,IAAI,YAAY;YAChB,IAAI,WAAW;gBACb,sCAAsC;gBACtC,oEAAoE;gBACpE,YAAY,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,kBAAkB;YACjD;YACA,IAAI,KAAK,cAAc,CAAC,MAAM,IAAI,CAAC,CAAC,aAAa,MAAM,WAAW,QAAQ,sBAAsB,gBAAgB,SAAS,KAAK,EAAE,OAAO;YACvI,IAAI,iBAAiB;gBACnB;;;;;;;SAOC,GACD,GAAG,mBAAmB,GAAG;YAC3B;YACA,YAAY,IAAI,MAAM,WAAW,WAAW,QAAQ,aAAa,sBAAsB,MAAM,IAAI,KAAK;YACtG,IAAI,YAAY;gBACd,GAAG,IAAI,CAAC;oBACN,OAAO;gBACT;YACF,OAAO,IAAI,iBAAiB;gBAC1B,wBAAwB,iBAAiB,gBAAgB,IAAI,QAAQ,WAAW,sBAAsB,OAAO;YAC/G,OAAO;gBACL,CAAA,GAAA,8JAAA,CAAA,YAAS,AAAD,EAAE,IAAI;oBACZ,OAAO;gBACT,GAAG,aAAa;YAClB;YACA,KAAK,gBAAgB,CAAC,WAAW;YACjC,MAAM,GAAG,CAAC;YACV,GAAG,MAAM,GAAG;QACd,GAAG,MAAM,CAAC,SAAU,QAAQ,EAAE,QAAQ;YACpC,IAAI,YAAY,KAAK,YAAY,CAAC;YAClC,IAAI,SAAS,SAAS,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM,UAAU;YACnD,IAAI,gBAAgB;gBAClB,IAAI,OAAO,KAAK;gBAChB,IAAI,SAAS,MAAM,KAAK,GAAG;oBACzB,OAAO,iBAAiB;gBAC1B,OAAO;oBACL,OAAO,QAAQ,CAAC,SAAS;oBACzB,KAAK,QAAQ,CAAC,gBAAgB,YAAY;oBAC1C,yCAAyC;oBACzC,IAAI,MAAM,IAAI,KAAK,eAAe;wBAChC,KAAK,QAAQ,CAAC,KAAK;oBACrB,OAAO;wBACL,KAAK,QAAQ,CAAC,gBAAgB;oBAChC;oBACA,KAAK,CAAC,SAAS,GAAG;gBACpB;gBACA,IAAI,WAAW,SAAS,CAAC,MAAM,IAAI,CAAC,CAAC,MAAM;gBAC3C,IAAI,QAAQ,sBAAsB,sBAAsB,UAAU;gBAClE,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,MAAM;oBAChB,OAAO;gBACT,GAAG,gBAAgB;YACrB;YACA,IAAI,KAAK,QAAQ,gBAAgB,CAAC;YAClC,IAAI,CAAC,KAAK,QAAQ,CAAC,aAAa,CAAC,aAAa,CAAC,MAAM,IAAI,CAAC,CAAC,SAAS;gBAClE,MAAM,MAAM,CAAC;gBACb;YACF;YACA,IAAI,YAAY;YAChB,IAAI,WAAW;gBACb,YAAY,IAAI,CAAC,MAAM,IAAI,CAAC,CAAC,kBAAkB;gBAC/C,IAAI,WAAW;oBACb,MAAM,MAAM,CAAC;gBACf;YACF;YACA,IAAI,CAAC,IAAI;gBACP,KAAK,cAAc,CAAC,MAAM,IAAI,CAAC,CAAC,aAAa,MAAM,UAAU,QAAQ,sBAAsB,gBAAgB,SAAS,KAAK,EAAE,CAAC,CAAC,IAAI;YACnI,OAAO;gBACL,CAAA,GAAA,8JAAA,CAAA,eAAY,AAAD,EAAE;YACf;YACA,IAAI,iBAAiB;gBACnB,GAAG,mBAAmB,GAAG;YAC3B;YACA,IAAI,eAAe;gBACjB,IAAI,SAAS,GAAG,cAAc;gBAC9B,IAAI,QAAQ;oBACV,IAAI,kBAAkB,CAAA,GAAA,qJAAA,CAAA,aAAU,AAAD,EAAE;oBACjC,IAAI,gBAAgB,SAAS,IAAI,MAAM;wBACrC;;;;;aAKC,GACD,gBAAgB,SAAS,GAAG,gBAAgB,KAAK;oBACnD;gBACF;YACF,OAGK;gBACH,YAAY,IAAI,MAAM,UAAU,WAAW,QAAQ,aAAa,sBAAsB,MAAM,IAAI,KAAK;YACvG;YACA,IAAI,YAAY;gBACd,GAAG,IAAI,CAAC;oBACN,OAAO;gBACT;YACF,OAAO,IAAI,iBAAiB;gBAC1B,wBAAwB,iBAAiB,gBAAgB,IAAI,QAAQ,UAAU,sBAAsB,MAAM;YAC7G,OAAO;gBACL,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE,IAAI;oBACd,OAAO;gBACT,GAAG,aAAa,UAAU;YAC5B;YACA,KAAK,gBAAgB,CAAC,UAAU;YAChC,GAAG,MAAM,GAAG;YACZ,MAAM,GAAG,CAAC;QACZ,GAAG,MAAM,CAAC,SAAU,SAAS;YAC3B,IAAI,KAAK,QAAQ,gBAAgB,CAAC;YAClC,MAAM,CAAA,GAAA,8JAAA,CAAA,2BAAwB,AAAD,EAAE,IAAI,aAAa;QAClD,GAAG,OAAO;QACV,IAAI,UAAU,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI,kJAAA,CAAA,UAAK,EAAE;QAC3E,QAAQ,SAAS;QACjB,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,EAAE,EAAG;YACrC,QAAQ,GAAG,CAAC,KAAK,CAAC,EAAE;QACtB;QACA,MAAM,GAAG,CAAC;QACV,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,KAAK,GAAG;IACf;IACA,QAAQ,SAAS,CAAC,YAAY,GAAG,SAAU,WAAW,EAAE,OAAO,EAAE,GAAG;QAClE,IAAI,CAAC,MAAM;QACX,YAAY,aAAa,IAAI,CAAC,KAAK;QACnC,IAAI,CAAC,gBAAgB,CAAC;IACxB;IACA,QAAQ,SAAS,CAAC,uBAAuB,GAAG,SAAU,MAAM,EAAE,WAAW;QACvE,IAAI,CAAC,iBAAiB;QACtB,YAAY,aAAa,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe,EAAE;IAC7D;IACA,QAAQ,SAAS,CAAC,gBAAgB,GAAG,SAAU,WAAW;QACxD,8BAA8B;QAC9B,IAAI,WAAW,YAAY,GAAG,CAAC,QAAQ,SAAS,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,gBAAgB,EAAE,OAAO;QACpG,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,UAAU;YACZ,MAAM,WAAW,CAAC;QACpB,OAAO;YACL,MAAM,cAAc;QACtB;IACF;IACA,QAAQ,SAAS,CAAC,mBAAmB,GAAG,SAAU,eAAe,EAAE,IAAI,EAAE,GAAG;QAC1E,IAAI,QAAQ,IAAI;QAChB,2DAA2D;QAC3D,IAAI,CAAC,KAAK,KAAK,IAAI;YACjB;QACF;QACA,IAAI,WAAW,gBAAgB,QAAQ;QACvC,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,IAAI,CAAC,iBAAiB,CAAC,MAAM,iBAAiB;YAC9C,IAAI,CAAC,aAAa,GAAG;QACvB,OAAO;YACL,IAAI,iBAAiB,SAAU,GAAG;gBAChC,IAAI,KAAK,KAAK,gBAAgB,CAAC;gBAC/B,IAAI,QAAQ,MAAM,GAAG,KAAK;gBAC1B,OAAO,SACP,uEAAuE;gBACvE,iEAAiE;gBACjE,KAAK,GAAG,CAAC,SAAS,YAAY,KAAK,MAAM,MAAM,GAAG,MAAM,KAAK,KAE1D;YACL;YACA,IAAI,CAAC,WAAW,GAAG;gBACjB,MAAM,yBAAyB,CAAC,MAAM,gBAAgB,UAAU;YAClE;YACA,IAAI,KAAK,GAAG,EAAE,CAAC,YAAY,IAAI,CAAC,WAAW;QAC7C;IACF;IACA,QAAQ,SAAS,CAAC,SAAS,GAAG,SAAU,IAAI,EAAE,QAAQ,EAAE,YAAY;QAClE,IAAI,OAAO,EAAE;QACb,KAAK,IAAI,CAAC,KAAK,YAAY,CAAC,SAAS,GAAG,GAAG,SAAU,aAAa,EAAE,OAAO;YACzE,IAAI,cAAc,aAAa;YAC/B,cAAc,eAAe,OAAO,MAAM;YAC1C,KAAK,IAAI,CAAC;gBACR,WAAW;gBACX,aAAa;gBACb,eAAe;YACjB;QACF;QACA,KAAK,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;YACtB,yCAAyC;YACzC,OAAO,EAAE,WAAW,GAAG,EAAE,WAAW;QACtC;QACA,OAAO;YACL,gBAAgB,CAAA,GAAA,8IAAA,CAAA,MAAG,AAAD,EAAE,MAAM,SAAU,IAAI;gBACtC,OAAO,KAAK,aAAa;YAC3B;QACF;IACF;IACA,QAAQ,SAAS,CAAC,6BAA6B,GAAG,SAAU,IAAI,EAAE,YAAY,EAAE,QAAQ;QACtF,IAAI,QAAQ,SAAS,KAAK;QAC1B,IAAI,iBAAiB,KAAK,YAAY,CAAC,SAAS,GAAG;QACnD,IAAI,YAAY,OAAO,SAAS;QAChC,IAAK,IAAI,UAAU,GAAG,MAAM,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,EAAE,UAAU,KAAK,EAAE,QAAS;YAC9F,IAAI,SAAS,KAAK,UAAU,CAAC,gBAAgB,MAAM,mBAAmB,CAAC;YACvE,IAAI,QAAQ,SAAS,IAEnB,OAAO,SAAS,GAEhB,aAAa,KAAK,eAAe,CAAC;YACpC,IAAI,QAAQ,WAAW;gBACrB,OAAO;YACT;YACA,YAAY;QACd;QACA,OAAO;IACT;IACA;;;;GAIC,GACD,QAAQ,SAAS,CAAC,uBAAuB,GAAG,SAAU,SAAS,EAAE,QAAQ;QACvE,IAAI,QAAQ,SAAS,KAAK;QAC1B,IAAI,SAAS,MAAM,SAAS;QAC5B,IAAI,UAAU,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC,EAAE;QACnC,IAAI,UAAU,KAAK,GAAG,CAAC,MAAM,CAAC,EAAE,EAAE,MAAM,cAAc,GAAG,UAAU,CAAC,MAAM,GAAG;QAC7E,MAAO,WAAW,SAAS,EAAE,QAAS;YACpC,IAAI,UAAU,cAAc,CAAC,QAAQ,KAAK,MAAM,mBAAmB,CAAC,UAAU;gBAC5E,OAAO;YACT;QACF;IACF;IACA,QAAQ,SAAS,CAAC,yBAAyB,GAAG,SAAU,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,GAAG;QACvF,IAAI,CAAC,IAAI,CAAC,6BAA6B,CAAC,MAAM,cAAc,WAAW;YACrE;QACF;QACA,IAAI,WAAW,IAAI,CAAC,SAAS,CAAC,MAAM,UAAU;QAC9C,IAAI,IAAI,CAAC,uBAAuB,CAAC,UAAU,WAAW;YACpD,IAAI,CAAC,yBAAyB,CAAC;YAC/B,IAAI,cAAc,CAAC;gBACjB,MAAM;gBACN,eAAe,SAAS,GAAG,GAAG;gBAC9B,QAAQ,SAAS,KAAK;gBACtB,UAAU;YACZ;QACF;IACF;IACA,QAAQ,SAAS,CAAC,iBAAiB,GAAG,SAAU,IAAI,EAAE,eAAe,EAAE,GAAG;QACxE,IAAI,WAAW,gBAAgB,QAAQ;QACvC,IAAI,aAAa,IAAI,CAAC,SAAS,CAAC,MAAM,UAAU,SAAU,OAAO;YAC/D,OAAO,KAAK,GAAG,CAAC,KAAK,YAAY,CAAC,gBAAgB,SAAS,CAAC,GAAG,GAAG;QACpE;QACA,IAAI,cAAc,CAAC;YACjB,MAAM;YACN,eAAe,SAAS,GAAG,GAAG;YAC9B,YAAY;YACZ,QAAQ,SAAS,KAAK;YACtB,UAAU;QACZ;IACF;IACA,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAU,OAAO,EAAE,GAAG;QAC/C,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,MAAM;QACvB,IAAI,CAAC,yBAAyB,CAAC;IACjC;IACA,QAAQ,SAAS,CAAC,OAAO,GAAG,SAAU,OAAO,EAAE,GAAG;QAChD,IAAI,CAAC,yBAAyB,CAAC;IACjC;IACA,QAAQ,SAAS,CAAC,yBAAyB,GAAG,SAAU,GAAG;QACzD,IAAI,IAAI,CAAC,WAAW,EAAE;YACpB,IAAI,KAAK,GAAG,GAAG,CAAC,YAAY,IAAI,CAAC,WAAW;YAC5C,IAAI,CAAC,WAAW,GAAG;QACrB;IACF;IACA,QAAQ,SAAS,CAAC,MAAM,GAAG,SAAU,KAAK;QACxC,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,OAAO,IAAI,CAAC,KAAK;QACrB,IAAI,SAAS,MAAM,kBAAkB,MAAM,QAAQ,CAAC,IAAI,CAAC,YAAY,EAAE;YACrE,IAAI,CAAC,iBAAiB;YACtB,IAAI,CAAC,cAAc,GAAG,EAAE;YACxB,KAAK,iBAAiB,CAAC,SAAU,EAAE;gBACjC,CAAA,GAAA,8JAAA,CAAA,2BAAwB,AAAD,EAAE,IAAI,OAAO,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,IAAI,SAAS;YAC7D;QACF,OAAO;YACL,MAAM,SAAS;QACjB;QACA,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,aAAa,GAAG;IACvB;IACA,QAAQ,SAAS,CAAC,iBAAiB,GAAG;QACpC,IAAI,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,gBAAgB;QACvC,IAAI,CAAC,gBAAgB,GAAG;IAC1B;IACA,QAAQ,IAAI,GAAG;IACf,OAAO;AACT,EAAE,+IAAA,CAAA,UAAS;AACX,IAAI,OAAO;IACT,aAAa,SAAU,oBAAoB,EAAE,MAAM;QACjD,IAAI,YAAY,OAAO,KAAK,GAAG,IAAI,CAAC,IAAI;QACxC,IAAI,aAAa,OAAO,MAAM,GAAG,IAAI,CAAC,IAAI;QAC1C,kCAAkC;QAClC,IAAI,YAAY,GAAG;YACjB,OAAO,CAAC,IAAI,OAAO,KAAK;YACxB,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK;QAC9B;QACA,IAAI,aAAa,GAAG;YAClB,OAAO,CAAC,IAAI,OAAO,MAAM;YACzB,OAAO,MAAM,GAAG,CAAC,OAAO,MAAM;QAChC;QACA,IAAI,aAAa,qBAAqB,CAAC,GAAG,qBAAqB,KAAK;QACpE,IAAI,aAAa,qBAAqB,CAAC,GAAG,qBAAqB,MAAM;QACrE,IAAI,IAAI,QAAQ,OAAO,CAAC,EAAE,qBAAqB,CAAC;QAChD,IAAI,KAAK,QAAQ,OAAO,CAAC,GAAG,OAAO,KAAK,EAAE;QAC1C,IAAI,IAAI,QAAQ,OAAO,CAAC,EAAE,qBAAqB,CAAC;QAChD,IAAI,KAAK,QAAQ,OAAO,CAAC,GAAG,OAAO,MAAM,EAAE;QAC3C,IAAI,WAAW,KAAK;QACpB,IAAI,WAAW,KAAK;QACpB,qEAAqE;QACrE,mFAAmF;QACnF,4EAA4E;QAC5E,4BAA4B;QAC5B,OAAO,CAAC,GAAG,YAAY,IAAI,aAAa,KAAK;QAC7C,OAAO,CAAC,GAAG,YAAY,IAAI,aAAa,KAAK;QAC7C,OAAO,KAAK,GAAG,WAAW,IAAI,KAAK;QACnC,OAAO,MAAM,GAAG,WAAW,IAAI,KAAK;QACpC,eAAe;QACf,IAAI,YAAY,GAAG;YACjB,OAAO,CAAC,IAAI,OAAO,KAAK;YACxB,OAAO,KAAK,GAAG,CAAC,OAAO,KAAK;QAC9B;QACA,IAAI,aAAa,GAAG;YAClB,OAAO,CAAC,IAAI,OAAO,MAAM;YACzB,OAAO,MAAM,GAAG,CAAC,OAAO,MAAM;QAChC;QACA,OAAO,YAAY;IACrB;IACA,OAAO,SAAU,gBAAgB,EAAE,MAAM;QACvC,IAAI,QAAQ,OAAO,EAAE,IAAI,OAAO,CAAC,GAAG,IAAI,CAAC;QACzC,gCAAgC;QAChC,IAAI,QAAQ,GAAG;YACb,IAAI,MAAM,OAAO,CAAC;YAClB,OAAO,CAAC,GAAG,OAAO,EAAE;YACpB,OAAO,EAAE,GAAG;QACd;QACA,IAAI,IAAI,QAAQ,OAAO,CAAC,EAAE,iBAAiB,CAAC;QAC5C,IAAI,KAAK,QAAQ,OAAO,EAAE,EAAE,iBAAiB,EAAE;QAC/C,OAAO,CAAC,GAAG;QACX,OAAO,EAAE,GAAG;QACZ,IAAI,UAAU,IAAI,KAAK;QACvB,eAAe;QACf,IAAI,QAAQ,GAAG;YACb,IAAI,MAAM,OAAO,CAAC;YAClB,OAAO,CAAC,GAAG,OAAO,EAAE;YACpB,OAAO,EAAE,GAAG;QACd;QACA,OAAO;IACT;AACF;AACA,IAAI,iBAAiB;IACnB,aAAa,SAAU,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ;QACrH,IAAI,OAAO,IAAI,6LAAA,CAAA,OAAI,CAAC;YAClB,OAAO,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG;YAClB,IAAI;QACN;QACA,KAAK,WAAW,GAAG;QACnB,KAAK,IAAI,GAAG;QACZ,IAAI,gBAAgB;YAClB,IAAI,YAAY,KAAK,KAAK;YAC1B,IAAI,kBAAkB,eAAe,WAAW;YAChD,SAAS,CAAC,gBAAgB,GAAG;QAC/B;QACA,OAAO;IACT;IACA,OAAO,SAAU,WAAW,EAAE,IAAI,EAAE,QAAQ,EAAE,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ;QAC3G,IAAI,aAAa,CAAC,YAAY,WAAW,0JAAA,CAAA,UAAO,GAAG,iMAAA,CAAA,SAAM;QACzD,IAAI,SAAS,IAAI,WAAW;YAC1B,OAAO;YACP,IAAI;QACN;QACA,OAAO,IAAI,GAAG;QACd,IAAI,cAAc,2BAA2B;QAC7C,OAAO,qBAAqB,GAAG,CAAA,GAAA,sJAAA,CAAA,oCAAiC,AAAD,EAAE,aAAa;YAC5E,YAAY,eAAe,0JAAA,CAAA,UAAO;QACpC;QACA,YAAY;QACZ,IAAI,gBAAgB;YAClB,IAAI,cAAc,OAAO,KAAK;YAC9B,IAAI,kBAAkB,WAAW,MAAM;YACvC,IAAI,gBAAgB,CAAC;YACrB,WAAW,CAAC,gBAAgB,GAAG,WAAW,OAAO,EAAE,GAAG,OAAO,UAAU;YACvE,aAAa,CAAC,gBAAgB,GAAG,MAAM,CAAC,gBAAgB;YACxD,CAAC,WAAW,8JAAA,CAAA,cAAW,GAAG,8JAAA,CAAA,YAAS,EAAE,QAAQ;gBAC3C,OAAO;YAET,GAAG;QACL;QACA,OAAO;IACT;AACF;AACA,SAAS,mBAAmB,WAAW,EAAE,QAAQ;IAC/C,IAAI,qBAAqB,YAAY,GAAG,CAAC,gBAAgB;IACzD,IAAI,WAAW,SAAS,WAAW;IACnC,wCAA2C;QACzC,IAAI,oBAAoB;YACtB,IAAI,SAAS,IAAI,KAAK,YAAY;gBAChC,CAAA,GAAA,6IAAA,CAAA,OAAI,AAAD,EAAE;YACP;YACA,IAAI,SAAS,IAAI,KAAK,eAAe;gBACnC,CAAA,GAAA,6IAAA,CAAA,OAAI,AAAD,EAAE;YACP;QACF;IACF;IACA,IAAI,sBAAsB,SAAS,IAAI,KAAK,cAAc,SAAS,IAAI,KAAK,eAAe;QACzF,OAAO;YACL,UAAU;YACV,WAAW,SAAS,YAAY,CAAC;QACnC;IACF;AACF;AACA,SAAS,wBAAwB,eAAe,EAAE,oBAAoB,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,aAAa;IACjI,IAAI;IACJ,IAAI;IACJ,IAAI,cAAc;QAChB,aAAa;YACX,GAAG,OAAO,CAAC;YACX,OAAO,OAAO,KAAK;QACrB;QACA,eAAe;YACb,GAAG,OAAO,CAAC;YACX,QAAQ,OAAO,MAAM;QACvB;IACF,OAAO;QACL,aAAa;YACX,GAAG,OAAO,CAAC;YACX,QAAQ,OAAO,MAAM;QACvB;QACA,eAAe;YACb,GAAG,OAAO,CAAC;YACX,OAAO,OAAO,KAAK;QACrB;IACF;IACA,IAAI,CAAC,eAAe;QAClB,iEAAiE;QACjE,6BAA6B;QAC7B,CAAC,WAAW,8JAAA,CAAA,cAAW,GAAG,8JAAA,CAAA,YAAS,EAAE,IAAI;YACvC,OAAO;QACT,GAAG,sBAAsB,UAAU;IACrC;IACA,IAAI,qBAAqB,uBAAuB,gBAAgB,QAAQ,CAAC,KAAK,GAAG;IACjF,CAAC,WAAW,8JAAA,CAAA,cAAW,GAAG,8JAAA,CAAA,YAAS,EAAE,IAAI;QACvC,OAAO;IACT,GAAG,oBAAoB;AACzB;AACA,SAAS,wBAAwB,GAAG,EAAE,KAAK;IACzC,IAAK,IAAI,IAAI,GAAG,IAAI,MAAM,MAAM,EAAE,IAAK;QACrC,IAAI,CAAC,SAAS,GAAG,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG;YAC5B,OAAO;QACT;IACF;IACA,OAAO;AACT;AACA,IAAI,eAAe;IAAC;IAAK;IAAK;IAAS;CAAS;AAChD,IAAI,gBAAgB;IAAC;IAAM;IAAM;IAAK;IAAc;CAAW;AAC/D,IAAI,gBAAgB;IAClB,aAAa,SAAU,MAAM;QAC3B,OAAO,CAAC,wBAAwB,QAAQ;IAC1C;IACA,OAAO,SAAU,MAAM;QACrB,OAAO,CAAC,wBAAwB,QAAQ;IAC1C;AACF;AACA,IAAI,YAAY;IACd,iEAAiE;IACjE,0CAA0C;IAC1C,aAAa,SAAU,IAAI,EAAE,SAAS,EAAE,SAAS;QAC/C,IAAI,SAAS,KAAK,aAAa,CAAC;QAChC,IAAI,iBAAiB,YAAY,aAAa,WAAW,UAAU;QACnE,4BAA4B;QAC5B,IAAI,QAAQ,OAAO,KAAK,GAAG,IAAI,IAAI,CAAC;QACpC,IAAI,QAAQ,OAAO,MAAM,GAAG,IAAI,IAAI,CAAC;QACrC,OAAO;YACL,GAAG,OAAO,CAAC,GAAG,QAAQ,iBAAiB;YACvC,GAAG,OAAO,CAAC,GAAG,QAAQ,iBAAiB;YACvC,OAAO,OAAO,KAAK,GAAG,QAAQ;YAC9B,QAAQ,OAAO,MAAM,GAAG,QAAQ;QAClC;IACF;IACA,OAAO,SAAU,IAAI,EAAE,SAAS,EAAE,SAAS;QACzC,IAAI,SAAS,KAAK,aAAa,CAAC;QAChC,OAAO;YACL,IAAI,OAAO,EAAE;YACb,IAAI,OAAO,EAAE;YACb,IAAI,OAAO,EAAE;YACb,GAAG,OAAO,CAAC;YACX,YAAY,OAAO,UAAU;YAC7B,UAAU,OAAO,QAAQ;YACzB,WAAW,OAAO,SAAS;QAC7B;IACF;AACF;AACA,SAAS,cAAc,MAAM;IAC3B,OAAO,OAAO,UAAU,IAAI,QAAQ,OAAO,QAAQ,IAAI,QAAQ,OAAO,UAAU,KAAK,OAAO,QAAQ;AACtG;AACA,SAAS,2BAA2B,QAAQ;IAC1C,OAAO,SAAU,QAAQ;QACvB,IAAI,aAAa,WAAW,QAAQ;QACpC,OAAO,SAAU,QAAQ;YACvB,OAAQ;gBACN,KAAK;gBACL,KAAK;gBACL,KAAK;gBACL,KAAK;oBACH,OAAO,WAAW;gBACpB;oBACE,OAAO;YACX;QACF;IACF,EAAE;AACJ;AACA,SAAS,YAAY,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,MAAM,EAAE,WAAW,EAAE,oBAAoB,EAAE,OAAO;IACrG,IAAI,QAAQ,KAAK,aAAa,CAAC,WAAW;IAC1C,IAAI,CAAC,SAAS;QACZ,IAAI,eAAe,UAAU,GAAG,CAAC;YAAC;YAAa;SAAe,KAAK;QACnE,GAAG,QAAQ,CAAC,KAAK;IACnB,OAAO,IAAI,CAAC,YAAY,GAAG,CAAC,aAAa;QACvC,IAAI,cAAc,GAAG,KAAK;QAC1B,IAAI,eAAe,CAAA,GAAA,iKAAA,CAAA,wBAAqB,AAAD,EAAE,UAAU,QAAQ,CAAC,cAAc,aAAa;QACvF,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,aAAa;QACpB,GAAG,QAAQ,CAAC;IACd;IACA,GAAG,QAAQ,CAAC;IACZ,IAAI,cAAc,UAAU,UAAU,CAAC;IACvC,eAAe,GAAG,IAAI,CAAC,UAAU;IACjC,IAAI,uBAAuB,UAAU,uBAAuB,OAAO,CAAC,IAAI,OAAO,EAAE,GAAG,WAAW,aAAa,OAAO,QAAQ,IAAI,OAAO,UAAU,GAAG,aAAa,eAAe,uBAAuB,OAAO,MAAM,IAAI,IAAI,WAAW,QAAQ,OAAO,KAAK,IAAI,IAAI,UAAU;IAC5Q,IAAI,oBAAoB,CAAA,GAAA,qJAAA,CAAA,uBAAoB,AAAD,EAAE;IAC7C,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE,IAAI,mBAAmB;QACnC,cAAc;QACd,gBAAgB;QAChB,aAAa,CAAA,GAAA,gKAAA,CAAA,kBAAe,AAAD,EAAE,YAAY,OAAO,IAAI;QACpD,cAAc,MAAM,IAAI;QACxB,gBAAgB,MAAM,OAAO;QAC7B,wBAAwB;IAC1B;IACA,IAAI,QAAQ,GAAG,cAAc;IAC7B,IAAI,WAAW,OAAO;QACpB,IAAI,WAAW,UAAU,GAAG,CAAC;YAAC;YAAS;SAAW;QAClD,GAAG,UAAU,CAAC,MAAM,GAAG,aAAa,WAAW,OAAO;QACtD,CAAA,GAAA,sJAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,aAAa,YAAY,uBAAuB,UAAU,2BAA2B,uBAAuB,UAAU,GAAG,CAAC;YAAC;YAAS;SAAS;IACzK;IACA,CAAA,GAAA,qJAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO,mBAAmB,YAAY,WAAW,CAAC,YAAY,SAAU,KAAK;QAClG,OAAO,CAAA,GAAA,gKAAA,CAAA,8BAA2B,AAAD,EAAE,MAAM;IAC3C;IACA,IAAI,gBAAgB,UAAU,QAAQ,CAAC;QAAC;KAAW;IACnD,CAAA,GAAA,gJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,cAAc,GAAG,CAAC,UAAU,cAAc,GAAG,CAAC,cAAc,cAAc,GAAG,CAAC;IACtG,CAAA,GAAA,gJAAA,CAAA,2BAAwB,AAAD,EAAE,IAAI;IAC7B,IAAI,cAAc,SAAS;QACzB,GAAG,KAAK,CAAC,IAAI,GAAG;QAChB,GAAG,KAAK,CAAC,MAAM,GAAG;QAClB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,GAAG,MAAM,EAAE,SAAU,KAAK;YAC7B,IAAI,MAAM,KAAK,EAAE;gBACf,MAAM,KAAK,CAAC,IAAI,GAAG,MAAM,KAAK,CAAC,MAAM,GAAG;YAC1C;QACF;IACF;AACF;AACA,yCAAyC;AACzC,SAAS,aAAa,SAAS,EAAE,SAAS;IACxC,iBAAiB;IACjB,IAAI,cAAc,UAAU,GAAG,CAAC;QAAC;QAAa;KAAc;IAC5D,IAAI,CAAC,eAAe,gBAAgB,QAAQ;QAC1C,OAAO;IACT;IACA,IAAI,YAAY,UAAU,GAAG,CAAC;QAAC;QAAa;KAAc,KAAK;IAC/D,4CAA4C;IAC5C,IAAI,QAAQ,MAAM,UAAU,KAAK,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,CAAC,UAAU,KAAK;IAChF,IAAI,SAAS,MAAM,UAAU,MAAM,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,CAAC,UAAU,MAAM;IACnF,OAAO,KAAK,GAAG,CAAC,WAAW,OAAO;AACpC;AACA,IAAI,gBAAgB,WAAW,GAAE;IAC/B,SAAS,iBAAiB;IAC1B,OAAO;AACT;AACA,IAAI,YAAY,WAAW,GAAE,SAAU,MAAM;IAC3C,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE,WAAW;IACrB,SAAS,UAAU,IAAI;QACrB,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,SAAS,IAAI;QAC3C,MAAM,IAAI,GAAG;QACb,OAAO;IACT;IACA,UAAU,SAAS,CAAC,eAAe,GAAG;QACpC,OAAO,IAAI;IACb;IACA,UAAU,SAAS,CAAC,SAAS,GAAG,SAAU,GAAG,EAAE,KAAK;QAClD,+CAA+C;QAC/C,iCAAiC;QACjC,IAAI,SAAS,MAAM,MAAM;QACzB,IAAI,aAAa,IAAI,CAAC,UAAU;QAChC,IAAI,cAAc,IAAI,IAAI,CAAC,UAAU;QACrC,IAAI,aAAa,EAAE;QACnB,IAAI,OAAO,EAAE;QACb,IAAI,WAAW,IAAI,CAAC,QAAQ;QAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,MAAM,EAAE,KAAK,EAAG;YACzC,IAAI,CAAC,WAAW,GAAG;YACnB,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,IAAI,EAAE;YACjC,UAAU,CAAC,WAAW,GAAG,MAAM,CAAC,IAAI,WAAW;YAC/C,UAAU,CAAC,YAAY,GAAG,MAAM,CAAC,IAAI,YAAY;YACjD,IAAI,IAAI,CAAC,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE,EAAE,IAAI,CAAC,EAAE;QACzD;IACF;IACA,OAAO;AACT,EAAE,iJAAA,CAAA,UAAI;AACN,SAAS,YAAY,WAAW,EAAE,KAAK,EAAE,cAAc,EAAE,WAAW;IAClE,qBAAqB;IACrB,IAAI,OAAO,YAAY,OAAO;IAC9B,IAAI,aAAa,KAAK,SAAS,CAAC,yBAAyB,IAAI;IAC7D,IAAI,mBAAmB,KAAK,SAAS,CAAC;IACtC,IAAI,WAAW,KAAK,SAAS,CAAC;IAC9B,IAAI,kBAAkB,YAAY,QAAQ,CAAC;IAC3C,IAAI,WAAW,KAAK,SAAS,CAAC;IAC9B,IAAI,UAAU;QACZ,IAAI,OAAO,IAAI,UAAU;YACvB,OAAO;gBACL,QAAQ;YACV;YACA,aAAa,CAAC,CAAC;YACf,QAAQ;YACR,IAAI;QACN;QACA,KAAK,UAAU,GAAG;QAClB,KAAK,gBAAgB,GAAG;QACxB,KAAK,QAAQ,GAAG;QAChB,KAAK,QAAQ,CAAC,gBAAgB,YAAY;QAC1C,MAAM,GAAG,CAAC;QACV,kBAAkB,eAAe,IAAI,CAAC;IACxC;IACA,IAAI,KAAK,IAAI,UAAU;QACrB,OAAO;YACL,QAAQ,KAAK,SAAS,CAAC;QACzB;QACA,aAAa,CAAC,CAAC;QACf,qBAAqB;QACrB,IAAI;IACN;IACA,GAAG,UAAU,GAAG;IAChB,GAAG,gBAAgB,GAAG;IACtB,GAAG,QAAQ,GAAG;IACd,MAAM,GAAG,CAAC;IACV,GAAG,QAAQ,CAAC,KAAK,SAAS,CAAC;IAC3B,0DAA0D;IAC1D,GAAG,KAAK,CAAC,MAAM,GAAG;IAClB,sDAAsD;IACtD,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,IAAI,WAAW,GAAG,YAAY,WAAW;IACnD,IAAI,CAAC,YAAY,GAAG,CAAC,WAAW;QAC9B,GAAG,EAAE,CAAC,aAAa;QACnB,GAAG,EAAE,CAAC,aAAa;IACrB;IACA,kBAAkB,eAAe,IAAI,CAAC;AACxC;AACA,+DAA+D;AAC/D,IAAI,2BAA2B,CAAA,GAAA,kJAAA,CAAA,WAAQ,AAAD,EAAE,SAAU,KAAK;IACrD,IAAI,YAAY,IAAI;IACpB,IAAI,YAAY,uBAAuB,WAAW,MAAM,OAAO,EAAE,MAAM,OAAO;IAC9E,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,WAAW,SAAS,GAAG,aAAa,IAAI,YAAY;AAChE,GAAG,IAAI;AACP,SAAS,uBAAuB,SAAS,EAAE,CAAC,EAAE,CAAC;IAC7C,IAAI,aAAa,UAAU,UAAU;IACrC,IAAI,cAAc,IAAI;IACtB,IAAI,SAAS,UAAU,KAAK,CAAC,MAAM;IACnC,IAAI,mBAAmB,UAAU,gBAAgB;IACjD,IAAI,aAAa,EAAE;IACnB,IAAI,OAAO,EAAE;IACb,IAAI,WAAW,UAAU,QAAQ;IACjC,IAAK,IAAI,IAAI,GAAG,MAAM,OAAO,MAAM,GAAG,GAAG,IAAI,KAAK,IAAK;QACrD,IAAI,KAAK,IAAI;QACb,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,GAAG,MAAM,CAAC,KAAK,EAAE;QAClC,UAAU,CAAC,WAAW,GAAG,MAAM,CAAC,KAAK,WAAW;QAChD,UAAU,CAAC,YAAY,GAAG,MAAM,CAAC,KAAK,YAAY;QAClD,IAAI,IAAI,CAAC,YAAY,GAAG,GAAG;YACzB,UAAU,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY;YAC5C,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,CAAC,YAAY;QACxC;QACA,IAAI,KAAK,UAAU,CAAC,EAAE,IAAI,KAAK,UAAU,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,IAAI,KAAK,UAAU,CAAC,EAAE,IAAI,KAAK,UAAU,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE,EAAE;YAC5G,OAAO,gBAAgB,CAAC,EAAE;QAC5B;IACF;IACA,OAAO,CAAC;AACV;AACA,SAAS,sBAAsB,oBAAoB,EAAE,MAAM,EAAE,KAAK;IAChE,IAAI,CAAA,GAAA,2JAAA,CAAA,yBAAsB,AAAD,EAAE,OAAO,gBAAgB;QAChD,IAAI,YAAY;QAChB,IAAI,cAAc,MAAM,OAAO;QAC/B,OAAO;YACL,GAAG,uBAAuB,UAAU,CAAC,GAAG,YAAY,CAAC;YACrD,GAAG,uBAAuB,YAAY,CAAC,GAAG,UAAU,CAAC;YACrD,OAAO,uBAAuB,UAAU,KAAK,GAAG,YAAY,KAAK;YACjE,QAAQ,uBAAuB,YAAY,MAAM,GAAG,UAAU,MAAM;QACtE;IACF,OAAO;QACL,IAAI,cAAc,MAAM,OAAO;QAC/B,IAAI,cAAc;QAClB,OAAO;YACL,IAAI,YAAY,EAAE;YAClB,IAAI,YAAY,EAAE;YAClB,IAAI,uBAAuB,YAAY,EAAE,GAAG,YAAY,EAAE;YAC1D,GAAG,uBAAuB,YAAY,CAAC,GAAG,YAAY,CAAC;YACvD,YAAY,uBAAuB,YAAY,UAAU,GAAG;YAC5D,UAAU,uBAAuB,YAAY,QAAQ,GAAG,KAAK,EAAE,GAAG;QACpE;IACF;AACF;AACA,SAAS,mBAAmB,KAAK,EAAE,oBAAoB,EAAE,MAAM;IAC7D,IAAI,aAAa,MAAM,IAAI,KAAK,UAAU,iMAAA,CAAA,SAAM,GAAG,6LAAA,CAAA,OAAI;IACvD,OAAO,IAAI,WAAW;QACpB,OAAO,sBAAsB,sBAAsB,QAAQ;QAC3D,QAAQ;QACR,IAAI;IACN;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/chart/bar/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { layout, createProgressiveLayout } from '../../layout/barGrid.js';\nimport dataSample from '../../processor/dataSample.js';\nimport BarSeries from './BarSeries.js';\nimport BarView from './BarView.js';\nexport function install(registers) {\n  registers.registerChartView(BarView);\n  registers.registerSeriesModel(BarSeries);\n  registers.registerLayout(registers.PRIORITY.VISUAL.LAYOUT, zrUtil.curry(layout, 'bar'));\n  // Do layout after other overall layout, which can prepare some information.\n  registers.registerLayout(registers.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT, createProgressiveLayout('bar'));\n  // Down sample after filter\n  registers.registerProcessor(registers.PRIORITY.PROCESSOR.STATISTIC, dataSample('bar'));\n  /**\r\n   * @payload\r\n   * @property {string} [componentType=series]\r\n   * @property {number} [dx]\r\n   * @property {number} [dy]\r\n   * @property {number} [zoom]\r\n   * @property {number} [originX]\r\n   * @property {number} [originY]\r\n   */\n  registers.registerAction({\n    type: 'changeAxisOrder',\n    event: 'changeAxisOrder',\n    update: 'update'\n  }, function (payload, ecModel) {\n    var componentType = payload.componentType || 'series';\n    ecModel.eachComponent({\n      mainType: componentType,\n      query: payload\n    }, function (componentModel) {\n      if (payload.sortInfo) {\n        componentModel.axis.setCategorySortInfo(payload.sortInfo);\n      }\n    });\n  });\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;;;;;;AACO,SAAS,QAAQ,SAAS;IAC/B,UAAU,iBAAiB,CAAC,yJAAA,CAAA,UAAO;IACnC,UAAU,mBAAmB,CAAC,2JAAA,CAAA,UAAS;IACvC,UAAU,cAAc,CAAC,UAAU,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,GAAA,8IAAA,CAAA,QAAY,AAAD,EAAE,mJAAA,CAAA,SAAM,EAAE;IAChF,4EAA4E;IAC5E,UAAU,cAAc,CAAC,UAAU,QAAQ,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAA,GAAA,mJAAA,CAAA,0BAAuB,AAAD,EAAE;IAC/F,2BAA2B;IAC3B,UAAU,iBAAiB,CAAC,UAAU,QAAQ,CAAC,SAAS,CAAC,SAAS,EAAE,CAAA,GAAA,yJAAA,CAAA,UAAU,AAAD,EAAE;IAC/E;;;;;;;;GAQC,GACD,UAAU,cAAc,CAAC;QACvB,MAAM;QACN,OAAO;QACP,QAAQ;IACV,GAAG,SAAU,OAAO,EAAE,OAAO;QAC3B,IAAI,gBAAgB,QAAQ,aAAa,IAAI;QAC7C,QAAQ,aAAa,CAAC;YACpB,UAAU;YACV,OAAO;QACT,GAAG,SAAU,cAAc;YACzB,IAAI,QAAQ,QAAQ,EAAE;gBACpB,eAAe,IAAI,CAAC,mBAAmB,CAAC,QAAQ,QAAQ;YAC1D;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1321, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/chart/bar/PictorialBarView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { toggleHoverEmphasis } from '../../util/states.js';\nimport { createSymbol, normalizeSymbolOffset } from '../../util/symbol.js';\nimport { parsePercent, isNumeric } from '../../util/number.js';\nimport ChartView from '../../view/Chart.js';\nimport { getDefaultLabel } from '../helper/labelHelper.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nimport ZRImage from 'zrender/lib/graphic/Image.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createClipPath } from '../helper/createClipPathFromCoordSys.js';\nvar BAR_BORDER_WIDTH_QUERY = ['itemStyle', 'borderWidth'];\n// index: +isHorizontal\nvar LAYOUT_ATTRS = [{\n  xy: 'x',\n  wh: 'width',\n  index: 0,\n  posDesc: ['left', 'right']\n}, {\n  xy: 'y',\n  wh: 'height',\n  index: 1,\n  posDesc: ['top', 'bottom']\n}];\nvar pathForLineWidth = new graphic.Circle();\nvar PictorialBarView = /** @class */function (_super) {\n  __extends(PictorialBarView, _super);\n  function PictorialBarView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = PictorialBarView.type;\n    return _this;\n  }\n  PictorialBarView.prototype.render = function (seriesModel, ecModel, api) {\n    var group = this.group;\n    var data = seriesModel.getData();\n    var oldData = this._data;\n    var cartesian = seriesModel.coordinateSystem;\n    var baseAxis = cartesian.getBaseAxis();\n    var isHorizontal = baseAxis.isHorizontal();\n    var coordSysRect = cartesian.master.getRect();\n    var opt = {\n      ecSize: {\n        width: api.getWidth(),\n        height: api.getHeight()\n      },\n      seriesModel: seriesModel,\n      coordSys: cartesian,\n      coordSysExtent: [[coordSysRect.x, coordSysRect.x + coordSysRect.width], [coordSysRect.y, coordSysRect.y + coordSysRect.height]],\n      isHorizontal: isHorizontal,\n      valueDim: LAYOUT_ATTRS[+isHorizontal],\n      categoryDim: LAYOUT_ATTRS[1 - +isHorizontal]\n    };\n    data.diff(oldData).add(function (dataIndex) {\n      if (!data.hasValue(dataIndex)) {\n        return;\n      }\n      var itemModel = getItemModel(data, dataIndex);\n      var symbolMeta = getSymbolMeta(data, dataIndex, itemModel, opt);\n      var bar = createBar(data, opt, symbolMeta);\n      data.setItemGraphicEl(dataIndex, bar);\n      group.add(bar);\n      updateCommon(bar, opt, symbolMeta);\n    }).update(function (newIndex, oldIndex) {\n      var bar = oldData.getItemGraphicEl(oldIndex);\n      if (!data.hasValue(newIndex)) {\n        group.remove(bar);\n        return;\n      }\n      var itemModel = getItemModel(data, newIndex);\n      var symbolMeta = getSymbolMeta(data, newIndex, itemModel, opt);\n      var pictorialShapeStr = getShapeStr(data, symbolMeta);\n      if (bar && pictorialShapeStr !== bar.__pictorialShapeStr) {\n        group.remove(bar);\n        data.setItemGraphicEl(newIndex, null);\n        bar = null;\n      }\n      if (bar) {\n        updateBar(bar, opt, symbolMeta);\n      } else {\n        bar = createBar(data, opt, symbolMeta, true);\n      }\n      data.setItemGraphicEl(newIndex, bar);\n      bar.__pictorialSymbolMeta = symbolMeta;\n      // Add back\n      group.add(bar);\n      updateCommon(bar, opt, symbolMeta);\n    }).remove(function (dataIndex) {\n      var bar = oldData.getItemGraphicEl(dataIndex);\n      bar && removeBar(oldData, dataIndex, bar.__pictorialSymbolMeta.animationModel, bar);\n    }).execute();\n    // Do clipping\n    var clipPath = seriesModel.get('clip', true) ? createClipPath(seriesModel.coordinateSystem, false, seriesModel) : null;\n    if (clipPath) {\n      group.setClipPath(clipPath);\n    } else {\n      group.removeClipPath();\n    }\n    this._data = data;\n    return this.group;\n  };\n  PictorialBarView.prototype.remove = function (ecModel, api) {\n    var group = this.group;\n    var data = this._data;\n    if (ecModel.get('animation')) {\n      if (data) {\n        data.eachItemGraphicEl(function (bar) {\n          removeBar(data, getECData(bar).dataIndex, ecModel, bar);\n        });\n      }\n    } else {\n      group.removeAll();\n    }\n  };\n  PictorialBarView.type = 'pictorialBar';\n  return PictorialBarView;\n}(ChartView);\n// Set or calculate default value about symbol, and calculate layout info.\nfunction getSymbolMeta(data, dataIndex, itemModel, opt) {\n  var layout = data.getItemLayout(dataIndex);\n  var symbolRepeat = itemModel.get('symbolRepeat');\n  var symbolClip = itemModel.get('symbolClip');\n  var symbolPosition = itemModel.get('symbolPosition') || 'start';\n  var symbolRotate = itemModel.get('symbolRotate');\n  var rotation = (symbolRotate || 0) * Math.PI / 180 || 0;\n  var symbolPatternSize = itemModel.get('symbolPatternSize') || 2;\n  var isAnimationEnabled = itemModel.isAnimationEnabled();\n  var symbolMeta = {\n    dataIndex: dataIndex,\n    layout: layout,\n    itemModel: itemModel,\n    symbolType: data.getItemVisual(dataIndex, 'symbol') || 'circle',\n    style: data.getItemVisual(dataIndex, 'style'),\n    symbolClip: symbolClip,\n    symbolRepeat: symbolRepeat,\n    symbolRepeatDirection: itemModel.get('symbolRepeatDirection'),\n    symbolPatternSize: symbolPatternSize,\n    rotation: rotation,\n    animationModel: isAnimationEnabled ? itemModel : null,\n    hoverScale: isAnimationEnabled && itemModel.get(['emphasis', 'scale']),\n    z2: itemModel.getShallow('z', true) || 0\n  };\n  prepareBarLength(itemModel, symbolRepeat, layout, opt, symbolMeta);\n  prepareSymbolSize(data, dataIndex, layout, symbolRepeat, symbolClip, symbolMeta.boundingLength, symbolMeta.pxSign, symbolPatternSize, opt, symbolMeta);\n  prepareLineWidth(itemModel, symbolMeta.symbolScale, rotation, opt, symbolMeta);\n  var symbolSize = symbolMeta.symbolSize;\n  var symbolOffset = normalizeSymbolOffset(itemModel.get('symbolOffset'), symbolSize);\n  prepareLayoutInfo(itemModel, symbolSize, layout, symbolRepeat, symbolClip, symbolOffset, symbolPosition, symbolMeta.valueLineWidth, symbolMeta.boundingLength, symbolMeta.repeatCutLength, opt, symbolMeta);\n  return symbolMeta;\n}\n// bar length can be negative.\nfunction prepareBarLength(itemModel, symbolRepeat, layout, opt, outputSymbolMeta) {\n  var valueDim = opt.valueDim;\n  var symbolBoundingData = itemModel.get('symbolBoundingData');\n  var valueAxis = opt.coordSys.getOtherAxis(opt.coordSys.getBaseAxis());\n  var zeroPx = valueAxis.toGlobalCoord(valueAxis.dataToCoord(0));\n  var pxSignIdx = 1 - +(layout[valueDim.wh] <= 0);\n  var boundingLength;\n  if (zrUtil.isArray(symbolBoundingData)) {\n    var symbolBoundingExtent = [convertToCoordOnAxis(valueAxis, symbolBoundingData[0]) - zeroPx, convertToCoordOnAxis(valueAxis, symbolBoundingData[1]) - zeroPx];\n    symbolBoundingExtent[1] < symbolBoundingExtent[0] && symbolBoundingExtent.reverse();\n    boundingLength = symbolBoundingExtent[pxSignIdx];\n  } else if (symbolBoundingData != null) {\n    boundingLength = convertToCoordOnAxis(valueAxis, symbolBoundingData) - zeroPx;\n  } else if (symbolRepeat) {\n    boundingLength = opt.coordSysExtent[valueDim.index][pxSignIdx] - zeroPx;\n  } else {\n    boundingLength = layout[valueDim.wh];\n  }\n  outputSymbolMeta.boundingLength = boundingLength;\n  if (symbolRepeat) {\n    outputSymbolMeta.repeatCutLength = layout[valueDim.wh];\n  }\n  // if 'pxSign' means sign of pixel,  it can't be zero, or symbolScale will be zero\n  // and when borderWidth be settled, the actual linewidth will be NaN\n  var isXAxis = valueDim.xy === 'x';\n  var isInverse = valueAxis.inverse;\n  outputSymbolMeta.pxSign = isXAxis && !isInverse || !isXAxis && isInverse ? boundingLength >= 0 ? 1 : -1 : boundingLength > 0 ? 1 : -1;\n}\nfunction convertToCoordOnAxis(axis, value) {\n  return axis.toGlobalCoord(axis.dataToCoord(axis.scale.parse(value)));\n}\n// Support ['100%', '100%']\nfunction prepareSymbolSize(data, dataIndex, layout, symbolRepeat, symbolClip, boundingLength, pxSign, symbolPatternSize, opt, outputSymbolMeta) {\n  var valueDim = opt.valueDim;\n  var categoryDim = opt.categoryDim;\n  var categorySize = Math.abs(layout[categoryDim.wh]);\n  var symbolSize = data.getItemVisual(dataIndex, 'symbolSize');\n  var parsedSymbolSize;\n  if (zrUtil.isArray(symbolSize)) {\n    parsedSymbolSize = symbolSize.slice();\n  } else {\n    if (symbolSize == null) {\n      // will parse to number below\n      parsedSymbolSize = ['100%', '100%'];\n    } else {\n      parsedSymbolSize = [symbolSize, symbolSize];\n    }\n  }\n  // Note: percentage symbolSize (like '100%') do not consider lineWidth, because it is\n  // to complicated to calculate real percent value if considering scaled lineWidth.\n  // So the actual size will bigger than layout size if lineWidth is bigger than zero,\n  // which can be tolerated in pictorial chart.\n  parsedSymbolSize[categoryDim.index] = parsePercent(parsedSymbolSize[categoryDim.index], categorySize);\n  parsedSymbolSize[valueDim.index] = parsePercent(parsedSymbolSize[valueDim.index], symbolRepeat ? categorySize : Math.abs(boundingLength));\n  outputSymbolMeta.symbolSize = parsedSymbolSize;\n  // If x or y is less than zero, show reversed shape.\n  var symbolScale = outputSymbolMeta.symbolScale = [parsedSymbolSize[0] / symbolPatternSize, parsedSymbolSize[1] / symbolPatternSize];\n  // Follow convention, 'right' and 'top' is the normal scale.\n  symbolScale[valueDim.index] *= (opt.isHorizontal ? -1 : 1) * pxSign;\n}\nfunction prepareLineWidth(itemModel, symbolScale, rotation, opt, outputSymbolMeta) {\n  // In symbols are drawn with scale, so do not need to care about the case that width\n  // or height are too small. But symbol use strokeNoScale, where acture lineWidth should\n  // be calculated.\n  var valueLineWidth = itemModel.get(BAR_BORDER_WIDTH_QUERY) || 0;\n  if (valueLineWidth) {\n    pathForLineWidth.attr({\n      scaleX: symbolScale[0],\n      scaleY: symbolScale[1],\n      rotation: rotation\n    });\n    pathForLineWidth.updateTransform();\n    valueLineWidth /= pathForLineWidth.getLineScale();\n    valueLineWidth *= symbolScale[opt.valueDim.index];\n  }\n  outputSymbolMeta.valueLineWidth = valueLineWidth || 0;\n}\nfunction prepareLayoutInfo(itemModel, symbolSize, layout, symbolRepeat, symbolClip, symbolOffset, symbolPosition, valueLineWidth, boundingLength, repeatCutLength, opt, outputSymbolMeta) {\n  var categoryDim = opt.categoryDim;\n  var valueDim = opt.valueDim;\n  var pxSign = outputSymbolMeta.pxSign;\n  var unitLength = Math.max(symbolSize[valueDim.index] + valueLineWidth, 0);\n  var pathLen = unitLength;\n  // Note: rotation will not effect the layout of symbols, because user may\n  // want symbols to rotate on its center, which should not be translated\n  // when rotating.\n  if (symbolRepeat) {\n    var absBoundingLength = Math.abs(boundingLength);\n    var symbolMargin = zrUtil.retrieve(itemModel.get('symbolMargin'), '15%') + '';\n    var hasEndGap = false;\n    if (symbolMargin.lastIndexOf('!') === symbolMargin.length - 1) {\n      hasEndGap = true;\n      symbolMargin = symbolMargin.slice(0, symbolMargin.length - 1);\n    }\n    var symbolMarginNumeric = parsePercent(symbolMargin, symbolSize[valueDim.index]);\n    var uLenWithMargin = Math.max(unitLength + symbolMarginNumeric * 2, 0);\n    // When symbol margin is less than 0, margin at both ends will be subtracted\n    // to ensure that all of the symbols will not be overflow the given area.\n    var endFix = hasEndGap ? 0 : symbolMarginNumeric * 2;\n    // Both final repeatTimes and final symbolMarginNumeric area calculated based on\n    // boundingLength.\n    var repeatSpecified = isNumeric(symbolRepeat);\n    var repeatTimes = repeatSpecified ? symbolRepeat : toIntTimes((absBoundingLength + endFix) / uLenWithMargin);\n    // Adjust calculate margin, to ensure each symbol is displayed\n    // entirely in the given layout area.\n    var mDiff = absBoundingLength - repeatTimes * unitLength;\n    symbolMarginNumeric = mDiff / 2 / (hasEndGap ? repeatTimes : Math.max(repeatTimes - 1, 1));\n    uLenWithMargin = unitLength + symbolMarginNumeric * 2;\n    endFix = hasEndGap ? 0 : symbolMarginNumeric * 2;\n    // Update repeatTimes when not all symbol will be shown.\n    if (!repeatSpecified && symbolRepeat !== 'fixed') {\n      repeatTimes = repeatCutLength ? toIntTimes((Math.abs(repeatCutLength) + endFix) / uLenWithMargin) : 0;\n    }\n    pathLen = repeatTimes * uLenWithMargin - endFix;\n    outputSymbolMeta.repeatTimes = repeatTimes;\n    outputSymbolMeta.symbolMargin = symbolMarginNumeric;\n  }\n  var sizeFix = pxSign * (pathLen / 2);\n  var pathPosition = outputSymbolMeta.pathPosition = [];\n  pathPosition[categoryDim.index] = layout[categoryDim.wh] / 2;\n  pathPosition[valueDim.index] = symbolPosition === 'start' ? sizeFix : symbolPosition === 'end' ? boundingLength - sizeFix : boundingLength / 2; // 'center'\n  if (symbolOffset) {\n    pathPosition[0] += symbolOffset[0];\n    pathPosition[1] += symbolOffset[1];\n  }\n  var bundlePosition = outputSymbolMeta.bundlePosition = [];\n  bundlePosition[categoryDim.index] = layout[categoryDim.xy];\n  bundlePosition[valueDim.index] = layout[valueDim.xy];\n  var barRectShape = outputSymbolMeta.barRectShape = zrUtil.extend({}, layout);\n  barRectShape[valueDim.wh] = pxSign * Math.max(Math.abs(layout[valueDim.wh]), Math.abs(pathPosition[valueDim.index] + sizeFix));\n  barRectShape[categoryDim.wh] = layout[categoryDim.wh];\n  var clipShape = outputSymbolMeta.clipShape = {};\n  // Consider that symbol may be overflow layout rect.\n  clipShape[categoryDim.xy] = -layout[categoryDim.xy];\n  clipShape[categoryDim.wh] = opt.ecSize[categoryDim.wh];\n  clipShape[valueDim.xy] = 0;\n  clipShape[valueDim.wh] = layout[valueDim.wh];\n}\nfunction createPath(symbolMeta) {\n  var symbolPatternSize = symbolMeta.symbolPatternSize;\n  var path = createSymbol(\n  // Consider texture img, make a big size.\n  symbolMeta.symbolType, -symbolPatternSize / 2, -symbolPatternSize / 2, symbolPatternSize, symbolPatternSize);\n  path.attr({\n    culling: true\n  });\n  path.type !== 'image' && path.setStyle({\n    strokeNoScale: true\n  });\n  return path;\n}\nfunction createOrUpdateRepeatSymbols(bar, opt, symbolMeta, isUpdate) {\n  var bundle = bar.__pictorialBundle;\n  var symbolSize = symbolMeta.symbolSize;\n  var valueLineWidth = symbolMeta.valueLineWidth;\n  var pathPosition = symbolMeta.pathPosition;\n  var valueDim = opt.valueDim;\n  var repeatTimes = symbolMeta.repeatTimes || 0;\n  var index = 0;\n  var unit = symbolSize[opt.valueDim.index] + valueLineWidth + symbolMeta.symbolMargin * 2;\n  eachPath(bar, function (path) {\n    path.__pictorialAnimationIndex = index;\n    path.__pictorialRepeatTimes = repeatTimes;\n    if (index < repeatTimes) {\n      updateAttr(path, null, makeTarget(index), symbolMeta, isUpdate);\n    } else {\n      updateAttr(path, null, {\n        scaleX: 0,\n        scaleY: 0\n      }, symbolMeta, isUpdate, function () {\n        bundle.remove(path);\n      });\n    }\n    // updateHoverAnimation(path, symbolMeta);\n    index++;\n  });\n  for (; index < repeatTimes; index++) {\n    var path = createPath(symbolMeta);\n    path.__pictorialAnimationIndex = index;\n    path.__pictorialRepeatTimes = repeatTimes;\n    bundle.add(path);\n    var target = makeTarget(index);\n    updateAttr(path, {\n      x: target.x,\n      y: target.y,\n      scaleX: 0,\n      scaleY: 0\n    }, {\n      scaleX: target.scaleX,\n      scaleY: target.scaleY,\n      rotation: target.rotation\n    }, symbolMeta, isUpdate);\n  }\n  function makeTarget(index) {\n    var position = pathPosition.slice();\n    // (start && pxSign > 0) || (end && pxSign < 0): i = repeatTimes - index\n    // Otherwise: i = index;\n    var pxSign = symbolMeta.pxSign;\n    var i = index;\n    if (symbolMeta.symbolRepeatDirection === 'start' ? pxSign > 0 : pxSign < 0) {\n      i = repeatTimes - 1 - index;\n    }\n    position[valueDim.index] = unit * (i - repeatTimes / 2 + 0.5) + pathPosition[valueDim.index];\n    return {\n      x: position[0],\n      y: position[1],\n      scaleX: symbolMeta.symbolScale[0],\n      scaleY: symbolMeta.symbolScale[1],\n      rotation: symbolMeta.rotation\n    };\n  }\n}\nfunction createOrUpdateSingleSymbol(bar, opt, symbolMeta, isUpdate) {\n  var bundle = bar.__pictorialBundle;\n  var mainPath = bar.__pictorialMainPath;\n  if (!mainPath) {\n    mainPath = bar.__pictorialMainPath = createPath(symbolMeta);\n    bundle.add(mainPath);\n    updateAttr(mainPath, {\n      x: symbolMeta.pathPosition[0],\n      y: symbolMeta.pathPosition[1],\n      scaleX: 0,\n      scaleY: 0,\n      rotation: symbolMeta.rotation\n    }, {\n      scaleX: symbolMeta.symbolScale[0],\n      scaleY: symbolMeta.symbolScale[1]\n    }, symbolMeta, isUpdate);\n  } else {\n    updateAttr(mainPath, null, {\n      x: symbolMeta.pathPosition[0],\n      y: symbolMeta.pathPosition[1],\n      scaleX: symbolMeta.symbolScale[0],\n      scaleY: symbolMeta.symbolScale[1],\n      rotation: symbolMeta.rotation\n    }, symbolMeta, isUpdate);\n  }\n}\n// bar rect is used for label.\nfunction createOrUpdateBarRect(bar, symbolMeta, isUpdate) {\n  var rectShape = zrUtil.extend({}, symbolMeta.barRectShape);\n  var barRect = bar.__pictorialBarRect;\n  if (!barRect) {\n    barRect = bar.__pictorialBarRect = new graphic.Rect({\n      z2: 2,\n      shape: rectShape,\n      silent: true,\n      style: {\n        stroke: 'transparent',\n        fill: 'transparent',\n        lineWidth: 0\n      }\n    });\n    barRect.disableMorphing = true;\n    bar.add(barRect);\n  } else {\n    updateAttr(barRect, null, {\n      shape: rectShape\n    }, symbolMeta, isUpdate);\n  }\n}\nfunction createOrUpdateClip(bar, opt, symbolMeta, isUpdate) {\n  // If not clip, symbol will be remove and rebuilt.\n  if (symbolMeta.symbolClip) {\n    var clipPath = bar.__pictorialClipPath;\n    var clipShape = zrUtil.extend({}, symbolMeta.clipShape);\n    var valueDim = opt.valueDim;\n    var animationModel = symbolMeta.animationModel;\n    var dataIndex = symbolMeta.dataIndex;\n    if (clipPath) {\n      graphic.updateProps(clipPath, {\n        shape: clipShape\n      }, animationModel, dataIndex);\n    } else {\n      clipShape[valueDim.wh] = 0;\n      clipPath = new graphic.Rect({\n        shape: clipShape\n      });\n      bar.__pictorialBundle.setClipPath(clipPath);\n      bar.__pictorialClipPath = clipPath;\n      var target = {};\n      target[valueDim.wh] = symbolMeta.clipShape[valueDim.wh];\n      graphic[isUpdate ? 'updateProps' : 'initProps'](clipPath, {\n        shape: target\n      }, animationModel, dataIndex);\n    }\n  }\n}\nfunction getItemModel(data, dataIndex) {\n  var itemModel = data.getItemModel(dataIndex);\n  itemModel.getAnimationDelayParams = getAnimationDelayParams;\n  itemModel.isAnimationEnabled = isAnimationEnabled;\n  return itemModel;\n}\nfunction getAnimationDelayParams(path) {\n  // The order is the same as the z-order, see `symbolRepeatDiretion`.\n  return {\n    index: path.__pictorialAnimationIndex,\n    count: path.__pictorialRepeatTimes\n  };\n}\nfunction isAnimationEnabled() {\n  // `animation` prop can be set on itemModel in pictorial bar chart.\n  return this.parentModel.isAnimationEnabled() && !!this.getShallow('animation');\n}\nfunction createBar(data, opt, symbolMeta, isUpdate) {\n  // bar is the main element for each data.\n  var bar = new graphic.Group();\n  // bundle is used for location and clip.\n  var bundle = new graphic.Group();\n  bar.add(bundle);\n  bar.__pictorialBundle = bundle;\n  bundle.x = symbolMeta.bundlePosition[0];\n  bundle.y = symbolMeta.bundlePosition[1];\n  if (symbolMeta.symbolRepeat) {\n    createOrUpdateRepeatSymbols(bar, opt, symbolMeta);\n  } else {\n    createOrUpdateSingleSymbol(bar, opt, symbolMeta);\n  }\n  createOrUpdateBarRect(bar, symbolMeta, isUpdate);\n  createOrUpdateClip(bar, opt, symbolMeta, isUpdate);\n  bar.__pictorialShapeStr = getShapeStr(data, symbolMeta);\n  bar.__pictorialSymbolMeta = symbolMeta;\n  return bar;\n}\nfunction updateBar(bar, opt, symbolMeta) {\n  var animationModel = symbolMeta.animationModel;\n  var dataIndex = symbolMeta.dataIndex;\n  var bundle = bar.__pictorialBundle;\n  graphic.updateProps(bundle, {\n    x: symbolMeta.bundlePosition[0],\n    y: symbolMeta.bundlePosition[1]\n  }, animationModel, dataIndex);\n  if (symbolMeta.symbolRepeat) {\n    createOrUpdateRepeatSymbols(bar, opt, symbolMeta, true);\n  } else {\n    createOrUpdateSingleSymbol(bar, opt, symbolMeta, true);\n  }\n  createOrUpdateBarRect(bar, symbolMeta, true);\n  createOrUpdateClip(bar, opt, symbolMeta, true);\n}\nfunction removeBar(data, dataIndex, animationModel, bar) {\n  // Not show text when animating\n  var labelRect = bar.__pictorialBarRect;\n  labelRect && labelRect.removeTextContent();\n  var paths = [];\n  eachPath(bar, function (path) {\n    paths.push(path);\n  });\n  bar.__pictorialMainPath && paths.push(bar.__pictorialMainPath);\n  // I do not find proper remove animation for clip yet.\n  bar.__pictorialClipPath && (animationModel = null);\n  zrUtil.each(paths, function (path) {\n    graphic.removeElement(path, {\n      scaleX: 0,\n      scaleY: 0\n    }, animationModel, dataIndex, function () {\n      bar.parent && bar.parent.remove(bar);\n    });\n  });\n  data.setItemGraphicEl(dataIndex, null);\n}\nfunction getShapeStr(data, symbolMeta) {\n  return [data.getItemVisual(symbolMeta.dataIndex, 'symbol') || 'none', !!symbolMeta.symbolRepeat, !!symbolMeta.symbolClip].join(':');\n}\nfunction eachPath(bar, cb, context) {\n  // Do not use Group#eachChild, because it do not support remove.\n  zrUtil.each(bar.__pictorialBundle.children(), function (el) {\n    el !== bar.__pictorialBarRect && cb.call(context, el);\n  });\n}\nfunction updateAttr(el, immediateAttrs, animationAttrs, symbolMeta, isUpdate, cb) {\n  immediateAttrs && el.attr(immediateAttrs);\n  // when symbolCip used, only clip path has init animation, otherwise it would be weird effect.\n  if (symbolMeta.symbolClip && !isUpdate) {\n    animationAttrs && el.attr(animationAttrs);\n  } else {\n    animationAttrs && graphic[isUpdate ? 'updateProps' : 'initProps'](el, animationAttrs, symbolMeta.animationModel, symbolMeta.dataIndex, cb);\n  }\n}\nfunction updateCommon(bar, opt, symbolMeta) {\n  var dataIndex = symbolMeta.dataIndex;\n  var itemModel = symbolMeta.itemModel;\n  // Color must be excluded.\n  // Because symbol provide setColor individually to set fill and stroke\n  var emphasisModel = itemModel.getModel('emphasis');\n  var emphasisStyle = emphasisModel.getModel('itemStyle').getItemStyle();\n  var blurStyle = itemModel.getModel(['blur', 'itemStyle']).getItemStyle();\n  var selectStyle = itemModel.getModel(['select', 'itemStyle']).getItemStyle();\n  var cursorStyle = itemModel.getShallow('cursor');\n  var focus = emphasisModel.get('focus');\n  var blurScope = emphasisModel.get('blurScope');\n  var hoverScale = emphasisModel.get('scale');\n  eachPath(bar, function (path) {\n    if (path instanceof ZRImage) {\n      var pathStyle = path.style;\n      path.useStyle(zrUtil.extend({\n        // TODO other properties like dx, dy ?\n        image: pathStyle.image,\n        x: pathStyle.x,\n        y: pathStyle.y,\n        width: pathStyle.width,\n        height: pathStyle.height\n      }, symbolMeta.style));\n    } else {\n      path.useStyle(symbolMeta.style);\n    }\n    var emphasisState = path.ensureState('emphasis');\n    emphasisState.style = emphasisStyle;\n    if (hoverScale) {\n      // NOTE: Must after scale is set after updateAttr\n      emphasisState.scaleX = path.scaleX * 1.1;\n      emphasisState.scaleY = path.scaleY * 1.1;\n    }\n    path.ensureState('blur').style = blurStyle;\n    path.ensureState('select').style = selectStyle;\n    cursorStyle && (path.cursor = cursorStyle);\n    path.z2 = symbolMeta.z2;\n  });\n  var barPositionOutside = opt.valueDim.posDesc[+(symbolMeta.boundingLength > 0)];\n  var barRect = bar.__pictorialBarRect;\n  barRect.ignoreClip = true;\n  setLabelStyle(barRect, getLabelStatesModels(itemModel), {\n    labelFetcher: opt.seriesModel,\n    labelDataIndex: dataIndex,\n    defaultText: getDefaultLabel(opt.seriesModel.getData(), dataIndex),\n    inheritColor: symbolMeta.style.fill,\n    defaultOpacity: symbolMeta.style.opacity,\n    defaultOutsidePosition: barPositionOutside\n  });\n  toggleHoverEmphasis(bar, focus, blurScope, emphasisModel.get('disabled'));\n}\nfunction toIntTimes(times) {\n  var roundedTimes = Math.round(times);\n  // Escapse accurate error\n  return Math.abs(times - roundedTimes) < 1e-4 ? roundedTimes : Math.ceil(times);\n}\nexport default PictorialBarView;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AACA,IAAI,yBAAyB;IAAC;IAAa;CAAc;AACzD,uBAAuB;AACvB,IAAI,eAAe;IAAC;QAClB,IAAI;QACJ,IAAI;QACJ,OAAO;QACP,SAAS;YAAC;YAAQ;SAAQ;IAC5B;IAAG;QACD,IAAI;QACJ,IAAI;QACJ,OAAO;QACP,SAAS;YAAC;YAAO;SAAS;IAC5B;CAAE;AACF,IAAI,mBAAmB,IAAI,kJAAQ,MAAM;AACzC,IAAI,mBAAmB,WAAW,GAAE,SAAU,MAAM;IAClD,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE,kBAAkB;IAC5B,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,iBAAiB,IAAI;QAClC,OAAO;IACT;IACA,iBAAiB,SAAS,CAAC,MAAM,GAAG,SAAU,WAAW,EAAE,OAAO,EAAE,GAAG;QACrE,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,OAAO,YAAY,OAAO;QAC9B,IAAI,UAAU,IAAI,CAAC,KAAK;QACxB,IAAI,YAAY,YAAY,gBAAgB;QAC5C,IAAI,WAAW,UAAU,WAAW;QACpC,IAAI,eAAe,SAAS,YAAY;QACxC,IAAI,eAAe,UAAU,MAAM,CAAC,OAAO;QAC3C,IAAI,MAAM;YACR,QAAQ;gBACN,OAAO,IAAI,QAAQ;gBACnB,QAAQ,IAAI,SAAS;YACvB;YACA,aAAa;YACb,UAAU;YACV,gBAAgB;gBAAC;oBAAC,aAAa,CAAC;oBAAE,aAAa,CAAC,GAAG,aAAa,KAAK;iBAAC;gBAAE;oBAAC,aAAa,CAAC;oBAAE,aAAa,CAAC,GAAG,aAAa,MAAM;iBAAC;aAAC;YAC/H,cAAc;YACd,UAAU,YAAY,CAAC,CAAC,aAAa;YACrC,aAAa,YAAY,CAAC,IAAI,CAAC,aAAa;QAC9C;QACA,KAAK,IAAI,CAAC,SAAS,GAAG,CAAC,SAAU,SAAS;YACxC,IAAI,CAAC,KAAK,QAAQ,CAAC,YAAY;gBAC7B;YACF;YACA,IAAI,YAAY,aAAa,MAAM;YACnC,IAAI,aAAa,cAAc,MAAM,WAAW,WAAW;YAC3D,IAAI,MAAM,UAAU,MAAM,KAAK;YAC/B,KAAK,gBAAgB,CAAC,WAAW;YACjC,MAAM,GAAG,CAAC;YACV,aAAa,KAAK,KAAK;QACzB,GAAG,MAAM,CAAC,SAAU,QAAQ,EAAE,QAAQ;YACpC,IAAI,MAAM,QAAQ,gBAAgB,CAAC;YACnC,IAAI,CAAC,KAAK,QAAQ,CAAC,WAAW;gBAC5B,MAAM,MAAM,CAAC;gBACb;YACF;YACA,IAAI,YAAY,aAAa,MAAM;YACnC,IAAI,aAAa,cAAc,MAAM,UAAU,WAAW;YAC1D,IAAI,oBAAoB,YAAY,MAAM;YAC1C,IAAI,OAAO,sBAAsB,IAAI,mBAAmB,EAAE;gBACxD,MAAM,MAAM,CAAC;gBACb,KAAK,gBAAgB,CAAC,UAAU;gBAChC,MAAM;YACR;YACA,IAAI,KAAK;gBACP,UAAU,KAAK,KAAK;YACtB,OAAO;gBACL,MAAM,UAAU,MAAM,KAAK,YAAY;YACzC;YACA,KAAK,gBAAgB,CAAC,UAAU;YAChC,IAAI,qBAAqB,GAAG;YAC5B,WAAW;YACX,MAAM,GAAG,CAAC;YACV,aAAa,KAAK,KAAK;QACzB,GAAG,MAAM,CAAC,SAAU,SAAS;YAC3B,IAAI,MAAM,QAAQ,gBAAgB,CAAC;YACnC,OAAO,UAAU,SAAS,WAAW,IAAI,qBAAqB,CAAC,cAAc,EAAE;QACjF,GAAG,OAAO;QACV,cAAc;QACd,IAAI,WAAW,YAAY,GAAG,CAAC,QAAQ,QAAQ,CAAA,GAAA,+KAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,gBAAgB,EAAE,OAAO,eAAe;QAClH,IAAI,UAAU;YACZ,MAAM,WAAW,CAAC;QACpB,OAAO;YACL,MAAM,cAAc;QACtB;QACA,IAAI,CAAC,KAAK,GAAG;QACb,OAAO,IAAI,CAAC,KAAK;IACnB;IACA,iBAAiB,SAAS,CAAC,MAAM,GAAG,SAAU,OAAO,EAAE,GAAG;QACxD,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,IAAI,OAAO,IAAI,CAAC,KAAK;QACrB,IAAI,QAAQ,GAAG,CAAC,cAAc;YAC5B,IAAI,MAAM;gBACR,KAAK,iBAAiB,CAAC,SAAU,GAAG;oBAClC,UAAU,MAAM,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,KAAK,SAAS,EAAE,SAAS;gBACrD;YACF;QACF,OAAO;YACL,MAAM,SAAS;QACjB;IACF;IACA,iBAAiB,IAAI,GAAG;IACxB,OAAO;AACT,EAAE,+IAAA,CAAA,UAAS;AACX,0EAA0E;AAC1E,SAAS,cAAc,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,GAAG;IACpD,IAAI,SAAS,KAAK,aAAa,CAAC;IAChC,IAAI,eAAe,UAAU,GAAG,CAAC;IACjC,IAAI,aAAa,UAAU,GAAG,CAAC;IAC/B,IAAI,iBAAiB,UAAU,GAAG,CAAC,qBAAqB;IACxD,IAAI,eAAe,UAAU,GAAG,CAAC;IACjC,IAAI,WAAW,CAAC,gBAAgB,CAAC,IAAI,KAAK,EAAE,GAAG,OAAO;IACtD,IAAI,oBAAoB,UAAU,GAAG,CAAC,wBAAwB;IAC9D,IAAI,qBAAqB,UAAU,kBAAkB;IACrD,IAAI,aAAa;QACf,WAAW;QACX,QAAQ;QACR,WAAW;QACX,YAAY,KAAK,aAAa,CAAC,WAAW,aAAa;QACvD,OAAO,KAAK,aAAa,CAAC,WAAW;QACrC,YAAY;QACZ,cAAc;QACd,uBAAuB,UAAU,GAAG,CAAC;QACrC,mBAAmB;QACnB,UAAU;QACV,gBAAgB,qBAAqB,YAAY;QACjD,YAAY,sBAAsB,UAAU,GAAG,CAAC;YAAC;YAAY;SAAQ;QACrE,IAAI,UAAU,UAAU,CAAC,KAAK,SAAS;IACzC;IACA,iBAAiB,WAAW,cAAc,QAAQ,KAAK;IACvD,kBAAkB,MAAM,WAAW,QAAQ,cAAc,YAAY,WAAW,cAAc,EAAE,WAAW,MAAM,EAAE,mBAAmB,KAAK;IAC3I,iBAAiB,WAAW,WAAW,WAAW,EAAE,UAAU,KAAK;IACnE,IAAI,aAAa,WAAW,UAAU;IACtC,IAAI,eAAe,CAAA,GAAA,gJAAA,CAAA,wBAAqB,AAAD,EAAE,UAAU,GAAG,CAAC,iBAAiB;IACxE,kBAAkB,WAAW,YAAY,QAAQ,cAAc,YAAY,cAAc,gBAAgB,WAAW,cAAc,EAAE,WAAW,cAAc,EAAE,WAAW,eAAe,EAAE,KAAK;IAChM,OAAO;AACT;AACA,8BAA8B;AAC9B,SAAS,iBAAiB,SAAS,EAAE,YAAY,EAAE,MAAM,EAAE,GAAG,EAAE,gBAAgB;IAC9E,IAAI,WAAW,IAAI,QAAQ;IAC3B,IAAI,qBAAqB,UAAU,GAAG,CAAC;IACvC,IAAI,YAAY,IAAI,QAAQ,CAAC,YAAY,CAAC,IAAI,QAAQ,CAAC,WAAW;IAClE,IAAI,SAAS,UAAU,aAAa,CAAC,UAAU,WAAW,CAAC;IAC3D,IAAI,YAAY,IAAI,CAAC,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,IAAI,CAAC;IAC9C,IAAI;IACJ,IAAI,CAAA,GAAA,8IAAA,CAAA,UAAc,AAAD,EAAE,qBAAqB;QACtC,IAAI,uBAAuB;YAAC,qBAAqB,WAAW,kBAAkB,CAAC,EAAE,IAAI;YAAQ,qBAAqB,WAAW,kBAAkB,CAAC,EAAE,IAAI;SAAO;QAC7J,oBAAoB,CAAC,EAAE,GAAG,oBAAoB,CAAC,EAAE,IAAI,qBAAqB,OAAO;QACjF,iBAAiB,oBAAoB,CAAC,UAAU;IAClD,OAAO,IAAI,sBAAsB,MAAM;QACrC,iBAAiB,qBAAqB,WAAW,sBAAsB;IACzE,OAAO,IAAI,cAAc;QACvB,iBAAiB,IAAI,cAAc,CAAC,SAAS,KAAK,CAAC,CAAC,UAAU,GAAG;IACnE,OAAO;QACL,iBAAiB,MAAM,CAAC,SAAS,EAAE,CAAC;IACtC;IACA,iBAAiB,cAAc,GAAG;IAClC,IAAI,cAAc;QAChB,iBAAiB,eAAe,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;IACxD;IACA,kFAAkF;IAClF,oEAAoE;IACpE,IAAI,UAAU,SAAS,EAAE,KAAK;IAC9B,IAAI,YAAY,UAAU,OAAO;IACjC,iBAAiB,MAAM,GAAG,WAAW,CAAC,aAAa,CAAC,WAAW,YAAY,kBAAkB,IAAI,IAAI,CAAC,IAAI,iBAAiB,IAAI,IAAI,CAAC;AACtI;AACA,SAAS,qBAAqB,IAAI,EAAE,KAAK;IACvC,OAAO,KAAK,aAAa,CAAC,KAAK,WAAW,CAAC,KAAK,KAAK,CAAC,KAAK,CAAC;AAC9D;AACA,2BAA2B;AAC3B,SAAS,kBAAkB,IAAI,EAAE,SAAS,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,cAAc,EAAE,MAAM,EAAE,iBAAiB,EAAE,GAAG,EAAE,gBAAgB;IAC5I,IAAI,WAAW,IAAI,QAAQ;IAC3B,IAAI,cAAc,IAAI,WAAW;IACjC,IAAI,eAAe,KAAK,GAAG,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;IAClD,IAAI,aAAa,KAAK,aAAa,CAAC,WAAW;IAC/C,IAAI;IACJ,IAAI,CAAA,GAAA,8IAAA,CAAA,UAAc,AAAD,EAAE,aAAa;QAC9B,mBAAmB,WAAW,KAAK;IACrC,OAAO;QACL,IAAI,cAAc,MAAM;YACtB,6BAA6B;YAC7B,mBAAmB;gBAAC;gBAAQ;aAAO;QACrC,OAAO;YACL,mBAAmB;gBAAC;gBAAY;aAAW;QAC7C;IACF;IACA,qFAAqF;IACrF,kFAAkF;IAClF,oFAAoF;IACpF,6CAA6C;IAC7C,gBAAgB,CAAC,YAAY,KAAK,CAAC,GAAG,CAAA,GAAA,gJAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,CAAC,YAAY,KAAK,CAAC,EAAE;IACxF,gBAAgB,CAAC,SAAS,KAAK,CAAC,GAAG,CAAA,GAAA,gJAAA,CAAA,eAAY,AAAD,EAAE,gBAAgB,CAAC,SAAS,KAAK,CAAC,EAAE,eAAe,eAAe,KAAK,GAAG,CAAC;IACzH,iBAAiB,UAAU,GAAG;IAC9B,oDAAoD;IACpD,IAAI,cAAc,iBAAiB,WAAW,GAAG;QAAC,gBAAgB,CAAC,EAAE,GAAG;QAAmB,gBAAgB,CAAC,EAAE,GAAG;KAAkB;IACnI,4DAA4D;IAC5D,WAAW,CAAC,SAAS,KAAK,CAAC,IAAI,CAAC,IAAI,YAAY,GAAG,CAAC,IAAI,CAAC,IAAI;AAC/D;AACA,SAAS,iBAAiB,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,GAAG,EAAE,gBAAgB;IAC/E,oFAAoF;IACpF,uFAAuF;IACvF,iBAAiB;IACjB,IAAI,iBAAiB,UAAU,GAAG,CAAC,2BAA2B;IAC9D,IAAI,gBAAgB;QAClB,iBAAiB,IAAI,CAAC;YACpB,QAAQ,WAAW,CAAC,EAAE;YACtB,QAAQ,WAAW,CAAC,EAAE;YACtB,UAAU;QACZ;QACA,iBAAiB,eAAe;QAChC,kBAAkB,iBAAiB,YAAY;QAC/C,kBAAkB,WAAW,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC;IACnD;IACA,iBAAiB,cAAc,GAAG,kBAAkB;AACtD;AACA,SAAS,kBAAkB,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,YAAY,EAAE,UAAU,EAAE,YAAY,EAAE,cAAc,EAAE,cAAc,EAAE,cAAc,EAAE,eAAe,EAAE,GAAG,EAAE,gBAAgB;IACtL,IAAI,cAAc,IAAI,WAAW;IACjC,IAAI,WAAW,IAAI,QAAQ;IAC3B,IAAI,SAAS,iBAAiB,MAAM;IACpC,IAAI,aAAa,KAAK,GAAG,CAAC,UAAU,CAAC,SAAS,KAAK,CAAC,GAAG,gBAAgB;IACvE,IAAI,UAAU;IACd,yEAAyE;IACzE,uEAAuE;IACvE,iBAAiB;IACjB,IAAI,cAAc;QAChB,IAAI,oBAAoB,KAAK,GAAG,CAAC;QACjC,IAAI,eAAe,CAAA,GAAA,8IAAA,CAAA,WAAe,AAAD,EAAE,UAAU,GAAG,CAAC,iBAAiB,SAAS;QAC3E,IAAI,YAAY;QAChB,IAAI,aAAa,WAAW,CAAC,SAAS,aAAa,MAAM,GAAG,GAAG;YAC7D,YAAY;YACZ,eAAe,aAAa,KAAK,CAAC,GAAG,aAAa,MAAM,GAAG;QAC7D;QACA,IAAI,sBAAsB,CAAA,GAAA,gJAAA,CAAA,eAAY,AAAD,EAAE,cAAc,UAAU,CAAC,SAAS,KAAK,CAAC;QAC/E,IAAI,iBAAiB,KAAK,GAAG,CAAC,aAAa,sBAAsB,GAAG;QACpE,4EAA4E;QAC5E,yEAAyE;QACzE,IAAI,SAAS,YAAY,IAAI,sBAAsB;QACnD,gFAAgF;QAChF,kBAAkB;QAClB,IAAI,kBAAkB,CAAA,GAAA,gJAAA,CAAA,YAAS,AAAD,EAAE;QAChC,IAAI,cAAc,kBAAkB,eAAe,WAAW,CAAC,oBAAoB,MAAM,IAAI;QAC7F,8DAA8D;QAC9D,qCAAqC;QACrC,IAAI,QAAQ,oBAAoB,cAAc;QAC9C,sBAAsB,QAAQ,IAAI,CAAC,YAAY,cAAc,KAAK,GAAG,CAAC,cAAc,GAAG,EAAE;QACzF,iBAAiB,aAAa,sBAAsB;QACpD,SAAS,YAAY,IAAI,sBAAsB;QAC/C,wDAAwD;QACxD,IAAI,CAAC,mBAAmB,iBAAiB,SAAS;YAChD,cAAc,kBAAkB,WAAW,CAAC,KAAK,GAAG,CAAC,mBAAmB,MAAM,IAAI,kBAAkB;QACtG;QACA,UAAU,cAAc,iBAAiB;QACzC,iBAAiB,WAAW,GAAG;QAC/B,iBAAiB,YAAY,GAAG;IAClC;IACA,IAAI,UAAU,SAAS,CAAC,UAAU,CAAC;IACnC,IAAI,eAAe,iBAAiB,YAAY,GAAG,EAAE;IACrD,YAAY,CAAC,YAAY,KAAK,CAAC,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC,GAAG;IAC3D,YAAY,CAAC,SAAS,KAAK,CAAC,GAAG,mBAAmB,UAAU,UAAU,mBAAmB,QAAQ,iBAAiB,UAAU,iBAAiB,GAAG,WAAW;IAC3J,IAAI,cAAc;QAChB,YAAY,CAAC,EAAE,IAAI,YAAY,CAAC,EAAE;QAClC,YAAY,CAAC,EAAE,IAAI,YAAY,CAAC,EAAE;IACpC;IACA,IAAI,iBAAiB,iBAAiB,cAAc,GAAG,EAAE;IACzD,cAAc,CAAC,YAAY,KAAK,CAAC,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;IAC1D,cAAc,CAAC,SAAS,KAAK,CAAC,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;IACpD,IAAI,eAAe,iBAAiB,YAAY,GAAG,CAAA,GAAA,8IAAA,CAAA,SAAa,AAAD,EAAE,CAAC,GAAG;IACrE,YAAY,CAAC,SAAS,EAAE,CAAC,GAAG,SAAS,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,SAAS,EAAE,CAAC,GAAG,KAAK,GAAG,CAAC,YAAY,CAAC,SAAS,KAAK,CAAC,GAAG;IACrH,YAAY,CAAC,YAAY,EAAE,CAAC,GAAG,MAAM,CAAC,YAAY,EAAE,CAAC;IACrD,IAAI,YAAY,iBAAiB,SAAS,GAAG,CAAC;IAC9C,oDAAoD;IACpD,SAAS,CAAC,YAAY,EAAE,CAAC,GAAG,CAAC,MAAM,CAAC,YAAY,EAAE,CAAC;IACnD,SAAS,CAAC,YAAY,EAAE,CAAC,GAAG,IAAI,MAAM,CAAC,YAAY,EAAE,CAAC;IACtD,SAAS,CAAC,SAAS,EAAE,CAAC,GAAG;IACzB,SAAS,CAAC,SAAS,EAAE,CAAC,GAAG,MAAM,CAAC,SAAS,EAAE,CAAC;AAC9C;AACA,SAAS,WAAW,UAAU;IAC5B,IAAI,oBAAoB,WAAW,iBAAiB;IACpD,IAAI,OAAO,CAAA,GAAA,gJAAA,CAAA,eAAY,AAAD,EACtB,yCAAyC;IACzC,WAAW,UAAU,EAAE,CAAC,oBAAoB,GAAG,CAAC,oBAAoB,GAAG,mBAAmB;IAC1F,KAAK,IAAI,CAAC;QACR,SAAS;IACX;IACA,KAAK,IAAI,KAAK,WAAW,KAAK,QAAQ,CAAC;QACrC,eAAe;IACjB;IACA,OAAO;AACT;AACA,SAAS,4BAA4B,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ;IACjE,IAAI,SAAS,IAAI,iBAAiB;IAClC,IAAI,aAAa,WAAW,UAAU;IACtC,IAAI,iBAAiB,WAAW,cAAc;IAC9C,IAAI,eAAe,WAAW,YAAY;IAC1C,IAAI,WAAW,IAAI,QAAQ;IAC3B,IAAI,cAAc,WAAW,WAAW,IAAI;IAC5C,IAAI,QAAQ;IACZ,IAAI,OAAO,UAAU,CAAC,IAAI,QAAQ,CAAC,KAAK,CAAC,GAAG,iBAAiB,WAAW,YAAY,GAAG;IACvF,SAAS,KAAK,SAAU,IAAI;QAC1B,KAAK,yBAAyB,GAAG;QACjC,KAAK,sBAAsB,GAAG;QAC9B,IAAI,QAAQ,aAAa;YACvB,WAAW,MAAM,MAAM,WAAW,QAAQ,YAAY;QACxD,OAAO;YACL,WAAW,MAAM,MAAM;gBACrB,QAAQ;gBACR,QAAQ;YACV,GAAG,YAAY,UAAU;gBACvB,OAAO,MAAM,CAAC;YAChB;QACF;QACA,0CAA0C;QAC1C;IACF;IACA,MAAO,QAAQ,aAAa,QAAS;QACnC,IAAI,OAAO,WAAW;QACtB,KAAK,yBAAyB,GAAG;QACjC,KAAK,sBAAsB,GAAG;QAC9B,OAAO,GAAG,CAAC;QACX,IAAI,SAAS,WAAW;QACxB,WAAW,MAAM;YACf,GAAG,OAAO,CAAC;YACX,GAAG,OAAO,CAAC;YACX,QAAQ;YACR,QAAQ;QACV,GAAG;YACD,QAAQ,OAAO,MAAM;YACrB,QAAQ,OAAO,MAAM;YACrB,UAAU,OAAO,QAAQ;QAC3B,GAAG,YAAY;IACjB;IACA,SAAS,WAAW,KAAK;QACvB,IAAI,WAAW,aAAa,KAAK;QACjC,wEAAwE;QACxE,wBAAwB;QACxB,IAAI,SAAS,WAAW,MAAM;QAC9B,IAAI,IAAI;QACR,IAAI,WAAW,qBAAqB,KAAK,UAAU,SAAS,IAAI,SAAS,GAAG;YAC1E,IAAI,cAAc,IAAI;QACxB;QACA,QAAQ,CAAC,SAAS,KAAK,CAAC,GAAG,OAAO,CAAC,IAAI,cAAc,IAAI,GAAG,IAAI,YAAY,CAAC,SAAS,KAAK,CAAC;QAC5F,OAAO;YACL,GAAG,QAAQ,CAAC,EAAE;YACd,GAAG,QAAQ,CAAC,EAAE;YACd,QAAQ,WAAW,WAAW,CAAC,EAAE;YACjC,QAAQ,WAAW,WAAW,CAAC,EAAE;YACjC,UAAU,WAAW,QAAQ;QAC/B;IACF;AACF;AACA,SAAS,2BAA2B,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ;IAChE,IAAI,SAAS,IAAI,iBAAiB;IAClC,IAAI,WAAW,IAAI,mBAAmB;IACtC,IAAI,CAAC,UAAU;QACb,WAAW,IAAI,mBAAmB,GAAG,WAAW;QAChD,OAAO,GAAG,CAAC;QACX,WAAW,UAAU;YACnB,GAAG,WAAW,YAAY,CAAC,EAAE;YAC7B,GAAG,WAAW,YAAY,CAAC,EAAE;YAC7B,QAAQ;YACR,QAAQ;YACR,UAAU,WAAW,QAAQ;QAC/B,GAAG;YACD,QAAQ,WAAW,WAAW,CAAC,EAAE;YACjC,QAAQ,WAAW,WAAW,CAAC,EAAE;QACnC,GAAG,YAAY;IACjB,OAAO;QACL,WAAW,UAAU,MAAM;YACzB,GAAG,WAAW,YAAY,CAAC,EAAE;YAC7B,GAAG,WAAW,YAAY,CAAC,EAAE;YAC7B,QAAQ,WAAW,WAAW,CAAC,EAAE;YACjC,QAAQ,WAAW,WAAW,CAAC,EAAE;YACjC,UAAU,WAAW,QAAQ;QAC/B,GAAG,YAAY;IACjB;AACF;AACA,8BAA8B;AAC9B,SAAS,sBAAsB,GAAG,EAAE,UAAU,EAAE,QAAQ;IACtD,IAAI,YAAY,CAAA,GAAA,8IAAA,CAAA,SAAa,AAAD,EAAE,CAAC,GAAG,WAAW,YAAY;IACzD,IAAI,UAAU,IAAI,kBAAkB;IACpC,IAAI,CAAC,SAAS;QACZ,UAAU,IAAI,kBAAkB,GAAG,IAAI,kJAAQ,IAAI,CAAC;YAClD,IAAI;YACJ,OAAO;YACP,QAAQ;YACR,OAAO;gBACL,QAAQ;gBACR,MAAM;gBACN,WAAW;YACb;QACF;QACA,QAAQ,eAAe,GAAG;QAC1B,IAAI,GAAG,CAAC;IACV,OAAO;QACL,WAAW,SAAS,MAAM;YACxB,OAAO;QACT,GAAG,YAAY;IACjB;AACF;AACA,SAAS,mBAAmB,GAAG,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ;IACxD,kDAAkD;IAClD,IAAI,WAAW,UAAU,EAAE;QACzB,IAAI,WAAW,IAAI,mBAAmB;QACtC,IAAI,YAAY,CAAA,GAAA,8IAAA,CAAA,SAAa,AAAD,EAAE,CAAC,GAAG,WAAW,SAAS;QACtD,IAAI,WAAW,IAAI,QAAQ;QAC3B,IAAI,iBAAiB,WAAW,cAAc;QAC9C,IAAI,YAAY,WAAW,SAAS;QACpC,IAAI,UAAU;YACZ,kJAAQ,WAAW,CAAC,UAAU;gBAC5B,OAAO;YACT,GAAG,gBAAgB;QACrB,OAAO;YACL,SAAS,CAAC,SAAS,EAAE,CAAC,GAAG;YACzB,WAAW,IAAI,kJAAQ,IAAI,CAAC;gBAC1B,OAAO;YACT;YACA,IAAI,iBAAiB,CAAC,WAAW,CAAC;YAClC,IAAI,mBAAmB,GAAG;YAC1B,IAAI,SAAS,CAAC;YACd,MAAM,CAAC,SAAS,EAAE,CAAC,GAAG,WAAW,SAAS,CAAC,SAAS,EAAE,CAAC;YACvD,iJAAO,CAAC,WAAW,gBAAgB,YAAY,CAAC,UAAU;gBACxD,OAAO;YACT,GAAG,gBAAgB;QACrB;IACF;AACF;AACA,SAAS,aAAa,IAAI,EAAE,SAAS;IACnC,IAAI,YAAY,KAAK,YAAY,CAAC;IAClC,UAAU,uBAAuB,GAAG;IACpC,UAAU,kBAAkB,GAAG;IAC/B,OAAO;AACT;AACA,SAAS,wBAAwB,IAAI;IACnC,oEAAoE;IACpE,OAAO;QACL,OAAO,KAAK,yBAAyB;QACrC,OAAO,KAAK,sBAAsB;IACpC;AACF;AACA,SAAS;IACP,mEAAmE;IACnE,OAAO,IAAI,CAAC,WAAW,CAAC,kBAAkB,MAAM,CAAC,CAAC,IAAI,CAAC,UAAU,CAAC;AACpE;AACA,SAAS,UAAU,IAAI,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ;IAChD,yCAAyC;IACzC,IAAI,MAAM,IAAI,kJAAQ,KAAK;IAC3B,wCAAwC;IACxC,IAAI,SAAS,IAAI,kJAAQ,KAAK;IAC9B,IAAI,GAAG,CAAC;IACR,IAAI,iBAAiB,GAAG;IACxB,OAAO,CAAC,GAAG,WAAW,cAAc,CAAC,EAAE;IACvC,OAAO,CAAC,GAAG,WAAW,cAAc,CAAC,EAAE;IACvC,IAAI,WAAW,YAAY,EAAE;QAC3B,4BAA4B,KAAK,KAAK;IACxC,OAAO;QACL,2BAA2B,KAAK,KAAK;IACvC;IACA,sBAAsB,KAAK,YAAY;IACvC,mBAAmB,KAAK,KAAK,YAAY;IACzC,IAAI,mBAAmB,GAAG,YAAY,MAAM;IAC5C,IAAI,qBAAqB,GAAG;IAC5B,OAAO;AACT;AACA,SAAS,UAAU,GAAG,EAAE,GAAG,EAAE,UAAU;IACrC,IAAI,iBAAiB,WAAW,cAAc;IAC9C,IAAI,YAAY,WAAW,SAAS;IACpC,IAAI,SAAS,IAAI,iBAAiB;IAClC,kJAAQ,WAAW,CAAC,QAAQ;QAC1B,GAAG,WAAW,cAAc,CAAC,EAAE;QAC/B,GAAG,WAAW,cAAc,CAAC,EAAE;IACjC,GAAG,gBAAgB;IACnB,IAAI,WAAW,YAAY,EAAE;QAC3B,4BAA4B,KAAK,KAAK,YAAY;IACpD,OAAO;QACL,2BAA2B,KAAK,KAAK,YAAY;IACnD;IACA,sBAAsB,KAAK,YAAY;IACvC,mBAAmB,KAAK,KAAK,YAAY;AAC3C;AACA,SAAS,UAAU,IAAI,EAAE,SAAS,EAAE,cAAc,EAAE,GAAG;IACrD,+BAA+B;IAC/B,IAAI,YAAY,IAAI,kBAAkB;IACtC,aAAa,UAAU,iBAAiB;IACxC,IAAI,QAAQ,EAAE;IACd,SAAS,KAAK,SAAU,IAAI;QAC1B,MAAM,IAAI,CAAC;IACb;IACA,IAAI,mBAAmB,IAAI,MAAM,IAAI,CAAC,IAAI,mBAAmB;IAC7D,sDAAsD;IACtD,IAAI,mBAAmB,IAAI,CAAC,iBAAiB,IAAI;IACjD,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE,OAAO,SAAU,IAAI;QAC/B,kJAAQ,aAAa,CAAC,MAAM;YAC1B,QAAQ;YACR,QAAQ;QACV,GAAG,gBAAgB,WAAW;YAC5B,IAAI,MAAM,IAAI,IAAI,MAAM,CAAC,MAAM,CAAC;QAClC;IACF;IACA,KAAK,gBAAgB,CAAC,WAAW;AACnC;AACA,SAAS,YAAY,IAAI,EAAE,UAAU;IACnC,OAAO;QAAC,KAAK,aAAa,CAAC,WAAW,SAAS,EAAE,aAAa;QAAQ,CAAC,CAAC,WAAW,YAAY;QAAE,CAAC,CAAC,WAAW,UAAU;KAAC,CAAC,IAAI,CAAC;AACjI;AACA,SAAS,SAAS,GAAG,EAAE,EAAE,EAAE,OAAO;IAChC,gEAAgE;IAChE,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE,IAAI,iBAAiB,CAAC,QAAQ,IAAI,SAAU,EAAE;QACxD,OAAO,IAAI,kBAAkB,IAAI,GAAG,IAAI,CAAC,SAAS;IACpD;AACF;AACA,SAAS,WAAW,EAAE,EAAE,cAAc,EAAE,cAAc,EAAE,UAAU,EAAE,QAAQ,EAAE,EAAE;IAC9E,kBAAkB,GAAG,IAAI,CAAC;IAC1B,8FAA8F;IAC9F,IAAI,WAAW,UAAU,IAAI,CAAC,UAAU;QACtC,kBAAkB,GAAG,IAAI,CAAC;IAC5B,OAAO;QACL,kBAAkB,iJAAO,CAAC,WAAW,gBAAgB,YAAY,CAAC,IAAI,gBAAgB,WAAW,cAAc,EAAE,WAAW,SAAS,EAAE;IACzI;AACF;AACA,SAAS,aAAa,GAAG,EAAE,GAAG,EAAE,UAAU;IACxC,IAAI,YAAY,WAAW,SAAS;IACpC,IAAI,YAAY,WAAW,SAAS;IACpC,0BAA0B;IAC1B,sEAAsE;IACtE,IAAI,gBAAgB,UAAU,QAAQ,CAAC;IACvC,IAAI,gBAAgB,cAAc,QAAQ,CAAC,aAAa,YAAY;IACpE,IAAI,YAAY,UAAU,QAAQ,CAAC;QAAC;QAAQ;KAAY,EAAE,YAAY;IACtE,IAAI,cAAc,UAAU,QAAQ,CAAC;QAAC;QAAU;KAAY,EAAE,YAAY;IAC1E,IAAI,cAAc,UAAU,UAAU,CAAC;IACvC,IAAI,QAAQ,cAAc,GAAG,CAAC;IAC9B,IAAI,YAAY,cAAc,GAAG,CAAC;IAClC,IAAI,aAAa,cAAc,GAAG,CAAC;IACnC,SAAS,KAAK,SAAU,IAAI;QAC1B,IAAI,gBAAgB,kJAAA,CAAA,UAAO,EAAE;YAC3B,IAAI,YAAY,KAAK,KAAK;YAC1B,KAAK,QAAQ,CAAC,CAAA,GAAA,8IAAA,CAAA,SAAa,AAAD,EAAE;gBAC1B,sCAAsC;gBACtC,OAAO,UAAU,KAAK;gBACtB,GAAG,UAAU,CAAC;gBACd,GAAG,UAAU,CAAC;gBACd,OAAO,UAAU,KAAK;gBACtB,QAAQ,UAAU,MAAM;YAC1B,GAAG,WAAW,KAAK;QACrB,OAAO;YACL,KAAK,QAAQ,CAAC,WAAW,KAAK;QAChC;QACA,IAAI,gBAAgB,KAAK,WAAW,CAAC;QACrC,cAAc,KAAK,GAAG;QACtB,IAAI,YAAY;YACd,iDAAiD;YACjD,cAAc,MAAM,GAAG,KAAK,MAAM,GAAG;YACrC,cAAc,MAAM,GAAG,KAAK,MAAM,GAAG;QACvC;QACA,KAAK,WAAW,CAAC,QAAQ,KAAK,GAAG;QACjC,KAAK,WAAW,CAAC,UAAU,KAAK,GAAG;QACnC,eAAe,CAAC,KAAK,MAAM,GAAG,WAAW;QACzC,KAAK,EAAE,GAAG,WAAW,EAAE;IACzB;IACA,IAAI,qBAAqB,IAAI,QAAQ,CAAC,OAAO,CAAC,CAAC,CAAC,WAAW,cAAc,GAAG,CAAC,EAAE;IAC/E,IAAI,UAAU,IAAI,kBAAkB;IACpC,QAAQ,UAAU,GAAG;IACrB,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE,SAAS,CAAA,GAAA,qJAAA,CAAA,uBAAoB,AAAD,EAAE,YAAY;QACtD,cAAc,IAAI,WAAW;QAC7B,gBAAgB;QAChB,aAAa,CAAA,GAAA,gKAAA,CAAA,kBAAe,AAAD,EAAE,IAAI,WAAW,CAAC,OAAO,IAAI;QACxD,cAAc,WAAW,KAAK,CAAC,IAAI;QACnC,gBAAgB,WAAW,KAAK,CAAC,OAAO;QACxC,wBAAwB;IAC1B;IACA,CAAA,GAAA,gJAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,OAAO,WAAW,cAAc,GAAG,CAAC;AAC/D;AACA,SAAS,WAAW,KAAK;IACvB,IAAI,eAAe,KAAK,KAAK,CAAC;IAC9B,yBAAyB;IACzB,OAAO,KAAK,GAAG,CAAC,QAAQ,gBAAgB,OAAO,eAAe,KAAK,IAAI,CAAC;AAC1E;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2012, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/chart/bar/PictorialBarSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport BaseBarSeriesModel from './BaseBarSeries.js';\nimport { inheritDefaultOption } from '../../util/component.js';\nvar PictorialBarSeriesModel = /** @class */function (_super) {\n  __extends(PictorialBarSeriesModel, _super);\n  function PictorialBarSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = PictorialBarSeriesModel.type;\n    _this.hasSymbolVisual = true;\n    _this.defaultSymbol = 'roundRect';\n    return _this;\n  }\n  PictorialBarSeriesModel.prototype.getInitialData = function (option) {\n    // Disable stack.\n    option.stack = null;\n    return _super.prototype.getInitialData.apply(this, arguments);\n  };\n  PictorialBarSeriesModel.type = 'series.pictorialBar';\n  PictorialBarSeriesModel.dependencies = ['grid'];\n  PictorialBarSeriesModel.defaultOption = inheritDefaultOption(BaseBarSeriesModel.defaultOption, {\n    symbol: 'circle',\n    symbolSize: null,\n    symbolRotate: null,\n    symbolPosition: null,\n    symbolOffset: null,\n    symbolMargin: null,\n    symbolRepeat: false,\n    symbolRepeatDirection: 'end',\n    symbolClip: false,\n    symbolBoundingData: null,\n    symbolPatternSize: 400,\n    barGap: '-100%',\n    // Pictorial bar do not clip by default because in many cases\n    // xAxis and yAxis are not displayed and it's expected not to clip\n    clip: false,\n    // z can be set in data item, which is z2 actually.\n    // Disable progressive\n    progressive: 0,\n    emphasis: {\n      // By default pictorialBar do not hover scale. Hover scale is not suitable\n      // for the case that both has foreground and background.\n      scale: false\n    },\n    select: {\n      itemStyle: {\n        borderColor: '#212121'\n      }\n    }\n  });\n  return PictorialBarSeriesModel;\n}(BaseBarSeriesModel);\nexport default PictorialBarSeriesModel;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;;;;AACA,IAAI,0BAA0B,WAAW,GAAE,SAAU,MAAM;IACzD,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE,yBAAyB;IACnC,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,wBAAwB,IAAI;QACzC,MAAM,eAAe,GAAG;QACxB,MAAM,aAAa,GAAG;QACtB,OAAO;IACT;IACA,wBAAwB,SAAS,CAAC,cAAc,GAAG,SAAU,MAAM;QACjE,iBAAiB;QACjB,OAAO,KAAK,GAAG;QACf,OAAO,OAAO,SAAS,CAAC,cAAc,CAAC,KAAK,CAAC,IAAI,EAAE;IACrD;IACA,wBAAwB,IAAI,GAAG;IAC/B,wBAAwB,YAAY,GAAG;QAAC;KAAO;IAC/C,wBAAwB,aAAa,GAAG,CAAA,GAAA,mJAAA,CAAA,uBAAoB,AAAD,EAAE,+JAAA,CAAA,UAAkB,CAAC,aAAa,EAAE;QAC7F,QAAQ;QACR,YAAY;QACZ,cAAc;QACd,gBAAgB;QAChB,cAAc;QACd,cAAc;QACd,cAAc;QACd,uBAAuB;QACvB,YAAY;QACZ,oBAAoB;QACpB,mBAAmB;QACnB,QAAQ;QACR,6DAA6D;QAC7D,kEAAkE;QAClE,MAAM;QACN,mDAAmD;QACnD,sBAAsB;QACtB,aAAa;QACb,UAAU;YACR,0EAA0E;YAC1E,wDAAwD;YACxD,OAAO;QACT;QACA,QAAQ;YACN,WAAW;gBACT,aAAa;YACf;QACF;IACF;IACA,OAAO;AACT,EAAE,+JAAA,CAAA,UAAkB;uCACL", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/chart/bar/installPictorialBar.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport PictorialBarView from './PictorialBarView.js';\nimport PictorialBarSeriesModel from './PictorialBarSeries.js';\nimport { createProgressiveLayout, layout } from '../../layout/barGrid.js';\nimport { curry } from 'zrender/lib/core/util.js';\nexport function install(registers) {\n  registers.registerChartView(PictorialBarView);\n  registers.registerSeriesModel(PictorialBarSeriesModel);\n  registers.registerLayout(registers.PRIORITY.VISUAL.LAYOUT, curry(layout, 'pictorialBar'));\n  // Do layout after other overall layout, which can prepare some information.\n  registers.registerLayout(registers.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT, createProgressiveLayout('pictorialBar'));\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;;;;;AACO,SAAS,QAAQ,SAAS;IAC/B,UAAU,iBAAiB,CAAC,kKAAA,CAAA,UAAgB;IAC5C,UAAU,mBAAmB,CAAC,oKAAA,CAAA,UAAuB;IACrD,UAAU,cAAc,CAAC,UAAU,QAAQ,CAAC,MAAM,CAAC,MAAM,EAAE,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE,mJAAA,CAAA,SAAM,EAAE;IACzE,4EAA4E;IAC5E,UAAU,cAAc,CAAC,UAAU,QAAQ,CAAC,MAAM,CAAC,kBAAkB,EAAE,CAAA,GAAA,mJAAA,CAAA,0BAAuB,AAAD,EAAE;AACjG", "ignoreList": [0], "debugId": null}}]}