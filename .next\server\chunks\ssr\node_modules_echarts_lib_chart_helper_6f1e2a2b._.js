module.exports = {

"[project]/node_modules/echarts/lib/chart/helper/createRenderPlanner.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "default": (()=>createRenderPlanner)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$model$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/util/model.js [app-ssr] (ecmascript)");
;
function createRenderPlanner() {
    var inner = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$model$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["makeInner"])();
    return function(seriesModel) {
        var fields = inner(seriesModel);
        var pipelineContext = seriesModel.pipelineContext;
        var originalLarge = !!fields.large;
        var originalProgressive = !!fields.progressiveRender;
        // FIXME: if the planner works on a filtered series, `pipelineContext` does not
        // exists. See #11611 . Probably we need to modify this structure, see the comment
        // on `performRawSeries` in `Schedular.js`.
        var large = fields.large = !!(pipelineContext && pipelineContext.large);
        var progressive = fields.progressiveRender = !!(pipelineContext && pipelineContext.progressiveRender);
        return !!(originalLarge !== large || originalProgressive !== progressive) && 'reset';
    };
}
}}),
"[project]/node_modules/echarts/lib/chart/helper/createSeriesData.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/core/util.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$SeriesData$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/data/SeriesData.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$createDimensions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/data/helper/createDimensions.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$dimensionHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/data/helper/dimensionHelper.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$model$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/util/model.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$core$2f$CoordinateSystem$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/core/CoordinateSystem.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$model$2f$referHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/model/referHelper.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$Source$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/data/Source.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$dataStackHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/data/helper/dataStackHelper.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$sourceHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/data/helper/sourceHelper.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/util/types.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
;
function getCoordSysDimDefs(seriesModel, coordSysInfo) {
    var coordSysName = seriesModel.get('coordinateSystem');
    var registeredCoordSys = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$core$2f$CoordinateSystem$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(coordSysName);
    var coordSysDimDefs;
    if (coordSysInfo && coordSysInfo.coordSysDims) {
        coordSysDimDefs = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["map"])(coordSysInfo.coordSysDims, function(dim) {
            var dimInfo = {
                name: dim
            };
            var axisModel = coordSysInfo.axisMap.get(dim);
            if (axisModel) {
                var axisType = axisModel.get('type');
                dimInfo.type = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$dimensionHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDimensionTypeByAxis"])(axisType);
            }
            return dimInfo;
        });
    }
    if (!coordSysDimDefs) {
        // Get dimensions from registered coordinate system
        coordSysDimDefs = registeredCoordSys && (registeredCoordSys.getDimensionsInfo ? registeredCoordSys.getDimensionsInfo() : registeredCoordSys.dimensions.slice()) || [
            'x',
            'y'
        ];
    }
    return coordSysDimDefs;
}
function injectOrdinalMeta(dimInfoList, createInvertedIndices, coordSysInfo) {
    var firstCategoryDimIndex;
    var hasNameEncode;
    coordSysInfo && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(dimInfoList, function(dimInfo, dimIndex) {
        var coordDim = dimInfo.coordDim;
        var categoryAxisModel = coordSysInfo.categoryAxisMap.get(coordDim);
        if (categoryAxisModel) {
            if (firstCategoryDimIndex == null) {
                firstCategoryDimIndex = dimIndex;
            }
            dimInfo.ordinalMeta = categoryAxisModel.getOrdinalMeta();
            if (createInvertedIndices) {
                dimInfo.createInvertedIndices = true;
            }
        }
        if (dimInfo.otherDims.itemName != null) {
            hasNameEncode = true;
        }
    });
    if (!hasNameEncode && firstCategoryDimIndex != null) {
        dimInfoList[firstCategoryDimIndex].otherDims.itemName = 0;
    }
    return firstCategoryDimIndex;
}
/**
 * Caution: there are side effects to `sourceManager` in this method.
 * Should better only be called in `Series['getInitialData']`.
 */ function createSeriesData(sourceRaw, seriesModel, opt) {
    opt = opt || {};
    var sourceManager = seriesModel.getSourceManager();
    var source;
    var isOriginalSource = false;
    if (sourceRaw) {
        isOriginalSource = true;
        source = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$Source$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSourceFromSeriesDataOption"])(sourceRaw);
    } else {
        source = sourceManager.getSource();
        // Is series.data. not dataset.
        isOriginalSource = source.sourceFormat === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SOURCE_FORMAT_ORIGINAL"];
    }
    var coordSysInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$model$2f$referHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getCoordSysInfoBySeries"])(seriesModel);
    var coordSysDimDefs = getCoordSysDimDefs(seriesModel, coordSysInfo);
    var useEncodeDefaulter = opt.useEncodeDefaulter;
    var encodeDefaulter = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"])(useEncodeDefaulter) ? useEncodeDefaulter : useEncodeDefaulter ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["curry"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$sourceHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["makeSeriesEncodeForAxisCoordSys"], coordSysDimDefs, seriesModel) : null;
    var createDimensionOptions = {
        coordDimensions: coordSysDimDefs,
        generateCoord: opt.generateCoord,
        encodeDefine: seriesModel.getEncode(),
        encodeDefaulter: encodeDefaulter,
        canOmitUnusedDimensions: !isOriginalSource
    };
    var schema = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$createDimensions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(source, createDimensionOptions);
    var firstCategoryDimIndex = injectOrdinalMeta(schema.dimensions, opt.createInvertedIndices, coordSysInfo);
    var store = !isOriginalSource ? sourceManager.getSharedDataStore(schema) : null;
    var stackCalculationInfo = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$dataStackHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["enableDataStack"])(seriesModel, {
        schema: schema,
        store: store
    });
    var data = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$SeriesData$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](schema, seriesModel);
    data.setCalculationInfo(stackCalculationInfo);
    var dimValueGetter = firstCategoryDimIndex != null && isNeedCompleteOrdinalData(source) ? function(itemOpt, dimName, dataIndex, dimIndex) {
        // Use dataIndex as ordinal value in categoryAxis
        return dimIndex === firstCategoryDimIndex ? dataIndex : this.defaultDimValueGetter(itemOpt, dimName, dataIndex, dimIndex);
    } : null;
    data.hasItemOption = false;
    data.initData(// Try to reuse the data store in sourceManager if using dataset.
    isOriginalSource ? source : store, null, dimValueGetter);
    return data;
}
function isNeedCompleteOrdinalData(source) {
    if (source.sourceFormat === __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$types$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SOURCE_FORMAT_ORIGINAL"]) {
        var sampleItem = firstDataNotNull(source.data || []);
        return !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isArray"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$model$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDataItemValue"])(sampleItem));
    }
}
function firstDataNotNull(arr) {
    var i = 0;
    while(i < arr.length && arr[i] == null){
        i++;
    }
    return arr[i];
}
const __TURBOPACK__default__export__ = createSeriesData;
}}),
"[project]/node_modules/echarts/lib/chart/helper/labelHelper.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "getDefaultInterpolatedLabel": (()=>getDefaultInterpolatedLabel),
    "getDefaultLabel": (()=>getDefaultLabel)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$dataProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/data/helper/dataProvider.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/core/util.js [app-ssr] (ecmascript)");
;
;
function getDefaultLabel(data, dataIndex) {
    var labelDims = data.mapDimensionsAll('defaultedLabel');
    var len = labelDims.length;
    // Simple optimization (in lots of cases, label dims length is 1)
    if (len === 1) {
        var rawVal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$dataProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["retrieveRawValue"])(data, dataIndex, labelDims[0]);
        return rawVal != null ? rawVal + '' : null;
    } else if (len) {
        var vals = [];
        for(var i = 0; i < labelDims.length; i++){
            vals.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$dataProvider$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["retrieveRawValue"])(data, dataIndex, labelDims[i]));
        }
        return vals.join(' ');
    }
}
function getDefaultInterpolatedLabel(data, interpolatedValue) {
    var labelDims = data.mapDimensionsAll('defaultedLabel');
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isArray"])(interpolatedValue)) {
        return interpolatedValue + '';
    }
    var vals = [];
    for(var i = 0; i < labelDims.length; i++){
        var dimIndex = data.getDimensionIndex(labelDims[i]);
        if (dimIndex >= 0) {
            vals.push(interpolatedValue[dimIndex]);
        }
    }
    return vals.join(' ');
}
}}),
"[project]/node_modules/echarts/lib/chart/helper/Symbol.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/node_modules/tslib/tslib.es6.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/util/symbol.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$animation$2f$basicTransition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/animation/basicTransition.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Group$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Group$3e$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/graphic/Group.js [app-ssr] (ecmascript) <export default as Group>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$innerStore$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/util/innerStore.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$states$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/util/states.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$chart$2f$helper$2f$labelHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/chart/helper/labelHelper.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/core/util.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$label$2f$labelStyle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/label/labelStyle.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/graphic/Image.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
;
var Symbol = /** @class */ function(_super) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__extends"])(Symbol, _super);
    function Symbol(data, idx, seriesScope, opts) {
        var _this = _super.call(this) || this;
        _this.updateData(data, idx, seriesScope, opts);
        return _this;
    }
    Symbol.prototype._createSymbol = function(symbolType, data, idx, symbolSize, keepAspect) {
        // Remove paths created before
        this.removeAll();
        // let symbolPath = createSymbol(
        //     symbolType, -0.5, -0.5, 1, 1, color
        // );
        // If width/height are set too small (e.g., set to 1) on ios10
        // and macOS Sierra, a circle stroke become a rect, no matter what
        // the scale is set. So we set width/height as 2. See #4150.
        var symbolPath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSymbol"])(symbolType, -1, -1, 2, 2, null, keepAspect);
        symbolPath.attr({
            z2: 100,
            culling: true,
            scaleX: symbolSize[0] / 2,
            scaleY: symbolSize[1] / 2
        });
        // Rewrite drift method
        symbolPath.drift = driftSymbol;
        this._symbolType = symbolType;
        this.add(symbolPath);
    };
    /**
   * Stop animation
   * @param {boolean} toLastFrame
   */ Symbol.prototype.stopSymbolAnimation = function(toLastFrame) {
        this.childAt(0).stopAnimation(null, toLastFrame);
    };
    Symbol.prototype.getSymbolType = function() {
        return this._symbolType;
    };
    /**
   * FIXME:
   * Caution: This method breaks the encapsulation of this module,
   * but it indeed brings convenience. So do not use the method
   * unless you detailedly know all the implements of `Symbol`,
   * especially animation.
   *
   * Get symbol path element.
   */ Symbol.prototype.getSymbolPath = function() {
        return this.childAt(0);
    };
    /**
   * Highlight symbol
   */ Symbol.prototype.highlight = function() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$states$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["enterEmphasis"])(this.childAt(0));
    };
    /**
   * Downplay symbol
   */ Symbol.prototype.downplay = function() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$states$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["leaveEmphasis"])(this.childAt(0));
    };
    /**
   * @param {number} zlevel
   * @param {number} z
   */ Symbol.prototype.setZ = function(zlevel, z) {
        var symbolPath = this.childAt(0);
        symbolPath.zlevel = zlevel;
        symbolPath.z = z;
    };
    Symbol.prototype.setDraggable = function(draggable, hasCursorOption) {
        var symbolPath = this.childAt(0);
        symbolPath.draggable = draggable;
        symbolPath.cursor = !hasCursorOption && draggable ? 'move' : symbolPath.cursor;
    };
    /**
   * Update symbol properties
   */ Symbol.prototype.updateData = function(data, idx, seriesScope, opts) {
        this.silent = false;
        var symbolType = data.getItemVisual(idx, 'symbol') || 'circle';
        var seriesModel = data.hostModel;
        var symbolSize = Symbol.getSymbolSize(data, idx);
        var isInit = symbolType !== this._symbolType;
        var disableAnimation = opts && opts.disableAnimation;
        if (isInit) {
            var keepAspect = data.getItemVisual(idx, 'symbolKeepAspect');
            this._createSymbol(symbolType, data, idx, symbolSize, keepAspect);
        } else {
            var symbolPath = this.childAt(0);
            symbolPath.silent = false;
            var target = {
                scaleX: symbolSize[0] / 2,
                scaleY: symbolSize[1] / 2
            };
            disableAnimation ? symbolPath.attr(target) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$animation$2f$basicTransition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateProps"])(symbolPath, target, seriesModel, idx);
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$animation$2f$basicTransition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["saveOldStyle"])(symbolPath);
        }
        this._updateCommon(data, idx, symbolSize, seriesScope, opts);
        if (isInit) {
            var symbolPath = this.childAt(0);
            if (!disableAnimation) {
                var target = {
                    scaleX: this._sizeX,
                    scaleY: this._sizeY,
                    style: {
                        // Always fadeIn. Because it has fadeOut animation when symbol is removed..
                        opacity: symbolPath.style.opacity
                    }
                };
                symbolPath.scaleX = symbolPath.scaleY = 0;
                symbolPath.style.opacity = 0;
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$animation$2f$basicTransition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["initProps"])(symbolPath, target, seriesModel, idx);
            }
        }
        if (disableAnimation) {
            // Must stop leave transition manually if don't call initProps or updateProps.
            this.childAt(0).stopAnimation('leave');
        }
    };
    Symbol.prototype._updateCommon = function(data, idx, symbolSize, seriesScope, opts) {
        var symbolPath = this.childAt(0);
        var seriesModel = data.hostModel;
        var emphasisItemStyle;
        var blurItemStyle;
        var selectItemStyle;
        var focus;
        var blurScope;
        var emphasisDisabled;
        var labelStatesModels;
        var hoverScale;
        var cursorStyle;
        if (seriesScope) {
            emphasisItemStyle = seriesScope.emphasisItemStyle;
            blurItemStyle = seriesScope.blurItemStyle;
            selectItemStyle = seriesScope.selectItemStyle;
            focus = seriesScope.focus;
            blurScope = seriesScope.blurScope;
            labelStatesModels = seriesScope.labelStatesModels;
            hoverScale = seriesScope.hoverScale;
            cursorStyle = seriesScope.cursorStyle;
            emphasisDisabled = seriesScope.emphasisDisabled;
        }
        if (!seriesScope || data.hasItemOption) {
            var itemModel = seriesScope && seriesScope.itemModel ? seriesScope.itemModel : data.getItemModel(idx);
            var emphasisModel = itemModel.getModel('emphasis');
            emphasisItemStyle = emphasisModel.getModel('itemStyle').getItemStyle();
            selectItemStyle = itemModel.getModel([
                'select',
                'itemStyle'
            ]).getItemStyle();
            blurItemStyle = itemModel.getModel([
                'blur',
                'itemStyle'
            ]).getItemStyle();
            focus = emphasisModel.get('focus');
            blurScope = emphasisModel.get('blurScope');
            emphasisDisabled = emphasisModel.get('disabled');
            labelStatesModels = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$label$2f$labelStyle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getLabelStatesModels"])(itemModel);
            hoverScale = emphasisModel.getShallow('scale');
            cursorStyle = itemModel.getShallow('cursor');
        }
        var symbolRotate = data.getItemVisual(idx, 'symbolRotate');
        symbolPath.attr('rotation', (symbolRotate || 0) * Math.PI / 180 || 0);
        var symbolOffset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeSymbolOffset"])(data.getItemVisual(idx, 'symbolOffset'), symbolSize);
        if (symbolOffset) {
            symbolPath.x = symbolOffset[0];
            symbolPath.y = symbolOffset[1];
        }
        cursorStyle && symbolPath.attr('cursor', cursorStyle);
        var symbolStyle = data.getItemVisual(idx, 'style');
        var visualColor = symbolStyle.fill;
        if (symbolPath instanceof __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Image$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]) {
            var pathStyle = symbolPath.style;
            symbolPath.useStyle((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({
                // TODO other properties like x, y ?
                image: pathStyle.image,
                x: pathStyle.x,
                y: pathStyle.y,
                width: pathStyle.width,
                height: pathStyle.height
            }, symbolStyle));
        } else {
            if (symbolPath.__isEmptyBrush) {
                // fill and stroke will be swapped if it's empty.
                // So we cloned a new style to avoid it affecting the original style in visual storage.
                // TODO Better implementation. No empty logic!
                symbolPath.useStyle((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, symbolStyle));
            } else {
                symbolPath.useStyle(symbolStyle);
            }
            // Disable decal because symbol scale will been applied on the decal.
            symbolPath.style.decal = null;
            symbolPath.setColor(visualColor, opts && opts.symbolInnerColor);
            symbolPath.style.strokeNoScale = true;
        }
        var liftZ = data.getItemVisual(idx, 'liftZ');
        var z2Origin = this._z2;
        if (liftZ != null) {
            if (z2Origin == null) {
                this._z2 = symbolPath.z2;
                symbolPath.z2 += liftZ;
            }
        } else if (z2Origin != null) {
            symbolPath.z2 = z2Origin;
            this._z2 = null;
        }
        var useNameLabel = opts && opts.useNameLabel;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$label$2f$labelStyle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setLabelStyle"])(symbolPath, labelStatesModels, {
            labelFetcher: seriesModel,
            labelDataIndex: idx,
            defaultText: getLabelDefaultText,
            inheritColor: visualColor,
            defaultOpacity: symbolStyle.opacity
        });
        // Do not execute util needed.
        function getLabelDefaultText(idx) {
            return useNameLabel ? data.getName(idx) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$chart$2f$helper$2f$labelHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDefaultLabel"])(data, idx);
        }
        this._sizeX = symbolSize[0] / 2;
        this._sizeY = symbolSize[1] / 2;
        var emphasisState = symbolPath.ensureState('emphasis');
        emphasisState.style = emphasisItemStyle;
        symbolPath.ensureState('select').style = selectItemStyle;
        symbolPath.ensureState('blur').style = blurItemStyle;
        // null / undefined / true means to use default strategy.
        // 0 / false / negative number / NaN / Infinity means no scale.
        var scaleRatio = hoverScale == null || hoverScale === true ? Math.max(1.1, 3 / this._sizeY) : isFinite(hoverScale) && hoverScale > 0 ? +hoverScale : 1;
        // always set scale to allow resetting
        emphasisState.scaleX = this._sizeX * scaleRatio;
        emphasisState.scaleY = this._sizeY * scaleRatio;
        this.setSymbolScale(1);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$states$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toggleHoverEmphasis"])(this, focus, blurScope, emphasisDisabled);
    };
    Symbol.prototype.setSymbolScale = function(scale) {
        this.scaleX = this.scaleY = scale;
    };
    Symbol.prototype.fadeOut = function(cb, seriesModel, opt) {
        var symbolPath = this.childAt(0);
        var dataIndex = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$innerStore$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getECData"])(this).dataIndex;
        var animationOpt = opt && opt.animation;
        // Avoid mistaken hover when fading out
        this.silent = symbolPath.silent = true;
        // Not show text when animating
        if (opt && opt.fadeLabel) {
            var textContent = symbolPath.getTextContent();
            if (textContent) {
                (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$animation$2f$basicTransition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["removeElement"])(textContent, {
                    style: {
                        opacity: 0
                    }
                }, seriesModel, {
                    dataIndex: dataIndex,
                    removeOpt: animationOpt,
                    cb: function() {
                        symbolPath.removeTextContent();
                    }
                });
            }
        } else {
            symbolPath.removeTextContent();
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$animation$2f$basicTransition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["removeElement"])(symbolPath, {
            style: {
                opacity: 0
            },
            scaleX: 0,
            scaleY: 0
        }, seriesModel, {
            dataIndex: dataIndex,
            cb: cb,
            removeOpt: animationOpt
        });
    };
    Symbol.getSymbolSize = function(data, idx) {
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeSymbolSize"])(data.getItemVisual(idx, 'symbolSize'));
    };
    return Symbol;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Group$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Group$3e$__["Group"]);
function driftSymbol(dx, dy) {
    this.parent.drift(dx, dy);
}
const __TURBOPACK__default__export__ = Symbol;
}}),
"[project]/node_modules/echarts/lib/chart/helper/SymbolDraw.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$animation$2f$basicTransition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/animation/basicTransition.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$graphic$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/util/graphic.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Group$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Group$3e$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/graphic/Group.js [app-ssr] (ecmascript) <export default as Group>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$chart$2f$helper$2f$Symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/chart/helper/Symbol.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/core/util.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$label$2f$labelStyle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/label/labelStyle.js [app-ssr] (ecmascript)");
;
;
;
;
function symbolNeedsDraw(data, point, idx, opt) {
    return point && !isNaN(point[0]) && !isNaN(point[1]) && !(opt.isIgnore && opt.isIgnore(idx)) && !(opt.clipShape && !opt.clipShape.contain(point[0], point[1])) && data.getItemVisual(idx, 'symbol') !== 'none';
}
function normalizeUpdateOpt(opt) {
    if (opt != null && !(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isObject"])(opt)) {
        opt = {
            isIgnore: opt
        };
    }
    return opt || {};
}
function makeSeriesScope(data) {
    var seriesModel = data.hostModel;
    var emphasisModel = seriesModel.getModel('emphasis');
    return {
        emphasisItemStyle: emphasisModel.getModel('itemStyle').getItemStyle(),
        blurItemStyle: seriesModel.getModel([
            'blur',
            'itemStyle'
        ]).getItemStyle(),
        selectItemStyle: seriesModel.getModel([
            'select',
            'itemStyle'
        ]).getItemStyle(),
        focus: emphasisModel.get('focus'),
        blurScope: emphasisModel.get('blurScope'),
        emphasisDisabled: emphasisModel.get('disabled'),
        hoverScale: emphasisModel.get('scale'),
        labelStatesModels: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$label$2f$labelStyle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getLabelStatesModels"])(seriesModel),
        cursorStyle: seriesModel.get('cursor')
    };
}
var SymbolDraw = /** @class */ function() {
    function SymbolDraw(SymbolCtor) {
        this.group = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Group$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Group$3e$__["Group"]();
        this._SymbolCtor = SymbolCtor || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$chart$2f$helper$2f$Symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
    }
    /**
   * Update symbols draw by new data
   */ SymbolDraw.prototype.updateData = function(data, opt) {
        // Remove progressive els.
        this._progressiveEls = null;
        opt = normalizeUpdateOpt(opt);
        var group = this.group;
        var seriesModel = data.hostModel;
        var oldData = this._data;
        var SymbolCtor = this._SymbolCtor;
        var disableAnimation = opt.disableAnimation;
        var seriesScope = makeSeriesScope(data);
        var symbolUpdateOpt = {
            disableAnimation: disableAnimation
        };
        var getSymbolPoint = opt.getSymbolPoint || function(idx) {
            return data.getItemLayout(idx);
        };
        // There is no oldLineData only when first rendering or switching from
        // stream mode to normal mode, where previous elements should be removed.
        if (!oldData) {
            group.removeAll();
        }
        data.diff(oldData).add(function(newIdx) {
            var point = getSymbolPoint(newIdx);
            if (symbolNeedsDraw(data, point, newIdx, opt)) {
                var symbolEl = new SymbolCtor(data, newIdx, seriesScope, symbolUpdateOpt);
                symbolEl.setPosition(point);
                data.setItemGraphicEl(newIdx, symbolEl);
                group.add(symbolEl);
            }
        }).update(function(newIdx, oldIdx) {
            var symbolEl = oldData.getItemGraphicEl(oldIdx);
            var point = getSymbolPoint(newIdx);
            if (!symbolNeedsDraw(data, point, newIdx, opt)) {
                group.remove(symbolEl);
                return;
            }
            var newSymbolType = data.getItemVisual(newIdx, 'symbol') || 'circle';
            var oldSymbolType = symbolEl && symbolEl.getSymbolType && symbolEl.getSymbolType();
            if (!symbolEl || oldSymbolType && oldSymbolType !== newSymbolType) {
                group.remove(symbolEl);
                symbolEl = new SymbolCtor(data, newIdx, seriesScope, symbolUpdateOpt);
                symbolEl.setPosition(point);
            } else {
                symbolEl.updateData(data, newIdx, seriesScope, symbolUpdateOpt);
                var target = {
                    x: point[0],
                    y: point[1]
                };
                disableAnimation ? symbolEl.attr(target) : (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$animation$2f$basicTransition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateProps"])(symbolEl, target, seriesModel);
            }
            // Add back
            group.add(symbolEl);
            data.setItemGraphicEl(newIdx, symbolEl);
        }).remove(function(oldIdx) {
            var el = oldData.getItemGraphicEl(oldIdx);
            el && el.fadeOut(function() {
                group.remove(el);
            }, seriesModel);
        }).execute();
        this._getSymbolPoint = getSymbolPoint;
        this._data = data;
    };
    ;
    SymbolDraw.prototype.updateLayout = function() {
        var _this = this;
        var data = this._data;
        if (data) {
            // Not use animation
            data.eachItemGraphicEl(function(el, idx) {
                var point = _this._getSymbolPoint(idx);
                el.setPosition(point);
                el.markRedraw();
            });
        }
    };
    ;
    SymbolDraw.prototype.incrementalPrepareUpdate = function(data) {
        this._seriesScope = makeSeriesScope(data);
        this._data = null;
        this.group.removeAll();
    };
    ;
    /**
   * Update symbols draw by new data
   */ SymbolDraw.prototype.incrementalUpdate = function(taskParams, data, opt) {
        // Clear
        this._progressiveEls = [];
        opt = normalizeUpdateOpt(opt);
        function updateIncrementalAndHover(el) {
            if (!el.isGroup) {
                el.incremental = true;
                el.ensureState('emphasis').hoverLayer = true;
            }
        }
        for(var idx = taskParams.start; idx < taskParams.end; idx++){
            var point = data.getItemLayout(idx);
            if (symbolNeedsDraw(data, point, idx, opt)) {
                var el = new this._SymbolCtor(data, idx, this._seriesScope);
                el.traverse(updateIncrementalAndHover);
                el.setPosition(point);
                this.group.add(el);
                data.setItemGraphicEl(idx, el);
                this._progressiveEls.push(el);
            }
        }
    };
    ;
    SymbolDraw.prototype.eachRendered = function(cb) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$graphic$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["traverseElements"])(this._progressiveEls || this.group, cb);
    };
    SymbolDraw.prototype.remove = function(enableAnimation) {
        var group = this.group;
        var data = this._data;
        // Incremental model do not have this._data.
        if (data && enableAnimation) {
            data.eachItemGraphicEl(function(el) {
                el.fadeOut(function() {
                    group.remove(el);
                }, data.hostModel);
            });
        } else {
            group.removeAll();
        }
    };
    ;
    return SymbolDraw;
}();
const __TURBOPACK__default__export__ = SymbolDraw;
}}),
"[project]/node_modules/echarts/lib/chart/helper/createClipPathFromCoordSys.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "createClipPath": (()=>createClipPath),
    "createGridClipPath": (()=>createGridClipPath),
    "createPolarClipPath": (()=>createPolarClipPath)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$shape$2f$Rect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Rect$3e$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/graphic/shape/Rect.js [app-ssr] (ecmascript) <export default as Rect>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$animation$2f$basicTransition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/animation/basicTransition.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$shape$2f$Sector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sector$3e$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/graphic/shape/Sector.js [app-ssr] (ecmascript) <export default as Sector>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$number$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/util/number.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/core/util.js [app-ssr] (ecmascript)");
;
;
;
function createGridClipPath(cartesian, hasAnimation, seriesModel, done, during) {
    var rect = cartesian.getArea();
    var x = rect.x;
    var y = rect.y;
    var width = rect.width;
    var height = rect.height;
    var lineWidth = seriesModel.get([
        'lineStyle',
        'width'
    ]) || 0;
    // Expand the clip path a bit to avoid the border is clipped and looks thinner
    x -= lineWidth / 2;
    y -= lineWidth / 2;
    width += lineWidth;
    height += lineWidth;
    // fix: https://github.com/apache/incubator-echarts/issues/11369
    width = Math.ceil(width);
    if (x !== Math.floor(x)) {
        x = Math.floor(x);
        // if no extra 1px on `width`, it will still be clipped since `x` is floored
        width++;
    }
    var clipPath = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$shape$2f$Rect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Rect$3e$__["Rect"]({
        shape: {
            x: x,
            y: y,
            width: width,
            height: height
        }
    });
    if (hasAnimation) {
        var baseAxis = cartesian.getBaseAxis();
        var isHorizontal = baseAxis.isHorizontal();
        var isAxisInversed = baseAxis.inverse;
        if (isHorizontal) {
            if (isAxisInversed) {
                clipPath.shape.x += width;
            }
            clipPath.shape.width = 0;
        } else {
            if (!isAxisInversed) {
                clipPath.shape.y += height;
            }
            clipPath.shape.height = 0;
        }
        var duringCb = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"])(during) ? function(percent) {
            during(percent, clipPath);
        } : null;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$animation$2f$basicTransition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["initProps"])(clipPath, {
            shape: {
                width: width,
                height: height,
                x: x,
                y: y
            }
        }, seriesModel, null, done, duringCb);
    }
    return clipPath;
}
function createPolarClipPath(polar, hasAnimation, seriesModel) {
    var sectorArea = polar.getArea();
    // Avoid float number rounding error for symbol on the edge of axis extent.
    var r0 = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$number$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["round"])(sectorArea.r0, 1);
    var r = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$number$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["round"])(sectorArea.r, 1);
    var clipPath = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$shape$2f$Sector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Sector$3e$__["Sector"]({
        shape: {
            cx: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$number$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["round"])(polar.cx, 1),
            cy: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$number$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["round"])(polar.cy, 1),
            r0: r0,
            r: r,
            startAngle: sectorArea.startAngle,
            endAngle: sectorArea.endAngle,
            clockwise: sectorArea.clockwise
        }
    });
    if (hasAnimation) {
        var isRadial = polar.getBaseAxis().dim === 'angle';
        if (isRadial) {
            clipPath.shape.endAngle = sectorArea.startAngle;
        } else {
            clipPath.shape.r = r0;
        }
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$animation$2f$basicTransition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["initProps"])(clipPath, {
            shape: {
                endAngle: sectorArea.endAngle,
                r: r
            }
        }, seriesModel);
    }
    return clipPath;
}
function createClipPath(coordSys, hasAnimation, seriesModel, done, during) {
    if (!coordSys) {
        return null;
    } else if (coordSys.type === 'polar') {
        return createPolarClipPath(coordSys, hasAnimation, seriesModel);
    } else if (coordSys.type === 'cartesian2d') {
        return createGridClipPath(coordSys, hasAnimation, seriesModel, done, during);
    }
    return null;
}
;
}}),
"[project]/node_modules/echarts/lib/chart/helper/sectorHelper.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "getSectorCornerRadius": (()=>getSectorCornerRadius)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/core/util.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$contain$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/contain/text.js [app-ssr] (ecmascript)");
;
;
function getSectorCornerRadius(model, shape, zeroIfNull) {
    var cornerRadius = model.get('borderRadius');
    if (cornerRadius == null) {
        return zeroIfNull ? {
            cornerRadius: 0
        } : null;
    }
    if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isArray"])(cornerRadius)) {
        cornerRadius = [
            cornerRadius,
            cornerRadius,
            cornerRadius,
            cornerRadius
        ];
    }
    var dr = Math.abs(shape.r || 0 - shape.r0 || 0);
    return {
        cornerRadius: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["map"])(cornerRadius, function(cr) {
            return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$contain$2f$text$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["parsePercent"])(cr, dr);
        })
    };
}
}}),
"[project]/node_modules/echarts/lib/chart/helper/createSeriesDataSimply.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "default": (()=>createSeriesDataSimply)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$createDimensions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/data/helper/createDimensions.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$SeriesData$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/data/SeriesData.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/core/util.js [app-ssr] (ecmascript)");
;
;
;
function createSeriesDataSimply(seriesModel, opt, nameList) {
    opt = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isArray"])(opt) && {
        coordDimensions: opt
    } || (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({
        encodeDefine: seriesModel.getEncode()
    }, opt);
    var source = seriesModel.getSource();
    var dimensions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$createDimensions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(source, opt).dimensions;
    var list = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$SeriesData$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](dimensions, seriesModel);
    list.initData(source, nameList);
    return list;
}
}}),
"[project]/node_modules/echarts/lib/chart/helper/LargeSymbolDraw.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/node_modules/tslib/tslib.es6.js [app-ssr] (ecmascript)");
/* global Float32Array */ // TODO Batch by color
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$BoundingRect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BoundingRect$3e$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/core/BoundingRect.js [app-ssr] (ecmascript) <export default as BoundingRect>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Path$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Path$3e$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/graphic/Path.js [app-ssr] (ecmascript) <export default as Path>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Group$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Group$3e$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/graphic/Group.js [app-ssr] (ecmascript) <export default as Group>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/util/symbol.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$innerStore$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/util/innerStore.js [app-ssr] (ecmascript)");
;
;
;
;
var BOOST_SIZE_THRESHOLD = 4;
var LargeSymbolPathShape = /** @class */ function() {
    function LargeSymbolPathShape() {}
    return LargeSymbolPathShape;
}();
var LargeSymbolPath = /** @class */ function(_super) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__extends"])(LargeSymbolPath, _super);
    function LargeSymbolPath(opts) {
        var _this = _super.call(this, opts) || this;
        _this._off = 0;
        _this.hoverDataIdx = -1;
        return _this;
    }
    LargeSymbolPath.prototype.getDefaultShape = function() {
        return new LargeSymbolPathShape();
    };
    LargeSymbolPath.prototype.reset = function() {
        this.notClear = false;
        this._off = 0;
    };
    LargeSymbolPath.prototype.buildPath = function(path, shape) {
        var points = shape.points;
        var size = shape.size;
        var symbolProxy = this.symbolProxy;
        var symbolProxyShape = symbolProxy.shape;
        var ctx = path.getContext ? path.getContext() : path;
        var canBoost = ctx && size[0] < BOOST_SIZE_THRESHOLD;
        var softClipShape = this.softClipShape;
        var i;
        // Do draw in afterBrush.
        if (canBoost) {
            this._ctx = ctx;
            return;
        }
        this._ctx = null;
        for(i = this._off; i < points.length;){
            var x = points[i++];
            var y = points[i++];
            if (isNaN(x) || isNaN(y)) {
                continue;
            }
            if (softClipShape && !softClipShape.contain(x, y)) {
                continue;
            }
            symbolProxyShape.x = x - size[0] / 2;
            symbolProxyShape.y = y - size[1] / 2;
            symbolProxyShape.width = size[0];
            symbolProxyShape.height = size[1];
            symbolProxy.buildPath(path, symbolProxyShape, true);
        }
        if (this.incremental) {
            this._off = i;
            this.notClear = true;
        }
    };
    LargeSymbolPath.prototype.afterBrush = function() {
        var shape = this.shape;
        var points = shape.points;
        var size = shape.size;
        var ctx = this._ctx;
        var softClipShape = this.softClipShape;
        var i;
        if (!ctx) {
            return;
        }
        // PENDING If style or other canvas status changed?
        for(i = this._off; i < points.length;){
            var x = points[i++];
            var y = points[i++];
            if (isNaN(x) || isNaN(y)) {
                continue;
            }
            if (softClipShape && !softClipShape.contain(x, y)) {
                continue;
            }
            // fillRect is faster than building a rect path and draw.
            // And it support light globalCompositeOperation.
            ctx.fillRect(x - size[0] / 2, y - size[1] / 2, size[0], size[1]);
        }
        if (this.incremental) {
            this._off = i;
            this.notClear = true;
        }
    };
    LargeSymbolPath.prototype.findDataIndex = function(x, y) {
        // TODO ???
        // Consider transform
        var shape = this.shape;
        var points = shape.points;
        var size = shape.size;
        var w = Math.max(size[0], 4);
        var h = Math.max(size[1], 4);
        // Not consider transform
        // Treat each element as a rect
        // top down traverse
        for(var idx = points.length / 2 - 1; idx >= 0; idx--){
            var i = idx * 2;
            var x0 = points[i] - w / 2;
            var y0 = points[i + 1] - h / 2;
            if (x >= x0 && y >= y0 && x <= x0 + w && y <= y0 + h) {
                return idx;
            }
        }
        return -1;
    };
    LargeSymbolPath.prototype.contain = function(x, y) {
        var localPos = this.transformCoordToLocal(x, y);
        var rect = this.getBoundingRect();
        x = localPos[0];
        y = localPos[1];
        if (rect.contain(x, y)) {
            // Cache found data index.
            var dataIdx = this.hoverDataIdx = this.findDataIndex(x, y);
            return dataIdx >= 0;
        }
        this.hoverDataIdx = -1;
        return false;
    };
    LargeSymbolPath.prototype.getBoundingRect = function() {
        // Ignore stroke for large symbol draw.
        var rect = this._rect;
        if (!rect) {
            var shape = this.shape;
            var points = shape.points;
            var size = shape.size;
            var w = size[0];
            var h = size[1];
            var minX = Infinity;
            var minY = Infinity;
            var maxX = -Infinity;
            var maxY = -Infinity;
            for(var i = 0; i < points.length;){
                var x = points[i++];
                var y = points[i++];
                minX = Math.min(x, minX);
                maxX = Math.max(x, maxX);
                minY = Math.min(y, minY);
                maxY = Math.max(y, maxY);
            }
            rect = this._rect = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$BoundingRect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BoundingRect$3e$__["BoundingRect"](minX - w / 2, minY - h / 2, maxX - minX + w, maxY - minY + h);
        }
        return rect;
    };
    return LargeSymbolPath;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Path$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Path$3e$__["Path"]);
var LargeSymbolDraw = /** @class */ function() {
    function LargeSymbolDraw() {
        this.group = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Group$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Group$3e$__["Group"]();
    }
    /**
   * Update symbols draw by new data
   */ LargeSymbolDraw.prototype.updateData = function(data, opt) {
        this._clear();
        var symbolEl = this._create();
        symbolEl.setShape({
            points: data.getLayout('points')
        });
        this._setCommon(symbolEl, data, opt);
    };
    LargeSymbolDraw.prototype.updateLayout = function(data) {
        var points = data.getLayout('points');
        this.group.eachChild(function(child) {
            if (child.startIndex != null) {
                var len = (child.endIndex - child.startIndex) * 2;
                var byteOffset = child.startIndex * 4 * 2;
                points = new Float32Array(points.buffer, byteOffset, len);
            }
            child.setShape('points', points);
            // Reset draw cursor.
            child.reset();
        });
    };
    LargeSymbolDraw.prototype.incrementalPrepareUpdate = function(data) {
        this._clear();
    };
    LargeSymbolDraw.prototype.incrementalUpdate = function(taskParams, data, opt) {
        var lastAdded = this._newAdded[0];
        var points = data.getLayout('points');
        var oldPoints = lastAdded && lastAdded.shape.points;
        // Merging the exists. Each element has 1e4 points.
        // Consider the performance balance between too much elements and too much points in one shape(may affect hover optimization)
        if (oldPoints && oldPoints.length < 2e4) {
            var oldLen = oldPoints.length;
            var newPoints = new Float32Array(oldLen + points.length);
            // Concat two array
            newPoints.set(oldPoints);
            newPoints.set(points, oldLen);
            // Update endIndex
            lastAdded.endIndex = taskParams.end;
            lastAdded.setShape({
                points: newPoints
            });
        } else {
            // Clear
            this._newAdded = [];
            var symbolEl = this._create();
            symbolEl.startIndex = taskParams.start;
            symbolEl.endIndex = taskParams.end;
            symbolEl.incremental = true;
            symbolEl.setShape({
                points: points
            });
            this._setCommon(symbolEl, data, opt);
        }
    };
    LargeSymbolDraw.prototype.eachRendered = function(cb) {
        this._newAdded[0] && cb(this._newAdded[0]);
    };
    LargeSymbolDraw.prototype._create = function() {
        var symbolEl = new LargeSymbolPath({
            cursor: 'default'
        });
        symbolEl.ignoreCoarsePointer = true;
        this.group.add(symbolEl);
        this._newAdded.push(symbolEl);
        return symbolEl;
    };
    LargeSymbolDraw.prototype._setCommon = function(symbolEl, data, opt) {
        var hostModel = data.hostModel;
        opt = opt || {};
        var size = data.getVisual('symbolSize');
        symbolEl.setShape('size', size instanceof Array ? size : [
            size,
            size
        ]);
        symbolEl.softClipShape = opt.clipShape || null;
        // Create symbolProxy to build path for each data
        symbolEl.symbolProxy = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSymbol"])(data.getVisual('symbol'), 0, 0, 0, 0);
        // Use symbolProxy setColor method
        symbolEl.setColor = symbolEl.symbolProxy.setColor;
        var extrudeShadow = symbolEl.shape.size[0] < BOOST_SIZE_THRESHOLD;
        symbolEl.useStyle(// Draw shadow when doing fillRect is extremely slow.
        hostModel.getModel('itemStyle').getItemStyle(extrudeShadow ? [
            'color',
            'shadowBlur',
            'shadowColor'
        ] : [
            'color'
        ]));
        var globalStyle = data.getVisual('style');
        var visualColor = globalStyle && globalStyle.fill;
        if (visualColor) {
            symbolEl.setColor(visualColor);
        }
        var ecData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$innerStore$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getECData"])(symbolEl);
        // Enable tooltip
        // PENDING May have performance issue when path is extremely large
        ecData.seriesIndex = hostModel.seriesIndex;
        symbolEl.on('mousemove', function(e) {
            ecData.dataIndex = null;
            var dataIndex = symbolEl.hoverDataIdx;
            if (dataIndex >= 0) {
                // Provide dataIndex for tooltip
                ecData.dataIndex = dataIndex + (symbolEl.startIndex || 0);
            }
        });
    };
    LargeSymbolDraw.prototype.remove = function() {
        this._clear();
    };
    LargeSymbolDraw.prototype._clear = function() {
        this._newAdded = [];
        this.group.removeAll();
    };
    return LargeSymbolDraw;
}();
const __TURBOPACK__default__export__ = LargeSymbolDraw;
}}),
"[project]/node_modules/echarts/lib/chart/helper/treeHelper.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "aboveViewRoot": (()=>aboveViewRoot),
    "getPathToRoot": (()=>getPathToRoot),
    "retrieveTargetInfo": (()=>retrieveTargetInfo),
    "wrapTreePathInfo": (()=>wrapTreePathInfo)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/core/util.js [app-ssr] (ecmascript)");
;
function retrieveTargetInfo(payload, validPayloadTypes, seriesModel) {
    if (payload && (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["indexOf"])(validPayloadTypes, payload.type) >= 0) {
        var root = seriesModel.getData().tree.root;
        var targetNode = payload.targetNode;
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isString"])(targetNode)) {
            targetNode = root.getNodeById(targetNode);
        }
        if (targetNode && root.contains(targetNode)) {
            return {
                node: targetNode
            };
        }
        var targetNodeId = payload.targetNodeId;
        if (targetNodeId != null && (targetNode = root.getNodeById(targetNodeId))) {
            return {
                node: targetNode
            };
        }
    }
}
function getPathToRoot(node) {
    var path = [];
    while(node){
        node = node.parentNode;
        node && path.push(node);
    }
    return path.reverse();
}
function aboveViewRoot(viewRoot, node) {
    var viewPath = getPathToRoot(viewRoot);
    return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["indexOf"])(viewPath, node) >= 0;
}
function wrapTreePathInfo(node, seriesModel) {
    var treePathInfo = [];
    while(node){
        var nodeDataIndex = node.dataIndex;
        treePathInfo.push({
            name: node.name,
            dataIndex: nodeDataIndex,
            value: seriesModel.getRawValue(nodeDataIndex)
        });
        node = node.parentNode;
    }
    treePathInfo.reverse();
    return treePathInfo;
}
}}),
"[project]/node_modules/echarts/lib/chart/helper/enableAriaDecalForTree.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "default": (()=>enableAriaDecalForTree)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$model$2f$mixin$2f$palette$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/model/mixin/palette.js [app-ssr] (ecmascript)");
;
function enableAriaDecalForTree(seriesModel) {
    var data = seriesModel.getData();
    var tree = data.tree;
    var decalPaletteScope = {};
    tree.eachNode(function(node) {
        // Use decal of level 1 node
        var current = node;
        while(current && current.depth > 1){
            current = current.parentNode;
        }
        var decal = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$model$2f$mixin$2f$palette$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDecalFromPalette"])(seriesModel.ecModel, current.name || current.dataIndex + '', decalPaletteScope);
        node.setVisual('decal', decal);
    });
}
}}),
"[project]/node_modules/echarts/lib/chart/helper/multipleGraphEdgeHelper.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ // @ts-nocheck
__turbopack_context__.s({
    "createEdgeMapForCurveness": (()=>createEdgeMapForCurveness),
    "getCurvenessForEdge": (()=>getCurvenessForEdge),
    "initCurvenessList": (()=>initCurvenessList)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/core/util.js [app-ssr] (ecmascript)");
;
var KEY_DELIMITER = '-->';
/**
 * params handler
 * @param {module:echarts/model/SeriesModel} seriesModel
 * @returns {*}
 */ var getAutoCurvenessParams = function(seriesModel) {
    return seriesModel.get('autoCurveness') || null;
};
/**
 * Generate a list of edge curvatures, 20 is the default
 * @param {module:echarts/model/SeriesModel} seriesModel
 * @param {number} appendLength
 * @return  20 => [0, -0.2, 0.2, -0.4, 0.4, -0.6, 0.6, -0.8, 0.8, -1, 1, -1.2, 1.2, -1.4, 1.4, -1.6, 1.6, -1.8, 1.8, -2]
 */ var createCurveness = function(seriesModel, appendLength) {
    var autoCurvenessParmas = getAutoCurvenessParams(seriesModel);
    var length = 20;
    var curvenessList = [];
    // handler the function set
    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isNumber"])(autoCurvenessParmas)) {
        length = autoCurvenessParmas;
    } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isArray"])(autoCurvenessParmas)) {
        seriesModel.__curvenessList = autoCurvenessParmas;
        return;
    }
    // append length
    if (appendLength > length) {
        length = appendLength;
    }
    // make sure the length is even
    var len = length % 2 ? length + 2 : length + 3;
    curvenessList = [];
    for(var i = 0; i < len; i++){
        curvenessList.push((i % 2 ? i + 1 : i) / 10 * (i % 2 ? -1 : 1));
    }
    seriesModel.__curvenessList = curvenessList;
};
/**
 * Create different cache key data in the positive and negative directions, in order to set the curvature later
 * @param {number|string|module:echarts/data/Graph.Node} n1
 * @param {number|string|module:echarts/data/Graph.Node} n2
 * @param {module:echarts/model/SeriesModel} seriesModel
 * @returns {string} key
 */ var getKeyOfEdges = function(n1, n2, seriesModel) {
    var source = [
        n1.id,
        n1.dataIndex
    ].join('.');
    var target = [
        n2.id,
        n2.dataIndex
    ].join('.');
    return [
        seriesModel.uid,
        source,
        target
    ].join(KEY_DELIMITER);
};
/**
 * get opposite key
 * @param {string} key
 * @returns {string}
 */ var getOppositeKey = function(key) {
    var keys = key.split(KEY_DELIMITER);
    return [
        keys[0],
        keys[2],
        keys[1]
    ].join(KEY_DELIMITER);
};
/**
 * get edgeMap with key
 * @param edge
 * @param {module:echarts/model/SeriesModel} seriesModel
 */ var getEdgeFromMap = function(edge, seriesModel) {
    var key = getKeyOfEdges(edge.node1, edge.node2, seriesModel);
    return seriesModel.__edgeMap[key];
};
/**
 * calculate all cases total length
 * @param edge
 * @param seriesModel
 * @returns {number}
 */ var getTotalLengthBetweenNodes = function(edge, seriesModel) {
    var len = getEdgeMapLengthWithKey(getKeyOfEdges(edge.node1, edge.node2, seriesModel), seriesModel);
    var lenV = getEdgeMapLengthWithKey(getKeyOfEdges(edge.node2, edge.node1, seriesModel), seriesModel);
    return len + lenV;
};
/**
 *
 * @param key
 */ var getEdgeMapLengthWithKey = function(key, seriesModel) {
    var edgeMap = seriesModel.__edgeMap;
    return edgeMap[key] ? edgeMap[key].length : 0;
};
function initCurvenessList(seriesModel) {
    if (!getAutoCurvenessParams(seriesModel)) {
        return;
    }
    seriesModel.__curvenessList = [];
    seriesModel.__edgeMap = {};
    // calc the array of curveness List
    createCurveness(seriesModel);
}
function createEdgeMapForCurveness(n1, n2, seriesModel, index) {
    if (!getAutoCurvenessParams(seriesModel)) {
        return;
    }
    var key = getKeyOfEdges(n1, n2, seriesModel);
    var edgeMap = seriesModel.__edgeMap;
    var oppositeEdges = edgeMap[getOppositeKey(key)];
    // set direction
    if (edgeMap[key] && !oppositeEdges) {
        edgeMap[key].isForward = true;
    } else if (oppositeEdges && edgeMap[key]) {
        oppositeEdges.isForward = true;
        edgeMap[key].isForward = false;
    }
    edgeMap[key] = edgeMap[key] || [];
    edgeMap[key].push(index);
}
function getCurvenessForEdge(edge, seriesModel, index, needReverse) {
    var autoCurvenessParams = getAutoCurvenessParams(seriesModel);
    var isArrayParam = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isArray"])(autoCurvenessParams);
    if (!autoCurvenessParams) {
        return null;
    }
    var edgeArray = getEdgeFromMap(edge, seriesModel);
    if (!edgeArray) {
        return null;
    }
    var edgeIndex = -1;
    for(var i = 0; i < edgeArray.length; i++){
        if (edgeArray[i] === index) {
            edgeIndex = i;
            break;
        }
    }
    // if totalLen is Longer createCurveness
    var totalLen = getTotalLengthBetweenNodes(edge, seriesModel);
    createCurveness(seriesModel, totalLen);
    edge.lineStyle = edge.lineStyle || {};
    // if is opposite edge, must set curvenss to opposite number
    var curKey = getKeyOfEdges(edge.node1, edge.node2, seriesModel);
    var curvenessList = seriesModel.__curvenessList;
    // if pass array no need parity
    var parityCorrection = isArrayParam ? 0 : totalLen % 2 ? 0 : 1;
    if (!edgeArray.isForward) {
        // the opposite edge show outside
        var oppositeKey = getOppositeKey(curKey);
        var len = getEdgeMapLengthWithKey(oppositeKey, seriesModel);
        var resValue = curvenessList[edgeIndex + len + parityCorrection];
        // isNeedReverse, simple, force type need reverse the curveness in the junction of the forword and the opposite
        if (needReverse) {
            // set as array may make the parity handle with the len of opposite
            if (isArrayParam) {
                if (autoCurvenessParams && autoCurvenessParams[0] === 0) {
                    return (len + parityCorrection) % 2 ? resValue : -resValue;
                } else {
                    return ((len % 2 ? 0 : 1) + parityCorrection) % 2 ? resValue : -resValue;
                }
            } else {
                return (len + parityCorrection) % 2 ? resValue : -resValue;
            }
        } else {
            return curvenessList[edgeIndex + len + parityCorrection];
        }
    } else {
        return curvenessList[parityCorrection + edgeIndex];
    }
}
}}),
"[project]/node_modules/echarts/lib/chart/helper/LinePath.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/node_modules/tslib/tslib.es6.js [app-ssr] (ecmascript)");
/**
 * Line path for bezier and straight line draw
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Path$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Path$3e$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/graphic/Path.js [app-ssr] (ecmascript) <export default as Path>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$shape$2f$Line$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Line$3e$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/graphic/shape/Line.js [app-ssr] (ecmascript) <export default as Line>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$shape$2f$BezierCurve$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BezierCurve$3e$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/graphic/shape/BezierCurve.js [app-ssr] (ecmascript) <export default as BezierCurve>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/core/vector.js [app-ssr] (ecmascript)");
;
;
;
var straightLineProto = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$shape$2f$Line$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Line$3e$__["Line"].prototype;
var bezierCurveProto = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$shape$2f$BezierCurve$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BezierCurve$3e$__["BezierCurve"].prototype;
var StraightLineShape = /** @class */ function() {
    function StraightLineShape() {
        // Start point
        this.x1 = 0;
        this.y1 = 0;
        // End point
        this.x2 = 0;
        this.y2 = 0;
        this.percent = 1;
    }
    return StraightLineShape;
}();
var CurveShape = /** @class */ function(_super) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__extends"])(CurveShape, _super);
    function CurveShape() {
        return _super !== null && _super.apply(this, arguments) || this;
    }
    return CurveShape;
}(StraightLineShape);
function isStraightLine(shape) {
    return isNaN(+shape.cpx1) || isNaN(+shape.cpy1);
}
var ECLinePath = /** @class */ function(_super) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__extends"])(ECLinePath, _super);
    function ECLinePath(opts) {
        var _this = _super.call(this, opts) || this;
        _this.type = 'ec-line';
        return _this;
    }
    ECLinePath.prototype.getDefaultStyle = function() {
        return {
            stroke: '#000',
            fill: null
        };
    };
    ECLinePath.prototype.getDefaultShape = function() {
        return new StraightLineShape();
    };
    ECLinePath.prototype.buildPath = function(ctx, shape) {
        if (isStraightLine(shape)) {
            straightLineProto.buildPath.call(this, ctx, shape);
        } else {
            bezierCurveProto.buildPath.call(this, ctx, shape);
        }
    };
    ECLinePath.prototype.pointAt = function(t) {
        if (isStraightLine(this.shape)) {
            return straightLineProto.pointAt.call(this, t);
        } else {
            return bezierCurveProto.pointAt.call(this, t);
        }
    };
    ECLinePath.prototype.tangentAt = function(t) {
        var shape = this.shape;
        var p = isStraightLine(shape) ? [
            shape.x2 - shape.x1,
            shape.y2 - shape.y1
        ] : bezierCurveProto.tangentAt.call(this, t);
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalize"])(p, p);
    };
    return ECLinePath;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Path$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Path$3e$__["Path"]);
const __TURBOPACK__default__export__ = ECLinePath;
}}),
"[project]/node_modules/echarts/lib/chart/helper/Line.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/node_modules/tslib/tslib.es6.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/core/util.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/core/vector.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/util/symbol.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$chart$2f$helper$2f$LinePath$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/chart/helper/LinePath.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$animation$2f$basicTransition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/animation/basicTransition.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Group$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Group$3e$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/graphic/Group.js [app-ssr] (ecmascript) <export default as Group>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$states$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/util/states.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$label$2f$labelStyle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/label/labelStyle.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$number$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/util/number.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
;
var SYMBOL_CATEGORIES = [
    'fromSymbol',
    'toSymbol'
];
function makeSymbolTypeKey(symbolCategory) {
    return '_' + symbolCategory + 'Type';
}
function makeSymbolTypeValue(name, lineData, idx) {
    var symbolType = lineData.getItemVisual(idx, name);
    if (!symbolType || symbolType === 'none') {
        return symbolType;
    }
    var symbolSize = lineData.getItemVisual(idx, name + 'Size');
    var symbolRotate = lineData.getItemVisual(idx, name + 'Rotate');
    var symbolOffset = lineData.getItemVisual(idx, name + 'Offset');
    var symbolKeepAspect = lineData.getItemVisual(idx, name + 'KeepAspect');
    var symbolSizeArr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeSymbolSize"])(symbolSize);
    var symbolOffsetArr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeSymbolOffset"])(symbolOffset || 0, symbolSizeArr);
    return symbolType + symbolSizeArr + symbolOffsetArr + (symbolRotate || '') + (symbolKeepAspect || '');
}
/**
 * @inner
 */ function createSymbol(name, lineData, idx) {
    var symbolType = lineData.getItemVisual(idx, name);
    if (!symbolType || symbolType === 'none') {
        return;
    }
    var symbolSize = lineData.getItemVisual(idx, name + 'Size');
    var symbolRotate = lineData.getItemVisual(idx, name + 'Rotate');
    var symbolOffset = lineData.getItemVisual(idx, name + 'Offset');
    var symbolKeepAspect = lineData.getItemVisual(idx, name + 'KeepAspect');
    var symbolSizeArr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeSymbolSize"])(symbolSize);
    var symbolOffsetArr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeSymbolOffset"])(symbolOffset || 0, symbolSizeArr);
    var symbolPath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSymbol"])(symbolType, -symbolSizeArr[0] / 2 + symbolOffsetArr[0], -symbolSizeArr[1] / 2 + symbolOffsetArr[1], symbolSizeArr[0], symbolSizeArr[1], null, symbolKeepAspect);
    symbolPath.__specifiedRotation = symbolRotate == null || isNaN(symbolRotate) ? void 0 : +symbolRotate * Math.PI / 180 || 0;
    symbolPath.name = name;
    return symbolPath;
}
function createLine(points) {
    var line = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$chart$2f$helper$2f$LinePath$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]({
        name: 'line',
        subPixelOptimize: true
    });
    setLinePoints(line.shape, points);
    return line;
}
function setLinePoints(targetShape, points) {
    targetShape.x1 = points[0][0];
    targetShape.y1 = points[0][1];
    targetShape.x2 = points[1][0];
    targetShape.y2 = points[1][1];
    targetShape.percent = 1;
    var cp1 = points[2];
    if (cp1) {
        targetShape.cpx1 = cp1[0];
        targetShape.cpy1 = cp1[1];
    } else {
        targetShape.cpx1 = NaN;
        targetShape.cpy1 = NaN;
    }
}
var Line = /** @class */ function(_super) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__extends"])(Line, _super);
    function Line(lineData, idx, seriesScope) {
        var _this = _super.call(this) || this;
        _this._createLine(lineData, idx, seriesScope);
        return _this;
    }
    Line.prototype._createLine = function(lineData, idx, seriesScope) {
        var seriesModel = lineData.hostModel;
        var linePoints = lineData.getItemLayout(idx);
        var line = createLine(linePoints);
        line.shape.percent = 0;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$animation$2f$basicTransition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["initProps"])(line, {
            shape: {
                percent: 1
            }
        }, seriesModel, idx);
        this.add(line);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(SYMBOL_CATEGORIES, function(symbolCategory) {
            var symbol = createSymbol(symbolCategory, lineData, idx);
            // symbols must added after line to make sure
            // it will be updated after line#update.
            // Or symbol position and rotation update in line#beforeUpdate will be one frame slow
            this.add(symbol);
            this[makeSymbolTypeKey(symbolCategory)] = makeSymbolTypeValue(symbolCategory, lineData, idx);
        }, this);
        this._updateCommonStl(lineData, idx, seriesScope);
    };
    // TODO More strict on the List type in parameters?
    Line.prototype.updateData = function(lineData, idx, seriesScope) {
        var seriesModel = lineData.hostModel;
        var line = this.childOfName('line');
        var linePoints = lineData.getItemLayout(idx);
        var target = {
            shape: {}
        };
        setLinePoints(target.shape, linePoints);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$animation$2f$basicTransition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateProps"])(line, target, seriesModel, idx);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(SYMBOL_CATEGORIES, function(symbolCategory) {
            var symbolType = makeSymbolTypeValue(symbolCategory, lineData, idx);
            var key = makeSymbolTypeKey(symbolCategory);
            // Symbol changed
            if (this[key] !== symbolType) {
                this.remove(this.childOfName(symbolCategory));
                var symbol = createSymbol(symbolCategory, lineData, idx);
                this.add(symbol);
            }
            this[key] = symbolType;
        }, this);
        this._updateCommonStl(lineData, idx, seriesScope);
    };
    ;
    Line.prototype.getLinePath = function() {
        return this.childAt(0);
    };
    Line.prototype._updateCommonStl = function(lineData, idx, seriesScope) {
        var seriesModel = lineData.hostModel;
        var line = this.childOfName('line');
        var emphasisLineStyle = seriesScope && seriesScope.emphasisLineStyle;
        var blurLineStyle = seriesScope && seriesScope.blurLineStyle;
        var selectLineStyle = seriesScope && seriesScope.selectLineStyle;
        var labelStatesModels = seriesScope && seriesScope.labelStatesModels;
        var emphasisDisabled = seriesScope && seriesScope.emphasisDisabled;
        var focus = seriesScope && seriesScope.focus;
        var blurScope = seriesScope && seriesScope.blurScope;
        // Optimization for large dataset
        if (!seriesScope || lineData.hasItemOption) {
            var itemModel = lineData.getItemModel(idx);
            var emphasisModel = itemModel.getModel('emphasis');
            emphasisLineStyle = emphasisModel.getModel('lineStyle').getLineStyle();
            blurLineStyle = itemModel.getModel([
                'blur',
                'lineStyle'
            ]).getLineStyle();
            selectLineStyle = itemModel.getModel([
                'select',
                'lineStyle'
            ]).getLineStyle();
            emphasisDisabled = emphasisModel.get('disabled');
            focus = emphasisModel.get('focus');
            blurScope = emphasisModel.get('blurScope');
            labelStatesModels = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$label$2f$labelStyle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getLabelStatesModels"])(itemModel);
        }
        var lineStyle = lineData.getItemVisual(idx, 'style');
        var visualColor = lineStyle.stroke;
        line.useStyle(lineStyle);
        line.style.fill = null;
        line.style.strokeNoScale = true;
        line.ensureState('emphasis').style = emphasisLineStyle;
        line.ensureState('blur').style = blurLineStyle;
        line.ensureState('select').style = selectLineStyle;
        // Update symbol
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(SYMBOL_CATEGORIES, function(symbolCategory) {
            var symbol = this.childOfName(symbolCategory);
            if (symbol) {
                // Share opacity and color with line.
                symbol.setColor(visualColor);
                symbol.style.opacity = lineStyle.opacity;
                for(var i = 0; i < __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$states$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SPECIAL_STATES"].length; i++){
                    var stateName = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$states$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["SPECIAL_STATES"][i];
                    var lineState = line.getState(stateName);
                    if (lineState) {
                        var lineStateStyle = lineState.style || {};
                        var state = symbol.ensureState(stateName);
                        var stateStyle = state.style || (state.style = {});
                        if (lineStateStyle.stroke != null) {
                            stateStyle[symbol.__isEmptyBrush ? 'stroke' : 'fill'] = lineStateStyle.stroke;
                        }
                        if (lineStateStyle.opacity != null) {
                            stateStyle.opacity = lineStateStyle.opacity;
                        }
                    }
                }
                symbol.markRedraw();
            }
        }, this);
        var rawVal = seriesModel.getRawValue(idx);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$label$2f$labelStyle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setLabelStyle"])(this, labelStatesModels, {
            labelDataIndex: idx,
            labelFetcher: {
                getFormattedLabel: function(dataIndex, stateName) {
                    return seriesModel.getFormattedLabel(dataIndex, stateName, lineData.dataType);
                }
            },
            inheritColor: visualColor || '#000',
            defaultOpacity: lineStyle.opacity,
            defaultText: (rawVal == null ? lineData.getName(idx) : isFinite(rawVal) ? (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$number$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["round"])(rawVal) : rawVal) + ''
        });
        var label = this.getTextContent();
        // Always set `textStyle` even if `normalStyle.text` is null, because default
        // values have to be set on `normalStyle`.
        if (label) {
            var labelNormalModel = labelStatesModels.normal;
            label.__align = label.style.align;
            label.__verticalAlign = label.style.verticalAlign;
            // 'start', 'middle', 'end'
            label.__position = labelNormalModel.get('position') || 'middle';
            var distance = labelNormalModel.get('distance');
            if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isArray"])(distance)) {
                distance = [
                    distance,
                    distance
                ];
            }
            label.__labelDistance = distance;
        }
        this.setTextConfig({
            position: null,
            local: true,
            inside: false // Can't be inside for stroke element.
        });
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$states$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toggleHoverEmphasis"])(this, focus, blurScope, emphasisDisabled);
    };
    Line.prototype.highlight = function() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$states$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["enterEmphasis"])(this);
    };
    Line.prototype.downplay = function() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$states$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["leaveEmphasis"])(this);
    };
    Line.prototype.updateLayout = function(lineData, idx) {
        this.setLinePoints(lineData.getItemLayout(idx));
    };
    Line.prototype.setLinePoints = function(points) {
        var linePath = this.childOfName('line');
        setLinePoints(linePath.shape, points);
        linePath.dirty();
    };
    Line.prototype.beforeUpdate = function() {
        var lineGroup = this;
        var symbolFrom = lineGroup.childOfName('fromSymbol');
        var symbolTo = lineGroup.childOfName('toSymbol');
        var label = lineGroup.getTextContent();
        // Quick reject
        if (!symbolFrom && !symbolTo && (!label || label.ignore)) {
            return;
        }
        var invScale = 1;
        var parentNode = this.parent;
        while(parentNode){
            if (parentNode.scaleX) {
                invScale /= parentNode.scaleX;
            }
            parentNode = parentNode.parent;
        }
        var line = lineGroup.childOfName('line');
        // If line not changed
        // FIXME Parent scale changed
        if (!this.__dirty && !line.__dirty) {
            return;
        }
        var percent = line.shape.percent;
        var fromPos = line.pointAt(0);
        var toPos = line.pointAt(percent);
        var d = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["sub"])([], toPos, fromPos);
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalize"])(d, d);
        function setSymbolRotation(symbol, percent) {
            // Fix #12388
            // when symbol is set to be 'arrow' in markLine,
            // symbolRotate value will be ignored, and compulsively use tangent angle.
            // rotate by default if symbol rotation is not specified
            var specifiedRotation = symbol.__specifiedRotation;
            if (specifiedRotation == null) {
                var tangent = line.tangentAt(percent);
                symbol.attr('rotation', (percent === 1 ? -1 : 1) * Math.PI / 2 - Math.atan2(tangent[1], tangent[0]));
            } else {
                symbol.attr('rotation', specifiedRotation);
            }
        }
        if (symbolFrom) {
            symbolFrom.setPosition(fromPos);
            setSymbolRotation(symbolFrom, 0);
            symbolFrom.scaleX = symbolFrom.scaleY = invScale * percent;
            symbolFrom.markRedraw();
        }
        if (symbolTo) {
            symbolTo.setPosition(toPos);
            setSymbolRotation(symbolTo, 1);
            symbolTo.scaleX = symbolTo.scaleY = invScale * percent;
            symbolTo.markRedraw();
        }
        if (label && !label.ignore) {
            label.x = label.y = 0;
            label.originX = label.originY = 0;
            var textAlign = void 0;
            var textVerticalAlign = void 0;
            var distance = label.__labelDistance;
            var distanceX = distance[0] * invScale;
            var distanceY = distance[1] * invScale;
            var halfPercent = percent / 2;
            var tangent = line.tangentAt(halfPercent);
            var n = [
                tangent[1],
                -tangent[0]
            ];
            var cp = line.pointAt(halfPercent);
            if (n[1] > 0) {
                n[0] = -n[0];
                n[1] = -n[1];
            }
            var dir = tangent[0] < 0 ? -1 : 1;
            if (label.__position !== 'start' && label.__position !== 'end') {
                var rotation = -Math.atan2(tangent[1], tangent[0]);
                if (toPos[0] < fromPos[0]) {
                    rotation = Math.PI + rotation;
                }
                label.rotation = rotation;
            }
            var dy = void 0;
            switch(label.__position){
                case 'insideStartTop':
                case 'insideMiddleTop':
                case 'insideEndTop':
                case 'middle':
                    dy = -distanceY;
                    textVerticalAlign = 'bottom';
                    break;
                case 'insideStartBottom':
                case 'insideMiddleBottom':
                case 'insideEndBottom':
                    dy = distanceY;
                    textVerticalAlign = 'top';
                    break;
                default:
                    dy = 0;
                    textVerticalAlign = 'middle';
            }
            switch(label.__position){
                case 'end':
                    label.x = d[0] * distanceX + toPos[0];
                    label.y = d[1] * distanceY + toPos[1];
                    textAlign = d[0] > 0.8 ? 'left' : d[0] < -0.8 ? 'right' : 'center';
                    textVerticalAlign = d[1] > 0.8 ? 'top' : d[1] < -0.8 ? 'bottom' : 'middle';
                    break;
                case 'start':
                    label.x = -d[0] * distanceX + fromPos[0];
                    label.y = -d[1] * distanceY + fromPos[1];
                    textAlign = d[0] > 0.8 ? 'right' : d[0] < -0.8 ? 'left' : 'center';
                    textVerticalAlign = d[1] > 0.8 ? 'bottom' : d[1] < -0.8 ? 'top' : 'middle';
                    break;
                case 'insideStartTop':
                case 'insideStart':
                case 'insideStartBottom':
                    label.x = distanceX * dir + fromPos[0];
                    label.y = fromPos[1] + dy;
                    textAlign = tangent[0] < 0 ? 'right' : 'left';
                    label.originX = -distanceX * dir;
                    label.originY = -dy;
                    break;
                case 'insideMiddleTop':
                case 'insideMiddle':
                case 'insideMiddleBottom':
                case 'middle':
                    label.x = cp[0];
                    label.y = cp[1] + dy;
                    textAlign = 'center';
                    label.originY = -dy;
                    break;
                case 'insideEndTop':
                case 'insideEnd':
                case 'insideEndBottom':
                    label.x = -distanceX * dir + toPos[0];
                    label.y = toPos[1] + dy;
                    textAlign = tangent[0] >= 0 ? 'right' : 'left';
                    label.originX = distanceX * dir;
                    label.originY = -dy;
                    break;
            }
            label.scaleX = label.scaleY = invScale;
            label.setStyle({
                // Use the user specified text align and baseline first
                verticalAlign: label.__verticalAlign || textVerticalAlign,
                align: label.__align || textAlign
            });
        }
    };
    return Line;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Group$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Group$3e$__["Group"]);
const __TURBOPACK__default__export__ = Line;
}}),
"[project]/node_modules/echarts/lib/chart/helper/LineDraw.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$graphic$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/util/graphic.js [app-ssr] (ecmascript) <locals>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Group$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Group$3e$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/graphic/Group.js [app-ssr] (ecmascript) <export default as Group>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$chart$2f$helper$2f$Line$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/chart/helper/Line.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$label$2f$labelStyle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/label/labelStyle.js [app-ssr] (ecmascript)");
;
;
;
var LineDraw = /** @class */ function() {
    function LineDraw(LineCtor) {
        this.group = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Group$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Group$3e$__["Group"]();
        this._LineCtor = LineCtor || __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$chart$2f$helper$2f$Line$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"];
    }
    LineDraw.prototype.updateData = function(lineData) {
        var _this = this;
        // Remove progressive els.
        this._progressiveEls = null;
        var lineDraw = this;
        var group = lineDraw.group;
        var oldLineData = lineDraw._lineData;
        lineDraw._lineData = lineData;
        // There is no oldLineData only when first rendering or switching from
        // stream mode to normal mode, where previous elements should be removed.
        if (!oldLineData) {
            group.removeAll();
        }
        var seriesScope = makeSeriesScope(lineData);
        lineData.diff(oldLineData).add(function(idx) {
            _this._doAdd(lineData, idx, seriesScope);
        }).update(function(newIdx, oldIdx) {
            _this._doUpdate(oldLineData, lineData, oldIdx, newIdx, seriesScope);
        }).remove(function(idx) {
            group.remove(oldLineData.getItemGraphicEl(idx));
        }).execute();
    };
    ;
    LineDraw.prototype.updateLayout = function() {
        var lineData = this._lineData;
        // Do not support update layout in incremental mode.
        if (!lineData) {
            return;
        }
        lineData.eachItemGraphicEl(function(el, idx) {
            el.updateLayout(lineData, idx);
        }, this);
    };
    ;
    LineDraw.prototype.incrementalPrepareUpdate = function(lineData) {
        this._seriesScope = makeSeriesScope(lineData);
        this._lineData = null;
        this.group.removeAll();
    };
    ;
    LineDraw.prototype.incrementalUpdate = function(taskParams, lineData) {
        this._progressiveEls = [];
        function updateIncrementalAndHover(el) {
            if (!el.isGroup && !isEffectObject(el)) {
                el.incremental = true;
                el.ensureState('emphasis').hoverLayer = true;
            }
        }
        for(var idx = taskParams.start; idx < taskParams.end; idx++){
            var itemLayout = lineData.getItemLayout(idx);
            if (lineNeedsDraw(itemLayout)) {
                var el = new this._LineCtor(lineData, idx, this._seriesScope);
                el.traverse(updateIncrementalAndHover);
                this.group.add(el);
                lineData.setItemGraphicEl(idx, el);
                this._progressiveEls.push(el);
            }
        }
    };
    ;
    LineDraw.prototype.remove = function() {
        this.group.removeAll();
    };
    ;
    LineDraw.prototype.eachRendered = function(cb) {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$graphic$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$locals$3e$__["traverseElements"])(this._progressiveEls || this.group, cb);
    };
    LineDraw.prototype._doAdd = function(lineData, idx, seriesScope) {
        var itemLayout = lineData.getItemLayout(idx);
        if (!lineNeedsDraw(itemLayout)) {
            return;
        }
        var el = new this._LineCtor(lineData, idx, seriesScope);
        lineData.setItemGraphicEl(idx, el);
        this.group.add(el);
    };
    LineDraw.prototype._doUpdate = function(oldLineData, newLineData, oldIdx, newIdx, seriesScope) {
        var itemEl = oldLineData.getItemGraphicEl(oldIdx);
        if (!lineNeedsDraw(newLineData.getItemLayout(newIdx))) {
            this.group.remove(itemEl);
            return;
        }
        if (!itemEl) {
            itemEl = new this._LineCtor(newLineData, newIdx, seriesScope);
        } else {
            itemEl.updateData(newLineData, newIdx, seriesScope);
        }
        newLineData.setItemGraphicEl(newIdx, itemEl);
        this.group.add(itemEl);
    };
    return LineDraw;
}();
function isEffectObject(el) {
    return el.animators && el.animators.length > 0;
}
function makeSeriesScope(lineData) {
    var hostModel = lineData.hostModel;
    var emphasisModel = hostModel.getModel('emphasis');
    return {
        lineStyle: hostModel.getModel('lineStyle').getLineStyle(),
        emphasisLineStyle: emphasisModel.getModel([
            'lineStyle'
        ]).getLineStyle(),
        blurLineStyle: hostModel.getModel([
            'blur',
            'lineStyle'
        ]).getLineStyle(),
        selectLineStyle: hostModel.getModel([
            'select',
            'lineStyle'
        ]).getLineStyle(),
        emphasisDisabled: emphasisModel.get('disabled'),
        blurScope: emphasisModel.get('blurScope'),
        focus: emphasisModel.get('focus'),
        labelStatesModels: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$label$2f$labelStyle$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getLabelStatesModels"])(hostModel)
    };
}
function isPointNaN(pt) {
    return isNaN(pt[0]) || isNaN(pt[1]);
}
function lineNeedsDraw(pts) {
    return pts && !isPointNaN(pts[0]) && !isPointNaN(pts[1]);
}
const __TURBOPACK__default__export__ = LineDraw;
}}),
"[project]/node_modules/echarts/lib/chart/helper/createGraphFromNodeEdge.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "default": (()=>createGraphFromNodeEdge)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/core/util.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$SeriesData$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/data/SeriesData.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$Graph$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/data/Graph.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$linkSeriesData$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/data/helper/linkSeriesData.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$createDimensions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/data/helper/createDimensions.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$core$2f$CoordinateSystem$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/core/CoordinateSystem.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$chart$2f$helper$2f$createSeriesData$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/chart/helper/createSeriesData.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$model$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/util/model.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
;
function createGraphFromNodeEdge(nodes, edges, seriesModel, directed, beforeLink) {
    // ??? TODO
    // support dataset?
    var graph = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$Graph$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](directed);
    for(var i = 0; i < nodes.length; i++){
        graph.addNode((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["retrieve"])(// Id, name, dataIndex
        nodes[i].id, nodes[i].name, i), i);
    }
    var linkNameList = [];
    var validEdges = [];
    var linkCount = 0;
    for(var i = 0; i < edges.length; i++){
        var link = edges[i];
        var source = link.source;
        var target = link.target;
        // addEdge may fail when source or target not exists
        if (graph.addEdge(source, target, linkCount)) {
            validEdges.push(link);
            linkNameList.push((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["retrieve"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$model$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["convertOptionIdName"])(link.id, null), source + ' > ' + target));
            linkCount++;
        }
    }
    var coordSys = seriesModel.get('coordinateSystem');
    var nodeData;
    if (coordSys === 'cartesian2d' || coordSys === 'polar') {
        nodeData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$chart$2f$helper$2f$createSeriesData$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(nodes, seriesModel);
    } else {
        var coordSysCtor = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$core$2f$CoordinateSystem$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"].get(coordSys);
        var coordDimensions = coordSysCtor ? coordSysCtor.dimensions || [] : [];
        // FIXME: Some geo do not need `value` dimenson, whereas `calendar` needs
        // `value` dimension, but graph need `value` dimension. It's better to
        // uniform this behavior.
        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["indexOf"])(coordDimensions, 'value') < 0) {
            coordDimensions.concat([
                'value'
            ]);
        }
        var dimensions = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$createDimensions$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(nodes, {
            coordDimensions: coordDimensions,
            encodeDefine: seriesModel.getEncode()
        }).dimensions;
        nodeData = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$SeriesData$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](dimensions, seriesModel);
        nodeData.initData(nodes);
    }
    var edgeData = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$SeriesData$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]([
        'value'
    ], seriesModel);
    edgeData.initData(validEdges, linkNameList);
    beforeLink && beforeLink(nodeData, edgeData);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$linkSeriesData$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])({
        mainData: nodeData,
        struct: graph,
        structAttr: 'graph',
        datas: {
            node: nodeData,
            edge: edgeData
        },
        datasAttr: {
            node: 'data',
            edge: 'edgeData'
        }
    });
    // Update dataIndex of nodes and edges because invalid edge may be removed
    graph.update();
    return graph;
}
}}),
"[project]/node_modules/echarts/lib/chart/helper/whiskerBoxCommon.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "WhiskerBoxCommonMixin": (()=>WhiskerBoxCommonMixin)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$chart$2f$helper$2f$createSeriesDataSimply$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/chart/helper/createSeriesDataSimply.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/core/util.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$dimensionHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/data/helper/dimensionHelper.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$sourceHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/data/helper/sourceHelper.js [app-ssr] (ecmascript)");
;
;
;
;
var WhiskerBoxCommonMixin = /** @class */ function() {
    function WhiskerBoxCommonMixin() {}
    /**
   * @private
   */ WhiskerBoxCommonMixin.prototype._hasEncodeRule = function(key) {
        var encodeRules = this.getEncode();
        return encodeRules && encodeRules.get(key) != null;
    };
    /**
   * @override
   */ WhiskerBoxCommonMixin.prototype.getInitialData = function(option, ecModel) {
        // When both types of xAxis and yAxis are 'value', layout is
        // needed to be specified by user. Otherwise, layout can be
        // judged by which axis is category.
        var ordinalMeta;
        var xAxisModel = ecModel.getComponent('xAxis', this.get('xAxisIndex'));
        var yAxisModel = ecModel.getComponent('yAxis', this.get('yAxisIndex'));
        var xAxisType = xAxisModel.get('type');
        var yAxisType = yAxisModel.get('type');
        var addOrdinal;
        // FIXME
        // Consider time axis.
        if (xAxisType === 'category') {
            option.layout = 'horizontal';
            ordinalMeta = xAxisModel.getOrdinalMeta();
            addOrdinal = !this._hasEncodeRule('x');
        } else if (yAxisType === 'category') {
            option.layout = 'vertical';
            ordinalMeta = yAxisModel.getOrdinalMeta();
            addOrdinal = !this._hasEncodeRule('y');
        } else {
            option.layout = option.layout || 'horizontal';
        }
        var coordDims = [
            'x',
            'y'
        ];
        var baseAxisDimIndex = option.layout === 'horizontal' ? 0 : 1;
        var baseAxisDim = this._baseAxisDim = coordDims[baseAxisDimIndex];
        var otherAxisDim = coordDims[1 - baseAxisDimIndex];
        var axisModels = [
            xAxisModel,
            yAxisModel
        ];
        var baseAxisType = axisModels[baseAxisDimIndex].get('type');
        var otherAxisType = axisModels[1 - baseAxisDimIndex].get('type');
        var data = option.data;
        // Clone a new data for next setOption({}) usage.
        // Avoid modifying current data will affect further update.
        if (data && addOrdinal) {
            var newOptionData_1 = [];
            (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["each"])(data, function(item, index) {
                var newItem;
                if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isArray"])(item)) {
                    newItem = item.slice();
                    // Modify current using data.
                    item.unshift(index);
                } else if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isArray"])(item.value)) {
                    newItem = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["extend"])({}, item);
                    newItem.value = newItem.value.slice();
                    // Modify current using data.
                    item.value.unshift(index);
                } else {
                    newItem = item;
                }
                newOptionData_1.push(newItem);
            });
            option.data = newOptionData_1;
        }
        var defaultValueDimensions = this.defaultValueDimensions;
        var coordDimensions = [
            {
                name: baseAxisDim,
                type: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$dimensionHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDimensionTypeByAxis"])(baseAxisType),
                ordinalMeta: ordinalMeta,
                otherDims: {
                    tooltip: false,
                    itemName: 0
                },
                dimsDef: [
                    'base'
                ]
            },
            {
                name: otherAxisDim,
                type: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$dimensionHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDimensionTypeByAxis"])(otherAxisType),
                dimsDef: defaultValueDimensions.slice()
            }
        ];
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$chart$2f$helper$2f$createSeriesDataSimply$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"])(this, {
            coordDimensions: coordDimensions,
            dimensionsCount: defaultValueDimensions.length + 1,
            encodeDefaulter: (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["curry"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$data$2f$helper$2f$sourceHelper$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["makeSeriesEncodeForAxisCoordSys"], coordDimensions, this)
        });
    };
    /**
   * If horizontal, base axis is x, otherwise y.
   * @override
   */ WhiskerBoxCommonMixin.prototype.getBaseAxis = function() {
        var dim = this._baseAxisDim;
        return this.ecModel.getComponent(dim + 'Axis', this.get(dim + 'AxisIndex')).axis;
    };
    return WhiskerBoxCommonMixin;
}();
;
;
}}),
"[project]/node_modules/echarts/lib/chart/helper/EffectSymbol.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/node_modules/tslib/tslib.es6.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/util/symbol.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Group$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Group$3e$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/graphic/Group.js [app-ssr] (ecmascript) <export default as Group>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$states$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/util/states.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$chart$2f$helper$2f$Symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/chart/helper/Symbol.js [app-ssr] (ecmascript)");
;
;
;
;
;
function updateRipplePath(rippleGroup, effectCfg) {
    var color = effectCfg.rippleEffectColor || effectCfg.color;
    rippleGroup.eachChild(function(ripplePath) {
        ripplePath.attr({
            z: effectCfg.z,
            zlevel: effectCfg.zlevel,
            style: {
                stroke: effectCfg.brushType === 'stroke' ? color : null,
                fill: effectCfg.brushType === 'fill' ? color : null
            }
        });
    });
}
var EffectSymbol = /** @class */ function(_super) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__extends"])(EffectSymbol, _super);
    function EffectSymbol(data, idx) {
        var _this = _super.call(this) || this;
        var symbol = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$chart$2f$helper$2f$Symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](data, idx);
        var rippleGroup = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Group$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Group$3e$__["Group"]();
        _this.add(symbol);
        _this.add(rippleGroup);
        _this.updateData(data, idx);
        return _this;
    }
    EffectSymbol.prototype.stopEffectAnimation = function() {
        this.childAt(1).removeAll();
    };
    EffectSymbol.prototype.startEffectAnimation = function(effectCfg) {
        var symbolType = effectCfg.symbolType;
        var color = effectCfg.color;
        var rippleNumber = effectCfg.rippleNumber;
        var rippleGroup = this.childAt(1);
        for(var i = 0; i < rippleNumber; i++){
            // If width/height are set too small (e.g., set to 1) on ios10
            // and macOS Sierra, a circle stroke become a rect, no matter what
            // the scale is set. So we set width/height as 2. See #4136.
            var ripplePath = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSymbol"])(symbolType, -1, -1, 2, 2, color);
            ripplePath.attr({
                style: {
                    strokeNoScale: true
                },
                z2: 99,
                silent: true,
                scaleX: 0.5,
                scaleY: 0.5
            });
            var delay = -i / rippleNumber * effectCfg.period + effectCfg.effectOffset;
            ripplePath.animate('', true).when(effectCfg.period, {
                scaleX: effectCfg.rippleScale / 2,
                scaleY: effectCfg.rippleScale / 2
            }).delay(delay).start();
            ripplePath.animateStyle(true).when(effectCfg.period, {
                opacity: 0
            }).delay(delay).start();
            rippleGroup.add(ripplePath);
        }
        updateRipplePath(rippleGroup, effectCfg);
    };
    /**
   * Update effect symbol
   */ EffectSymbol.prototype.updateEffectAnimation = function(effectCfg) {
        var oldEffectCfg = this._effectCfg;
        var rippleGroup = this.childAt(1);
        // Must reinitialize effect if following configuration changed
        var DIFFICULT_PROPS = [
            'symbolType',
            'period',
            'rippleScale',
            'rippleNumber'
        ];
        for(var i = 0; i < DIFFICULT_PROPS.length; i++){
            var propName = DIFFICULT_PROPS[i];
            if (oldEffectCfg[propName] !== effectCfg[propName]) {
                this.stopEffectAnimation();
                this.startEffectAnimation(effectCfg);
                return;
            }
        }
        updateRipplePath(rippleGroup, effectCfg);
    };
    /**
   * Highlight symbol
   */ EffectSymbol.prototype.highlight = function() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$states$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["enterEmphasis"])(this);
    };
    /**
   * Downplay symbol
   */ EffectSymbol.prototype.downplay = function() {
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$states$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["leaveEmphasis"])(this);
    };
    EffectSymbol.prototype.getSymbolType = function() {
        var symbol = this.childAt(0);
        return symbol && symbol.getSymbolType();
    };
    /**
   * Update symbol properties
   */ EffectSymbol.prototype.updateData = function(data, idx) {
        var _this = this;
        var seriesModel = data.hostModel;
        this.childAt(0).updateData(data, idx);
        var rippleGroup = this.childAt(1);
        var itemModel = data.getItemModel(idx);
        var symbolType = data.getItemVisual(idx, 'symbol');
        var symbolSize = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeSymbolSize"])(data.getItemVisual(idx, 'symbolSize'));
        var symbolStyle = data.getItemVisual(idx, 'style');
        var color = symbolStyle && symbolStyle.fill;
        var emphasisModel = itemModel.getModel('emphasis');
        rippleGroup.setScale(symbolSize);
        rippleGroup.traverse(function(ripplePath) {
            ripplePath.setStyle('fill', color);
        });
        var symbolOffset = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["normalizeSymbolOffset"])(data.getItemVisual(idx, 'symbolOffset'), symbolSize);
        if (symbolOffset) {
            rippleGroup.x = symbolOffset[0];
            rippleGroup.y = symbolOffset[1];
        }
        var symbolRotate = data.getItemVisual(idx, 'symbolRotate');
        rippleGroup.rotation = (symbolRotate || 0) * Math.PI / 180 || 0;
        var effectCfg = {};
        effectCfg.showEffectOn = seriesModel.get('showEffectOn');
        effectCfg.rippleScale = itemModel.get([
            'rippleEffect',
            'scale'
        ]);
        effectCfg.brushType = itemModel.get([
            'rippleEffect',
            'brushType'
        ]);
        effectCfg.period = itemModel.get([
            'rippleEffect',
            'period'
        ]) * 1000;
        effectCfg.effectOffset = idx / data.count();
        effectCfg.z = seriesModel.getShallow('z') || 0;
        effectCfg.zlevel = seriesModel.getShallow('zlevel') || 0;
        effectCfg.symbolType = symbolType;
        effectCfg.color = color;
        effectCfg.rippleEffectColor = itemModel.get([
            'rippleEffect',
            'color'
        ]);
        effectCfg.rippleNumber = itemModel.get([
            'rippleEffect',
            'number'
        ]);
        if (effectCfg.showEffectOn === 'render') {
            this._effectCfg ? this.updateEffectAnimation(effectCfg) : this.startEffectAnimation(effectCfg);
            this._effectCfg = effectCfg;
        } else {
            // Not keep old effect config
            this._effectCfg = null;
            this.stopEffectAnimation();
            this.onHoverStateChange = function(toState) {
                if (toState === 'emphasis') {
                    if (effectCfg.showEffectOn !== 'render') {
                        _this.startEffectAnimation(effectCfg);
                    }
                } else if (toState === 'normal') {
                    if (effectCfg.showEffectOn !== 'render') {
                        _this.stopEffectAnimation();
                    }
                }
            };
        }
        this._effectCfg = effectCfg;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$states$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toggleHoverEmphasis"])(this, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));
    };
    ;
    EffectSymbol.prototype.fadeOut = function(cb) {
        cb && cb();
    };
    ;
    return EffectSymbol;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Group$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Group$3e$__["Group"]);
const __TURBOPACK__default__export__ = EffectSymbol;
}}),
"[project]/node_modules/echarts/lib/chart/helper/EffectLine.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/node_modules/tslib/tslib.es6.js [app-ssr] (ecmascript)");
/**
 * Provide effect for line
 */ var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Group$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Group$3e$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/graphic/Group.js [app-ssr] (ecmascript) <export default as Group>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$chart$2f$helper$2f$Line$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/chart/helper/Line.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/core/util.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/util/symbol.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/core/vector.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$curve$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/core/curve.js [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
var EffectLine = /** @class */ function(_super) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__extends"])(EffectLine, _super);
    function EffectLine(lineData, idx, seriesScope) {
        var _this = _super.call(this) || this;
        _this.add(_this.createLine(lineData, idx, seriesScope));
        _this._updateEffectSymbol(lineData, idx);
        return _this;
    }
    EffectLine.prototype.createLine = function(lineData, idx, seriesScope) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$chart$2f$helper$2f$Line$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](lineData, idx, seriesScope);
    };
    EffectLine.prototype._updateEffectSymbol = function(lineData, idx) {
        var itemModel = lineData.getItemModel(idx);
        var effectModel = itemModel.getModel('effect');
        var size = effectModel.get('symbolSize');
        var symbolType = effectModel.get('symbol');
        if (!(0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isArray"])(size)) {
            size = [
                size,
                size
            ];
        }
        var lineStyle = lineData.getItemVisual(idx, 'style');
        var color = effectModel.get('color') || lineStyle && lineStyle.stroke;
        var symbol = this.childAt(1);
        if (this._symbolType !== symbolType) {
            // Remove previous
            this.remove(symbol);
            symbol = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$symbol$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["createSymbol"])(symbolType, -0.5, -0.5, 1, 1, color);
            symbol.z2 = 100;
            symbol.culling = true;
            this.add(symbol);
        }
        // Symbol may be removed if loop is false
        if (!symbol) {
            return;
        }
        // Shadow color is same with color in default
        symbol.setStyle('shadowColor', color);
        symbol.setStyle(effectModel.getItemStyle([
            'color'
        ]));
        symbol.scaleX = size[0];
        symbol.scaleY = size[1];
        symbol.setColor(color);
        this._symbolType = symbolType;
        this._symbolScale = size;
        this._updateEffectAnimation(lineData, effectModel, idx);
    };
    EffectLine.prototype._updateEffectAnimation = function(lineData, effectModel, idx) {
        var symbol = this.childAt(1);
        if (!symbol) {
            return;
        }
        var points = lineData.getItemLayout(idx);
        var period = effectModel.get('period') * 1000;
        var loop = effectModel.get('loop');
        var roundTrip = effectModel.get('roundTrip');
        var constantSpeed = effectModel.get('constantSpeed');
        var delayExpr = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["retrieve"])(effectModel.get('delay'), function(idx) {
            return idx / lineData.count() * period / 3;
        });
        // Ignore when updating
        symbol.ignore = true;
        this._updateAnimationPoints(symbol, points);
        if (constantSpeed > 0) {
            period = this._getLineLength(symbol) / constantSpeed * 1000;
        }
        if (period !== this._period || loop !== this._loop || roundTrip !== this._roundTrip) {
            symbol.stopAnimation();
            var delayNum = void 0;
            if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$util$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["isFunction"])(delayExpr)) {
                delayNum = delayExpr(idx);
            } else {
                delayNum = delayExpr;
            }
            if (symbol.__t > 0) {
                delayNum = -period * symbol.__t;
            }
            this._animateSymbol(symbol, period, delayNum, loop, roundTrip);
        }
        this._period = period;
        this._loop = loop;
        this._roundTrip = roundTrip;
    };
    EffectLine.prototype._animateSymbol = function(symbol, period, delayNum, loop, roundTrip) {
        if (period > 0) {
            symbol.__t = 0;
            var self_1 = this;
            var animator = symbol.animate('', loop).when(roundTrip ? period * 2 : period, {
                __t: roundTrip ? 2 : 1
            }).delay(delayNum).during(function() {
                self_1._updateSymbolPosition(symbol);
            });
            if (!loop) {
                animator.done(function() {
                    self_1.remove(symbol);
                });
            }
            animator.start();
        }
    };
    EffectLine.prototype._getLineLength = function(symbol) {
        // Not so accurate
        return (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["dist"])(symbol.__p1, symbol.__cp1) + (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["dist"])(symbol.__cp1, symbol.__p2);
    };
    EffectLine.prototype._updateAnimationPoints = function(symbol, points) {
        symbol.__p1 = points[0];
        symbol.__p2 = points[1];
        symbol.__cp1 = points[2] || [
            (points[0][0] + points[1][0]) / 2,
            (points[0][1] + points[1][1]) / 2
        ];
    };
    EffectLine.prototype.updateData = function(lineData, idx, seriesScope) {
        this.childAt(0).updateData(lineData, idx, seriesScope);
        this._updateEffectSymbol(lineData, idx);
    };
    EffectLine.prototype._updateSymbolPosition = function(symbol) {
        var p1 = symbol.__p1;
        var p2 = symbol.__p2;
        var cp1 = symbol.__cp1;
        var t = symbol.__t < 1 ? symbol.__t : 2 - symbol.__t;
        var pos = [
            symbol.x,
            symbol.y
        ];
        var lastPos = pos.slice();
        var quadraticAt = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$curve$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["quadraticAt"];
        var quadraticDerivativeAt = __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$curve$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["quadraticDerivativeAt"];
        pos[0] = quadraticAt(p1[0], cp1[0], p2[0], t);
        pos[1] = quadraticAt(p1[1], cp1[1], p2[1], t);
        // Tangent
        var tx = symbol.__t < 1 ? quadraticDerivativeAt(p1[0], cp1[0], p2[0], t) : quadraticDerivativeAt(p2[0], cp1[0], p1[0], 1 - t);
        var ty = symbol.__t < 1 ? quadraticDerivativeAt(p1[1], cp1[1], p2[1], t) : quadraticDerivativeAt(p2[1], cp1[1], p1[1], 1 - t);
        symbol.rotation = -Math.atan2(ty, tx) - Math.PI / 2;
        // enable continuity trail for 'line', 'rect', 'roundRect' symbolType
        if (this._symbolType === 'line' || this._symbolType === 'rect' || this._symbolType === 'roundRect') {
            if (symbol.__lastT !== undefined && symbol.__lastT < symbol.__t) {
                symbol.scaleY = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["dist"])(lastPos, pos) * 1.05;
                // make sure the last segment render within endPoint
                if (t === 1) {
                    pos[0] = lastPos[0] + (pos[0] - lastPos[0]) / 2;
                    pos[1] = lastPos[1] + (pos[1] - lastPos[1]) / 2;
                }
            } else if (symbol.__lastT === 1) {
                // After first loop, symbol.__t does NOT start with 0, so connect p1 to pos directly.
                symbol.scaleY = 2 * (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["dist"])(p1, pos);
            } else {
                symbol.scaleY = this._symbolScale[1];
            }
        }
        symbol.__lastT = symbol.__t;
        symbol.ignore = false;
        symbol.x = pos[0];
        symbol.y = pos[1];
    };
    EffectLine.prototype.updateLayout = function(lineData, idx) {
        this.childAt(0).updateLayout(lineData, idx);
        var effectModel = lineData.getItemModel(idx).getModel('effect');
        this._updateEffectAnimation(lineData, effectModel, idx);
    };
    return EffectLine;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Group$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Group$3e$__["Group"]);
const __TURBOPACK__default__export__ = EffectLine;
}}),
"[project]/node_modules/echarts/lib/chart/helper/Polyline.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/node_modules/tslib/tslib.es6.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$shape$2f$Polyline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Polyline$3e$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/graphic/shape/Polyline.js [app-ssr] (ecmascript) <export default as Polyline>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$animation$2f$basicTransition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/animation/basicTransition.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Group$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Group$3e$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/graphic/Group.js [app-ssr] (ecmascript) <export default as Group>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$states$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/util/states.js [app-ssr] (ecmascript)");
;
;
;
var Polyline = /** @class */ function(_super) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__extends"])(Polyline, _super);
    function Polyline(lineData, idx, seriesScope) {
        var _this = _super.call(this) || this;
        _this._createPolyline(lineData, idx, seriesScope);
        return _this;
    }
    Polyline.prototype._createPolyline = function(lineData, idx, seriesScope) {
        // let seriesModel = lineData.hostModel;
        var points = lineData.getItemLayout(idx);
        var line = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$shape$2f$Polyline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Polyline$3e$__["Polyline"]({
            shape: {
                points: points
            }
        });
        this.add(line);
        this._updateCommonStl(lineData, idx, seriesScope);
    };
    ;
    Polyline.prototype.updateData = function(lineData, idx, seriesScope) {
        var seriesModel = lineData.hostModel;
        var line = this.childAt(0);
        var target = {
            shape: {
                points: lineData.getItemLayout(idx)
            }
        };
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$animation$2f$basicTransition$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["updateProps"])(line, target, seriesModel, idx);
        this._updateCommonStl(lineData, idx, seriesScope);
    };
    ;
    Polyline.prototype._updateCommonStl = function(lineData, idx, seriesScope) {
        var line = this.childAt(0);
        var itemModel = lineData.getItemModel(idx);
        var emphasisLineStyle = seriesScope && seriesScope.emphasisLineStyle;
        var focus = seriesScope && seriesScope.focus;
        var blurScope = seriesScope && seriesScope.blurScope;
        var emphasisDisabled = seriesScope && seriesScope.emphasisDisabled;
        if (!seriesScope || lineData.hasItemOption) {
            var emphasisModel = itemModel.getModel('emphasis');
            emphasisLineStyle = emphasisModel.getModel('lineStyle').getLineStyle();
            emphasisDisabled = emphasisModel.get('disabled');
            focus = emphasisModel.get('focus');
            blurScope = emphasisModel.get('blurScope');
        }
        line.useStyle(lineData.getItemVisual(idx, 'style'));
        line.style.fill = null;
        line.style.strokeNoScale = true;
        var lineEmphasisState = line.ensureState('emphasis');
        lineEmphasisState.style = emphasisLineStyle;
        (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$states$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["toggleHoverEmphasis"])(this, focus, blurScope, emphasisDisabled);
    };
    ;
    Polyline.prototype.updateLayout = function(lineData, idx) {
        var polyline = this.childAt(0);
        polyline.setShape('points', lineData.getItemLayout(idx));
    };
    ;
    return Polyline;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Group$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Group$3e$__["Group"]);
const __TURBOPACK__default__export__ = Polyline;
}}),
"[project]/node_modules/echarts/lib/chart/helper/EffectPolyline.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/node_modules/tslib/tslib.es6.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$chart$2f$helper$2f$Polyline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/chart/helper/Polyline.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$chart$2f$helper$2f$EffectLine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/chart/helper/EffectLine.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/core/vector.js [app-ssr] (ecmascript)");
;
;
;
;
var EffectPolyline = /** @class */ function(_super) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__extends"])(EffectPolyline, _super);
    function EffectPolyline() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this._lastFrame = 0;
        _this._lastFramePercent = 0;
        return _this;
    }
    // Override
    EffectPolyline.prototype.createLine = function(lineData, idx, seriesScope) {
        return new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$chart$2f$helper$2f$Polyline$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"](lineData, idx, seriesScope);
    };
    ;
    // Override
    EffectPolyline.prototype._updateAnimationPoints = function(symbol, points) {
        this._points = points;
        var accLenArr = [
            0
        ];
        var len = 0;
        for(var i = 1; i < points.length; i++){
            var p1 = points[i - 1];
            var p2 = points[i];
            len += (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$vector$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["dist"])(p1, p2);
            accLenArr.push(len);
        }
        if (len === 0) {
            this._length = 0;
            return;
        }
        for(var i = 0; i < accLenArr.length; i++){
            accLenArr[i] /= len;
        }
        this._offsets = accLenArr;
        this._length = len;
    };
    ;
    // Override
    EffectPolyline.prototype._getLineLength = function() {
        return this._length;
    };
    ;
    // Override
    EffectPolyline.prototype._updateSymbolPosition = function(symbol) {
        var t = symbol.__t < 1 ? symbol.__t : 2 - symbol.__t;
        var points = this._points;
        var offsets = this._offsets;
        var len = points.length;
        if (!offsets) {
            // Has length 0
            return;
        }
        var lastFrame = this._lastFrame;
        var frame;
        if (t < this._lastFramePercent) {
            // Start from the next frame
            // PENDING start from lastFrame ?
            var start = Math.min(lastFrame + 1, len - 1);
            for(frame = start; frame >= 0; frame--){
                if (offsets[frame] <= t) {
                    break;
                }
            }
            // PENDING really need to do this ?
            frame = Math.min(frame, len - 2);
        } else {
            for(frame = lastFrame; frame < len; frame++){
                if (offsets[frame] > t) {
                    break;
                }
            }
            frame = Math.min(frame - 1, len - 2);
        }
        var p = (t - offsets[frame]) / (offsets[frame + 1] - offsets[frame]);
        var p0 = points[frame];
        var p1 = points[frame + 1];
        symbol.x = p0[0] * (1 - p) + p * p1[0];
        symbol.y = p0[1] * (1 - p) + p * p1[1];
        var tx = symbol.__t < 1 ? p1[0] - p0[0] : p0[0] - p1[0];
        var ty = symbol.__t < 1 ? p1[1] - p0[1] : p0[1] - p1[1];
        symbol.rotation = -Math.atan2(ty, tx) - Math.PI / 2;
        this._lastFrame = frame;
        this._lastFramePercent = t;
        symbol.ignore = false;
    };
    ;
    return EffectPolyline;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$chart$2f$helper$2f$EffectLine$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"]);
const __TURBOPACK__default__export__ = EffectPolyline;
}}),
"[project]/node_modules/echarts/lib/chart/helper/LargeLineDraw.js [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ /**
 * AUTO-GENERATED FILE. DO NOT MODIFY.
 */ /*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/ __turbopack_context__.s({
    "default": (()=>__TURBOPACK__default__export__)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/node_modules/tslib/tslib.es6.js [app-ssr] (ecmascript)");
// TODO Batch by color
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$BoundingRect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BoundingRect$3e$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/core/BoundingRect.js [app-ssr] (ecmascript) <export default as BoundingRect>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Path$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Path$3e$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/graphic/Path.js [app-ssr] (ecmascript) <export default as Path>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Group$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Group$3e$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/graphic/Group.js [app-ssr] (ecmascript) <export default as Group>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$contain$2f$line$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/contain/line.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$contain$2f$quadratic$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/zrender/lib/contain/quadratic.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$innerStore$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/echarts/lib/util/innerStore.js [app-ssr] (ecmascript)");
;
;
;
;
;
var LargeLinesPathShape = /** @class */ function() {
    function LargeLinesPathShape() {
        this.polyline = false;
        this.curveness = 0;
        this.segs = [];
    }
    return LargeLinesPathShape;
}();
var LargeLinesPath = /** @class */ function(_super) {
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$node_modules$2f$tslib$2f$tslib$2e$es6$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["__extends"])(LargeLinesPath, _super);
    function LargeLinesPath(opts) {
        var _this = _super.call(this, opts) || this;
        _this._off = 0;
        _this.hoverDataIdx = -1;
        return _this;
    }
    LargeLinesPath.prototype.reset = function() {
        this.notClear = false;
        this._off = 0;
    };
    LargeLinesPath.prototype.getDefaultStyle = function() {
        return {
            stroke: '#000',
            fill: null
        };
    };
    LargeLinesPath.prototype.getDefaultShape = function() {
        return new LargeLinesPathShape();
    };
    LargeLinesPath.prototype.buildPath = function(ctx, shape) {
        var segs = shape.segs;
        var curveness = shape.curveness;
        var i;
        if (shape.polyline) {
            for(i = this._off; i < segs.length;){
                var count = segs[i++];
                if (count > 0) {
                    ctx.moveTo(segs[i++], segs[i++]);
                    for(var k = 1; k < count; k++){
                        ctx.lineTo(segs[i++], segs[i++]);
                    }
                }
            }
        } else {
            for(i = this._off; i < segs.length;){
                var x0 = segs[i++];
                var y0 = segs[i++];
                var x1 = segs[i++];
                var y1 = segs[i++];
                ctx.moveTo(x0, y0);
                if (curveness > 0) {
                    var x2 = (x0 + x1) / 2 - (y0 - y1) * curveness;
                    var y2 = (y0 + y1) / 2 - (x1 - x0) * curveness;
                    ctx.quadraticCurveTo(x2, y2, x1, y1);
                } else {
                    ctx.lineTo(x1, y1);
                }
            }
        }
        if (this.incremental) {
            this._off = i;
            this.notClear = true;
        }
    };
    LargeLinesPath.prototype.findDataIndex = function(x, y) {
        var shape = this.shape;
        var segs = shape.segs;
        var curveness = shape.curveness;
        var lineWidth = this.style.lineWidth;
        if (shape.polyline) {
            var dataIndex = 0;
            for(var i = 0; i < segs.length;){
                var count = segs[i++];
                if (count > 0) {
                    var x0 = segs[i++];
                    var y0 = segs[i++];
                    for(var k = 1; k < count; k++){
                        var x1 = segs[i++];
                        var y1 = segs[i++];
                        if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$contain$2f$line$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["containStroke"])(x0, y0, x1, y1, lineWidth, x, y)) {
                            return dataIndex;
                        }
                    }
                }
                dataIndex++;
            }
        } else {
            var dataIndex = 0;
            for(var i = 0; i < segs.length;){
                var x0 = segs[i++];
                var y0 = segs[i++];
                var x1 = segs[i++];
                var y1 = segs[i++];
                if (curveness > 0) {
                    var x2 = (x0 + x1) / 2 - (y0 - y1) * curveness;
                    var y2 = (y0 + y1) / 2 - (x1 - x0) * curveness;
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$contain$2f$quadratic$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["containStroke"])(x0, y0, x2, y2, x1, y1, lineWidth, x, y)) {
                        return dataIndex;
                    }
                } else {
                    if ((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$contain$2f$line$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["containStroke"])(x0, y0, x1, y1, lineWidth, x, y)) {
                        return dataIndex;
                    }
                }
                dataIndex++;
            }
        }
        return -1;
    };
    LargeLinesPath.prototype.contain = function(x, y) {
        var localPos = this.transformCoordToLocal(x, y);
        var rect = this.getBoundingRect();
        x = localPos[0];
        y = localPos[1];
        if (rect.contain(x, y)) {
            // Cache found data index.
            var dataIdx = this.hoverDataIdx = this.findDataIndex(x, y);
            return dataIdx >= 0;
        }
        this.hoverDataIdx = -1;
        return false;
    };
    LargeLinesPath.prototype.getBoundingRect = function() {
        // Ignore stroke for large symbol draw.
        var rect = this._rect;
        if (!rect) {
            var shape = this.shape;
            var points = shape.segs;
            var minX = Infinity;
            var minY = Infinity;
            var maxX = -Infinity;
            var maxY = -Infinity;
            for(var i = 0; i < points.length;){
                var x = points[i++];
                var y = points[i++];
                minX = Math.min(x, minX);
                maxX = Math.max(x, maxX);
                minY = Math.min(y, minY);
                maxY = Math.max(y, maxY);
            }
            rect = this._rect = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$core$2f$BoundingRect$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__BoundingRect$3e$__["BoundingRect"](minX, minY, maxX, maxY);
        }
        return rect;
    };
    return LargeLinesPath;
}(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Path$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Path$3e$__["Path"]);
var LargeLineDraw = /** @class */ function() {
    function LargeLineDraw() {
        this.group = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$zrender$2f$lib$2f$graphic$2f$Group$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$export__default__as__Group$3e$__["Group"]();
    }
    /**
   * Update symbols draw by new data
   */ LargeLineDraw.prototype.updateData = function(data) {
        this._clear();
        var lineEl = this._create();
        lineEl.setShape({
            segs: data.getLayout('linesPoints')
        });
        this._setCommon(lineEl, data);
    };
    ;
    /**
   * @override
   */ LargeLineDraw.prototype.incrementalPrepareUpdate = function(data) {
        this.group.removeAll();
        this._clear();
    };
    ;
    /**
   * @override
   */ LargeLineDraw.prototype.incrementalUpdate = function(taskParams, data) {
        var lastAdded = this._newAdded[0];
        var linePoints = data.getLayout('linesPoints');
        var oldSegs = lastAdded && lastAdded.shape.segs;
        // Merging the exists. Each element has 1e4 points.
        // Consider the performance balance between too much elements and too much points in one shape(may affect hover optimization)
        if (oldSegs && oldSegs.length < 2e4) {
            var oldLen = oldSegs.length;
            var newSegs = new Float32Array(oldLen + linePoints.length);
            // Concat two array
            newSegs.set(oldSegs);
            newSegs.set(linePoints, oldLen);
            lastAdded.setShape({
                segs: newSegs
            });
        } else {
            // Clear
            this._newAdded = [];
            var lineEl = this._create();
            lineEl.incremental = true;
            lineEl.setShape({
                segs: linePoints
            });
            this._setCommon(lineEl, data);
            lineEl.__startIndex = taskParams.start;
        }
    };
    /**
   * @override
   */ LargeLineDraw.prototype.remove = function() {
        this._clear();
    };
    LargeLineDraw.prototype.eachRendered = function(cb) {
        this._newAdded[0] && cb(this._newAdded[0]);
    };
    LargeLineDraw.prototype._create = function() {
        var lineEl = new LargeLinesPath({
            cursor: 'default',
            ignoreCoarsePointer: true
        });
        this._newAdded.push(lineEl);
        this.group.add(lineEl);
        return lineEl;
    };
    LargeLineDraw.prototype._setCommon = function(lineEl, data, isIncremental) {
        var hostModel = data.hostModel;
        lineEl.setShape({
            polyline: hostModel.get('polyline'),
            curveness: hostModel.get([
                'lineStyle',
                'curveness'
            ])
        });
        lineEl.useStyle(hostModel.getModel('lineStyle').getLineStyle());
        lineEl.style.strokeNoScale = true;
        var style = data.getVisual('style');
        if (style && style.stroke) {
            lineEl.setStyle('stroke', style.stroke);
        }
        lineEl.setStyle('fill', null);
        var ecData = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$echarts$2f$lib$2f$util$2f$innerStore$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getECData"])(lineEl);
        // Enable tooltip
        // PENDING May have performance issue when path is extremely large
        ecData.seriesIndex = hostModel.seriesIndex;
        lineEl.on('mousemove', function(e) {
            ecData.dataIndex = null;
            var dataIndex = lineEl.hoverDataIdx;
            if (dataIndex > 0) {
                // Provide dataIndex for tooltip
                ecData.dataIndex = dataIndex + lineEl.__startIndex;
            }
        });
    };
    ;
    LargeLineDraw.prototype._clear = function() {
        this._newAdded = [];
        this.group.removeAll();
    };
    ;
    return LargeLineDraw;
}();
const __TURBOPACK__default__export__ = LargeLineDraw;
}}),

};

//# sourceMappingURL=node_modules_echarts_lib_chart_helper_6f1e2a2b._.js.map