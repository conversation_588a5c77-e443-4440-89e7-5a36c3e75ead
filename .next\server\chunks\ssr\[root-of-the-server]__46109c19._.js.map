{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/src/app/login/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport { motion } from 'framer-motion'\nimport { Eye, EyeOff, Lock, Mail, ArrowRight, Store } from 'lucide-react'\nimport Link from 'next/link'\nimport { useAuth } from '@/contexts/AuthContext'\n\nexport default function LoginPage() {\n  const [email, setEmail] = useState('')\n  const [password, setPassword] = useState('')\n  const [showPassword, setShowPassword] = useState(false)\n  const [error, setError] = useState('')\n  const [isSubmitting, setIsSubmitting] = useState(false)\n  \n  const { login, isAuthenticated, isLoading } = useAuth()\n  const router = useRouter()\n\n  useEffect(() => {\n    if (isAuthenticated && !isLoading) {\n      router.push('/admin')\n    }\n  }, [isAuthenticated, isLoading, router])\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault()\n    setError('')\n    setIsSubmitting(true)\n\n    if (!email || !password) {\n      setError('Please fill in all fields')\n      setIsSubmitting(false)\n      return\n    }\n\n    const success = await login(email, password)\n    \n    if (success) {\n      router.push('/admin')\n    } else {\n      setError('Invalid email or password')\n    }\n    \n    setIsSubmitting(false)\n  }\n\n  if (isLoading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-slate-900\">\n        <div className=\"text-center\">\n          <div className=\"w-16 h-16 hero-gradient rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse\">\n            <span className=\"text-white font-bold text-2xl\">R</span>\n          </div>\n          <p className=\"text-gray-600 dark:text-gray-400\">Loading...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen flex\">\n      {/* Left Side - Login Form */}\n      <div className=\"flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8 bg-white dark:bg-slate-900\">\n        <motion.div\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n          className=\"max-w-md w-full space-y-8\"\n        >\n          {/* Header */}\n          <div className=\"text-center\">\n            <Link href=\"/landing\" className=\"inline-flex items-center space-x-2 mb-6\">\n              <div className=\"w-12 h-12 hero-gradient rounded-full flex items-center justify-center\">\n                <span className=\"text-white font-bold text-xl\">R</span>\n              </div>\n              <span className=\"text-2xl font-bold text-gradient\">Revantad Store</span>\n            </Link>\n            \n            <h2 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n              Welcome Back\n            </h2>\n            <p className=\"mt-2 text-gray-600 dark:text-gray-400\">\n              Sign in to your admin dashboard\n            </p>\n          </div>\n\n          {/* Demo Credentials */}\n          <div className=\"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4\">\n            <h3 className=\"text-sm font-medium text-green-800 dark:text-green-400 mb-2\">\n              Demo Credentials\n            </h3>\n            <div className=\"text-sm text-green-700 dark:text-green-300 space-y-1\">\n              <p><strong>Email:</strong> <EMAIL></p>\n              <p><strong>Password:</strong> admin123</p>\n            </div>\n          </div>\n\n          {/* Login Form */}\n          <form onSubmit={handleSubmit} className=\"space-y-6\">\n            {error && (\n              <motion.div\n                initial={{ opacity: 0, scale: 0.95 }}\n                animate={{ opacity: 1, scale: 1 }}\n                className=\"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4\"\n              >\n                <p className=\"text-sm text-red-600 dark:text-red-400\">{error}</p>\n              </motion.div>\n            )}\n\n            <div>\n              <label htmlFor=\"email\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Email Address\n              </label>\n              <div className=\"relative\">\n                <Mail className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\" />\n                <input\n                  id=\"email\"\n                  type=\"email\"\n                  value={email}\n                  onChange={(e) => setEmail(e.target.value)}\n                  className=\"w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-800 dark:text-white\"\n                  placeholder=\"Enter your email\"\n                  required\n                />\n              </div>\n            </div>\n\n            <div>\n              <label htmlFor=\"password\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                Password\n              </label>\n              <div className=\"relative\">\n                <Lock className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5\" />\n                <input\n                  id=\"password\"\n                  type={showPassword ? 'text' : 'password'}\n                  value={password}\n                  onChange={(e) => setPassword(e.target.value)}\n                  className=\"w-full pl-10 pr-12 py-3 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-800 dark:text-white\"\n                  placeholder=\"Enter your password\"\n                  required\n                />\n                <button\n                  type=\"button\"\n                  onClick={() => setShowPassword(!showPassword)}\n                  className=\"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n                >\n                  {showPassword ? <EyeOff className=\"h-5 w-5\" /> : <Eye className=\"h-5 w-5\" />}\n                </button>\n              </div>\n            </div>\n\n            <button\n              type=\"submit\"\n              disabled={isSubmitting}\n              className=\"w-full btn-primary flex items-center justify-center disabled:opacity-50 disabled:cursor-not-allowed\"\n            >\n              {isSubmitting ? (\n                <div className=\"w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin\" />\n              ) : (\n                <>\n                  Sign In\n                  <ArrowRight className=\"ml-2 h-4 w-4\" />\n                </>\n              )}\n            </button>\n          </form>\n\n          {/* Footer */}\n          <div className=\"text-center\">\n            <p className=\"text-sm text-gray-600 dark:text-gray-400\">\n              Don&apos;t have an account?{' '}\n              <Link href=\"/landing\" className=\"text-green-600 dark:text-green-400 hover:underline\">\n                Contact Administrator\n              </Link>\n            </p>\n          </div>\n        </motion.div>\n      </div>\n\n      {/* Right Side - Hero Image */}\n      <div className=\"hidden lg:flex lg:flex-1 bg-gradient-to-br from-green-500 via-green-600 to-yellow-400 items-center justify-center p-12\">\n        <motion.div\n          initial={{ opacity: 0, scale: 0.8 }}\n          animate={{ opacity: 1, scale: 1 }}\n          transition={{ duration: 0.8, delay: 0.2 }}\n          className=\"text-center text-white\"\n        >\n          <Store className=\"h-24 w-24 mx-auto mb-6 opacity-90\" />\n          <h2 className=\"text-4xl font-bold mb-4\">\n            Manage Your Store\n          </h2>\n          <p className=\"text-xl text-green-100 max-w-md\">\n            Professional admin dashboard for your Revantad Store with advanced analytics and management tools.\n          </p>\n          \n          <div className=\"mt-8 grid grid-cols-2 gap-4 text-sm\">\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-4\">\n              <div className=\"text-2xl font-bold\">247</div>\n              <div className=\"text-green-100\">Products</div>\n            </div>\n            <div className=\"bg-white/10 backdrop-blur-sm rounded-lg p-4\">\n              <div className=\"text-2xl font-bold\">₱45K</div>\n              <div className=\"text-green-100\">Revenue</div>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAPA;;;;;;;;AASe,SAAS;IACtB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IACpD,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,mBAAmB,CAAC,WAAW;YACjC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QACT,gBAAgB;QAEhB,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,SAAS;YACT,gBAAgB;YAChB;QACF;QAEA,MAAM,UAAU,MAAM,MAAM,OAAO;QAEnC,IAAI,SAAS;YACX,OAAO,IAAI,CAAC;QACd,OAAO;YACL,SAAS;QACX;QAEA,gBAAgB;IAClB;IAEA,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAgC;;;;;;;;;;;kCAElD,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;;;;;;;;;;;;IAIxD;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,UAAU;oBAAI;oBAC5B,WAAU;;sCAGV,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAW,WAAU;;sDAC9B,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDAAK,WAAU;0DAA+B;;;;;;;;;;;sDAEjD,8OAAC;4CAAK,WAAU;sDAAmC;;;;;;;;;;;;8CAGrD,8OAAC;oCAAG,WAAU;8CAAmD;;;;;;8CAGjE,8OAAC;oCAAE,WAAU;8CAAwC;;;;;;;;;;;;sCAMvD,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA8D;;;;;;8CAG5E,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DAAE,8OAAC;8DAAO;;;;;;gDAAe;;;;;;;sDAC1B,8OAAC;;8DAAE,8OAAC;8DAAO;;;;;;gDAAkB;;;;;;;;;;;;;;;;;;;sCAKjC,8OAAC;4BAAK,UAAU;4BAAc,WAAU;;gCACrC,uBACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAK;oCACnC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,WAAU;8CAEV,cAAA,8OAAC;wCAAE,WAAU;kDAA0C;;;;;;;;;;;8CAI3D,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAQ,WAAU;sDAAkE;;;;;;sDAGnG,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACL,OAAO;oDACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;oDACxC,WAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;;;;;;;;;;;;;8CAKd,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;sDAAkE;;;;;;sDAGtG,8OAAC;4CAAI,WAAU;;8DACb,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;8DAChB,8OAAC;oDACC,IAAG;oDACH,MAAM,eAAe,SAAS;oDAC9B,OAAO;oDACP,UAAU,CAAC,IAAM,YAAY,EAAE,MAAM,CAAC,KAAK;oDAC3C,WAAU;oDACV,aAAY;oDACZ,QAAQ;;;;;;8DAEV,8OAAC;oDACC,MAAK;oDACL,SAAS,IAAM,gBAAgB,CAAC;oDAChC,WAAU;8DAET,6BAAe,8OAAC,0MAAA,CAAA,SAAM;wDAAC,WAAU;;;;;6EAAe,8OAAC,gMAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;8CAKtE,8OAAC;oCACC,MAAK;oCACL,UAAU;oCACV,WAAU;8CAET,6BACC,8OAAC;wCAAI,WAAU;;;;;6DAEf;;4CAAE;0DAEA,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;sCAO9B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;;oCAA2C;oCAC1B;kDAC5B,8OAAC,4JAAA,CAAA,UAAI;wCAAC,MAAK;wCAAW,WAAU;kDAAqD;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7F,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;oBACT,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAI;oBAClC,SAAS;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAChC,YAAY;wBAAE,UAAU;wBAAK,OAAO;oBAAI;oBACxC,WAAU;;sCAEV,8OAAC,oMAAA,CAAA,QAAK;4BAAC,WAAU;;;;;;sCACjB,8OAAC;4BAAG,WAAU;sCAA0B;;;;;;sCAGxC,8OAAC;4BAAE,WAAU;sCAAkC;;;;;;sCAI/C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDAAiB;;;;;;;;;;;;8CAElC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDAAqB;;;;;;sDACpC,8OAAC;4CAAI,WAAU;sDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO9C", "debugId": null}}]}