{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/model/mixin/makeStyleMapper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// TODO Parse shadow style\n// TODO Only shallow path support\nimport * as zrUtil from 'zrender/lib/core/util.js';\nexport default function makeStyleMapper(properties, ignoreParent) {\n  // Normalize\n  for (var i = 0; i < properties.length; i++) {\n    if (!properties[i][1]) {\n      properties[i][1] = properties[i][0];\n    }\n  }\n  ignoreParent = ignoreParent || false;\n  return function (model, excludes, includes) {\n    var style = {};\n    for (var i = 0; i < properties.length; i++) {\n      var propName = properties[i][1];\n      if (excludes && zrUtil.indexOf(excludes, propName) >= 0 || includes && zrUtil.indexOf(includes, propName) < 0) {\n        continue;\n      }\n      var val = model.getShallow(propName, ignoreParent);\n      if (val != null) {\n        style[properties[i][0]] = val;\n      }\n    }\n    // TODO Text or image?\n    return style;\n  };\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA,GACA,0BAA0B;AAC1B,iCAAiC;;;;AACjC;;AACe,SAAS,gBAAgB,UAAU,EAAE,YAAY;IAC9D,YAAY;IACZ,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;QAC1C,IAAI,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,EAAE;YACrB,UAAU,CAAC,EAAE,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,CAAC,EAAE;QACrC;IACF;IACA,eAAe,gBAAgB;IAC/B,OAAO,SAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ;QACxC,IAAI,QAAQ,CAAC;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YAC1C,IAAI,WAAW,UAAU,CAAC,EAAE,CAAC,EAAE;YAC/B,IAAI,YAAY,CAAA,GAAA,iJAAA,CAAA,UAAc,AAAD,EAAE,UAAU,aAAa,KAAK,YAAY,CAAA,GAAA,iJAAA,CAAA,UAAc,AAAD,EAAE,UAAU,YAAY,GAAG;gBAC7G;YACF;YACA,IAAI,MAAM,MAAM,UAAU,CAAC,UAAU;YACrC,IAAI,OAAO,MAAM;gBACf,KAAK,CAAC,UAAU,CAAC,EAAE,CAAC,EAAE,CAAC,GAAG;YAC5B;QACF;QACA,sBAAsB;QACtB,OAAO;IACT;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/model/mixin/areaStyle.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport makeStyleMapper from './makeStyleMapper.js';\nexport var AREA_STYLE_KEY_MAP = [['fill', 'color'], ['shadowBlur'], ['shadowOffsetX'], ['shadowOffsetY'], ['opacity'], ['shadowColor']\n// Option decal is in `DecalObject` but style.decal is in `PatternObject`.\n// So do not transfer decal directly.\n];\nvar getAreaStyle = makeStyleMapper(AREA_STYLE_KEY_MAP);\nvar AreaStyleMixin = /** @class */function () {\n  function AreaStyleMixin() {}\n  AreaStyleMixin.prototype.getAreaStyle = function (excludes, includes) {\n    return getAreaStyle(this, excludes, includes);\n  };\n  return AreaStyleMixin;\n}();\n;\nexport { AreaStyleMixin };"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;AACA;;AACO,IAAI,qBAAqB;IAAC;QAAC;QAAQ;KAAQ;IAAE;QAAC;KAAa;IAAE;QAAC;KAAgB;IAAE;QAAC;KAAgB;IAAE;QAAC;KAAU;IAAE;QAAC;KAAc;CAGrI;AACD,IAAI,eAAe,CAAA,GAAA,sKAAA,CAAA,UAAe,AAAD,EAAE;AACnC,IAAI,iBAAiB,WAAW,GAAE;IAChC,SAAS,kBAAkB;IAC3B,eAAe,SAAS,CAAC,YAAY,GAAG,SAAU,QAAQ,EAAE,QAAQ;QAClE,OAAO,aAAa,IAAI,EAAE,UAAU;IACtC;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/model/mixin/textStyle.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { getFont } from '../../label/labelStyle.js';\nimport ZRText from 'zrender/lib/graphic/Text.js';\nvar PATH_COLOR = ['textStyle', 'color'];\nvar textStyleParams = ['fontStyle', 'fontWeight', 'fontSize', 'fontFamily', 'padding', 'lineHeight', 'rich', 'width', 'height', 'overflow'];\n// TODO Performance improvement?\nvar tmpText = new ZRText();\nvar TextStyleMixin = /** @class */function () {\n  function TextStyleMixin() {}\n  /**\r\n   * Get color property or get color from option.textStyle.color\r\n   */\n  // TODO Callback\n  TextStyleMixin.prototype.getTextColor = function (isEmphasis) {\n    var ecModel = this.ecModel;\n    return this.getShallow('color') || (!isEmphasis && ecModel ? ecModel.get(PATH_COLOR) : null);\n  };\n  /**\r\n   * Create font string from fontStyle, fontWeight, fontSize, fontFamily\r\n   * @return {string}\r\n   */\n  TextStyleMixin.prototype.getFont = function () {\n    return getFont({\n      fontStyle: this.getShallow('fontStyle'),\n      fontWeight: this.getShallow('fontWeight'),\n      fontSize: this.getShallow('fontSize'),\n      fontFamily: this.getShallow('fontFamily')\n    }, this.ecModel);\n  };\n  TextStyleMixin.prototype.getTextRect = function (text) {\n    var style = {\n      text: text,\n      verticalAlign: this.getShallow('verticalAlign') || this.getShallow('baseline')\n    };\n    for (var i = 0; i < textStyleParams.length; i++) {\n      style[textStyleParams[i]] = this.getShallow(textStyleParams[i]);\n    }\n    tmpText.useStyle(style);\n    tmpText.update();\n    return tmpText.getBoundingRect();\n  };\n  return TextStyleMixin;\n}();\n;\nexport default TextStyleMixin;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;;;AACA,IAAI,aAAa;IAAC;IAAa;CAAQ;AACvC,IAAI,kBAAkB;IAAC;IAAa;IAAc;IAAY;IAAc;IAAW;IAAc;IAAQ;IAAS;IAAU;CAAW;AAC3I,gCAAgC;AAChC,IAAI,UAAU,IAAI,oJAAA,CAAA,UAAM;AACxB,IAAI,iBAAiB,WAAW,GAAE;IAChC,SAAS,kBAAkB;IAC3B;;GAEC,GACD,gBAAgB;IAChB,eAAe,SAAS,CAAC,YAAY,GAAG,SAAU,UAAU;QAC1D,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1B,OAAO,IAAI,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC,cAAc,UAAU,QAAQ,GAAG,CAAC,cAAc,IAAI;IAC7F;IACA;;;GAGC,GACD,eAAe,SAAS,CAAC,OAAO,GAAG;QACjC,OAAO,CAAA,GAAA,wJAAA,CAAA,UAAO,AAAD,EAAE;YACb,WAAW,IAAI,CAAC,UAAU,CAAC;YAC3B,YAAY,IAAI,CAAC,UAAU,CAAC;YAC5B,UAAU,IAAI,CAAC,UAAU,CAAC;YAC1B,YAAY,IAAI,CAAC,UAAU,CAAC;QAC9B,GAAG,IAAI,CAAC,OAAO;IACjB;IACA,eAAe,SAAS,CAAC,WAAW,GAAG,SAAU,IAAI;QACnD,IAAI,QAAQ;YACV,MAAM;YACN,eAAe,IAAI,CAAC,UAAU,CAAC,oBAAoB,IAAI,CAAC,UAAU,CAAC;QACrE;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAK;YAC/C,KAAK,CAAC,eAAe,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,UAAU,CAAC,eAAe,CAAC,EAAE;QAChE;QACA,QAAQ,QAAQ,CAAC;QACjB,QAAQ,MAAM;QACd,OAAO,QAAQ,eAAe;IAChC;IACA,OAAO;AACT;;uCAEe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 262, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/model/mixin/lineStyle.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport makeStyleMapper from './makeStyleMapper.js';\nexport var LINE_STYLE_KEY_MAP = [['lineWidth', 'width'], ['stroke', 'color'], ['opacity'], ['shadowBlur'], ['shadowOffsetX'], ['shadowOffsetY'], ['shadowColor'], ['lineDash', 'type'], ['lineDashOffset', 'dashOffset'], ['lineCap', 'cap'], ['lineJoin', 'join'], ['miterLimit']\n// Option decal is in `DecalObject` but style.decal is in `PatternObject`.\n// So do not transfer decal directly.\n];\nvar getLineStyle = makeStyleMapper(LINE_STYLE_KEY_MAP);\nvar LineStyleMixin = /** @class */function () {\n  function LineStyleMixin() {}\n  LineStyleMixin.prototype.getLineStyle = function (excludes) {\n    return getLineStyle(this, excludes);\n  };\n  return LineStyleMixin;\n}();\n;\nexport { LineStyleMixin };"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;AACA;;AACO,IAAI,qBAAqB;IAAC;QAAC;QAAa;KAAQ;IAAE;QAAC;QAAU;KAAQ;IAAE;QAAC;KAAU;IAAE;QAAC;KAAa;IAAE;QAAC;KAAgB;IAAE;QAAC;KAAgB;IAAE;QAAC;KAAc;IAAE;QAAC;QAAY;KAAO;IAAE;QAAC;QAAkB;KAAa;IAAE;QAAC;QAAW;KAAM;IAAE;QAAC;QAAY;KAAO;IAAE;QAAC;KAAa;CAGjR;AACD,IAAI,eAAe,CAAA,GAAA,sKAAA,CAAA,UAAe,AAAD,EAAE;AACnC,IAAI,iBAAiB,WAAW,GAAE;IAChC,SAAS,kBAAkB;IAC3B,eAAe,SAAS,CAAC,YAAY,GAAG,SAAU,QAAQ;QACxD,OAAO,aAAa,IAAI,EAAE;IAC5B;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 364, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/model/mixin/itemStyle.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport makeStyleMapper from './makeStyleMapper.js';\nexport var ITEM_STYLE_KEY_MAP = [['fill', 'color'], ['stroke', 'borderColor'], ['lineWidth', 'borderWidth'], ['opacity'], ['shadowBlur'], ['shadowOffsetX'], ['shadowOffsetY'], ['shadowColor'], ['lineDash', 'borderType'], ['lineDashOffset', 'borderDashOffset'], ['lineCap', 'borderCap'], ['lineJoin', 'borderJoin'], ['miterLimit', 'borderMiterLimit']\n// Option decal is in `DecalObject` but style.decal is in `PatternObject`.\n// So do not transfer decal directly.\n];\nvar getItemStyle = makeStyleMapper(ITEM_STYLE_KEY_MAP);\nvar ItemStyleMixin = /** @class */function () {\n  function ItemStyleMixin() {}\n  ItemStyleMixin.prototype.getItemStyle = function (excludes, includes) {\n    return getItemStyle(this, excludes, includes);\n  };\n  return ItemStyleMixin;\n}();\nexport { ItemStyleMixin };"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;AACA;;AACO,IAAI,qBAAqB;IAAC;QAAC;QAAQ;KAAQ;IAAE;QAAC;QAAU;KAAc;IAAE;QAAC;QAAa;KAAc;IAAE;QAAC;KAAU;IAAE;QAAC;KAAa;IAAE;QAAC;KAAgB;IAAE;QAAC;KAAgB;IAAE;QAAC;KAAc;IAAE;QAAC;QAAY;KAAa;IAAE;QAAC;QAAkB;KAAmB;IAAE;QAAC;QAAW;KAAY;IAAE;QAAC;QAAY;KAAa;IAAE;QAAC;QAAc;KAAmB;CAG5V;AACD,IAAI,eAAe,CAAA,GAAA,sKAAA,CAAA,UAAe,AAAD,EAAE;AACnC,IAAI,iBAAiB,WAAW,GAAE;IAChC,SAAS,kBAAkB;IAC3B,eAAe,SAAS,CAAC,YAAY,GAAG,SAAU,QAAQ,EAAE,QAAQ;QAClE,OAAO,aAAa,IAAI,EAAE,UAAU;IACtC;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 470, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/model/Model.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport env from 'zrender/lib/core/env.js';\nimport { enableClassExtend, enableClassCheck } from '../util/clazz.js';\nimport { AreaStyleMixin } from './mixin/areaStyle.js';\nimport TextStyleMixin from './mixin/textStyle.js';\nimport { LineStyleMixin } from './mixin/lineStyle.js';\nimport { ItemStyleMixin } from './mixin/itemStyle.js';\nimport { mixin, clone, merge } from 'zrender/lib/core/util.js';\nvar Model = /** @class */function () {\n  function Model(option, parentModel, ecModel) {\n    this.parentModel = parentModel;\n    this.ecModel = ecModel;\n    this.option = option;\n    // Simple optimization\n    // if (this.init) {\n    //     if (arguments.length <= 4) {\n    //         this.init(option, parentModel, ecModel, extraOpt);\n    //     }\n    //     else {\n    //         this.init.apply(this, arguments);\n    //     }\n    // }\n  }\n  Model.prototype.init = function (option, parentModel, ecModel) {\n    var rest = [];\n    for (var _i = 3; _i < arguments.length; _i++) {\n      rest[_i - 3] = arguments[_i];\n    }\n  };\n  /**\r\n   * Merge the input option to me.\r\n   */\n  Model.prototype.mergeOption = function (option, ecModel) {\n    merge(this.option, option, true);\n  };\n  // `path` can be 'a.b.c', so the return value type have to be `ModelOption`\n  // TODO: TYPE strict key check?\n  // get(path: string | string[], ignoreParent?: boolean): ModelOption;\n  Model.prototype.get = function (path, ignoreParent) {\n    if (path == null) {\n      return this.option;\n    }\n    return this._doGet(this.parsePath(path), !ignoreParent && this.parentModel);\n  };\n  Model.prototype.getShallow = function (key, ignoreParent) {\n    var option = this.option;\n    var val = option == null ? option : option[key];\n    if (val == null && !ignoreParent) {\n      var parentModel = this.parentModel;\n      if (parentModel) {\n        // FIXME:TS do not know how to make it works\n        val = parentModel.getShallow(key);\n      }\n    }\n    return val;\n  };\n  // `path` can be 'a.b.c', so the return value type have to be `Model<ModelOption>`\n  // getModel(path: string | string[], parentModel?: Model): Model;\n  // TODO 'a.b.c' is deprecated\n  Model.prototype.getModel = function (path, parentModel) {\n    var hasPath = path != null;\n    var pathFinal = hasPath ? this.parsePath(path) : null;\n    var obj = hasPath ? this._doGet(pathFinal) : this.option;\n    parentModel = parentModel || this.parentModel && this.parentModel.getModel(this.resolveParentPath(pathFinal));\n    return new Model(obj, parentModel, this.ecModel);\n  };\n  /**\r\n   * If model has option\r\n   */\n  Model.prototype.isEmpty = function () {\n    return this.option == null;\n  };\n  Model.prototype.restoreData = function () {};\n  // Pending\n  Model.prototype.clone = function () {\n    var Ctor = this.constructor;\n    return new Ctor(clone(this.option));\n  };\n  // setReadOnly(properties): void {\n  // clazzUtil.setReadOnly(this, properties);\n  // }\n  // If path is null/undefined, return null/undefined.\n  Model.prototype.parsePath = function (path) {\n    if (typeof path === 'string') {\n      return path.split('.');\n    }\n    return path;\n  };\n  // Resolve path for parent. Perhaps useful when parent use a different property.\n  // Default to be a identity resolver.\n  // Can be modified to a different resolver.\n  Model.prototype.resolveParentPath = function (path) {\n    return path;\n  };\n  // FIXME:TS check whether put this method here\n  Model.prototype.isAnimationEnabled = function () {\n    if (!env.node && this.option) {\n      if (this.option.animation != null) {\n        return !!this.option.animation;\n      } else if (this.parentModel) {\n        return this.parentModel.isAnimationEnabled();\n      }\n    }\n  };\n  Model.prototype._doGet = function (pathArr, parentModel) {\n    var obj = this.option;\n    if (!pathArr) {\n      return obj;\n    }\n    for (var i = 0; i < pathArr.length; i++) {\n      // Ignore empty\n      if (!pathArr[i]) {\n        continue;\n      }\n      // obj could be number/string/... (like 0)\n      obj = obj && typeof obj === 'object' ? obj[pathArr[i]] : null;\n      if (obj == null) {\n        break;\n      }\n    }\n    if (obj == null && parentModel) {\n      obj = parentModel._doGet(this.resolveParentPath(pathArr), parentModel.parentModel);\n    }\n    return obj;\n  };\n  return Model;\n}();\n;\n// Enable Model.extend.\nenableClassExtend(Model);\nenableClassCheck(Model);\nmixin(Model, LineStyleMixin);\nmixin(Model, ItemStyleMixin);\nmixin(Model, AreaStyleMixin);\nmixin(Model, TextStyleMixin);\nexport default Model;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACA,IAAI,QAAQ,WAAW,GAAE;IACvB,SAAS,MAAM,MAAM,EAAE,WAAW,EAAE,OAAO;QACzC,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,MAAM,GAAG;IACd,sBAAsB;IACtB,mBAAmB;IACnB,mCAAmC;IACnC,6DAA6D;IAC7D,QAAQ;IACR,aAAa;IACb,4CAA4C;IAC5C,QAAQ;IACR,IAAI;IACN;IACA,MAAM,SAAS,CAAC,IAAI,GAAG,SAAU,MAAM,EAAE,WAAW,EAAE,OAAO;QAC3D,IAAI,OAAO,EAAE;QACb,IAAK,IAAI,KAAK,GAAG,KAAK,UAAU,MAAM,EAAE,KAAM;YAC5C,IAAI,CAAC,KAAK,EAAE,GAAG,SAAS,CAAC,GAAG;QAC9B;IACF;IACA;;GAEC,GACD,MAAM,SAAS,CAAC,WAAW,GAAG,SAAU,MAAM,EAAE,OAAO;QACrD,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ;IAC7B;IACA,2EAA2E;IAC3E,+BAA+B;IAC/B,qEAAqE;IACrE,MAAM,SAAS,CAAC,GAAG,GAAG,SAAU,IAAI,EAAE,YAAY;QAChD,IAAI,QAAQ,MAAM;YAChB,OAAO,IAAI,CAAC,MAAM;QACpB;QACA,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,OAAO,CAAC,gBAAgB,IAAI,CAAC,WAAW;IAC5E;IACA,MAAM,SAAS,CAAC,UAAU,GAAG,SAAU,GAAG,EAAE,YAAY;QACtD,IAAI,SAAS,IAAI,CAAC,MAAM;QACxB,IAAI,MAAM,UAAU,OAAO,SAAS,MAAM,CAAC,IAAI;QAC/C,IAAI,OAAO,QAAQ,CAAC,cAAc;YAChC,IAAI,cAAc,IAAI,CAAC,WAAW;YAClC,IAAI,aAAa;gBACf,4CAA4C;gBAC5C,MAAM,YAAY,UAAU,CAAC;YAC/B;QACF;QACA,OAAO;IACT;IACA,kFAAkF;IAClF,iEAAiE;IACjE,6BAA6B;IAC7B,MAAM,SAAS,CAAC,QAAQ,GAAG,SAAU,IAAI,EAAE,WAAW;QACpD,IAAI,UAAU,QAAQ;QACtB,IAAI,YAAY,UAAU,IAAI,CAAC,SAAS,CAAC,QAAQ;QACjD,IAAI,MAAM,UAAU,IAAI,CAAC,MAAM,CAAC,aAAa,IAAI,CAAC,MAAM;QACxD,cAAc,eAAe,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC;QAClG,OAAO,IAAI,MAAM,KAAK,aAAa,IAAI,CAAC,OAAO;IACjD;IACA;;GAEC,GACD,MAAM,SAAS,CAAC,OAAO,GAAG;QACxB,OAAO,IAAI,CAAC,MAAM,IAAI;IACxB;IACA,MAAM,SAAS,CAAC,WAAW,GAAG,YAAa;IAC3C,UAAU;IACV,MAAM,SAAS,CAAC,KAAK,GAAG;QACtB,IAAI,OAAO,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,KAAK,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,IAAI,CAAC,MAAM;IACnC;IACA,kCAAkC;IAClC,2CAA2C;IAC3C,IAAI;IACJ,oDAAoD;IACpD,MAAM,SAAS,CAAC,SAAS,GAAG,SAAU,IAAI;QACxC,IAAI,OAAO,SAAS,UAAU;YAC5B,OAAO,KAAK,KAAK,CAAC;QACpB;QACA,OAAO;IACT;IACA,gFAAgF;IAChF,qCAAqC;IACrC,2CAA2C;IAC3C,MAAM,SAAS,CAAC,iBAAiB,GAAG,SAAU,IAAI;QAChD,OAAO;IACT;IACA,8CAA8C;IAC9C,MAAM,SAAS,CAAC,kBAAkB,GAAG;QACnC,IAAI,CAAC,gJAAA,CAAA,UAAG,CAAC,IAAI,IAAI,IAAI,CAAC,MAAM,EAAE;YAC5B,IAAI,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,MAAM;gBACjC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,SAAS;YAChC,OAAO,IAAI,IAAI,CAAC,WAAW,EAAE;gBAC3B,OAAO,IAAI,CAAC,WAAW,CAAC,kBAAkB;YAC5C;QACF;IACF;IACA,MAAM,SAAS,CAAC,MAAM,GAAG,SAAU,OAAO,EAAE,WAAW;QACrD,IAAI,MAAM,IAAI,CAAC,MAAM;QACrB,IAAI,CAAC,SAAS;YACZ,OAAO;QACT;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;YACvC,eAAe;YACf,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;gBACf;YACF;YACA,0CAA0C;YAC1C,MAAM,OAAO,OAAO,QAAQ,WAAW,GAAG,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG;YACzD,IAAI,OAAO,MAAM;gBACf;YACF;QACF;QACA,IAAI,OAAO,QAAQ,aAAa;YAC9B,MAAM,YAAY,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAC,UAAU,YAAY,WAAW;QACnF;QACA,OAAO;IACT;IACA,OAAO;AACT;;AAEA,uBAAuB;AACvB,CAAA,GAAA,kJAAA,CAAA,oBAAiB,AAAD,EAAE;AAClB,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE;AACjB,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,OAAO,gKAAA,CAAA,iBAAc;AAC3B,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,OAAO,gKAAA,CAAA,iBAAc;AAC3B,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,OAAO,gKAAA,CAAA,iBAAc;AAC3B,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,OAAO,gKAAA,CAAA,UAAc;uCACZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/model/Component.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport Model from './Model.js';\nimport * as componentUtil from '../util/component.js';\nimport { enableClassManagement, parseClassType, isExtendedClass, mountExtend } from '../util/clazz.js';\nimport { makeInner, queryReferringComponents } from '../util/model.js';\nimport * as layout from '../util/layout.js';\nvar inner = makeInner();\nvar ComponentModel = /** @class */function (_super) {\n  __extends(ComponentModel, _super);\n  function ComponentModel(option, parentModel, ecModel) {\n    var _this = _super.call(this, option, parentModel, ecModel) || this;\n    _this.uid = componentUtil.getUID('ec_cpt_model');\n    return _this;\n  }\n  ComponentModel.prototype.init = function (option, parentModel, ecModel) {\n    this.mergeDefaultAndTheme(option, ecModel);\n  };\n  ComponentModel.prototype.mergeDefaultAndTheme = function (option, ecModel) {\n    var layoutMode = layout.fetchLayoutMode(this);\n    var inputPositionParams = layoutMode ? layout.getLayoutParams(option) : {};\n    var themeModel = ecModel.getTheme();\n    zrUtil.merge(option, themeModel.get(this.mainType));\n    zrUtil.merge(option, this.getDefaultOption());\n    if (layoutMode) {\n      layout.mergeLayoutParam(option, inputPositionParams, layoutMode);\n    }\n  };\n  ComponentModel.prototype.mergeOption = function (option, ecModel) {\n    zrUtil.merge(this.option, option, true);\n    var layoutMode = layout.fetchLayoutMode(this);\n    if (layoutMode) {\n      layout.mergeLayoutParam(this.option, option, layoutMode);\n    }\n  };\n  /**\r\n   * Called immediately after `init` or `mergeOption` of this instance called.\r\n   */\n  ComponentModel.prototype.optionUpdated = function (newCptOption, isInit) {};\n  /**\r\n   * [How to declare defaultOption]:\r\n   *\r\n   * (A) If using class declaration in typescript (since echarts 5):\r\n   * ```ts\r\n   * import {ComponentOption} from '../model/option.js';\r\n   * export interface XxxOption extends ComponentOption {\r\n   *     aaa: number\r\n   * }\r\n   * export class XxxModel extends Component {\r\n   *     static type = 'xxx';\r\n   *     static defaultOption: XxxOption = {\r\n   *         aaa: 123\r\n   *     }\r\n   * }\r\n   * Component.registerClass(XxxModel);\r\n   * ```\r\n   * ```ts\r\n   * import {inheritDefaultOption} from '../util/component.js';\r\n   * import {XxxModel, XxxOption} from './XxxModel.js';\r\n   * export interface XxxSubOption extends XxxOption {\r\n   *     bbb: number\r\n   * }\r\n   * class XxxSubModel extends XxxModel {\r\n   *     static defaultOption: XxxSubOption = inheritDefaultOption(XxxModel.defaultOption, {\r\n   *         bbb: 456\r\n   *     })\r\n   *     fn() {\r\n   *         let opt = this.getDefaultOption();\r\n   *         // opt is {aaa: 123, bbb: 456}\r\n   *     }\r\n   * }\r\n   * ```\r\n   *\r\n   * (B) If using class extend (previous approach in echarts 3 & 4):\r\n   * ```js\r\n   * let XxxComponent = Component.extend({\r\n   *     defaultOption: {\r\n   *         xx: 123\r\n   *     }\r\n   * })\r\n   * ```\r\n   * ```js\r\n   * let XxxSubComponent = XxxComponent.extend({\r\n   *     defaultOption: {\r\n   *         yy: 456\r\n   *     },\r\n   *     fn: function () {\r\n   *         let opt = this.getDefaultOption();\r\n   *         // opt is {xx: 123, yy: 456}\r\n   *     }\r\n   * })\r\n   * ```\r\n   */\n  ComponentModel.prototype.getDefaultOption = function () {\n    var ctor = this.constructor;\n    // If using class declaration, it is different to travel super class\n    // in legacy env and auto merge defaultOption. So if using class\n    // declaration, defaultOption should be merged manually.\n    if (!isExtendedClass(ctor)) {\n      // When using ts class, defaultOption must be declared as static.\n      return ctor.defaultOption;\n    }\n    // FIXME: remove this approach?\n    var fields = inner(this);\n    if (!fields.defaultOption) {\n      var optList = [];\n      var clz = ctor;\n      while (clz) {\n        var opt = clz.prototype.defaultOption;\n        opt && optList.push(opt);\n        clz = clz.superClass;\n      }\n      var defaultOption = {};\n      for (var i = optList.length - 1; i >= 0; i--) {\n        defaultOption = zrUtil.merge(defaultOption, optList[i], true);\n      }\n      fields.defaultOption = defaultOption;\n    }\n    return fields.defaultOption;\n  };\n  /**\r\n   * Notice: always force to input param `useDefault` in case that forget to consider it.\r\n   * The same behavior as `modelUtil.parseFinder`.\r\n   *\r\n   * @param useDefault In many cases like series refer axis and axis refer grid,\r\n   *        If axis index / axis id not specified, use the first target as default.\r\n   *        In other cases like dataZoom refer axis, if not specified, measn no refer.\r\n   */\n  ComponentModel.prototype.getReferringComponents = function (mainType, opt) {\n    var indexKey = mainType + 'Index';\n    var idKey = mainType + 'Id';\n    return queryReferringComponents(this.ecModel, mainType, {\n      index: this.get(indexKey, true),\n      id: this.get(idKey, true)\n    }, opt);\n  };\n  ComponentModel.prototype.getBoxLayoutParams = function () {\n    // Consider itself having box layout configs.\n    var boxLayoutModel = this;\n    return {\n      left: boxLayoutModel.get('left'),\n      top: boxLayoutModel.get('top'),\n      right: boxLayoutModel.get('right'),\n      bottom: boxLayoutModel.get('bottom'),\n      width: boxLayoutModel.get('width'),\n      height: boxLayoutModel.get('height')\n    };\n  };\n  /**\r\n   * Get key for zlevel.\r\n   * If developers don't configure zlevel. We will assign zlevel to series based on the key.\r\n   * For example, lines with trail effect and progressive series will in an individual zlevel.\r\n   */\n  ComponentModel.prototype.getZLevelKey = function () {\n    return '';\n  };\n  ComponentModel.prototype.setZLevel = function (zlevel) {\n    this.option.zlevel = zlevel;\n  };\n  ComponentModel.protoInitialize = function () {\n    var proto = ComponentModel.prototype;\n    proto.type = 'component';\n    proto.id = '';\n    proto.name = '';\n    proto.mainType = '';\n    proto.subType = '';\n    proto.componentIndex = 0;\n  }();\n  return ComponentModel;\n}(Model);\nmountExtend(ComponentModel, Model);\nenableClassManagement(ComponentModel);\ncomponentUtil.enableSubTypeDefaulter(ComponentModel);\ncomponentUtil.enableTopologicalTravel(ComponentModel, getDependencies);\nfunction getDependencies(componentType) {\n  var deps = [];\n  zrUtil.each(ComponentModel.getClassesByMainType(componentType), function (clz) {\n    deps = deps.concat(clz.dependencies || clz.prototype.dependencies || []);\n  });\n  // Ensure main type.\n  deps = zrUtil.map(deps, function (type) {\n    return parseClassType(type).main;\n  });\n  // Hack dataset for convenience.\n  if (componentType !== 'dataset' && zrUtil.indexOf(deps, 'dataset') <= 0) {\n    deps.unshift('dataset');\n  }\n  return deps;\n}\nexport default ComponentModel;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACA,IAAI,QAAQ,CAAA,GAAA,kJAAA,CAAA,YAAS,AAAD;AACpB,IAAI,iBAAiB,WAAW,GAAE,SAAU,MAAM;IAChD,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;IAC1B,SAAS,eAAe,MAAM,EAAE,WAAW,EAAE,OAAO;QAClD,IAAI,QAAQ,OAAO,IAAI,CAAC,IAAI,EAAE,QAAQ,aAAa,YAAY,IAAI;QACnE,MAAM,GAAG,GAAG,CAAA,GAAA,sJAAA,CAAA,SAAoB,AAAD,EAAE;QACjC,OAAO;IACT;IACA,eAAe,SAAS,CAAC,IAAI,GAAG,SAAU,MAAM,EAAE,WAAW,EAAE,OAAO;QACpE,IAAI,CAAC,oBAAoB,CAAC,QAAQ;IACpC;IACA,eAAe,SAAS,CAAC,oBAAoB,GAAG,SAAU,MAAM,EAAE,OAAO;QACvE,IAAI,aAAa,CAAA,GAAA,mJAAA,CAAA,kBAAsB,AAAD,EAAE,IAAI;QAC5C,IAAI,sBAAsB,aAAa,CAAA,GAAA,mJAAA,CAAA,kBAAsB,AAAD,EAAE,UAAU,CAAC;QACzE,IAAI,aAAa,QAAQ,QAAQ;QACjC,CAAA,GAAA,iJAAA,CAAA,QAAY,AAAD,EAAE,QAAQ,WAAW,GAAG,CAAC,IAAI,CAAC,QAAQ;QACjD,CAAA,GAAA,iJAAA,CAAA,QAAY,AAAD,EAAE,QAAQ,IAAI,CAAC,gBAAgB;QAC1C,IAAI,YAAY;YACd,CAAA,GAAA,mJAAA,CAAA,mBAAuB,AAAD,EAAE,QAAQ,qBAAqB;QACvD;IACF;IACA,eAAe,SAAS,CAAC,WAAW,GAAG,SAAU,MAAM,EAAE,OAAO;QAC9D,CAAA,GAAA,iJAAA,CAAA,QAAY,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ;QAClC,IAAI,aAAa,CAAA,GAAA,mJAAA,CAAA,kBAAsB,AAAD,EAAE,IAAI;QAC5C,IAAI,YAAY;YACd,CAAA,GAAA,mJAAA,CAAA,mBAAuB,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE,QAAQ;QAC/C;IACF;IACA;;GAEC,GACD,eAAe,SAAS,CAAC,aAAa,GAAG,SAAU,YAAY,EAAE,MAAM,GAAG;IAC1E;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAqDC,GACD,eAAe,SAAS,CAAC,gBAAgB,GAAG;QAC1C,IAAI,OAAO,IAAI,CAAC,WAAW;QAC3B,oEAAoE;QACpE,gEAAgE;QAChE,wDAAwD;QACxD,IAAI,CAAC,CAAA,GAAA,kJAAA,CAAA,kBAAe,AAAD,EAAE,OAAO;YAC1B,iEAAiE;YACjE,OAAO,KAAK,aAAa;QAC3B;QACA,+BAA+B;QAC/B,IAAI,SAAS,MAAM,IAAI;QACvB,IAAI,CAAC,OAAO,aAAa,EAAE;YACzB,IAAI,UAAU,EAAE;YAChB,IAAI,MAAM;YACV,MAAO,IAAK;gBACV,IAAI,MAAM,IAAI,SAAS,CAAC,aAAa;gBACrC,OAAO,QAAQ,IAAI,CAAC;gBACpB,MAAM,IAAI,UAAU;YACtB;YACA,IAAI,gBAAgB,CAAC;YACrB,IAAK,IAAI,IAAI,QAAQ,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;gBAC5C,gBAAgB,CAAA,GAAA,iJAAA,CAAA,QAAY,AAAD,EAAE,eAAe,OAAO,CAAC,EAAE,EAAE;YAC1D;YACA,OAAO,aAAa,GAAG;QACzB;QACA,OAAO,OAAO,aAAa;IAC7B;IACA;;;;;;;GAOC,GACD,eAAe,SAAS,CAAC,sBAAsB,GAAG,SAAU,QAAQ,EAAE,GAAG;QACvE,IAAI,WAAW,WAAW;QAC1B,IAAI,QAAQ,WAAW;QACvB,OAAO,CAAA,GAAA,kJAAA,CAAA,2BAAwB,AAAD,EAAE,IAAI,CAAC,OAAO,EAAE,UAAU;YACtD,OAAO,IAAI,CAAC,GAAG,CAAC,UAAU;YAC1B,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO;QACtB,GAAG;IACL;IACA,eAAe,SAAS,CAAC,kBAAkB,GAAG;QAC5C,6CAA6C;QAC7C,IAAI,iBAAiB,IAAI;QACzB,OAAO;YACL,MAAM,eAAe,GAAG,CAAC;YACzB,KAAK,eAAe,GAAG,CAAC;YACxB,OAAO,eAAe,GAAG,CAAC;YAC1B,QAAQ,eAAe,GAAG,CAAC;YAC3B,OAAO,eAAe,GAAG,CAAC;YAC1B,QAAQ,eAAe,GAAG,CAAC;QAC7B;IACF;IACA;;;;GAIC,GACD,eAAe,SAAS,CAAC,YAAY,GAAG;QACtC,OAAO;IACT;IACA,eAAe,SAAS,CAAC,SAAS,GAAG,SAAU,MAAM;QACnD,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG;IACvB;IACA,eAAe,eAAe,GAAG;QAC/B,IAAI,QAAQ,eAAe,SAAS;QACpC,MAAM,IAAI,GAAG;QACb,MAAM,EAAE,GAAG;QACX,MAAM,IAAI,GAAG;QACb,MAAM,QAAQ,GAAG;QACjB,MAAM,OAAO,GAAG;QAChB,MAAM,cAAc,GAAG;IACzB;IACA,OAAO;AACT,EAAE,mJAAA,CAAA,UAAK;AACP,CAAA,GAAA,kJAAA,CAAA,cAAW,AAAD,EAAE,gBAAgB,mJAAA,CAAA,UAAK;AACjC,CAAA,GAAA,kJAAA,CAAA,wBAAqB,AAAD,EAAE;AACtB,CAAA,GAAA,sJAAA,CAAA,yBAAoC,AAAD,EAAE;AACrC,CAAA,GAAA,sJAAA,CAAA,0BAAqC,AAAD,EAAE,gBAAgB;AACtD,SAAS,gBAAgB,aAAa;IACpC,IAAI,OAAO,EAAE;IACb,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,eAAe,oBAAoB,CAAC,gBAAgB,SAAU,GAAG;QAC3E,OAAO,KAAK,MAAM,CAAC,IAAI,YAAY,IAAI,IAAI,SAAS,CAAC,YAAY,IAAI,EAAE;IACzE;IACA,oBAAoB;IACpB,OAAO,CAAA,GAAA,iJAAA,CAAA,MAAU,AAAD,EAAE,MAAM,SAAU,IAAI;QACpC,OAAO,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,IAAI;IAClC;IACA,gCAAgC;IAChC,IAAI,kBAAkB,aAAa,CAAA,GAAA,iJAAA,CAAA,UAAc,AAAD,EAAE,MAAM,cAAc,GAAG;QACvE,KAAK,OAAO,CAAC;IACf;IACA,OAAO;AACT;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 893, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/model/globalDefault.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nvar platform = '';\n// Navigator not exists in node\nif (typeof navigator !== 'undefined') {\n  /* global navigator */\n  platform = navigator.platform || '';\n}\nvar decalColor = 'rgba(0, 0, 0, 0.2)';\nexport default {\n  darkMode: 'auto',\n  // backgroundColor: 'rgba(0,0,0,0)',\n  colorBy: 'series',\n  color: ['#5470c6', '#91cc75', '#fac858', '#ee6666', '#73c0de', '#3ba272', '#fc8452', '#9a60b4', '#ea7ccc'],\n  gradientColor: ['#f6efa6', '#d88273', '#bf444c'],\n  aria: {\n    decal: {\n      decals: [{\n        color: decalColor,\n        dashArrayX: [1, 0],\n        dashArrayY: [2, 5],\n        symbolSize: 1,\n        rotation: Math.PI / 6\n      }, {\n        color: decalColor,\n        symbol: 'circle',\n        dashArrayX: [[8, 8], [0, 8, 8, 0]],\n        dashArrayY: [6, 0],\n        symbolSize: 0.8\n      }, {\n        color: decalColor,\n        dashArrayX: [1, 0],\n        dashArrayY: [4, 3],\n        rotation: -Math.PI / 4\n      }, {\n        color: decalColor,\n        dashArrayX: [[6, 6], [0, 6, 6, 0]],\n        dashArrayY: [6, 0]\n      }, {\n        color: decalColor,\n        dashArrayX: [[1, 0], [1, 6]],\n        dashArrayY: [1, 0, 6, 0],\n        rotation: Math.PI / 4\n      }, {\n        color: decalColor,\n        symbol: 'triangle',\n        dashArrayX: [[9, 9], [0, 9, 9, 0]],\n        dashArrayY: [7, 2],\n        symbolSize: 0.75\n      }]\n    }\n  },\n  // If xAxis and yAxis declared, grid is created by default.\n  // grid: {},\n  textStyle: {\n    // color: '#000',\n    // decoration: 'none',\n    // PENDING\n    fontFamily: platform.match(/^Win/) ? 'Microsoft YaHei' : 'sans-serif',\n    // fontFamily: 'Arial, Verdana, sans-serif',\n    fontSize: 12,\n    fontStyle: 'normal',\n    fontWeight: 'normal'\n  },\n  // http://blogs.adobe.com/webplatform/2014/02/24/using-blend-modes-in-html-canvas/\n  // https://developer.mozilla.org/en-US/docs/Web/API/CanvasRenderingContext2D/globalCompositeOperation\n  // Default is source-over\n  blendMode: null,\n  stateAnimation: {\n    duration: 300,\n    easing: 'cubicOut'\n  },\n  animation: 'auto',\n  animationDuration: 1000,\n  animationDurationUpdate: 500,\n  animationEasing: 'cubicInOut',\n  animationEasingUpdate: 'cubicInOut',\n  animationThreshold: 2000,\n  // Configuration for progressive/incremental rendering\n  progressiveThreshold: 3000,\n  progressive: 400,\n  // Threshold of if use single hover layer to optimize.\n  // It is recommended that `hoverLayerThreshold` is equivalent to or less than\n  // `progressiveThreshold`, otherwise hover will cause restart of progressive,\n  // which is unexpected.\n  // see example <echarts/test/heatmap-large.html>.\n  hoverLayerThreshold: 3000,\n  // See: module:echarts/scale/Time\n  useUTC: false\n};"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA,IAAI,WAAW;AACf,+BAA+B;AAC/B,IAAI,OAAO,cAAc,aAAa;IACpC,oBAAoB,GACpB,WAAW,UAAU,QAAQ,IAAI;AACnC;AACA,IAAI,aAAa;uCACF;IACb,UAAU;IACV,oCAAoC;IACpC,SAAS;IACT,OAAO;QAAC;QAAW;QAAW;QAAW;QAAW;QAAW;QAAW;QAAW;QAAW;KAAU;IAC1G,eAAe;QAAC;QAAW;QAAW;KAAU;IAChD,MAAM;QACJ,OAAO;YACL,QAAQ;gBAAC;oBACP,OAAO;oBACP,YAAY;wBAAC;wBAAG;qBAAE;oBAClB,YAAY;wBAAC;wBAAG;qBAAE;oBAClB,YAAY;oBACZ,UAAU,KAAK,EAAE,GAAG;gBACtB;gBAAG;oBACD,OAAO;oBACP,QAAQ;oBACR,YAAY;wBAAC;4BAAC;4BAAG;yBAAE;wBAAE;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE;qBAAC;oBAClC,YAAY;wBAAC;wBAAG;qBAAE;oBAClB,YAAY;gBACd;gBAAG;oBACD,OAAO;oBACP,YAAY;wBAAC;wBAAG;qBAAE;oBAClB,YAAY;wBAAC;wBAAG;qBAAE;oBAClB,UAAU,CAAC,KAAK,EAAE,GAAG;gBACvB;gBAAG;oBACD,OAAO;oBACP,YAAY;wBAAC;4BAAC;4BAAG;yBAAE;wBAAE;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE;qBAAC;oBAClC,YAAY;wBAAC;wBAAG;qBAAE;gBACpB;gBAAG;oBACD,OAAO;oBACP,YAAY;wBAAC;4BAAC;4BAAG;yBAAE;wBAAE;4BAAC;4BAAG;yBAAE;qBAAC;oBAC5B,YAAY;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE;oBACxB,UAAU,KAAK,EAAE,GAAG;gBACtB;gBAAG;oBACD,OAAO;oBACP,QAAQ;oBACR,YAAY;wBAAC;4BAAC;4BAAG;yBAAE;wBAAE;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE;qBAAC;oBAClC,YAAY;wBAAC;wBAAG;qBAAE;oBAClB,YAAY;gBACd;aAAE;QACJ;IACF;IACA,2DAA2D;IAC3D,YAAY;IACZ,WAAW;QACT,iBAAiB;QACjB,sBAAsB;QACtB,UAAU;QACV,YAAY,SAAS,KAAK,CAAC,UAAU,oBAAoB;QACzD,4CAA4C;QAC5C,UAAU;QACV,WAAW;QACX,YAAY;IACd;IACA,kFAAkF;IAClF,qGAAqG;IACrG,yBAAyB;IACzB,WAAW;IACX,gBAAgB;QACd,UAAU;QACV,QAAQ;IACV;IACA,WAAW;IACX,mBAAmB;IACnB,yBAAyB;IACzB,iBAAiB;IACjB,uBAAuB;IACvB,oBAAoB;IACpB,sDAAsD;IACtD,sBAAsB;IACtB,aAAa;IACb,sDAAsD;IACtD,6EAA6E;IAC7E,6EAA6E;IAC7E,uBAAuB;IACvB,iDAAiD;IACjD,qBAAqB;IACrB,iCAAiC;IACjC,QAAQ;AACV", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/model/internalComponentCreator.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { createHashMap, assert } from 'zrender/lib/core/util.js';\nimport { isComponentIdInternal } from '../util/model.js';\nvar internalOptionCreatorMap = createHashMap();\nexport function registerInternalOptionCreator(mainType, creator) {\n  assert(internalOptionCreatorMap.get(mainType) == null && creator);\n  internalOptionCreatorMap.set(mainType, creator);\n}\nexport function concatInternalOptions(ecModel, mainType, newCmptOptionList) {\n  var internalOptionCreator = internalOptionCreatorMap.get(mainType);\n  if (!internalOptionCreator) {\n    return newCmptOptionList;\n  }\n  var internalOptions = internalOptionCreator(ecModel);\n  if (!internalOptions) {\n    return newCmptOptionList;\n  }\n  if (process.env.NODE_ENV !== 'production') {\n    for (var i = 0; i < internalOptions.length; i++) {\n      assert(isComponentIdInternal(internalOptions[i]));\n    }\n  }\n  return newCmptOptionList.concat(internalOptions);\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;AAiBM;AAhBN;AACA;;;AACA,IAAI,2BAA2B,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;AACpC,SAAS,8BAA8B,QAAQ,EAAE,OAAO;IAC7D,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,yBAAyB,GAAG,CAAC,aAAa,QAAQ;IACzD,yBAAyB,GAAG,CAAC,UAAU;AACzC;AACO,SAAS,sBAAsB,OAAO,EAAE,QAAQ,EAAE,iBAAiB;IACxE,IAAI,wBAAwB,yBAAyB,GAAG,CAAC;IACzD,IAAI,CAAC,uBAAuB;QAC1B,OAAO;IACT;IACA,IAAI,kBAAkB,sBAAsB;IAC5C,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IACA,wCAA2C;QACzC,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAK;YAC/C,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,kJAAA,CAAA,wBAAqB,AAAD,EAAE,eAAe,CAAC,EAAE;QACjD;IACF;IACA,OAAO,kBAAkB,MAAM,CAAC;AAClC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1186, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/model/mixin/palette.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { makeInner, normalizeToArray } from '../../util/model.js';\nvar innerColor = makeInner();\nvar innerDecal = makeInner();\nvar PaletteMixin = /** @class */function () {\n  function PaletteMixin() {}\n  PaletteMixin.prototype.getColorFromPalette = function (name, scope, requestNum) {\n    var defaultPalette = normalizeToArray(this.get('color', true));\n    var layeredPalette = this.get('colorLayer', true);\n    return getFromPalette(this, innerColor, defaultPalette, layeredPalette, name, scope, requestNum);\n  };\n  PaletteMixin.prototype.clearColorPalette = function () {\n    clearPalette(this, innerColor);\n  };\n  return PaletteMixin;\n}();\nexport function getDecalFromPalette(ecModel, name, scope, requestNum) {\n  var defaultDecals = normalizeToArray(ecModel.get(['aria', 'decal', 'decals']));\n  return getFromPalette(ecModel, innerDecal, defaultDecals, null, name, scope, requestNum);\n}\nfunction getNearestPalette(palettes, requestColorNum) {\n  var paletteNum = palettes.length;\n  // TODO palettes must be in order\n  for (var i = 0; i < paletteNum; i++) {\n    if (palettes[i].length > requestColorNum) {\n      return palettes[i];\n    }\n  }\n  return palettes[paletteNum - 1];\n}\n/**\r\n * @param name MUST NOT be null/undefined. Otherwise call this function\r\n *             twise with the same parameters will get different result.\r\n * @param scope default this.\r\n * @return Can be null/undefined\r\n */\nfunction getFromPalette(that, inner, defaultPalette, layeredPalette, name, scope, requestNum) {\n  scope = scope || that;\n  var scopeFields = inner(scope);\n  var paletteIdx = scopeFields.paletteIdx || 0;\n  var paletteNameMap = scopeFields.paletteNameMap = scopeFields.paletteNameMap || {};\n  // Use `hasOwnProperty` to avoid conflict with Object.prototype.\n  if (paletteNameMap.hasOwnProperty(name)) {\n    return paletteNameMap[name];\n  }\n  var palette = requestNum == null || !layeredPalette ? defaultPalette : getNearestPalette(layeredPalette, requestNum);\n  // In case can't find in layered color palette.\n  palette = palette || defaultPalette;\n  if (!palette || !palette.length) {\n    return;\n  }\n  var pickedPaletteItem = palette[paletteIdx];\n  if (name) {\n    paletteNameMap[name] = pickedPaletteItem;\n  }\n  scopeFields.paletteIdx = (paletteIdx + 1) % palette.length;\n  return pickedPaletteItem;\n}\nfunction clearPalette(that, inner) {\n  inner(that).paletteIdx = 0;\n  inner(that).paletteNameMap = {};\n}\nexport { PaletteMixin };"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;AACA;;AACA,IAAI,aAAa,CAAA,GAAA,kJAAA,CAAA,YAAS,AAAD;AACzB,IAAI,aAAa,CAAA,GAAA,kJAAA,CAAA,YAAS,AAAD;AACzB,IAAI,eAAe,WAAW,GAAE;IAC9B,SAAS,gBAAgB;IACzB,aAAa,SAAS,CAAC,mBAAmB,GAAG,SAAU,IAAI,EAAE,KAAK,EAAE,UAAU;QAC5E,IAAI,iBAAiB,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,CAAC,GAAG,CAAC,SAAS;QACxD,IAAI,iBAAiB,IAAI,CAAC,GAAG,CAAC,cAAc;QAC5C,OAAO,eAAe,IAAI,EAAE,YAAY,gBAAgB,gBAAgB,MAAM,OAAO;IACvF;IACA,aAAa,SAAS,CAAC,iBAAiB,GAAG;QACzC,aAAa,IAAI,EAAE;IACrB;IACA,OAAO;AACT;AACO,SAAS,oBAAoB,OAAO,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU;IAClE,IAAI,gBAAgB,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,GAAG,CAAC;QAAC;QAAQ;QAAS;KAAS;IAC5E,OAAO,eAAe,SAAS,YAAY,eAAe,MAAM,MAAM,OAAO;AAC/E;AACA,SAAS,kBAAkB,QAAQ,EAAE,eAAe;IAClD,IAAI,aAAa,SAAS,MAAM;IAChC,iCAAiC;IACjC,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,IAAK;QACnC,IAAI,QAAQ,CAAC,EAAE,CAAC,MAAM,GAAG,iBAAiB;YACxC,OAAO,QAAQ,CAAC,EAAE;QACpB;IACF;IACA,OAAO,QAAQ,CAAC,aAAa,EAAE;AACjC;AACA;;;;;CAKC,GACD,SAAS,eAAe,IAAI,EAAE,KAAK,EAAE,cAAc,EAAE,cAAc,EAAE,IAAI,EAAE,KAAK,EAAE,UAAU;IAC1F,QAAQ,SAAS;IACjB,IAAI,cAAc,MAAM;IACxB,IAAI,aAAa,YAAY,UAAU,IAAI;IAC3C,IAAI,iBAAiB,YAAY,cAAc,GAAG,YAAY,cAAc,IAAI,CAAC;IACjF,gEAAgE;IAChE,IAAI,eAAe,cAAc,CAAC,OAAO;QACvC,OAAO,cAAc,CAAC,KAAK;IAC7B;IACA,IAAI,UAAU,cAAc,QAAQ,CAAC,iBAAiB,iBAAiB,kBAAkB,gBAAgB;IACzG,+CAA+C;IAC/C,UAAU,WAAW;IACrB,IAAI,CAAC,WAAW,CAAC,QAAQ,MAAM,EAAE;QAC/B;IACF;IACA,IAAI,oBAAoB,OAAO,CAAC,WAAW;IAC3C,IAAI,MAAM;QACR,cAAc,CAAC,KAAK,GAAG;IACzB;IACA,YAAY,UAAU,GAAG,CAAC,aAAa,CAAC,IAAI,QAAQ,MAAM;IAC1D,OAAO;AACT;AACA,SAAS,aAAa,IAAI,EAAE,KAAK;IAC/B,MAAM,MAAM,UAAU,GAAG;IACzB,MAAM,MAAM,cAAc,GAAG,CAAC;AAChC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1298, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/model/Global.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\n/**\r\n * Caution: If the mechanism should be changed some day, these cases\r\n * should be considered:\r\n *\r\n * (1) In `merge option` mode, if using the same option to call `setOption`\r\n * many times, the result should be the same (try our best to ensure that).\r\n * (2) In `merge option` mode, if a component has no id/name specified, it\r\n * will be merged by index, and the result sequence of the components is\r\n * consistent to the original sequence.\r\n * (3) In `replaceMerge` mode, keep the result sequence of the components is\r\n * consistent to the original sequence, even though there might result in \"hole\".\r\n * (4) `reset` feature (in toolbox). Find detailed info in comments about\r\n * `mergeOption` in module:echarts/model/OptionManager.\r\n */\nimport { each, filter, isArray, isObject, isString, createHashMap, assert, clone, merge, extend, mixin, isFunction } from 'zrender/lib/core/util.js';\nimport * as modelUtil from '../util/model.js';\nimport Model from './Model.js';\nimport ComponentModel from './Component.js';\nimport globalDefault from './globalDefault.js';\nimport { resetSourceDefaulter } from '../data/helper/sourceHelper.js';\nimport { concatInternalOptions } from './internalComponentCreator.js';\nimport { PaletteMixin } from './mixin/palette.js';\nimport { error, warn } from '../util/log.js';\n// -----------------------\n// Internal method names:\n// -----------------------\nvar reCreateSeriesIndices;\nvar assertSeriesInitialized;\nvar initBase;\nvar OPTION_INNER_KEY = '\\0_ec_inner';\nvar OPTION_INNER_VALUE = 1;\nvar BUITIN_COMPONENTS_MAP = {\n  grid: 'GridComponent',\n  polar: 'PolarComponent',\n  geo: 'GeoComponent',\n  singleAxis: 'SingleAxisComponent',\n  parallel: 'ParallelComponent',\n  calendar: 'CalendarComponent',\n  graphic: 'GraphicComponent',\n  toolbox: 'ToolboxComponent',\n  tooltip: 'TooltipComponent',\n  axisPointer: 'AxisPointerComponent',\n  brush: 'BrushComponent',\n  title: 'TitleComponent',\n  timeline: 'TimelineComponent',\n  markPoint: 'MarkPointComponent',\n  markLine: 'MarkLineComponent',\n  markArea: 'MarkAreaComponent',\n  legend: 'LegendComponent',\n  dataZoom: 'DataZoomComponent',\n  visualMap: 'VisualMapComponent',\n  // aria: 'AriaComponent',\n  // dataset: 'DatasetComponent',\n  // Dependencies\n  xAxis: 'GridComponent',\n  yAxis: 'GridComponent',\n  angleAxis: 'PolarComponent',\n  radiusAxis: 'PolarComponent'\n};\nvar BUILTIN_CHARTS_MAP = {\n  line: 'LineChart',\n  bar: 'BarChart',\n  pie: 'PieChart',\n  scatter: 'ScatterChart',\n  radar: 'RadarChart',\n  map: 'MapChart',\n  tree: 'TreeChart',\n  treemap: 'TreemapChart',\n  graph: 'GraphChart',\n  gauge: 'GaugeChart',\n  funnel: 'FunnelChart',\n  parallel: 'ParallelChart',\n  sankey: 'SankeyChart',\n  boxplot: 'BoxplotChart',\n  candlestick: 'CandlestickChart',\n  effectScatter: 'EffectScatterChart',\n  lines: 'LinesChart',\n  heatmap: 'HeatmapChart',\n  pictorialBar: 'PictorialBarChart',\n  themeRiver: 'ThemeRiverChart',\n  sunburst: 'SunburstChart',\n  custom: 'CustomChart'\n};\nvar componetsMissingLogPrinted = {};\nfunction checkMissingComponents(option) {\n  each(option, function (componentOption, mainType) {\n    if (!ComponentModel.hasClass(mainType)) {\n      var componentImportName = BUITIN_COMPONENTS_MAP[mainType];\n      if (componentImportName && !componetsMissingLogPrinted[componentImportName]) {\n        error(\"Component \" + mainType + \" is used but not imported.\\nimport { \" + componentImportName + \" } from 'echarts/components';\\necharts.use([\" + componentImportName + \"]);\");\n        componetsMissingLogPrinted[componentImportName] = true;\n      }\n    }\n  });\n}\nvar GlobalModel = /** @class */function (_super) {\n  __extends(GlobalModel, _super);\n  function GlobalModel() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  GlobalModel.prototype.init = function (option, parentModel, ecModel, theme, locale, optionManager) {\n    theme = theme || {};\n    this.option = null; // Mark as not initialized.\n    this._theme = new Model(theme);\n    this._locale = new Model(locale);\n    this._optionManager = optionManager;\n  };\n  GlobalModel.prototype.setOption = function (option, opts, optionPreprocessorFuncs) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(option != null, 'option is null/undefined');\n      assert(option[OPTION_INNER_KEY] !== OPTION_INNER_VALUE, 'please use chart.getOption()');\n    }\n    var innerOpt = normalizeSetOptionInput(opts);\n    this._optionManager.setOption(option, optionPreprocessorFuncs, innerOpt);\n    this._resetOption(null, innerOpt);\n  };\n  /**\r\n   * @param type null/undefined: reset all.\r\n   *        'recreate': force recreate all.\r\n   *        'timeline': only reset timeline option\r\n   *        'media': only reset media query option\r\n   * @return Whether option changed.\r\n   */\n  GlobalModel.prototype.resetOption = function (type, opt) {\n    return this._resetOption(type, normalizeSetOptionInput(opt));\n  };\n  GlobalModel.prototype._resetOption = function (type, opt) {\n    var optionChanged = false;\n    var optionManager = this._optionManager;\n    if (!type || type === 'recreate') {\n      var baseOption = optionManager.mountOption(type === 'recreate');\n      if (process.env.NODE_ENV !== 'production') {\n        checkMissingComponents(baseOption);\n      }\n      if (!this.option || type === 'recreate') {\n        initBase(this, baseOption);\n      } else {\n        this.restoreData();\n        this._mergeOption(baseOption, opt);\n      }\n      optionChanged = true;\n    }\n    if (type === 'timeline' || type === 'media') {\n      this.restoreData();\n    }\n    // By design, if `setOption(option2)` at the second time, and `option2` is a `ECUnitOption`,\n    // it should better not have the same props with `MediaUnit['option']`.\n    // Because either `option2` or `MediaUnit['option']` will be always merged to \"current option\"\n    // rather than original \"baseOption\". If they both override a prop, the result might be\n    // unexpected when media state changed after `setOption` called.\n    // If we really need to modify a props in each `MediaUnit['option']`, use the full version\n    // (`{baseOption, media}`) in `setOption`.\n    // For `timeline`, the case is the same.\n    if (!type || type === 'recreate' || type === 'timeline') {\n      var timelineOption = optionManager.getTimelineOption(this);\n      if (timelineOption) {\n        optionChanged = true;\n        this._mergeOption(timelineOption, opt);\n      }\n    }\n    if (!type || type === 'recreate' || type === 'media') {\n      var mediaOptions = optionManager.getMediaOption(this);\n      if (mediaOptions.length) {\n        each(mediaOptions, function (mediaOption) {\n          optionChanged = true;\n          this._mergeOption(mediaOption, opt);\n        }, this);\n      }\n    }\n    return optionChanged;\n  };\n  GlobalModel.prototype.mergeOption = function (option) {\n    this._mergeOption(option, null);\n  };\n  GlobalModel.prototype._mergeOption = function (newOption, opt) {\n    var option = this.option;\n    var componentsMap = this._componentsMap;\n    var componentsCount = this._componentsCount;\n    var newCmptTypes = [];\n    var newCmptTypeMap = createHashMap();\n    var replaceMergeMainTypeMap = opt && opt.replaceMergeMainTypeMap;\n    resetSourceDefaulter(this);\n    // If no component class, merge directly.\n    // For example: color, animaiton options, etc.\n    each(newOption, function (componentOption, mainType) {\n      if (componentOption == null) {\n        return;\n      }\n      if (!ComponentModel.hasClass(mainType)) {\n        // globalSettingTask.dirty();\n        option[mainType] = option[mainType] == null ? clone(componentOption) : merge(option[mainType], componentOption, true);\n      } else if (mainType) {\n        newCmptTypes.push(mainType);\n        newCmptTypeMap.set(mainType, true);\n      }\n    });\n    if (replaceMergeMainTypeMap) {\n      // If there is a mainType `xxx` in `replaceMerge` but not declared in option,\n      // we trade it as it is declared in option as `{xxx: []}`. Because:\n      // (1) for normal merge, `{xxx: null/undefined}` are the same meaning as `{xxx: []}`.\n      // (2) some preprocessor may convert some of `{xxx: null/undefined}` to `{xxx: []}`.\n      replaceMergeMainTypeMap.each(function (val, mainTypeInReplaceMerge) {\n        if (ComponentModel.hasClass(mainTypeInReplaceMerge) && !newCmptTypeMap.get(mainTypeInReplaceMerge)) {\n          newCmptTypes.push(mainTypeInReplaceMerge);\n          newCmptTypeMap.set(mainTypeInReplaceMerge, true);\n        }\n      });\n    }\n    ComponentModel.topologicalTravel(newCmptTypes, ComponentModel.getAllClassMainTypes(), visitComponent, this);\n    function visitComponent(mainType) {\n      var newCmptOptionList = concatInternalOptions(this, mainType, modelUtil.normalizeToArray(newOption[mainType]));\n      var oldCmptList = componentsMap.get(mainType);\n      var mergeMode =\n      // `!oldCmptList` means init. See the comment in `mappingToExists`\n      !oldCmptList ? 'replaceAll' : replaceMergeMainTypeMap && replaceMergeMainTypeMap.get(mainType) ? 'replaceMerge' : 'normalMerge';\n      var mappingResult = modelUtil.mappingToExists(oldCmptList, newCmptOptionList, mergeMode);\n      // Set mainType and complete subType.\n      modelUtil.setComponentTypeToKeyInfo(mappingResult, mainType, ComponentModel);\n      // Empty it before the travel, in order to prevent `this._componentsMap`\n      // from being used in the `init`/`mergeOption`/`optionUpdated` of some\n      // components, which is probably incorrect logic.\n      option[mainType] = null;\n      componentsMap.set(mainType, null);\n      componentsCount.set(mainType, 0);\n      var optionsByMainType = [];\n      var cmptsByMainType = [];\n      var cmptsCountByMainType = 0;\n      var tooltipExists;\n      var tooltipWarningLogged;\n      each(mappingResult, function (resultItem, index) {\n        var componentModel = resultItem.existing;\n        var newCmptOption = resultItem.newOption;\n        if (!newCmptOption) {\n          if (componentModel) {\n            // Consider where is no new option and should be merged using {},\n            // see removeEdgeAndAdd in topologicalTravel and\n            // ComponentModel.getAllClassMainTypes.\n            componentModel.mergeOption({}, this);\n            componentModel.optionUpdated({}, false);\n          }\n          // If no both `resultItem.exist` and `resultItem.option`,\n          // either it is in `replaceMerge` and not matched by any id,\n          // or it has been removed in previous `replaceMerge` and left a \"hole\" in this component index.\n        } else {\n          var isSeriesType = mainType === 'series';\n          var ComponentModelClass = ComponentModel.getClass(mainType, resultItem.keyInfo.subType, !isSeriesType // Give a more detailed warn later if series don't exists\n          );\n          if (!ComponentModelClass) {\n            if (process.env.NODE_ENV !== 'production') {\n              var subType = resultItem.keyInfo.subType;\n              var seriesImportName = BUILTIN_CHARTS_MAP[subType];\n              if (!componetsMissingLogPrinted[subType]) {\n                componetsMissingLogPrinted[subType] = true;\n                if (seriesImportName) {\n                  error(\"Series \" + subType + \" is used but not imported.\\nimport { \" + seriesImportName + \" } from 'echarts/charts';\\necharts.use([\" + seriesImportName + \"]);\");\n                } else {\n                  error(\"Unknown series \" + subType);\n                }\n              }\n            }\n            return;\n          }\n          // TODO Before multiple tooltips get supported, we do this check to avoid unexpected exception.\n          if (mainType === 'tooltip') {\n            if (tooltipExists) {\n              if (process.env.NODE_ENV !== 'production') {\n                if (!tooltipWarningLogged) {\n                  warn('Currently only one tooltip component is allowed.');\n                  tooltipWarningLogged = true;\n                }\n              }\n              return;\n            }\n            tooltipExists = true;\n          }\n          if (componentModel && componentModel.constructor === ComponentModelClass) {\n            componentModel.name = resultItem.keyInfo.name;\n            // componentModel.settingTask && componentModel.settingTask.dirty();\n            componentModel.mergeOption(newCmptOption, this);\n            componentModel.optionUpdated(newCmptOption, false);\n          } else {\n            // PENDING Global as parent ?\n            var extraOpt = extend({\n              componentIndex: index\n            }, resultItem.keyInfo);\n            componentModel = new ComponentModelClass(newCmptOption, this, this, extraOpt);\n            // Assign `keyInfo`\n            extend(componentModel, extraOpt);\n            if (resultItem.brandNew) {\n              componentModel.__requireNewView = true;\n            }\n            componentModel.init(newCmptOption, this, this);\n            // Call optionUpdated after init.\n            // newCmptOption has been used as componentModel.option\n            // and may be merged with theme and default, so pass null\n            // to avoid confusion.\n            componentModel.optionUpdated(null, true);\n          }\n        }\n        if (componentModel) {\n          optionsByMainType.push(componentModel.option);\n          cmptsByMainType.push(componentModel);\n          cmptsCountByMainType++;\n        } else {\n          // Always do assign to avoid elided item in array.\n          optionsByMainType.push(void 0);\n          cmptsByMainType.push(void 0);\n        }\n      }, this);\n      option[mainType] = optionsByMainType;\n      componentsMap.set(mainType, cmptsByMainType);\n      componentsCount.set(mainType, cmptsCountByMainType);\n      // Backup series for filtering.\n      if (mainType === 'series') {\n        reCreateSeriesIndices(this);\n      }\n    }\n    // If no series declared, ensure `_seriesIndices` initialized.\n    if (!this._seriesIndices) {\n      reCreateSeriesIndices(this);\n    }\n  };\n  /**\r\n   * Get option for output (cloned option and inner info removed)\r\n   */\n  GlobalModel.prototype.getOption = function () {\n    var option = clone(this.option);\n    each(option, function (optInMainType, mainType) {\n      if (ComponentModel.hasClass(mainType)) {\n        var opts = modelUtil.normalizeToArray(optInMainType);\n        // Inner cmpts need to be removed.\n        // Inner cmpts might not be at last since ec5.0, but still\n        // compatible for users: if inner cmpt at last, splice the returned array.\n        var realLen = opts.length;\n        var metNonInner = false;\n        for (var i = realLen - 1; i >= 0; i--) {\n          // Remove options with inner id.\n          if (opts[i] && !modelUtil.isComponentIdInternal(opts[i])) {\n            metNonInner = true;\n          } else {\n            opts[i] = null;\n            !metNonInner && realLen--;\n          }\n        }\n        opts.length = realLen;\n        option[mainType] = opts;\n      }\n    });\n    delete option[OPTION_INNER_KEY];\n    return option;\n  };\n  GlobalModel.prototype.getTheme = function () {\n    return this._theme;\n  };\n  GlobalModel.prototype.getLocaleModel = function () {\n    return this._locale;\n  };\n  GlobalModel.prototype.setUpdatePayload = function (payload) {\n    this._payload = payload;\n  };\n  GlobalModel.prototype.getUpdatePayload = function () {\n    return this._payload;\n  };\n  /**\r\n   * @param idx If not specified, return the first one.\r\n   */\n  GlobalModel.prototype.getComponent = function (mainType, idx) {\n    var list = this._componentsMap.get(mainType);\n    if (list) {\n      var cmpt = list[idx || 0];\n      if (cmpt) {\n        return cmpt;\n      } else if (idx == null) {\n        for (var i = 0; i < list.length; i++) {\n          if (list[i]) {\n            return list[i];\n          }\n        }\n      }\n    }\n  };\n  /**\r\n   * @return Never be null/undefined.\r\n   */\n  GlobalModel.prototype.queryComponents = function (condition) {\n    var mainType = condition.mainType;\n    if (!mainType) {\n      return [];\n    }\n    var index = condition.index;\n    var id = condition.id;\n    var name = condition.name;\n    var cmpts = this._componentsMap.get(mainType);\n    if (!cmpts || !cmpts.length) {\n      return [];\n    }\n    var result;\n    if (index != null) {\n      result = [];\n      each(modelUtil.normalizeToArray(index), function (idx) {\n        cmpts[idx] && result.push(cmpts[idx]);\n      });\n    } else if (id != null) {\n      result = queryByIdOrName('id', id, cmpts);\n    } else if (name != null) {\n      result = queryByIdOrName('name', name, cmpts);\n    } else {\n      // Return all non-empty components in that mainType\n      result = filter(cmpts, function (cmpt) {\n        return !!cmpt;\n      });\n    }\n    return filterBySubType(result, condition);\n  };\n  /**\r\n   * The interface is different from queryComponents,\r\n   * which is convenient for inner usage.\r\n   *\r\n   * @usage\r\n   * let result = findComponents(\r\n   *     {mainType: 'dataZoom', query: {dataZoomId: 'abc'}}\r\n   * );\r\n   * let result = findComponents(\r\n   *     {mainType: 'series', subType: 'pie', query: {seriesName: 'uio'}}\r\n   * );\r\n   * let result = findComponents(\r\n   *     {mainType: 'series',\r\n   *     filter: function (model, index) {...}}\r\n   * );\r\n   * // result like [component0, componnet1, ...]\r\n   */\n  GlobalModel.prototype.findComponents = function (condition) {\n    var query = condition.query;\n    var mainType = condition.mainType;\n    var queryCond = getQueryCond(query);\n    var result = queryCond ? this.queryComponents(queryCond)\n    // Retrieve all non-empty components.\n    : filter(this._componentsMap.get(mainType), function (cmpt) {\n      return !!cmpt;\n    });\n    return doFilter(filterBySubType(result, condition));\n    function getQueryCond(q) {\n      var indexAttr = mainType + 'Index';\n      var idAttr = mainType + 'Id';\n      var nameAttr = mainType + 'Name';\n      return q && (q[indexAttr] != null || q[idAttr] != null || q[nameAttr] != null) ? {\n        mainType: mainType,\n        // subType will be filtered finally.\n        index: q[indexAttr],\n        id: q[idAttr],\n        name: q[nameAttr]\n      } : null;\n    }\n    function doFilter(res) {\n      return condition.filter ? filter(res, condition.filter) : res;\n    }\n  };\n  GlobalModel.prototype.eachComponent = function (mainType, cb, context) {\n    var componentsMap = this._componentsMap;\n    if (isFunction(mainType)) {\n      var ctxForAll_1 = cb;\n      var cbForAll_1 = mainType;\n      componentsMap.each(function (cmpts, componentType) {\n        for (var i = 0; cmpts && i < cmpts.length; i++) {\n          var cmpt = cmpts[i];\n          cmpt && cbForAll_1.call(ctxForAll_1, componentType, cmpt, cmpt.componentIndex);\n        }\n      });\n    } else {\n      var cmpts = isString(mainType) ? componentsMap.get(mainType) : isObject(mainType) ? this.findComponents(mainType) : null;\n      for (var i = 0; cmpts && i < cmpts.length; i++) {\n        var cmpt = cmpts[i];\n        cmpt && cb.call(context, cmpt, cmpt.componentIndex);\n      }\n    }\n  };\n  /**\r\n   * Get series list before filtered by name.\r\n   */\n  GlobalModel.prototype.getSeriesByName = function (name) {\n    var nameStr = modelUtil.convertOptionIdName(name, null);\n    return filter(this._componentsMap.get('series'), function (oneSeries) {\n      return !!oneSeries && nameStr != null && oneSeries.name === nameStr;\n    });\n  };\n  /**\r\n   * Get series list before filtered by index.\r\n   */\n  GlobalModel.prototype.getSeriesByIndex = function (seriesIndex) {\n    return this._componentsMap.get('series')[seriesIndex];\n  };\n  /**\r\n   * Get series list before filtered by type.\r\n   * FIXME: rename to getRawSeriesByType?\r\n   */\n  GlobalModel.prototype.getSeriesByType = function (subType) {\n    return filter(this._componentsMap.get('series'), function (oneSeries) {\n      return !!oneSeries && oneSeries.subType === subType;\n    });\n  };\n  /**\r\n   * Get all series before filtered.\r\n   */\n  GlobalModel.prototype.getSeries = function () {\n    return filter(this._componentsMap.get('series'), function (oneSeries) {\n      return !!oneSeries;\n    });\n  };\n  /**\r\n   * Count series before filtered.\r\n   */\n  GlobalModel.prototype.getSeriesCount = function () {\n    return this._componentsCount.get('series');\n  };\n  /**\r\n   * After filtering, series may be different\r\n   * from raw series.\r\n   */\n  GlobalModel.prototype.eachSeries = function (cb, context) {\n    assertSeriesInitialized(this);\n    each(this._seriesIndices, function (rawSeriesIndex) {\n      var series = this._componentsMap.get('series')[rawSeriesIndex];\n      cb.call(context, series, rawSeriesIndex);\n    }, this);\n  };\n  /**\r\n   * Iterate raw series before filtered.\r\n   *\r\n   * @param {Function} cb\r\n   * @param {*} context\r\n   */\n  GlobalModel.prototype.eachRawSeries = function (cb, context) {\n    each(this._componentsMap.get('series'), function (series) {\n      series && cb.call(context, series, series.componentIndex);\n    });\n  };\n  /**\r\n   * After filtering, series may be different.\r\n   * from raw series.\r\n   */\n  GlobalModel.prototype.eachSeriesByType = function (subType, cb, context) {\n    assertSeriesInitialized(this);\n    each(this._seriesIndices, function (rawSeriesIndex) {\n      var series = this._componentsMap.get('series')[rawSeriesIndex];\n      if (series.subType === subType) {\n        cb.call(context, series, rawSeriesIndex);\n      }\n    }, this);\n  };\n  /**\r\n   * Iterate raw series before filtered of given type.\r\n   */\n  GlobalModel.prototype.eachRawSeriesByType = function (subType, cb, context) {\n    return each(this.getSeriesByType(subType), cb, context);\n  };\n  GlobalModel.prototype.isSeriesFiltered = function (seriesModel) {\n    assertSeriesInitialized(this);\n    return this._seriesIndicesMap.get(seriesModel.componentIndex) == null;\n  };\n  GlobalModel.prototype.getCurrentSeriesIndices = function () {\n    return (this._seriesIndices || []).slice();\n  };\n  GlobalModel.prototype.filterSeries = function (cb, context) {\n    assertSeriesInitialized(this);\n    var newSeriesIndices = [];\n    each(this._seriesIndices, function (seriesRawIdx) {\n      var series = this._componentsMap.get('series')[seriesRawIdx];\n      cb.call(context, series, seriesRawIdx) && newSeriesIndices.push(seriesRawIdx);\n    }, this);\n    this._seriesIndices = newSeriesIndices;\n    this._seriesIndicesMap = createHashMap(newSeriesIndices);\n  };\n  GlobalModel.prototype.restoreData = function (payload) {\n    reCreateSeriesIndices(this);\n    var componentsMap = this._componentsMap;\n    var componentTypes = [];\n    componentsMap.each(function (components, componentType) {\n      if (ComponentModel.hasClass(componentType)) {\n        componentTypes.push(componentType);\n      }\n    });\n    ComponentModel.topologicalTravel(componentTypes, ComponentModel.getAllClassMainTypes(), function (componentType) {\n      each(componentsMap.get(componentType), function (component) {\n        if (component && (componentType !== 'series' || !isNotTargetSeries(component, payload))) {\n          component.restoreData();\n        }\n      });\n    });\n  };\n  GlobalModel.internalField = function () {\n    reCreateSeriesIndices = function (ecModel) {\n      var seriesIndices = ecModel._seriesIndices = [];\n      each(ecModel._componentsMap.get('series'), function (series) {\n        // series may have been removed by `replaceMerge`.\n        series && seriesIndices.push(series.componentIndex);\n      });\n      ecModel._seriesIndicesMap = createHashMap(seriesIndices);\n    };\n    assertSeriesInitialized = function (ecModel) {\n      // Components that use _seriesIndices should depends on series component,\n      // which make sure that their initialization is after series.\n      if (process.env.NODE_ENV !== 'production') {\n        if (!ecModel._seriesIndices) {\n          throw new Error('Option should contains series.');\n        }\n      }\n    };\n    initBase = function (ecModel, baseOption) {\n      // Using OPTION_INNER_KEY to mark that this option cannot be used outside,\n      // i.e. `chart.setOption(chart.getModel().option);` is forbidden.\n      ecModel.option = {};\n      ecModel.option[OPTION_INNER_KEY] = OPTION_INNER_VALUE;\n      // Init with series: [], in case of calling findSeries method\n      // before series initialized.\n      ecModel._componentsMap = createHashMap({\n        series: []\n      });\n      ecModel._componentsCount = createHashMap();\n      // If user spefied `option.aria`, aria will be enable. This detection should be\n      // performed before theme and globalDefault merge.\n      var airaOption = baseOption.aria;\n      if (isObject(airaOption) && airaOption.enabled == null) {\n        airaOption.enabled = true;\n      }\n      mergeTheme(baseOption, ecModel._theme.option);\n      // TODO Needs clone when merging to the unexisted property\n      merge(baseOption, globalDefault, false);\n      ecModel._mergeOption(baseOption, null);\n    };\n  }();\n  return GlobalModel;\n}(Model);\nfunction isNotTargetSeries(seriesModel, payload) {\n  if (payload) {\n    var index = payload.seriesIndex;\n    var id = payload.seriesId;\n    var name_1 = payload.seriesName;\n    return index != null && seriesModel.componentIndex !== index || id != null && seriesModel.id !== id || name_1 != null && seriesModel.name !== name_1;\n  }\n}\nfunction mergeTheme(option, theme) {\n  // PENDING\n  // NOT use `colorLayer` in theme if option has `color`\n  var notMergeColorLayer = option.color && !option.colorLayer;\n  each(theme, function (themeItem, name) {\n    if (name === 'colorLayer' && notMergeColorLayer) {\n      return;\n    }\n    // If it is component model mainType, the model handles that merge later.\n    // otherwise, merge them here.\n    if (!ComponentModel.hasClass(name)) {\n      if (typeof themeItem === 'object') {\n        option[name] = !option[name] ? clone(themeItem) : merge(option[name], themeItem, false);\n      } else {\n        if (option[name] == null) {\n          option[name] = themeItem;\n        }\n      }\n    }\n  });\n}\nfunction queryByIdOrName(attr, idOrName, cmpts) {\n  // Here is a break from echarts4: string and number are\n  // treated as equal.\n  if (isArray(idOrName)) {\n    var keyMap_1 = createHashMap();\n    each(idOrName, function (idOrNameItem) {\n      if (idOrNameItem != null) {\n        var idName = modelUtil.convertOptionIdName(idOrNameItem, null);\n        idName != null && keyMap_1.set(idOrNameItem, true);\n      }\n    });\n    return filter(cmpts, function (cmpt) {\n      return cmpt && keyMap_1.get(cmpt[attr]);\n    });\n  } else {\n    var idName_1 = modelUtil.convertOptionIdName(idOrName, null);\n    return filter(cmpts, function (cmpt) {\n      return cmpt && idName_1 != null && cmpt[attr] === idName_1;\n    });\n  }\n}\nfunction filterBySubType(components, condition) {\n  // Using hasOwnProperty for restrict. Consider\n  // subType is undefined in user payload.\n  return condition.hasOwnProperty('subType') ? filter(components, function (cmpt) {\n    return cmpt && cmpt.subType === condition.subType;\n  }) : components;\n}\nfunction normalizeSetOptionInput(opts) {\n  var replaceMergeMainTypeMap = createHashMap();\n  opts && each(modelUtil.normalizeToArray(opts.replaceMerge), function (mainType) {\n    if (process.env.NODE_ENV !== 'production') {\n      assert(ComponentModel.hasClass(mainType), '\"' + mainType + '\" is not valid component main type in \"replaceMerge\"');\n    }\n    replaceMergeMainTypeMap.set(mainType, true);\n  });\n  return {\n    replaceMergeMainTypeMap: replaceMergeMainTypeMap\n  };\n}\nmixin(GlobalModel, PaletteMixin);\nexport default GlobalModel;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AA8GQ;AA7GR;AACA;;;;;;;;;;;;;CAaC,GACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;AACA,0BAA0B;AAC1B,yBAAyB;AACzB,0BAA0B;AAC1B,IAAI;AACJ,IAAI;AACJ,IAAI;AACJ,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,IAAI,wBAAwB;IAC1B,MAAM;IACN,OAAO;IACP,KAAK;IACL,YAAY;IACZ,UAAU;IACV,UAAU;IACV,SAAS;IACT,SAAS;IACT,SAAS;IACT,aAAa;IACb,OAAO;IACP,OAAO;IACP,UAAU;IACV,WAAW;IACX,UAAU;IACV,UAAU;IACV,QAAQ;IACR,UAAU;IACV,WAAW;IACX,yBAAyB;IACzB,+BAA+B;IAC/B,eAAe;IACf,OAAO;IACP,OAAO;IACP,WAAW;IACX,YAAY;AACd;AACA,IAAI,qBAAqB;IACvB,MAAM;IACN,KAAK;IACL,KAAK;IACL,SAAS;IACT,OAAO;IACP,KAAK;IACL,MAAM;IACN,SAAS;IACT,OAAO;IACP,OAAO;IACP,QAAQ;IACR,UAAU;IACV,QAAQ;IACR,SAAS;IACT,aAAa;IACb,eAAe;IACf,OAAO;IACP,SAAS;IACT,cAAc;IACd,YAAY;IACZ,UAAU;IACV,QAAQ;AACV;AACA,IAAI,6BAA6B,CAAC;AAClC,SAAS,uBAAuB,MAAM;IACpC,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,SAAU,eAAe,EAAE,QAAQ;QAC9C,IAAI,CAAC,uJAAA,CAAA,UAAc,CAAC,QAAQ,CAAC,WAAW;YACtC,IAAI,sBAAsB,qBAAqB,CAAC,SAAS;YACzD,IAAI,uBAAuB,CAAC,0BAA0B,CAAC,oBAAoB,EAAE;gBAC3E,CAAA,GAAA,gJAAA,CAAA,QAAK,AAAD,EAAE,eAAe,WAAW,0CAA0C,sBAAsB,iDAAiD,sBAAsB;gBACvK,0BAA0B,CAAC,oBAAoB,GAAG;YACpD;QACF;IACF;AACF;AACA,IAAI,cAAc,WAAW,GAAE,SAAU,MAAM;IAC7C,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,aAAa;IACvB,SAAS;QACP,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACjE;IACA,YAAY,SAAS,CAAC,IAAI,GAAG,SAAU,MAAM,EAAE,WAAW,EAAE,OAAO,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa;QAC/F,QAAQ,SAAS,CAAC;QAClB,IAAI,CAAC,MAAM,GAAG,MAAM,2BAA2B;QAC/C,IAAI,CAAC,MAAM,GAAG,IAAI,mJAAA,CAAA,UAAK,CAAC;QACxB,IAAI,CAAC,OAAO,GAAG,IAAI,mJAAA,CAAA,UAAK,CAAC;QACzB,IAAI,CAAC,cAAc,GAAG;IACxB;IACA,YAAY,SAAS,CAAC,SAAS,GAAG,SAAU,MAAM,EAAE,IAAI,EAAE,uBAAuB;QAC/E,wCAA2C;YACzC,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,UAAU,MAAM;YACvB,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,MAAM,CAAC,iBAAiB,KAAK,oBAAoB;QAC1D;QACA,IAAI,WAAW,wBAAwB;QACvC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,QAAQ,yBAAyB;QAC/D,IAAI,CAAC,YAAY,CAAC,MAAM;IAC1B;IACA;;;;;;GAMC,GACD,YAAY,SAAS,CAAC,WAAW,GAAG,SAAU,IAAI,EAAE,GAAG;QACrD,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM,wBAAwB;IACzD;IACA,YAAY,SAAS,CAAC,YAAY,GAAG,SAAU,IAAI,EAAE,GAAG;QACtD,IAAI,gBAAgB;QACpB,IAAI,gBAAgB,IAAI,CAAC,cAAc;QACvC,IAAI,CAAC,QAAQ,SAAS,YAAY;YAChC,IAAI,aAAa,cAAc,WAAW,CAAC,SAAS;YACpD,wCAA2C;gBACzC,uBAAuB;YACzB;YACA,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,SAAS,YAAY;gBACvC,SAAS,IAAI,EAAE;YACjB,OAAO;gBACL,IAAI,CAAC,WAAW;gBAChB,IAAI,CAAC,YAAY,CAAC,YAAY;YAChC;YACA,gBAAgB;QAClB;QACA,IAAI,SAAS,cAAc,SAAS,SAAS;YAC3C,IAAI,CAAC,WAAW;QAClB;QACA,4FAA4F;QAC5F,uEAAuE;QACvE,8FAA8F;QAC9F,uFAAuF;QACvF,gEAAgE;QAChE,0FAA0F;QAC1F,0CAA0C;QAC1C,wCAAwC;QACxC,IAAI,CAAC,QAAQ,SAAS,cAAc,SAAS,YAAY;YACvD,IAAI,iBAAiB,cAAc,iBAAiB,CAAC,IAAI;YACzD,IAAI,gBAAgB;gBAClB,gBAAgB;gBAChB,IAAI,CAAC,YAAY,CAAC,gBAAgB;YACpC;QACF;QACA,IAAI,CAAC,QAAQ,SAAS,cAAc,SAAS,SAAS;YACpD,IAAI,eAAe,cAAc,cAAc,CAAC,IAAI;YACpD,IAAI,aAAa,MAAM,EAAE;gBACvB,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,cAAc,SAAU,WAAW;oBACtC,gBAAgB;oBAChB,IAAI,CAAC,YAAY,CAAC,aAAa;gBACjC,GAAG,IAAI;YACT;QACF;QACA,OAAO;IACT;IACA,YAAY,SAAS,CAAC,WAAW,GAAG,SAAU,MAAM;QAClD,IAAI,CAAC,YAAY,CAAC,QAAQ;IAC5B;IACA,YAAY,SAAS,CAAC,YAAY,GAAG,SAAU,SAAS,EAAE,GAAG;QAC3D,IAAI,SAAS,IAAI,CAAC,MAAM;QACxB,IAAI,gBAAgB,IAAI,CAAC,cAAc;QACvC,IAAI,kBAAkB,IAAI,CAAC,gBAAgB;QAC3C,IAAI,eAAe,EAAE;QACrB,IAAI,iBAAiB,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;QACjC,IAAI,0BAA0B,OAAO,IAAI,uBAAuB;QAChE,CAAA,GAAA,mKAAA,CAAA,uBAAoB,AAAD,EAAE,IAAI;QACzB,yCAAyC;QACzC,8CAA8C;QAC9C,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,WAAW,SAAU,eAAe,EAAE,QAAQ;YACjD,IAAI,mBAAmB,MAAM;gBAC3B;YACF;YACA,IAAI,CAAC,uJAAA,CAAA,UAAc,CAAC,QAAQ,CAAC,WAAW;gBACtC,6BAA6B;gBAC7B,MAAM,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS,IAAI,OAAO,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,mBAAmB,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,MAAM,CAAC,SAAS,EAAE,iBAAiB;YAClH,OAAO,IAAI,UAAU;gBACnB,aAAa,IAAI,CAAC;gBAClB,eAAe,GAAG,CAAC,UAAU;YAC/B;QACF;QACA,IAAI,yBAAyB;YAC3B,6EAA6E;YAC7E,mEAAmE;YACnE,qFAAqF;YACrF,oFAAoF;YACpF,wBAAwB,IAAI,CAAC,SAAU,GAAG,EAAE,sBAAsB;gBAChE,IAAI,uJAAA,CAAA,UAAc,CAAC,QAAQ,CAAC,2BAA2B,CAAC,eAAe,GAAG,CAAC,yBAAyB;oBAClG,aAAa,IAAI,CAAC;oBAClB,eAAe,GAAG,CAAC,wBAAwB;gBAC7C;YACF;QACF;QACA,uJAAA,CAAA,UAAc,CAAC,iBAAiB,CAAC,cAAc,uJAAA,CAAA,UAAc,CAAC,oBAAoB,IAAI,gBAAgB,IAAI;QAC1G,SAAS,eAAe,QAAQ;YAC9B,IAAI,oBAAoB,CAAA,GAAA,sKAAA,CAAA,wBAAqB,AAAD,EAAE,IAAI,EAAE,UAAU,CAAA,GAAA,kJAAA,CAAA,mBAA0B,AAAD,EAAE,SAAS,CAAC,SAAS;YAC5G,IAAI,cAAc,cAAc,GAAG,CAAC;YACpC,IAAI,YACJ,kEAAkE;YAClE,CAAC,cAAc,eAAe,2BAA2B,wBAAwB,GAAG,CAAC,YAAY,iBAAiB;YAClH,IAAI,gBAAgB,CAAA,GAAA,kJAAA,CAAA,kBAAyB,AAAD,EAAE,aAAa,mBAAmB;YAC9E,qCAAqC;YACrC,CAAA,GAAA,kJAAA,CAAA,4BAAmC,AAAD,EAAE,eAAe,UAAU,uJAAA,CAAA,UAAc;YAC3E,wEAAwE;YACxE,sEAAsE;YACtE,iDAAiD;YACjD,MAAM,CAAC,SAAS,GAAG;YACnB,cAAc,GAAG,CAAC,UAAU;YAC5B,gBAAgB,GAAG,CAAC,UAAU;YAC9B,IAAI,oBAAoB,EAAE;YAC1B,IAAI,kBAAkB,EAAE;YACxB,IAAI,uBAAuB;YAC3B,IAAI;YACJ,IAAI;YACJ,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,eAAe,SAAU,UAAU,EAAE,KAAK;gBAC7C,IAAI,iBAAiB,WAAW,QAAQ;gBACxC,IAAI,gBAAgB,WAAW,SAAS;gBACxC,IAAI,CAAC,eAAe;oBAClB,IAAI,gBAAgB;wBAClB,iEAAiE;wBACjE,gDAAgD;wBAChD,uCAAuC;wBACvC,eAAe,WAAW,CAAC,CAAC,GAAG,IAAI;wBACnC,eAAe,aAAa,CAAC,CAAC,GAAG;oBACnC;gBACA,yDAAyD;gBACzD,4DAA4D;gBAC5D,+FAA+F;gBACjG,OAAO;oBACL,IAAI,eAAe,aAAa;oBAChC,IAAI,sBAAsB,uJAAA,CAAA,UAAc,CAAC,QAAQ,CAAC,UAAU,WAAW,OAAO,CAAC,OAAO,EAAE,CAAC,aAAa,yDAAyD;;oBAE/J,IAAI,CAAC,qBAAqB;wBACxB,wCAA2C;4BACzC,IAAI,UAAU,WAAW,OAAO,CAAC,OAAO;4BACxC,IAAI,mBAAmB,kBAAkB,CAAC,QAAQ;4BAClD,IAAI,CAAC,0BAA0B,CAAC,QAAQ,EAAE;gCACxC,0BAA0B,CAAC,QAAQ,GAAG;gCACtC,IAAI,kBAAkB;oCACpB,CAAA,GAAA,gJAAA,CAAA,QAAK,AAAD,EAAE,YAAY,UAAU,0CAA0C,mBAAmB,6CAA6C,mBAAmB;gCAC3J,OAAO;oCACL,CAAA,GAAA,gJAAA,CAAA,QAAK,AAAD,EAAE,oBAAoB;gCAC5B;4BACF;wBACF;wBACA;oBACF;oBACA,+FAA+F;oBAC/F,IAAI,aAAa,WAAW;wBAC1B,IAAI,eAAe;4BACjB,wCAA2C;gCACzC,IAAI,CAAC,sBAAsB;oCACzB,CAAA,GAAA,gJAAA,CAAA,OAAI,AAAD,EAAE;oCACL,uBAAuB;gCACzB;4BACF;4BACA;wBACF;wBACA,gBAAgB;oBAClB;oBACA,IAAI,kBAAkB,eAAe,WAAW,KAAK,qBAAqB;wBACxE,eAAe,IAAI,GAAG,WAAW,OAAO,CAAC,IAAI;wBAC7C,oEAAoE;wBACpE,eAAe,WAAW,CAAC,eAAe,IAAI;wBAC9C,eAAe,aAAa,CAAC,eAAe;oBAC9C,OAAO;wBACL,6BAA6B;wBAC7B,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE;4BACpB,gBAAgB;wBAClB,GAAG,WAAW,OAAO;wBACrB,iBAAiB,IAAI,oBAAoB,eAAe,IAAI,EAAE,IAAI,EAAE;wBACpE,mBAAmB;wBACnB,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,gBAAgB;wBACvB,IAAI,WAAW,QAAQ,EAAE;4BACvB,eAAe,gBAAgB,GAAG;wBACpC;wBACA,eAAe,IAAI,CAAC,eAAe,IAAI,EAAE,IAAI;wBAC7C,iCAAiC;wBACjC,uDAAuD;wBACvD,yDAAyD;wBACzD,sBAAsB;wBACtB,eAAe,aAAa,CAAC,MAAM;oBACrC;gBACF;gBACA,IAAI,gBAAgB;oBAClB,kBAAkB,IAAI,CAAC,eAAe,MAAM;oBAC5C,gBAAgB,IAAI,CAAC;oBACrB;gBACF,OAAO;oBACL,kDAAkD;oBAClD,kBAAkB,IAAI,CAAC,KAAK;oBAC5B,gBAAgB,IAAI,CAAC,KAAK;gBAC5B;YACF,GAAG,IAAI;YACP,MAAM,CAAC,SAAS,GAAG;YACnB,cAAc,GAAG,CAAC,UAAU;YAC5B,gBAAgB,GAAG,CAAC,UAAU;YAC9B,+BAA+B;YAC/B,IAAI,aAAa,UAAU;gBACzB,sBAAsB,IAAI;YAC5B;QACF;QACA,8DAA8D;QAC9D,IAAI,CAAC,IAAI,CAAC,cAAc,EAAE;YACxB,sBAAsB,IAAI;QAC5B;IACF;IACA;;GAEC,GACD,YAAY,SAAS,CAAC,SAAS,GAAG;QAChC,IAAI,SAAS,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,IAAI,CAAC,MAAM;QAC9B,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,SAAU,aAAa,EAAE,QAAQ;YAC5C,IAAI,uJAAA,CAAA,UAAc,CAAC,QAAQ,CAAC,WAAW;gBACrC,IAAI,OAAO,CAAA,GAAA,kJAAA,CAAA,mBAA0B,AAAD,EAAE;gBACtC,kCAAkC;gBAClC,0DAA0D;gBAC1D,0EAA0E;gBAC1E,IAAI,UAAU,KAAK,MAAM;gBACzB,IAAI,cAAc;gBAClB,IAAK,IAAI,IAAI,UAAU,GAAG,KAAK,GAAG,IAAK;oBACrC,gCAAgC;oBAChC,IAAI,IAAI,CAAC,EAAE,IAAI,CAAC,CAAA,GAAA,kJAAA,CAAA,wBAA+B,AAAD,EAAE,IAAI,CAAC,EAAE,GAAG;wBACxD,cAAc;oBAChB,OAAO;wBACL,IAAI,CAAC,EAAE,GAAG;wBACV,CAAC,eAAe;oBAClB;gBACF;gBACA,KAAK,MAAM,GAAG;gBACd,MAAM,CAAC,SAAS,GAAG;YACrB;QACF;QACA,OAAO,MAAM,CAAC,iBAAiB;QAC/B,OAAO;IACT;IACA,YAAY,SAAS,CAAC,QAAQ,GAAG;QAC/B,OAAO,IAAI,CAAC,MAAM;IACpB;IACA,YAAY,SAAS,CAAC,cAAc,GAAG;QACrC,OAAO,IAAI,CAAC,OAAO;IACrB;IACA,YAAY,SAAS,CAAC,gBAAgB,GAAG,SAAU,OAAO;QACxD,IAAI,CAAC,QAAQ,GAAG;IAClB;IACA,YAAY,SAAS,CAAC,gBAAgB,GAAG;QACvC,OAAO,IAAI,CAAC,QAAQ;IACtB;IACA;;GAEC,GACD,YAAY,SAAS,CAAC,YAAY,GAAG,SAAU,QAAQ,EAAE,GAAG;QAC1D,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QACnC,IAAI,MAAM;YACR,IAAI,OAAO,IAAI,CAAC,OAAO,EAAE;YACzB,IAAI,MAAM;gBACR,OAAO;YACT,OAAO,IAAI,OAAO,MAAM;gBACtB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;oBACpC,IAAI,IAAI,CAAC,EAAE,EAAE;wBACX,OAAO,IAAI,CAAC,EAAE;oBAChB;gBACF;YACF;QACF;IACF;IACA;;GAEC,GACD,YAAY,SAAS,CAAC,eAAe,GAAG,SAAU,SAAS;QACzD,IAAI,WAAW,UAAU,QAAQ;QACjC,IAAI,CAAC,UAAU;YACb,OAAO,EAAE;QACX;QACA,IAAI,QAAQ,UAAU,KAAK;QAC3B,IAAI,KAAK,UAAU,EAAE;QACrB,IAAI,OAAO,UAAU,IAAI;QACzB,IAAI,QAAQ,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QACpC,IAAI,CAAC,SAAS,CAAC,MAAM,MAAM,EAAE;YAC3B,OAAO,EAAE;QACX;QACA,IAAI;QACJ,IAAI,SAAS,MAAM;YACjB,SAAS,EAAE;YACX,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,kJAAA,CAAA,mBAA0B,AAAD,EAAE,QAAQ,SAAU,GAAG;gBACnD,KAAK,CAAC,IAAI,IAAI,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI;YACtC;QACF,OAAO,IAAI,MAAM,MAAM;YACrB,SAAS,gBAAgB,MAAM,IAAI;QACrC,OAAO,IAAI,QAAQ,MAAM;YACvB,SAAS,gBAAgB,QAAQ,MAAM;QACzC,OAAO;YACL,mDAAmD;YACnD,SAAS,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,OAAO,SAAU,IAAI;gBACnC,OAAO,CAAC,CAAC;YACX;QACF;QACA,OAAO,gBAAgB,QAAQ;IACjC;IACA;;;;;;;;;;;;;;;;GAgBC,GACD,YAAY,SAAS,CAAC,cAAc,GAAG,SAAU,SAAS;QACxD,IAAI,QAAQ,UAAU,KAAK;QAC3B,IAAI,WAAW,UAAU,QAAQ;QACjC,IAAI,YAAY,aAAa;QAC7B,IAAI,SAAS,YAAY,IAAI,CAAC,eAAe,CAAC,aAE5C,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,SAAU,IAAI;YACxD,OAAO,CAAC,CAAC;QACX;QACA,OAAO,SAAS,gBAAgB,QAAQ;;QACxC,SAAS,aAAa,CAAC;YACrB,IAAI,YAAY,WAAW;YAC3B,IAAI,SAAS,WAAW;YACxB,IAAI,WAAW,WAAW;YAC1B,OAAO,KAAK,CAAC,CAAC,CAAC,UAAU,IAAI,QAAQ,CAAC,CAAC,OAAO,IAAI,QAAQ,CAAC,CAAC,SAAS,IAAI,IAAI,IAAI;gBAC/E,UAAU;gBACV,oCAAoC;gBACpC,OAAO,CAAC,CAAC,UAAU;gBACnB,IAAI,CAAC,CAAC,OAAO;gBACb,MAAM,CAAC,CAAC,SAAS;YACnB,IAAI;QACN;QACA,SAAS,SAAS,GAAG;YACnB,OAAO,UAAU,MAAM,GAAG,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,KAAK,UAAU,MAAM,IAAI;QAC5D;IACF;IACA,YAAY,SAAS,CAAC,aAAa,GAAG,SAAU,QAAQ,EAAE,EAAE,EAAE,OAAO;QACnE,IAAI,gBAAgB,IAAI,CAAC,cAAc;QACvC,IAAI,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE,WAAW;YACxB,IAAI,cAAc;YAClB,IAAI,aAAa;YACjB,cAAc,IAAI,CAAC,SAAU,KAAK,EAAE,aAAa;gBAC/C,IAAK,IAAI,IAAI,GAAG,SAAS,IAAI,MAAM,MAAM,EAAE,IAAK;oBAC9C,IAAI,OAAO,KAAK,CAAC,EAAE;oBACnB,QAAQ,WAAW,IAAI,CAAC,aAAa,eAAe,MAAM,KAAK,cAAc;gBAC/E;YACF;QACF,OAAO;YACL,IAAI,QAAQ,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,cAAc,GAAG,CAAC,YAAY,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,IAAI,CAAC,cAAc,CAAC,YAAY;YACpH,IAAK,IAAI,IAAI,GAAG,SAAS,IAAI,MAAM,MAAM,EAAE,IAAK;gBAC9C,IAAI,OAAO,KAAK,CAAC,EAAE;gBACnB,QAAQ,GAAG,IAAI,CAAC,SAAS,MAAM,KAAK,cAAc;YACpD;QACF;IACF;IACA;;GAEC,GACD,YAAY,SAAS,CAAC,eAAe,GAAG,SAAU,IAAI;QACpD,IAAI,UAAU,CAAA,GAAA,kJAAA,CAAA,sBAA6B,AAAD,EAAE,MAAM;QAClD,OAAO,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,SAAU,SAAS;YAClE,OAAO,CAAC,CAAC,aAAa,WAAW,QAAQ,UAAU,IAAI,KAAK;QAC9D;IACF;IACA;;GAEC,GACD,YAAY,SAAS,CAAC,gBAAgB,GAAG,SAAU,WAAW;QAC5D,OAAO,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY;IACvD;IACA;;;GAGC,GACD,YAAY,SAAS,CAAC,eAAe,GAAG,SAAU,OAAO;QACvD,OAAO,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,SAAU,SAAS;YAClE,OAAO,CAAC,CAAC,aAAa,UAAU,OAAO,KAAK;QAC9C;IACF;IACA;;GAEC,GACD,YAAY,SAAS,CAAC,SAAS,GAAG;QAChC,OAAO,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,SAAU,SAAS;YAClE,OAAO,CAAC,CAAC;QACX;IACF;IACA;;GAEC,GACD,YAAY,SAAS,CAAC,cAAc,GAAG;QACrC,OAAO,IAAI,CAAC,gBAAgB,CAAC,GAAG,CAAC;IACnC;IACA;;;GAGC,GACD,YAAY,SAAS,CAAC,UAAU,GAAG,SAAU,EAAE,EAAE,OAAO;QACtD,wBAAwB,IAAI;QAC5B,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,cAAc,EAAE,SAAU,cAAc;YAChD,IAAI,SAAS,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe;YAC9D,GAAG,IAAI,CAAC,SAAS,QAAQ;QAC3B,GAAG,IAAI;IACT;IACA;;;;;GAKC,GACD,YAAY,SAAS,CAAC,aAAa,GAAG,SAAU,EAAE,EAAE,OAAO;QACzD,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,WAAW,SAAU,MAAM;YACtD,UAAU,GAAG,IAAI,CAAC,SAAS,QAAQ,OAAO,cAAc;QAC1D;IACF;IACA;;;GAGC,GACD,YAAY,SAAS,CAAC,gBAAgB,GAAG,SAAU,OAAO,EAAE,EAAE,EAAE,OAAO;QACrE,wBAAwB,IAAI;QAC5B,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,cAAc,EAAE,SAAU,cAAc;YAChD,IAAI,SAAS,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,eAAe;YAC9D,IAAI,OAAO,OAAO,KAAK,SAAS;gBAC9B,GAAG,IAAI,CAAC,SAAS,QAAQ;YAC3B;QACF,GAAG,IAAI;IACT;IACA;;GAEC,GACD,YAAY,SAAS,CAAC,mBAAmB,GAAG,SAAU,OAAO,EAAE,EAAE,EAAE,OAAO;QACxE,OAAO,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,eAAe,CAAC,UAAU,IAAI;IACjD;IACA,YAAY,SAAS,CAAC,gBAAgB,GAAG,SAAU,WAAW;QAC5D,wBAAwB,IAAI;QAC5B,OAAO,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,YAAY,cAAc,KAAK;IACnE;IACA,YAAY,SAAS,CAAC,uBAAuB,GAAG;QAC9C,OAAO,CAAC,IAAI,CAAC,cAAc,IAAI,EAAE,EAAE,KAAK;IAC1C;IACA,YAAY,SAAS,CAAC,YAAY,GAAG,SAAU,EAAE,EAAE,OAAO;QACxD,wBAAwB,IAAI;QAC5B,IAAI,mBAAmB,EAAE;QACzB,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,cAAc,EAAE,SAAU,YAAY;YAC9C,IAAI,SAAS,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,SAAS,CAAC,aAAa;YAC5D,GAAG,IAAI,CAAC,SAAS,QAAQ,iBAAiB,iBAAiB,IAAI,CAAC;QAClE,GAAG,IAAI;QACP,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,CAAC,iBAAiB,GAAG,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD,EAAE;IACzC;IACA,YAAY,SAAS,CAAC,WAAW,GAAG,SAAU,OAAO;QACnD,sBAAsB,IAAI;QAC1B,IAAI,gBAAgB,IAAI,CAAC,cAAc;QACvC,IAAI,iBAAiB,EAAE;QACvB,cAAc,IAAI,CAAC,SAAU,UAAU,EAAE,aAAa;YACpD,IAAI,uJAAA,CAAA,UAAc,CAAC,QAAQ,CAAC,gBAAgB;gBAC1C,eAAe,IAAI,CAAC;YACtB;QACF;QACA,uJAAA,CAAA,UAAc,CAAC,iBAAiB,CAAC,gBAAgB,uJAAA,CAAA,UAAc,CAAC,oBAAoB,IAAI,SAAU,aAAa;YAC7G,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,cAAc,GAAG,CAAC,gBAAgB,SAAU,SAAS;gBACxD,IAAI,aAAa,CAAC,kBAAkB,YAAY,CAAC,kBAAkB,WAAW,QAAQ,GAAG;oBACvF,UAAU,WAAW;gBACvB;YACF;QACF;IACF;IACA,YAAY,aAAa,GAAG;QAC1B,wBAAwB,SAAU,OAAO;YACvC,IAAI,gBAAgB,QAAQ,cAAc,GAAG,EAAE;YAC/C,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,cAAc,CAAC,GAAG,CAAC,WAAW,SAAU,MAAM;gBACzD,kDAAkD;gBAClD,UAAU,cAAc,IAAI,CAAC,OAAO,cAAc;YACpD;YACA,QAAQ,iBAAiB,GAAG,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD,EAAE;QAC5C;QACA,0BAA0B,SAAU,OAAO;YACzC,yEAAyE;YACzE,6DAA6D;YAC7D,wCAA2C;gBACzC,IAAI,CAAC,QAAQ,cAAc,EAAE;oBAC3B,MAAM,IAAI,MAAM;gBAClB;YACF;QACF;QACA,WAAW,SAAU,OAAO,EAAE,UAAU;YACtC,0EAA0E;YAC1E,iEAAiE;YACjE,QAAQ,MAAM,GAAG,CAAC;YAClB,QAAQ,MAAM,CAAC,iBAAiB,GAAG;YACnC,6DAA6D;YAC7D,6BAA6B;YAC7B,QAAQ,cAAc,GAAG,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD,EAAE;gBACrC,QAAQ,EAAE;YACZ;YACA,QAAQ,gBAAgB,GAAG,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;YACvC,+EAA+E;YAC/E,kDAAkD;YAClD,IAAI,aAAa,WAAW,IAAI;YAChC,IAAI,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,WAAW,OAAO,IAAI,MAAM;gBACtD,WAAW,OAAO,GAAG;YACvB;YACA,WAAW,YAAY,QAAQ,MAAM,CAAC,MAAM;YAC5C,0DAA0D;YAC1D,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,YAAY,2JAAA,CAAA,UAAa,EAAE;YACjC,QAAQ,YAAY,CAAC,YAAY;QACnC;IACF;IACA,OAAO;AACT,EAAE,mJAAA,CAAA,UAAK;AACP,SAAS,kBAAkB,WAAW,EAAE,OAAO;IAC7C,IAAI,SAAS;QACX,IAAI,QAAQ,QAAQ,WAAW;QAC/B,IAAI,KAAK,QAAQ,QAAQ;QACzB,IAAI,SAAS,QAAQ,UAAU;QAC/B,OAAO,SAAS,QAAQ,YAAY,cAAc,KAAK,SAAS,MAAM,QAAQ,YAAY,EAAE,KAAK,MAAM,UAAU,QAAQ,YAAY,IAAI,KAAK;IAChJ;AACF;AACA,SAAS,WAAW,MAAM,EAAE,KAAK;IAC/B,UAAU;IACV,sDAAsD;IACtD,IAAI,qBAAqB,OAAO,KAAK,IAAI,CAAC,OAAO,UAAU;IAC3D,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,OAAO,SAAU,SAAS,EAAE,IAAI;QACnC,IAAI,SAAS,gBAAgB,oBAAoB;YAC/C;QACF;QACA,yEAAyE;QACzE,8BAA8B;QAC9B,IAAI,CAAC,uJAAA,CAAA,UAAc,CAAC,QAAQ,CAAC,OAAO;YAClC,IAAI,OAAO,cAAc,UAAU;gBACjC,MAAM,CAAC,KAAK,GAAG,CAAC,MAAM,CAAC,KAAK,GAAG,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,aAAa,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,MAAM,CAAC,KAAK,EAAE,WAAW;YACnF,OAAO;gBACL,IAAI,MAAM,CAAC,KAAK,IAAI,MAAM;oBACxB,MAAM,CAAC,KAAK,GAAG;gBACjB;YACF;QACF;IACF;AACF;AACA,SAAS,gBAAgB,IAAI,EAAE,QAAQ,EAAE,KAAK;IAC5C,uDAAuD;IACvD,oBAAoB;IACpB,IAAI,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,WAAW;QACrB,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;QAC3B,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,UAAU,SAAU,YAAY;YACnC,IAAI,gBAAgB,MAAM;gBACxB,IAAI,SAAS,CAAA,GAAA,kJAAA,CAAA,sBAA6B,AAAD,EAAE,cAAc;gBACzD,UAAU,QAAQ,SAAS,GAAG,CAAC,cAAc;YAC/C;QACF;QACA,OAAO,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,OAAO,SAAU,IAAI;YACjC,OAAO,QAAQ,SAAS,GAAG,CAAC,IAAI,CAAC,KAAK;QACxC;IACF,OAAO;QACL,IAAI,WAAW,CAAA,GAAA,kJAAA,CAAA,sBAA6B,AAAD,EAAE,UAAU;QACvD,OAAO,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,OAAO,SAAU,IAAI;YACjC,OAAO,QAAQ,YAAY,QAAQ,IAAI,CAAC,KAAK,KAAK;QACpD;IACF;AACF;AACA,SAAS,gBAAgB,UAAU,EAAE,SAAS;IAC5C,8CAA8C;IAC9C,wCAAwC;IACxC,OAAO,UAAU,cAAc,CAAC,aAAa,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,YAAY,SAAU,IAAI;QAC5E,OAAO,QAAQ,KAAK,OAAO,KAAK,UAAU,OAAO;IACnD,KAAK;AACP;AACA,SAAS,wBAAwB,IAAI;IACnC,IAAI,0BAA0B,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;IAC1C,QAAQ,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,kJAAA,CAAA,mBAA0B,AAAD,EAAE,KAAK,YAAY,GAAG,SAAU,QAAQ;QAC5E,wCAA2C;YACzC,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE,uJAAA,CAAA,UAAc,CAAC,QAAQ,CAAC,WAAW,MAAM,WAAW;QAC7D;QACA,wBAAwB,GAAG,CAAC,UAAU;IACxC;IACA,OAAO;QACL,yBAAyB;IAC3B;AACF;AACA,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,aAAa,8JAAA,CAAA,eAAY;uCAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2041, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/model/OptionManager.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { normalizeToArray\n// , MappingExistingItem, setComponentTypeToKeyInfo, mappingToExists\n} from '../util/model.js';\nimport { each, clone, map, isTypedArray, setAsPrimitive, isArray, isObject\n// , HashMap , createHashMap, extend, merge,\n} from 'zrender/lib/core/util.js';\nimport { error } from '../util/log.js';\nvar QUERY_REG = /^(min|max)?(.+)$/;\n// Key: mainType\n// type FakeComponentsMap = HashMap<(MappingExistingItem & { subType: string })[]>;\n/**\r\n * TERM EXPLANATIONS:\r\n * See `ECOption` and `ECUnitOption` in `src/util/types.ts`.\r\n */\nvar OptionManager = /** @class */function () {\n  // timeline.notMerge is not supported in ec3. Firstly there is rearly\n  // case that notMerge is needed. Secondly supporting 'notMerge' requires\n  // rawOption cloned and backuped when timeline changed, which does no\n  // good to performance. What's more, that both timeline and setOption\n  // method supply 'notMerge' brings complex and some problems.\n  // Consider this case:\n  // (step1) chart.setOption({timeline: {notMerge: false}, ...}, false);\n  // (step2) chart.setOption({timeline: {notMerge: true}, ...}, false);\n  function OptionManager(api) {\n    this._timelineOptions = [];\n    this._mediaList = [];\n    /**\r\n     * -1, means default.\r\n     * empty means no media.\r\n     */\n    this._currentMediaIndices = [];\n    this._api = api;\n  }\n  OptionManager.prototype.setOption = function (rawOption, optionPreprocessorFuncs, opt) {\n    if (rawOption) {\n      // That set dat primitive is dangerous if user reuse the data when setOption again.\n      each(normalizeToArray(rawOption.series), function (series) {\n        series && series.data && isTypedArray(series.data) && setAsPrimitive(series.data);\n      });\n      each(normalizeToArray(rawOption.dataset), function (dataset) {\n        dataset && dataset.source && isTypedArray(dataset.source) && setAsPrimitive(dataset.source);\n      });\n    }\n    // Caution: some series modify option data, if do not clone,\n    // it should ensure that the repeat modify correctly\n    // (create a new object when modify itself).\n    rawOption = clone(rawOption);\n    // FIXME\n    // If some property is set in timeline options or media option but\n    // not set in baseOption, a warning should be given.\n    var optionBackup = this._optionBackup;\n    var newParsedOption = parseRawOption(rawOption, optionPreprocessorFuncs, !optionBackup);\n    this._newBaseOption = newParsedOption.baseOption;\n    // For setOption at second time (using merge mode);\n    if (optionBackup) {\n      // FIXME\n      // the restore merge solution is essentially incorrect.\n      // the mapping can not be 100% consistent with ecModel, which probably brings\n      // potential bug!\n      // The first merge is delayed, because in most cases, users do not call `setOption` twice.\n      // let fakeCmptsMap = this._fakeCmptsMap;\n      // if (!fakeCmptsMap) {\n      //     fakeCmptsMap = this._fakeCmptsMap = createHashMap();\n      //     mergeToBackupOption(fakeCmptsMap, null, optionBackup.baseOption, null);\n      // }\n      // mergeToBackupOption(\n      //     fakeCmptsMap, optionBackup.baseOption, newParsedOption.baseOption, opt\n      // );\n      // For simplicity, timeline options and media options do not support merge,\n      // that is, if you `setOption` twice and both has timeline options, the latter\n      // timeline options will not be merged to the former, but just substitute them.\n      if (newParsedOption.timelineOptions.length) {\n        optionBackup.timelineOptions = newParsedOption.timelineOptions;\n      }\n      if (newParsedOption.mediaList.length) {\n        optionBackup.mediaList = newParsedOption.mediaList;\n      }\n      if (newParsedOption.mediaDefault) {\n        optionBackup.mediaDefault = newParsedOption.mediaDefault;\n      }\n    } else {\n      this._optionBackup = newParsedOption;\n    }\n  };\n  OptionManager.prototype.mountOption = function (isRecreate) {\n    var optionBackup = this._optionBackup;\n    this._timelineOptions = optionBackup.timelineOptions;\n    this._mediaList = optionBackup.mediaList;\n    this._mediaDefault = optionBackup.mediaDefault;\n    this._currentMediaIndices = [];\n    return clone(isRecreate\n    // this._optionBackup.baseOption, which is created at the first `setOption`\n    // called, and is merged into every new option by inner method `mergeToBackupOption`\n    // each time `setOption` called, can be only used in `isRecreate`, because\n    // its reliability is under suspicion. In other cases option merge is\n    // performed by `model.mergeOption`.\n    ? optionBackup.baseOption : this._newBaseOption);\n  };\n  OptionManager.prototype.getTimelineOption = function (ecModel) {\n    var option;\n    var timelineOptions = this._timelineOptions;\n    if (timelineOptions.length) {\n      // getTimelineOption can only be called after ecModel inited,\n      // so we can get currentIndex from timelineModel.\n      var timelineModel = ecModel.getComponent('timeline');\n      if (timelineModel) {\n        option = clone(\n        // FIXME:TS as TimelineModel or quivlant interface\n        timelineOptions[timelineModel.getCurrentIndex()]);\n      }\n    }\n    return option;\n  };\n  OptionManager.prototype.getMediaOption = function (ecModel) {\n    var ecWidth = this._api.getWidth();\n    var ecHeight = this._api.getHeight();\n    var mediaList = this._mediaList;\n    var mediaDefault = this._mediaDefault;\n    var indices = [];\n    var result = [];\n    // No media defined.\n    if (!mediaList.length && !mediaDefault) {\n      return result;\n    }\n    // Multi media may be applied, the latter defined media has higher priority.\n    for (var i = 0, len = mediaList.length; i < len; i++) {\n      if (applyMediaQuery(mediaList[i].query, ecWidth, ecHeight)) {\n        indices.push(i);\n      }\n    }\n    // FIXME\n    // Whether mediaDefault should force users to provide? Otherwise\n    // the change by media query can not be recorvered.\n    if (!indices.length && mediaDefault) {\n      indices = [-1];\n    }\n    if (indices.length && !indicesEquals(indices, this._currentMediaIndices)) {\n      result = map(indices, function (index) {\n        return clone(index === -1 ? mediaDefault.option : mediaList[index].option);\n      });\n    }\n    // Otherwise return nothing.\n    this._currentMediaIndices = indices;\n    return result;\n  };\n  return OptionManager;\n}();\n/**\r\n * [RAW_OPTION_PATTERNS]\r\n * (Note: \"series: []\" represents all other props in `ECUnitOption`)\r\n *\r\n * (1) No prop \"baseOption\" declared:\r\n * Root option is used as \"baseOption\" (except prop \"options\" and \"media\").\r\n * ```js\r\n * option = {\r\n *     series: [],\r\n *     timeline: {},\r\n *     options: [],\r\n * };\r\n * option = {\r\n *     series: [],\r\n *     media: {},\r\n * };\r\n * option = {\r\n *     series: [],\r\n *     timeline: {},\r\n *     options: [],\r\n *     media: {},\r\n * }\r\n * ```\r\n *\r\n * (2) Prop \"baseOption\" declared:\r\n * If \"baseOption\" declared, `ECUnitOption` props can only be declared\r\n * inside \"baseOption\" except prop \"timeline\" (compat ec2).\r\n * ```js\r\n * option = {\r\n *     baseOption: {\r\n *         timeline: {},\r\n *         series: [],\r\n *     },\r\n *     options: []\r\n * };\r\n * option = {\r\n *     baseOption: {\r\n *         series: [],\r\n *     },\r\n *     media: []\r\n * };\r\n * option = {\r\n *     baseOption: {\r\n *         timeline: {},\r\n *         series: [],\r\n *     },\r\n *     options: []\r\n *     media: []\r\n * };\r\n * option = {\r\n *     // ec3 compat ec2: allow (only) `timeline` declared\r\n *     // outside baseOption. Keep this setting for compat.\r\n *     timeline: {},\r\n *     baseOption: {\r\n *         series: [],\r\n *     },\r\n *     options: [],\r\n *     media: []\r\n * };\r\n * ```\r\n */\nfunction parseRawOption(\n// `rawOption` May be modified\nrawOption, optionPreprocessorFuncs, isNew) {\n  var mediaList = [];\n  var mediaDefault;\n  var baseOption;\n  var declaredBaseOption = rawOption.baseOption;\n  // Compatible with ec2, [RAW_OPTION_PATTERNS] above.\n  var timelineOnRoot = rawOption.timeline;\n  var timelineOptionsOnRoot = rawOption.options;\n  var mediaOnRoot = rawOption.media;\n  var hasMedia = !!rawOption.media;\n  var hasTimeline = !!(timelineOptionsOnRoot || timelineOnRoot || declaredBaseOption && declaredBaseOption.timeline);\n  if (declaredBaseOption) {\n    baseOption = declaredBaseOption;\n    // For merge option.\n    if (!baseOption.timeline) {\n      baseOption.timeline = timelineOnRoot;\n    }\n  }\n  // For convenience, enable to use the root option as the `baseOption`:\n  // `{ ...normalOptionProps, media: [{ ... }, { ... }] }`\n  else {\n    if (hasTimeline || hasMedia) {\n      rawOption.options = rawOption.media = null;\n    }\n    baseOption = rawOption;\n  }\n  if (hasMedia) {\n    if (isArray(mediaOnRoot)) {\n      each(mediaOnRoot, function (singleMedia) {\n        if (process.env.NODE_ENV !== 'production') {\n          // Real case of wrong config.\n          if (singleMedia && !singleMedia.option && isObject(singleMedia.query) && isObject(singleMedia.query.option)) {\n            error('Illegal media option. Must be like { media: [ { query: {}, option: {} } ] }');\n          }\n        }\n        if (singleMedia && singleMedia.option) {\n          if (singleMedia.query) {\n            mediaList.push(singleMedia);\n          } else if (!mediaDefault) {\n            // Use the first media default.\n            mediaDefault = singleMedia;\n          }\n        }\n      });\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        // Real case of wrong config.\n        error('Illegal media option. Must be an array. Like { media: [ {...}, {...} ] }');\n      }\n    }\n  }\n  doPreprocess(baseOption);\n  each(timelineOptionsOnRoot, function (option) {\n    return doPreprocess(option);\n  });\n  each(mediaList, function (media) {\n    return doPreprocess(media.option);\n  });\n  function doPreprocess(option) {\n    each(optionPreprocessorFuncs, function (preProcess) {\n      preProcess(option, isNew);\n    });\n  }\n  return {\n    baseOption: baseOption,\n    timelineOptions: timelineOptionsOnRoot || [],\n    mediaDefault: mediaDefault,\n    mediaList: mediaList\n  };\n}\n/**\r\n * @see <http://www.w3.org/TR/css3-mediaqueries/#media1>\r\n * Support: width, height, aspectRatio\r\n * Can use max or min as prefix.\r\n */\nfunction applyMediaQuery(query, ecWidth, ecHeight) {\n  var realMap = {\n    width: ecWidth,\n    height: ecHeight,\n    aspectratio: ecWidth / ecHeight // lower case for convenience.\n  };\n  var applicable = true;\n  each(query, function (value, attr) {\n    var matched = attr.match(QUERY_REG);\n    if (!matched || !matched[1] || !matched[2]) {\n      return;\n    }\n    var operator = matched[1];\n    var realAttr = matched[2].toLowerCase();\n    if (!compare(realMap[realAttr], value, operator)) {\n      applicable = false;\n    }\n  });\n  return applicable;\n}\nfunction compare(real, expect, operator) {\n  if (operator === 'min') {\n    return real >= expect;\n  } else if (operator === 'max') {\n    return real <= expect;\n  } else {\n    // Equals\n    return real === expect;\n  }\n}\nfunction indicesEquals(indices1, indices2) {\n  // indices is always order by asc and has only finite number.\n  return indices1.join(',') === indices2.join(',');\n}\n/**\r\n * Consider case:\r\n * `chart.setOption(opt1);`\r\n * Then user do some interaction like dataZoom, dataView changing.\r\n * `chart.setOption(opt2);`\r\n * Then user press 'reset button' in toolbox.\r\n *\r\n * After doing that all of the interaction effects should be reset, the\r\n * chart should be the same as the result of invoke\r\n * `chart.setOption(opt1); chart.setOption(opt2);`.\r\n *\r\n * Although it is not able ensure that\r\n * `chart.setOption(opt1); chart.setOption(opt2);` is equivalents to\r\n * `chart.setOption(merge(opt1, opt2));` exactly,\r\n * this might be the only simple way to implement that feature.\r\n *\r\n * MEMO: We've considered some other approaches:\r\n * 1. Each model handles its self restoration but not uniform treatment.\r\n *     (Too complex in logic and error-prone)\r\n * 2. Use a shadow ecModel. (Performance expensive)\r\n *\r\n * FIXME: A possible solution:\r\n * Add a extra level of model for each component model. The inheritance chain would be:\r\n * ecModel <- componentModel <- componentActionModel <- dataItemModel\r\n * And all of the actions can only modify the `componentActionModel` rather than\r\n * `componentModel`. `setOption` will only modify the `ecModel` and `componentModel`.\r\n * When \"resotre\" action triggered, model from `componentActionModel` will be discarded\r\n * instead of recreating the \"ecModel\" from the \"_optionBackup\".\r\n */\n// function mergeToBackupOption(\n//     fakeCmptsMap: FakeComponentsMap,\n//     // `tarOption` Can be null/undefined, means init\n//     tarOption: ECUnitOption,\n//     newOption: ECUnitOption,\n//     // Can be null/undefined\n//     opt: InnerSetOptionOpts\n// ): void {\n//     newOption = newOption || {} as ECUnitOption;\n//     const notInit = !!tarOption;\n//     each(newOption, function (newOptsInMainType, mainType) {\n//         if (newOptsInMainType == null) {\n//             return;\n//         }\n//         if (!ComponentModel.hasClass(mainType)) {\n//             if (tarOption) {\n//                 tarOption[mainType] = merge(tarOption[mainType], newOptsInMainType, true);\n//             }\n//         }\n//         else {\n//             const oldTarOptsInMainType = notInit ? normalizeToArray(tarOption[mainType]) : null;\n//             const oldFakeCmptsInMainType = fakeCmptsMap.get(mainType) || [];\n//             const resultTarOptsInMainType = notInit ? (tarOption[mainType] = [] as ComponentOption[]) : null;\n//             const resultFakeCmptsInMainType = fakeCmptsMap.set(mainType, []);\n//             const mappingResult = mappingToExists(\n//                 oldFakeCmptsInMainType,\n//                 normalizeToArray(newOptsInMainType),\n//                 (opt && opt.replaceMergeMainTypeMap.get(mainType)) ? 'replaceMerge' : 'normalMerge'\n//             );\n//             setComponentTypeToKeyInfo(mappingResult, mainType, ComponentModel as ComponentModelConstructor);\n//             each(mappingResult, function (resultItem, index) {\n//                 // The same logic as `Global.ts#_mergeOption`.\n//                 let fakeCmpt = resultItem.existing;\n//                 const newOption = resultItem.newOption;\n//                 const keyInfo = resultItem.keyInfo;\n//                 let fakeCmptOpt;\n//                 if (!newOption) {\n//                     fakeCmptOpt = oldTarOptsInMainType[index];\n//                 }\n//                 else {\n//                     if (fakeCmpt && fakeCmpt.subType === keyInfo.subType) {\n//                         fakeCmpt.name = keyInfo.name;\n//                         if (notInit) {\n//                             fakeCmptOpt = merge(oldTarOptsInMainType[index], newOption, true);\n//                         }\n//                     }\n//                     else {\n//                         fakeCmpt = extend({}, keyInfo);\n//                         if (notInit) {\n//                             fakeCmptOpt = clone(newOption);\n//                         }\n//                     }\n//                 }\n//                 if (fakeCmpt) {\n//                     notInit && resultTarOptsInMainType.push(fakeCmptOpt);\n//                     resultFakeCmptsInMainType.push(fakeCmpt);\n//                 }\n//                 else {\n//                     notInit && resultTarOptsInMainType.push(void 0);\n//                     resultFakeCmptsInMainType.push(void 0);\n//                 }\n//             });\n//         }\n//     });\n// }\nexport default OptionManager;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AAgQU;AA/PV;AAGA;AAGA;;;;AACA,IAAI,YAAY;AAChB,gBAAgB;AAChB,mFAAmF;AACnF;;;CAGC,GACD,IAAI,gBAAgB,WAAW,GAAE;IAC/B,qEAAqE;IACrE,wEAAwE;IACxE,qEAAqE;IACrE,qEAAqE;IACrE,6DAA6D;IAC7D,sBAAsB;IACtB,sEAAsE;IACtE,qEAAqE;IACrE,SAAS,cAAc,GAAG;QACxB,IAAI,CAAC,gBAAgB,GAAG,EAAE;QAC1B,IAAI,CAAC,UAAU,GAAG,EAAE;QACpB;;;KAGC,GACD,IAAI,CAAC,oBAAoB,GAAG,EAAE;QAC9B,IAAI,CAAC,IAAI,GAAG;IACd;IACA,cAAc,SAAS,CAAC,SAAS,GAAG,SAAU,SAAS,EAAE,uBAAuB,EAAE,GAAG;QACnF,IAAI,WAAW;YACb,mFAAmF;YACnF,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,MAAM,GAAG,SAAU,MAAM;gBACvD,UAAU,OAAO,IAAI,IAAI,CAAA,GAAA,iJAAA,CAAA,eAAY,AAAD,EAAE,OAAO,IAAI,KAAK,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,IAAI;YAClF;YACA,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,kJAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU,OAAO,GAAG,SAAU,OAAO;gBACzD,WAAW,QAAQ,MAAM,IAAI,CAAA,GAAA,iJAAA,CAAA,eAAY,AAAD,EAAE,QAAQ,MAAM,KAAK,CAAA,GAAA,iJAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM;YAC5F;QACF;QACA,4DAA4D;QAC5D,oDAAoD;QACpD,4CAA4C;QAC5C,YAAY,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE;QAClB,QAAQ;QACR,kEAAkE;QAClE,oDAAoD;QACpD,IAAI,eAAe,IAAI,CAAC,aAAa;QACrC,IAAI,kBAAkB,eAAe,WAAW,yBAAyB,CAAC;QAC1E,IAAI,CAAC,cAAc,GAAG,gBAAgB,UAAU;QAChD,mDAAmD;QACnD,IAAI,cAAc;YAChB,QAAQ;YACR,uDAAuD;YACvD,6EAA6E;YAC7E,iBAAiB;YACjB,0FAA0F;YAC1F,yCAAyC;YACzC,uBAAuB;YACvB,2DAA2D;YAC3D,8EAA8E;YAC9E,IAAI;YACJ,uBAAuB;YACvB,6EAA6E;YAC7E,KAAK;YACL,2EAA2E;YAC3E,8EAA8E;YAC9E,+EAA+E;YAC/E,IAAI,gBAAgB,eAAe,CAAC,MAAM,EAAE;gBAC1C,aAAa,eAAe,GAAG,gBAAgB,eAAe;YAChE;YACA,IAAI,gBAAgB,SAAS,CAAC,MAAM,EAAE;gBACpC,aAAa,SAAS,GAAG,gBAAgB,SAAS;YACpD;YACA,IAAI,gBAAgB,YAAY,EAAE;gBAChC,aAAa,YAAY,GAAG,gBAAgB,YAAY;YAC1D;QACF,OAAO;YACL,IAAI,CAAC,aAAa,GAAG;QACvB;IACF;IACA,cAAc,SAAS,CAAC,WAAW,GAAG,SAAU,UAAU;QACxD,IAAI,eAAe,IAAI,CAAC,aAAa;QACrC,IAAI,CAAC,gBAAgB,GAAG,aAAa,eAAe;QACpD,IAAI,CAAC,UAAU,GAAG,aAAa,SAAS;QACxC,IAAI,CAAC,aAAa,GAAG,aAAa,YAAY;QAC9C,IAAI,CAAC,oBAAoB,GAAG,EAAE;QAC9B,OAAO,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,aAMX,aAAa,UAAU,GAAG,IAAI,CAAC,cAAc;IACjD;IACA,cAAc,SAAS,CAAC,iBAAiB,GAAG,SAAU,OAAO;QAC3D,IAAI;QACJ,IAAI,kBAAkB,IAAI,CAAC,gBAAgB;QAC3C,IAAI,gBAAgB,MAAM,EAAE;YAC1B,6DAA6D;YAC7D,iDAAiD;YACjD,IAAI,gBAAgB,QAAQ,YAAY,CAAC;YACzC,IAAI,eAAe;gBACjB,SAAS,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EACb,kDAAkD;gBAClD,eAAe,CAAC,cAAc,eAAe,GAAG;YAClD;QACF;QACA,OAAO;IACT;IACA,cAAc,SAAS,CAAC,cAAc,GAAG,SAAU,OAAO;QACxD,IAAI,UAAU,IAAI,CAAC,IAAI,CAAC,QAAQ;QAChC,IAAI,WAAW,IAAI,CAAC,IAAI,CAAC,SAAS;QAClC,IAAI,YAAY,IAAI,CAAC,UAAU;QAC/B,IAAI,eAAe,IAAI,CAAC,aAAa;QACrC,IAAI,UAAU,EAAE;QAChB,IAAI,SAAS,EAAE;QACf,oBAAoB;QACpB,IAAI,CAAC,UAAU,MAAM,IAAI,CAAC,cAAc;YACtC,OAAO;QACT;QACA,4EAA4E;QAC5E,IAAK,IAAI,IAAI,GAAG,MAAM,UAAU,MAAM,EAAE,IAAI,KAAK,IAAK;YACpD,IAAI,gBAAgB,SAAS,CAAC,EAAE,CAAC,KAAK,EAAE,SAAS,WAAW;gBAC1D,QAAQ,IAAI,CAAC;YACf;QACF;QACA,QAAQ;QACR,gEAAgE;QAChE,mDAAmD;QACnD,IAAI,CAAC,QAAQ,MAAM,IAAI,cAAc;YACnC,UAAU;gBAAC,CAAC;aAAE;QAChB;QACA,IAAI,QAAQ,MAAM,IAAI,CAAC,cAAc,SAAS,IAAI,CAAC,oBAAoB,GAAG;YACxE,SAAS,CAAA,GAAA,iJAAA,CAAA,MAAG,AAAD,EAAE,SAAS,SAAU,KAAK;gBACnC,OAAO,CAAA,GAAA,iJAAA,CAAA,QAAK,AAAD,EAAE,UAAU,CAAC,IAAI,aAAa,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC,MAAM;YAC3E;QACF;QACA,4BAA4B;QAC5B,IAAI,CAAC,oBAAoB,GAAG;QAC5B,OAAO;IACT;IACA,OAAO;AACT;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;CA4DC,GACD,SAAS,eACT,8BAA8B;AAC9B,SAAS,EAAE,uBAAuB,EAAE,KAAK;IACvC,IAAI,YAAY,EAAE;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI,qBAAqB,UAAU,UAAU;IAC7C,oDAAoD;IACpD,IAAI,iBAAiB,UAAU,QAAQ;IACvC,IAAI,wBAAwB,UAAU,OAAO;IAC7C,IAAI,cAAc,UAAU,KAAK;IACjC,IAAI,WAAW,CAAC,CAAC,UAAU,KAAK;IAChC,IAAI,cAAc,CAAC,CAAC,CAAC,yBAAyB,kBAAkB,sBAAsB,mBAAmB,QAAQ;IACjH,IAAI,oBAAoB;QACtB,aAAa;QACb,oBAAoB;QACpB,IAAI,CAAC,WAAW,QAAQ,EAAE;YACxB,WAAW,QAAQ,GAAG;QACxB;IACF,OAGK;QACH,IAAI,eAAe,UAAU;YAC3B,UAAU,OAAO,GAAG,UAAU,KAAK,GAAG;QACxC;QACA,aAAa;IACf;IACA,IAAI,UAAU;QACZ,IAAI,CAAA,GAAA,iJAAA,CAAA,UAAO,AAAD,EAAE,cAAc;YACxB,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,aAAa,SAAU,WAAW;gBACrC,wCAA2C;oBACzC,6BAA6B;oBAC7B,IAAI,eAAe,CAAC,YAAY,MAAM,IAAI,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,KAAK,KAAK,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,KAAK,CAAC,MAAM,GAAG;wBAC3G,CAAA,GAAA,gJAAA,CAAA,QAAK,AAAD,EAAE;oBACR;gBACF;gBACA,IAAI,eAAe,YAAY,MAAM,EAAE;oBACrC,IAAI,YAAY,KAAK,EAAE;wBACrB,UAAU,IAAI,CAAC;oBACjB,OAAO,IAAI,CAAC,cAAc;wBACxB,+BAA+B;wBAC/B,eAAe;oBACjB;gBACF;YACF;QACF,OAAO;YACL,wCAA2C;gBACzC,6BAA6B;gBAC7B,CAAA,GAAA,gJAAA,CAAA,QAAK,AAAD,EAAE;YACR;QACF;IACF;IACA,aAAa;IACb,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,uBAAuB,SAAU,MAAM;QAC1C,OAAO,aAAa;IACtB;IACA,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,WAAW,SAAU,KAAK;QAC7B,OAAO,aAAa,MAAM,MAAM;IAClC;IACA,SAAS,aAAa,MAAM;QAC1B,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,yBAAyB,SAAU,UAAU;YAChD,WAAW,QAAQ;QACrB;IACF;IACA,OAAO;QACL,YAAY;QACZ,iBAAiB,yBAAyB,EAAE;QAC5C,cAAc;QACd,WAAW;IACb;AACF;AACA;;;;CAIC,GACD,SAAS,gBAAgB,KAAK,EAAE,OAAO,EAAE,QAAQ;IAC/C,IAAI,UAAU;QACZ,OAAO;QACP,QAAQ;QACR,aAAa,UAAU,SAAS,8BAA8B;IAChE;IACA,IAAI,aAAa;IACjB,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,OAAO,SAAU,KAAK,EAAE,IAAI;QAC/B,IAAI,UAAU,KAAK,KAAK,CAAC;QACzB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,EAAE,IAAI,CAAC,OAAO,CAAC,EAAE,EAAE;YAC1C;QACF;QACA,IAAI,WAAW,OAAO,CAAC,EAAE;QACzB,IAAI,WAAW,OAAO,CAAC,EAAE,CAAC,WAAW;QACrC,IAAI,CAAC,QAAQ,OAAO,CAAC,SAAS,EAAE,OAAO,WAAW;YAChD,aAAa;QACf;IACF;IACA,OAAO;AACT;AACA,SAAS,QAAQ,IAAI,EAAE,MAAM,EAAE,QAAQ;IACrC,IAAI,aAAa,OAAO;QACtB,OAAO,QAAQ;IACjB,OAAO,IAAI,aAAa,OAAO;QAC7B,OAAO,QAAQ;IACjB,OAAO;QACL,SAAS;QACT,OAAO,SAAS;IAClB;AACF;AACA,SAAS,cAAc,QAAQ,EAAE,QAAQ;IACvC,6DAA6D;IAC7D,OAAO,SAAS,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC;AAC9C;uCA+Fe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2393, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/model/mixin/dataFormat.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { retrieveRawValue } from '../../data/helper/dataProvider.js';\nimport { formatTpl } from '../../util/format.js';\nimport { error, makePrintable } from '../../util/log.js';\nvar DIMENSION_LABEL_REG = /\\{@(.+?)\\}/g;\nvar DataFormatMixin = /** @class */function () {\n  function DataFormatMixin() {}\n  /**\r\n   * Get params for formatter\r\n   */\n  DataFormatMixin.prototype.getDataParams = function (dataIndex, dataType) {\n    var data = this.getData(dataType);\n    var rawValue = this.getRawValue(dataIndex, dataType);\n    var rawDataIndex = data.getRawIndex(dataIndex);\n    var name = data.getName(dataIndex);\n    var itemOpt = data.getRawDataItem(dataIndex);\n    var style = data.getItemVisual(dataIndex, 'style');\n    var color = style && style[data.getItemVisual(dataIndex, 'drawType') || 'fill'];\n    var borderColor = style && style.stroke;\n    var mainType = this.mainType;\n    var isSeries = mainType === 'series';\n    var userOutput = data.userOutput && data.userOutput.get();\n    return {\n      componentType: mainType,\n      componentSubType: this.subType,\n      componentIndex: this.componentIndex,\n      seriesType: isSeries ? this.subType : null,\n      seriesIndex: this.seriesIndex,\n      seriesId: isSeries ? this.id : null,\n      seriesName: isSeries ? this.name : null,\n      name: name,\n      dataIndex: rawDataIndex,\n      data: itemOpt,\n      dataType: dataType,\n      value: rawValue,\n      color: color,\n      borderColor: borderColor,\n      dimensionNames: userOutput ? userOutput.fullDimensions : null,\n      encode: userOutput ? userOutput.encode : null,\n      // Param name list for mapping `a`, `b`, `c`, `d`, `e`\n      $vars: ['seriesName', 'name', 'value']\n    };\n  };\n  /**\r\n   * Format label\r\n   * @param dataIndex\r\n   * @param status 'normal' by default\r\n   * @param dataType\r\n   * @param labelDimIndex Only used in some chart that\r\n   *        use formatter in different dimensions, like radar.\r\n   * @param formatter Formatter given outside.\r\n   * @return return null/undefined if no formatter\r\n   */\n  DataFormatMixin.prototype.getFormattedLabel = function (dataIndex, status, dataType, labelDimIndex, formatter, extendParams) {\n    status = status || 'normal';\n    var data = this.getData(dataType);\n    var params = this.getDataParams(dataIndex, dataType);\n    if (extendParams) {\n      params.value = extendParams.interpolatedValue;\n    }\n    if (labelDimIndex != null && zrUtil.isArray(params.value)) {\n      params.value = params.value[labelDimIndex];\n    }\n    if (!formatter) {\n      var itemModel = data.getItemModel(dataIndex);\n      // @ts-ignore\n      formatter = itemModel.get(status === 'normal' ? ['label', 'formatter'] : [status, 'label', 'formatter']);\n    }\n    if (zrUtil.isFunction(formatter)) {\n      params.status = status;\n      params.dimensionIndex = labelDimIndex;\n      return formatter(params);\n    } else if (zrUtil.isString(formatter)) {\n      var str = formatTpl(formatter, params);\n      // Support 'aaa{@[3]}bbb{@product}ccc'.\n      // Do not support '}' in dim name util have to.\n      return str.replace(DIMENSION_LABEL_REG, function (origin, dimStr) {\n        var len = dimStr.length;\n        var dimLoose = dimStr;\n        if (dimLoose.charAt(0) === '[' && dimLoose.charAt(len - 1) === ']') {\n          dimLoose = +dimLoose.slice(1, len - 1); // Also support: '[]' => 0\n          if (process.env.NODE_ENV !== 'production') {\n            if (isNaN(dimLoose)) {\n              error(\"Invalide label formatter: @\" + dimStr + \", only support @[0], @[1], @[2], ...\");\n            }\n          }\n        }\n        var val = retrieveRawValue(data, dataIndex, dimLoose);\n        if (extendParams && zrUtil.isArray(extendParams.interpolatedValue)) {\n          var dimIndex = data.getDimensionIndex(dimLoose);\n          if (dimIndex >= 0) {\n            val = extendParams.interpolatedValue[dimIndex];\n          }\n        }\n        return val != null ? val + '' : '';\n      });\n    }\n  };\n  /**\r\n   * Get raw value in option\r\n   */\n  DataFormatMixin.prototype.getRawValue = function (idx, dataType) {\n    return retrieveRawValue(this.getData(dataType), idx);\n  };\n  /**\r\n   * Should be implemented.\r\n   * @param {number} dataIndex\r\n   * @param {boolean} [multipleSeries=false]\r\n   * @param {string} [dataType]\r\n   */\n  DataFormatMixin.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    // Empty function\n    return;\n  };\n  return DataFormatMixin;\n}();\nexport { DataFormatMixin };\n;\n// PENDING: previously we accept this type when calling `formatTooltip`,\n// but guess little chance has been used outside. Do we need to backward\n// compat it?\n// type TooltipFormatResultLegacyObject = {\n//     // `html` means the markup language text, either in 'html' or 'richText'.\n//     // The name `html` is not appropriate because in 'richText' it is not a HTML\n//     // string. But still support it for backward compatibility.\n//     html: string;\n//     markers: Dictionary<ColorString>;\n// };\n/**\r\n * For backward compat, normalize the return from `formatTooltip`.\r\n */\nexport function normalizeTooltipFormatResult(result) {\n  var markupText;\n  // let markers: Dictionary<ColorString>;\n  var markupFragment;\n  if (zrUtil.isObject(result)) {\n    if (result.type) {\n      markupFragment = result;\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn('The return type of `formatTooltip` is not supported: ' + makePrintable(result));\n      }\n    }\n    // else {\n    //     markupText = (result as TooltipFormatResultLegacyObject).html;\n    //     markers = (result as TooltipFormatResultLegacyObject).markers;\n    //     if (markersExisting) {\n    //         markers = zrUtil.merge(markersExisting, markers);\n    //     }\n    // }\n  } else {\n    markupText = result;\n  }\n  return {\n    text: markupText,\n    // markers: markers || markersExisting,\n    frag: markupFragment\n  };\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;AAkFc;AAjFd;AACA;AACA;AACA;;;;;AACA,IAAI,sBAAsB;AAC1B,IAAI,kBAAkB,WAAW,GAAE;IACjC,SAAS,mBAAmB;IAC5B;;GAEC,GACD,gBAAgB,SAAS,CAAC,aAAa,GAAG,SAAU,SAAS,EAAE,QAAQ;QACrE,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,WAAW;QAC3C,IAAI,eAAe,KAAK,WAAW,CAAC;QACpC,IAAI,OAAO,KAAK,OAAO,CAAC;QACxB,IAAI,UAAU,KAAK,cAAc,CAAC;QAClC,IAAI,QAAQ,KAAK,aAAa,CAAC,WAAW;QAC1C,IAAI,QAAQ,SAAS,KAAK,CAAC,KAAK,aAAa,CAAC,WAAW,eAAe,OAAO;QAC/E,IAAI,cAAc,SAAS,MAAM,MAAM;QACvC,IAAI,WAAW,IAAI,CAAC,QAAQ;QAC5B,IAAI,WAAW,aAAa;QAC5B,IAAI,aAAa,KAAK,UAAU,IAAI,KAAK,UAAU,CAAC,GAAG;QACvD,OAAO;YACL,eAAe;YACf,kBAAkB,IAAI,CAAC,OAAO;YAC9B,gBAAgB,IAAI,CAAC,cAAc;YACnC,YAAY,WAAW,IAAI,CAAC,OAAO,GAAG;YACtC,aAAa,IAAI,CAAC,WAAW;YAC7B,UAAU,WAAW,IAAI,CAAC,EAAE,GAAG;YAC/B,YAAY,WAAW,IAAI,CAAC,IAAI,GAAG;YACnC,MAAM;YACN,WAAW;YACX,MAAM;YACN,UAAU;YACV,OAAO;YACP,OAAO;YACP,aAAa;YACb,gBAAgB,aAAa,WAAW,cAAc,GAAG;YACzD,QAAQ,aAAa,WAAW,MAAM,GAAG;YACzC,sDAAsD;YACtD,OAAO;gBAAC;gBAAc;gBAAQ;aAAQ;QACxC;IACF;IACA;;;;;;;;;GASC,GACD,gBAAgB,SAAS,CAAC,iBAAiB,GAAG,SAAU,SAAS,EAAE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,SAAS,EAAE,YAAY;QACzH,SAAS,UAAU;QACnB,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB,IAAI,SAAS,IAAI,CAAC,aAAa,CAAC,WAAW;QAC3C,IAAI,cAAc;YAChB,OAAO,KAAK,GAAG,aAAa,iBAAiB;QAC/C;QACA,IAAI,iBAAiB,QAAQ,CAAA,GAAA,iJAAA,CAAA,UAAc,AAAD,EAAE,OAAO,KAAK,GAAG;YACzD,OAAO,KAAK,GAAG,OAAO,KAAK,CAAC,cAAc;QAC5C;QACA,IAAI,CAAC,WAAW;YACd,IAAI,YAAY,KAAK,YAAY,CAAC;YAClC,aAAa;YACb,YAAY,UAAU,GAAG,CAAC,WAAW,WAAW;gBAAC;gBAAS;aAAY,GAAG;gBAAC;gBAAQ;gBAAS;aAAY;QACzG;QACA,IAAI,CAAA,GAAA,iJAAA,CAAA,aAAiB,AAAD,EAAE,YAAY;YAChC,OAAO,MAAM,GAAG;YAChB,OAAO,cAAc,GAAG;YACxB,OAAO,UAAU;QACnB,OAAO,IAAI,CAAA,GAAA,iJAAA,CAAA,WAAe,AAAD,EAAE,YAAY;YACrC,IAAI,MAAM,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,WAAW;YAC/B,uCAAuC;YACvC,+CAA+C;YAC/C,OAAO,IAAI,OAAO,CAAC,qBAAqB,SAAU,MAAM,EAAE,MAAM;gBAC9D,IAAI,MAAM,OAAO,MAAM;gBACvB,IAAI,WAAW;gBACf,IAAI,SAAS,MAAM,CAAC,OAAO,OAAO,SAAS,MAAM,CAAC,MAAM,OAAO,KAAK;oBAClE,WAAW,CAAC,SAAS,KAAK,CAAC,GAAG,MAAM,IAAI,0BAA0B;oBAClE,wCAA2C;wBACzC,IAAI,MAAM,WAAW;4BACnB,CAAA,GAAA,gJAAA,CAAA,QAAK,AAAD,EAAE,gCAAgC,SAAS;wBACjD;oBACF;gBACF;gBACA,IAAI,MAAM,CAAA,GAAA,mKAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,WAAW;gBAC5C,IAAI,gBAAgB,CAAA,GAAA,iJAAA,CAAA,UAAc,AAAD,EAAE,aAAa,iBAAiB,GAAG;oBAClE,IAAI,WAAW,KAAK,iBAAiB,CAAC;oBACtC,IAAI,YAAY,GAAG;wBACjB,MAAM,aAAa,iBAAiB,CAAC,SAAS;oBAChD;gBACF;gBACA,OAAO,OAAO,OAAO,MAAM,KAAK;YAClC;QACF;IACF;IACA;;GAEC,GACD,gBAAgB,SAAS,CAAC,WAAW,GAAG,SAAU,GAAG,EAAE,QAAQ;QAC7D,OAAO,CAAA,GAAA,mKAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW;IAClD;IACA;;;;;GAKC,GACD,gBAAgB,SAAS,CAAC,aAAa,GAAG,SAAU,SAAS,EAAE,cAAc,EAAE,QAAQ;QACrF,iBAAiB;QACjB;IACF;IACA,OAAO;AACT;;;AAgBO,SAAS,6BAA6B,MAAM;IACjD,IAAI;IACJ,wCAAwC;IACxC,IAAI;IACJ,IAAI,CAAA,GAAA,iJAAA,CAAA,WAAe,AAAD,EAAE,SAAS;QAC3B,IAAI,OAAO,IAAI,EAAE;YACf,iBAAiB;QACnB,OAAO;YACL,wCAA2C;gBACzC,QAAQ,IAAI,CAAC,0DAA0D,CAAA,GAAA,gJAAA,CAAA,gBAAa,AAAD,EAAE;YACvF;QACF;IACA,SAAS;IACT,qEAAqE;IACrE,qEAAqE;IACrE,6BAA6B;IAC7B,4DAA4D;IAC5D,QAAQ;IACR,IAAI;IACN,OAAO;QACL,aAAa;IACf;IACA,OAAO;QACL,MAAM;QACN,uCAAuC;QACvC,MAAM;IACR;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2597, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/model/Series.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport * as modelUtil from '../util/model.js';\nimport ComponentModel from './Component.js';\nimport { PaletteMixin } from './mixin/palette.js';\nimport { DataFormatMixin } from '../model/mixin/dataFormat.js';\nimport { getLayoutParams, mergeLayoutParam, fetchLayoutMode } from '../util/layout.js';\nimport { createTask } from '../core/task.js';\nimport { mountExtend } from '../util/clazz.js';\nimport { SourceManager } from '../data/helper/sourceManager.js';\nimport { defaultSeriesFormatTooltip } from '../component/tooltip/seriesFormatTooltip.js';\nvar inner = modelUtil.makeInner();\nfunction getSelectionKey(data, dataIndex) {\n  return data.getName(dataIndex) || data.getId(dataIndex);\n}\nexport var SERIES_UNIVERSAL_TRANSITION_PROP = '__universalTransitionEnabled';\nvar SeriesModel = /** @class */function (_super) {\n  __extends(SeriesModel, _super);\n  function SeriesModel() {\n    // [Caution]: Because this class or desecendants can be used as `XXX.extend(subProto)`,\n    // the class members must not be initialized in constructor or declaration place.\n    // Otherwise there is bad case:\n    //   class A {xxx = 1;}\n    //   enableClassExtend(A);\n    //   class B extends A {}\n    //   var C = B.extend({xxx: 5});\n    //   var c = new C();\n    //   console.log(c.xxx); // expect 5 but always 1.\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    // ---------------------------------------\n    // Props about data selection\n    // ---------------------------------------\n    _this._selectedDataIndicesMap = {};\n    return _this;\n  }\n  SeriesModel.prototype.init = function (option, parentModel, ecModel) {\n    this.seriesIndex = this.componentIndex;\n    this.dataTask = createTask({\n      count: dataTaskCount,\n      reset: dataTaskReset\n    });\n    this.dataTask.context = {\n      model: this\n    };\n    this.mergeDefaultAndTheme(option, ecModel);\n    var sourceManager = inner(this).sourceManager = new SourceManager(this);\n    sourceManager.prepareSource();\n    var data = this.getInitialData(option, ecModel);\n    wrapData(data, this);\n    this.dataTask.context.data = data;\n    if (process.env.NODE_ENV !== 'production') {\n      zrUtil.assert(data, 'getInitialData returned invalid data.');\n    }\n    inner(this).dataBeforeProcessed = data;\n    // If we reverse the order (make data firstly, and then make\n    // dataBeforeProcessed by cloneShallow), cloneShallow will\n    // cause data.graph.data !== data when using\n    // module:echarts/data/Graph or module:echarts/data/Tree.\n    // See module:echarts/data/helper/linkSeriesData\n    // Theoretically, it is unreasonable to call `seriesModel.getData()` in the model\n    // init or merge stage, because the data can be restored. So we do not `restoreData`\n    // and `setData` here, which forbids calling `seriesModel.getData()` in this stage.\n    // Call `seriesModel.getRawData()` instead.\n    // this.restoreData();\n    autoSeriesName(this);\n    this._initSelectedMapFromData(data);\n  };\n  /**\r\n   * Util for merge default and theme to option\r\n   */\n  SeriesModel.prototype.mergeDefaultAndTheme = function (option, ecModel) {\n    var layoutMode = fetchLayoutMode(this);\n    var inputPositionParams = layoutMode ? getLayoutParams(option) : {};\n    // Backward compat: using subType on theme.\n    // But if name duplicate between series subType\n    // (for example: parallel) add component mainType,\n    // add suffix 'Series'.\n    var themeSubType = this.subType;\n    if (ComponentModel.hasClass(themeSubType)) {\n      themeSubType += 'Series';\n    }\n    zrUtil.merge(option, ecModel.getTheme().get(this.subType));\n    zrUtil.merge(option, this.getDefaultOption());\n    // Default label emphasis `show`\n    modelUtil.defaultEmphasis(option, 'label', ['show']);\n    this.fillDataTextStyle(option.data);\n    if (layoutMode) {\n      mergeLayoutParam(option, inputPositionParams, layoutMode);\n    }\n  };\n  SeriesModel.prototype.mergeOption = function (newSeriesOption, ecModel) {\n    // this.settingTask.dirty();\n    newSeriesOption = zrUtil.merge(this.option, newSeriesOption, true);\n    this.fillDataTextStyle(newSeriesOption.data);\n    var layoutMode = fetchLayoutMode(this);\n    if (layoutMode) {\n      mergeLayoutParam(this.option, newSeriesOption, layoutMode);\n    }\n    var sourceManager = inner(this).sourceManager;\n    sourceManager.dirty();\n    sourceManager.prepareSource();\n    var data = this.getInitialData(newSeriesOption, ecModel);\n    wrapData(data, this);\n    this.dataTask.dirty();\n    this.dataTask.context.data = data;\n    inner(this).dataBeforeProcessed = data;\n    autoSeriesName(this);\n    this._initSelectedMapFromData(data);\n  };\n  SeriesModel.prototype.fillDataTextStyle = function (data) {\n    // Default data label emphasis `show`\n    // FIXME Tree structure data ?\n    // FIXME Performance ?\n    if (data && !zrUtil.isTypedArray(data)) {\n      var props = ['show'];\n      for (var i = 0; i < data.length; i++) {\n        if (data[i] && data[i].label) {\n          modelUtil.defaultEmphasis(data[i], 'label', props);\n        }\n      }\n    }\n  };\n  /**\r\n   * Init a data structure from data related option in series\r\n   * Must be overridden.\r\n   */\n  SeriesModel.prototype.getInitialData = function (option, ecModel) {\n    return;\n  };\n  /**\r\n   * Append data to list\r\n   */\n  SeriesModel.prototype.appendData = function (params) {\n    // FIXME ???\n    // (1) If data from dataset, forbidden append.\n    // (2) support append data of dataset.\n    var data = this.getRawData();\n    data.appendData(params.data);\n  };\n  /**\r\n   * Consider some method like `filter`, `map` need make new data,\r\n   * We should make sure that `seriesModel.getData()` get correct\r\n   * data in the stream procedure. So we fetch data from upstream\r\n   * each time `task.perform` called.\r\n   */\n  SeriesModel.prototype.getData = function (dataType) {\n    var task = getCurrentTask(this);\n    if (task) {\n      var data = task.context.data;\n      return dataType == null || !data.getLinkedData ? data : data.getLinkedData(dataType);\n    } else {\n      // When series is not alive (that may happen when click toolbox\n      // restore or setOption with not merge mode), series data may\n      // be still need to judge animation or something when graphic\n      // elements want to know whether fade out.\n      return inner(this).data;\n    }\n  };\n  SeriesModel.prototype.getAllData = function () {\n    var mainData = this.getData();\n    return mainData && mainData.getLinkedDataAll ? mainData.getLinkedDataAll() : [{\n      data: mainData\n    }];\n  };\n  SeriesModel.prototype.setData = function (data) {\n    var task = getCurrentTask(this);\n    if (task) {\n      var context = task.context;\n      // Consider case: filter, data sample.\n      // FIXME:TS never used, so comment it\n      // if (context.data !== data && task.modifyOutputEnd) {\n      //     task.setOutputEnd(data.count());\n      // }\n      context.outputData = data;\n      // Caution: setData should update context.data,\n      // Because getData may be called multiply in a\n      // single stage and expect to get the data just\n      // set. (For example, AxisProxy, x y both call\n      // getData and setDate sequentially).\n      // So the context.data should be fetched from\n      // upstream each time when a stage starts to be\n      // performed.\n      if (task !== this.dataTask) {\n        context.data = data;\n      }\n    }\n    inner(this).data = data;\n  };\n  SeriesModel.prototype.getEncode = function () {\n    var encode = this.get('encode', true);\n    if (encode) {\n      return zrUtil.createHashMap(encode);\n    }\n  };\n  SeriesModel.prototype.getSourceManager = function () {\n    return inner(this).sourceManager;\n  };\n  SeriesModel.prototype.getSource = function () {\n    return this.getSourceManager().getSource();\n  };\n  /**\r\n   * Get data before processed\r\n   */\n  SeriesModel.prototype.getRawData = function () {\n    return inner(this).dataBeforeProcessed;\n  };\n  SeriesModel.prototype.getColorBy = function () {\n    var colorBy = this.get('colorBy');\n    return colorBy || 'series';\n  };\n  SeriesModel.prototype.isColorBySeries = function () {\n    return this.getColorBy() === 'series';\n  };\n  /**\r\n   * Get base axis if has coordinate system and has axis.\r\n   * By default use coordSys.getBaseAxis();\r\n   * Can be overridden for some chart.\r\n   * @return {type} description\r\n   */\n  SeriesModel.prototype.getBaseAxis = function () {\n    var coordSys = this.coordinateSystem;\n    // @ts-ignore\n    return coordSys && coordSys.getBaseAxis && coordSys.getBaseAxis();\n  };\n  /**\r\n   * Default tooltip formatter\r\n   *\r\n   * @param dataIndex\r\n   * @param multipleSeries\r\n   * @param dataType\r\n   * @param renderMode valid values: 'html'(by default) and 'richText'.\r\n   *        'html' is used for rendering tooltip in extra DOM form, and the result\r\n   *        string is used as DOM HTML content.\r\n   *        'richText' is used for rendering tooltip in rich text form, for those where\r\n   *        DOM operation is not supported.\r\n   * @return formatted tooltip with `html` and `markers`\r\n   *        Notice: The override method can also return string\r\n   */\n  SeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    return defaultSeriesFormatTooltip({\n      series: this,\n      dataIndex: dataIndex,\n      multipleSeries: multipleSeries\n    });\n  };\n  SeriesModel.prototype.isAnimationEnabled = function () {\n    var ecModel = this.ecModel;\n    // Disable animation if using echarts in node but not give ssr flag.\n    // In ssr mode, renderToString will generate svg with css animation.\n    if (env.node && !(ecModel && ecModel.ssr)) {\n      return false;\n    }\n    var animationEnabled = this.getShallow('animation');\n    if (animationEnabled) {\n      if (this.getData().count() > this.getShallow('animationThreshold')) {\n        animationEnabled = false;\n      }\n    }\n    return !!animationEnabled;\n  };\n  SeriesModel.prototype.restoreData = function () {\n    this.dataTask.dirty();\n  };\n  SeriesModel.prototype.getColorFromPalette = function (name, scope, requestColorNum) {\n    var ecModel = this.ecModel;\n    // PENDING\n    var color = PaletteMixin.prototype.getColorFromPalette.call(this, name, scope, requestColorNum);\n    if (!color) {\n      color = ecModel.getColorFromPalette(name, scope, requestColorNum);\n    }\n    return color;\n  };\n  /**\r\n   * Use `data.mapDimensionsAll(coordDim)` instead.\r\n   * @deprecated\r\n   */\n  SeriesModel.prototype.coordDimToDataDim = function (coordDim) {\n    return this.getRawData().mapDimensionsAll(coordDim);\n  };\n  /**\r\n   * Get progressive rendering count each step\r\n   */\n  SeriesModel.prototype.getProgressive = function () {\n    return this.get('progressive');\n  };\n  /**\r\n   * Get progressive rendering count each step\r\n   */\n  SeriesModel.prototype.getProgressiveThreshold = function () {\n    return this.get('progressiveThreshold');\n  };\n  // PENGING If selectedMode is null ?\n  SeriesModel.prototype.select = function (innerDataIndices, dataType) {\n    this._innerSelect(this.getData(dataType), innerDataIndices);\n  };\n  SeriesModel.prototype.unselect = function (innerDataIndices, dataType) {\n    var selectedMap = this.option.selectedMap;\n    if (!selectedMap) {\n      return;\n    }\n    var selectedMode = this.option.selectedMode;\n    var data = this.getData(dataType);\n    if (selectedMode === 'series' || selectedMap === 'all') {\n      this.option.selectedMap = {};\n      this._selectedDataIndicesMap = {};\n      return;\n    }\n    for (var i = 0; i < innerDataIndices.length; i++) {\n      var dataIndex = innerDataIndices[i];\n      var nameOrId = getSelectionKey(data, dataIndex);\n      selectedMap[nameOrId] = false;\n      this._selectedDataIndicesMap[nameOrId] = -1;\n    }\n  };\n  SeriesModel.prototype.toggleSelect = function (innerDataIndices, dataType) {\n    var tmpArr = [];\n    for (var i = 0; i < innerDataIndices.length; i++) {\n      tmpArr[0] = innerDataIndices[i];\n      this.isSelected(innerDataIndices[i], dataType) ? this.unselect(tmpArr, dataType) : this.select(tmpArr, dataType);\n    }\n  };\n  SeriesModel.prototype.getSelectedDataIndices = function () {\n    if (this.option.selectedMap === 'all') {\n      return [].slice.call(this.getData().getIndices());\n    }\n    var selectedDataIndicesMap = this._selectedDataIndicesMap;\n    var nameOrIds = zrUtil.keys(selectedDataIndicesMap);\n    var dataIndices = [];\n    for (var i = 0; i < nameOrIds.length; i++) {\n      var dataIndex = selectedDataIndicesMap[nameOrIds[i]];\n      if (dataIndex >= 0) {\n        dataIndices.push(dataIndex);\n      }\n    }\n    return dataIndices;\n  };\n  SeriesModel.prototype.isSelected = function (dataIndex, dataType) {\n    var selectedMap = this.option.selectedMap;\n    if (!selectedMap) {\n      return false;\n    }\n    var data = this.getData(dataType);\n    return (selectedMap === 'all' || selectedMap[getSelectionKey(data, dataIndex)]) && !data.getItemModel(dataIndex).get(['select', 'disabled']);\n  };\n  SeriesModel.prototype.isUniversalTransitionEnabled = function () {\n    if (this[SERIES_UNIVERSAL_TRANSITION_PROP]) {\n      return true;\n    }\n    var universalTransitionOpt = this.option.universalTransition;\n    // Quick reject\n    if (!universalTransitionOpt) {\n      return false;\n    }\n    if (universalTransitionOpt === true) {\n      return true;\n    }\n    // Can be simply 'universalTransition: true'\n    return universalTransitionOpt && universalTransitionOpt.enabled;\n  };\n  SeriesModel.prototype._innerSelect = function (data, innerDataIndices) {\n    var _a, _b;\n    var option = this.option;\n    var selectedMode = option.selectedMode;\n    var len = innerDataIndices.length;\n    if (!selectedMode || !len) {\n      return;\n    }\n    if (selectedMode === 'series') {\n      option.selectedMap = 'all';\n    } else if (selectedMode === 'multiple') {\n      if (!zrUtil.isObject(option.selectedMap)) {\n        option.selectedMap = {};\n      }\n      var selectedMap = option.selectedMap;\n      for (var i = 0; i < len; i++) {\n        var dataIndex = innerDataIndices[i];\n        // TODO different types of data share same object.\n        var nameOrId = getSelectionKey(data, dataIndex);\n        selectedMap[nameOrId] = true;\n        this._selectedDataIndicesMap[nameOrId] = data.getRawIndex(dataIndex);\n      }\n    } else if (selectedMode === 'single' || selectedMode === true) {\n      var lastDataIndex = innerDataIndices[len - 1];\n      var nameOrId = getSelectionKey(data, lastDataIndex);\n      option.selectedMap = (_a = {}, _a[nameOrId] = true, _a);\n      this._selectedDataIndicesMap = (_b = {}, _b[nameOrId] = data.getRawIndex(lastDataIndex), _b);\n    }\n  };\n  SeriesModel.prototype._initSelectedMapFromData = function (data) {\n    // Ignore select info in data if selectedMap exists.\n    // NOTE It's only for legacy usage. edge data is not supported.\n    if (this.option.selectedMap) {\n      return;\n    }\n    var dataIndices = [];\n    if (data.hasItemOption) {\n      data.each(function (idx) {\n        var rawItem = data.getRawDataItem(idx);\n        if (rawItem && rawItem.selected) {\n          dataIndices.push(idx);\n        }\n      });\n    }\n    if (dataIndices.length > 0) {\n      this._innerSelect(data, dataIndices);\n    }\n  };\n  // /**\n  //  * @see {module:echarts/stream/Scheduler}\n  //  */\n  // abstract pipeTask: null\n  SeriesModel.registerClass = function (clz) {\n    return ComponentModel.registerClass(clz);\n  };\n  SeriesModel.protoInitialize = function () {\n    var proto = SeriesModel.prototype;\n    proto.type = 'series.__base__';\n    proto.seriesIndex = 0;\n    proto.ignoreStyleOnData = false;\n    proto.hasSymbolVisual = false;\n    proto.defaultSymbol = 'circle';\n    // Make sure the values can be accessed!\n    proto.visualStyleAccessPath = 'itemStyle';\n    proto.visualDrawType = 'fill';\n  }();\n  return SeriesModel;\n}(ComponentModel);\nzrUtil.mixin(SeriesModel, DataFormatMixin);\nzrUtil.mixin(SeriesModel, PaletteMixin);\nmountExtend(SeriesModel, ComponentModel);\n/**\r\n * MUST be called after `prepareSource` called\r\n * Here we need to make auto series, especially for auto legend. But we\r\n * do not modify series.name in option to avoid side effects.\r\n */\nfunction autoSeriesName(seriesModel) {\n  // User specified name has higher priority, otherwise it may cause\n  // series can not be queried unexpectedly.\n  var name = seriesModel.name;\n  if (!modelUtil.isNameSpecified(seriesModel)) {\n    seriesModel.name = getSeriesAutoName(seriesModel) || name;\n  }\n}\nfunction getSeriesAutoName(seriesModel) {\n  var data = seriesModel.getRawData();\n  var dataDims = data.mapDimensionsAll('seriesName');\n  var nameArr = [];\n  zrUtil.each(dataDims, function (dataDim) {\n    var dimInfo = data.getDimensionInfo(dataDim);\n    dimInfo.displayName && nameArr.push(dimInfo.displayName);\n  });\n  return nameArr.join(' ');\n}\nfunction dataTaskCount(context) {\n  return context.model.getRawData().count();\n}\nfunction dataTaskReset(context) {\n  var seriesModel = context.model;\n  seriesModel.setData(seriesModel.getRawData().cloneShallow());\n  return dataTaskProgress;\n}\nfunction dataTaskProgress(param, context) {\n  // Avoid repeat cloneShallow when data just created in reset.\n  if (context.outputData && param.end > context.outputData.count()) {\n    context.model.getRawData().cloneShallow(context.outputData);\n  }\n}\n// TODO refactor\nfunction wrapData(data, seriesModel) {\n  zrUtil.each(zrUtil.concatArray(data.CHANGABLE_METHODS, data.DOWNSAMPLE_METHODS), function (methodName) {\n    data.wrapMethod(methodName, zrUtil.curry(onDataChange, seriesModel));\n  });\n}\nfunction onDataChange(seriesModel, newList) {\n  var task = getCurrentTask(seriesModel);\n  if (task) {\n    // Consider case: filter, selectRange\n    task.setOutputEnd((newList || this).count());\n  }\n  return newList;\n}\nfunction getCurrentTask(seriesModel) {\n  var scheduler = (seriesModel.ecModel || {}).scheduler;\n  var pipeline = scheduler && scheduler.getPipeline(seriesModel.uid);\n  if (pipeline) {\n    // When pipline finished, the currrentTask keep the last\n    // task (renderTask).\n    var task = pipeline.currentTask;\n    if (task) {\n      var agentStubMap = task.agentStubMap;\n      if (agentStubMap) {\n        task = agentStubMap.get(seriesModel.uid);\n      }\n    }\n    return task;\n  }\n}\nexport default SeriesModel;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;AAoDQ;AAnDR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;AACA,IAAI,QAAQ,CAAA,GAAA,kJAAA,CAAA,YAAmB,AAAD;AAC9B,SAAS,gBAAgB,IAAI,EAAE,SAAS;IACtC,OAAO,KAAK,OAAO,CAAC,cAAc,KAAK,KAAK,CAAC;AAC/C;AACO,IAAI,mCAAmC;AAC9C,IAAI,cAAc,WAAW,GAAE,SAAU,MAAM;IAC7C,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,aAAa;IACvB,SAAS;QACP,uFAAuF;QACvF,iFAAiF;QACjF,+BAA+B;QAC/B,uBAAuB;QACvB,0BAA0B;QAC1B,yBAAyB;QACzB,gCAAgC;QAChC,qBAAqB;QACrB,kDAAkD;QAClD,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,0CAA0C;QAC1C,6BAA6B;QAC7B,0CAA0C;QAC1C,MAAM,uBAAuB,GAAG,CAAC;QACjC,OAAO;IACT;IACA,YAAY,SAAS,CAAC,IAAI,GAAG,SAAU,MAAM,EAAE,WAAW,EAAE,OAAO;QACjE,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,cAAc;QACtC,IAAI,CAAC,QAAQ,GAAG,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE;YACzB,OAAO;YACP,OAAO;QACT;QACA,IAAI,CAAC,QAAQ,CAAC,OAAO,GAAG;YACtB,OAAO,IAAI;QACb;QACA,IAAI,CAAC,oBAAoB,CAAC,QAAQ;QAClC,IAAI,gBAAgB,MAAM,IAAI,EAAE,aAAa,GAAG,IAAI,oKAAA,CAAA,gBAAa,CAAC,IAAI;QACtE,cAAc,aAAa;QAC3B,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,QAAQ;QACvC,SAAS,MAAM,IAAI;QACnB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,GAAG;QAC7B,wCAA2C;YACzC,CAAA,GAAA,iJAAA,CAAA,SAAa,AAAD,EAAE,MAAM;QACtB;QACA,MAAM,IAAI,EAAE,mBAAmB,GAAG;QAClC,4DAA4D;QAC5D,0DAA0D;QAC1D,4CAA4C;QAC5C,yDAAyD;QACzD,gDAAgD;QAChD,iFAAiF;QACjF,oFAAoF;QACpF,mFAAmF;QACnF,2CAA2C;QAC3C,sBAAsB;QACtB,eAAe,IAAI;QACnB,IAAI,CAAC,wBAAwB,CAAC;IAChC;IACA;;GAEC,GACD,YAAY,SAAS,CAAC,oBAAoB,GAAG,SAAU,MAAM,EAAE,OAAO;QACpE,IAAI,aAAa,CAAA,GAAA,mJAAA,CAAA,kBAAe,AAAD,EAAE,IAAI;QACrC,IAAI,sBAAsB,aAAa,CAAA,GAAA,mJAAA,CAAA,kBAAe,AAAD,EAAE,UAAU,CAAC;QAClE,2CAA2C;QAC3C,+CAA+C;QAC/C,kDAAkD;QAClD,uBAAuB;QACvB,IAAI,eAAe,IAAI,CAAC,OAAO;QAC/B,IAAI,uJAAA,CAAA,UAAc,CAAC,QAAQ,CAAC,eAAe;YACzC,gBAAgB;QAClB;QACA,CAAA,GAAA,iJAAA,CAAA,QAAY,AAAD,EAAE,QAAQ,QAAQ,QAAQ,GAAG,GAAG,CAAC,IAAI,CAAC,OAAO;QACxD,CAAA,GAAA,iJAAA,CAAA,QAAY,AAAD,EAAE,QAAQ,IAAI,CAAC,gBAAgB;QAC1C,gCAAgC;QAChC,CAAA,GAAA,kJAAA,CAAA,kBAAyB,AAAD,EAAE,QAAQ,SAAS;YAAC;SAAO;QACnD,IAAI,CAAC,iBAAiB,CAAC,OAAO,IAAI;QAClC,IAAI,YAAY;YACd,CAAA,GAAA,mJAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,qBAAqB;QAChD;IACF;IACA,YAAY,SAAS,CAAC,WAAW,GAAG,SAAU,eAAe,EAAE,OAAO;QACpE,4BAA4B;QAC5B,kBAAkB,CAAA,GAAA,iJAAA,CAAA,QAAY,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE,iBAAiB;QAC7D,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,IAAI;QAC3C,IAAI,aAAa,CAAA,GAAA,mJAAA,CAAA,kBAAe,AAAD,EAAE,IAAI;QACrC,IAAI,YAAY;YACd,CAAA,GAAA,mJAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE,iBAAiB;QACjD;QACA,IAAI,gBAAgB,MAAM,IAAI,EAAE,aAAa;QAC7C,cAAc,KAAK;QACnB,cAAc,aAAa;QAC3B,IAAI,OAAO,IAAI,CAAC,cAAc,CAAC,iBAAiB;QAChD,SAAS,MAAM,IAAI;QACnB,IAAI,CAAC,QAAQ,CAAC,KAAK;QACnB,IAAI,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,GAAG;QAC7B,MAAM,IAAI,EAAE,mBAAmB,GAAG;QAClC,eAAe,IAAI;QACnB,IAAI,CAAC,wBAAwB,CAAC;IAChC;IACA,YAAY,SAAS,CAAC,iBAAiB,GAAG,SAAU,IAAI;QACtD,qCAAqC;QACrC,8BAA8B;QAC9B,sBAAsB;QACtB,IAAI,QAAQ,CAAC,CAAA,GAAA,iJAAA,CAAA,eAAmB,AAAD,EAAE,OAAO;YACtC,IAAI,QAAQ;gBAAC;aAAO;YACpB,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAK;gBACpC,IAAI,IAAI,CAAC,EAAE,IAAI,IAAI,CAAC,EAAE,CAAC,KAAK,EAAE;oBAC5B,CAAA,GAAA,kJAAA,CAAA,kBAAyB,AAAD,EAAE,IAAI,CAAC,EAAE,EAAE,SAAS;gBAC9C;YACF;QACF;IACF;IACA;;;GAGC,GACD,YAAY,SAAS,CAAC,cAAc,GAAG,SAAU,MAAM,EAAE,OAAO;QAC9D;IACF;IACA;;GAEC,GACD,YAAY,SAAS,CAAC,UAAU,GAAG,SAAU,MAAM;QACjD,YAAY;QACZ,8CAA8C;QAC9C,sCAAsC;QACtC,IAAI,OAAO,IAAI,CAAC,UAAU;QAC1B,KAAK,UAAU,CAAC,OAAO,IAAI;IAC7B;IACA;;;;;GAKC,GACD,YAAY,SAAS,CAAC,OAAO,GAAG,SAAU,QAAQ;QAChD,IAAI,OAAO,eAAe,IAAI;QAC9B,IAAI,MAAM;YACR,IAAI,OAAO,KAAK,OAAO,CAAC,IAAI;YAC5B,OAAO,YAAY,QAAQ,CAAC,KAAK,aAAa,GAAG,OAAO,KAAK,aAAa,CAAC;QAC7E,OAAO;YACL,+DAA+D;YAC/D,6DAA6D;YAC7D,6DAA6D;YAC7D,0CAA0C;YAC1C,OAAO,MAAM,IAAI,EAAE,IAAI;QACzB;IACF;IACA,YAAY,SAAS,CAAC,UAAU,GAAG;QACjC,IAAI,WAAW,IAAI,CAAC,OAAO;QAC3B,OAAO,YAAY,SAAS,gBAAgB,GAAG,SAAS,gBAAgB,KAAK;YAAC;gBAC5E,MAAM;YACR;SAAE;IACJ;IACA,YAAY,SAAS,CAAC,OAAO,GAAG,SAAU,IAAI;QAC5C,IAAI,OAAO,eAAe,IAAI;QAC9B,IAAI,MAAM;YACR,IAAI,UAAU,KAAK,OAAO;YAC1B,sCAAsC;YACtC,qCAAqC;YACrC,uDAAuD;YACvD,uCAAuC;YACvC,IAAI;YACJ,QAAQ,UAAU,GAAG;YACrB,+CAA+C;YAC/C,8CAA8C;YAC9C,+CAA+C;YAC/C,8CAA8C;YAC9C,qCAAqC;YACrC,6CAA6C;YAC7C,+CAA+C;YAC/C,aAAa;YACb,IAAI,SAAS,IAAI,CAAC,QAAQ,EAAE;gBAC1B,QAAQ,IAAI,GAAG;YACjB;QACF;QACA,MAAM,IAAI,EAAE,IAAI,GAAG;IACrB;IACA,YAAY,SAAS,CAAC,SAAS,GAAG;QAChC,IAAI,SAAS,IAAI,CAAC,GAAG,CAAC,UAAU;QAChC,IAAI,QAAQ;YACV,OAAO,CAAA,GAAA,iJAAA,CAAA,gBAAoB,AAAD,EAAE;QAC9B;IACF;IACA,YAAY,SAAS,CAAC,gBAAgB,GAAG;QACvC,OAAO,MAAM,IAAI,EAAE,aAAa;IAClC;IACA,YAAY,SAAS,CAAC,SAAS,GAAG;QAChC,OAAO,IAAI,CAAC,gBAAgB,GAAG,SAAS;IAC1C;IACA;;GAEC,GACD,YAAY,SAAS,CAAC,UAAU,GAAG;QACjC,OAAO,MAAM,IAAI,EAAE,mBAAmB;IACxC;IACA,YAAY,SAAS,CAAC,UAAU,GAAG;QACjC,IAAI,UAAU,IAAI,CAAC,GAAG,CAAC;QACvB,OAAO,WAAW;IACpB;IACA,YAAY,SAAS,CAAC,eAAe,GAAG;QACtC,OAAO,IAAI,CAAC,UAAU,OAAO;IAC/B;IACA;;;;;GAKC,GACD,YAAY,SAAS,CAAC,WAAW,GAAG;QAClC,IAAI,WAAW,IAAI,CAAC,gBAAgB;QACpC,aAAa;QACb,OAAO,YAAY,SAAS,WAAW,IAAI,SAAS,WAAW;IACjE;IACA;;;;;;;;;;;;;GAaC,GACD,YAAY,SAAS,CAAC,aAAa,GAAG,SAAU,SAAS,EAAE,cAAc,EAAE,QAAQ;QACjF,OAAO,CAAA,GAAA,gLAAA,CAAA,6BAA0B,AAAD,EAAE;YAChC,QAAQ,IAAI;YACZ,WAAW;YACX,gBAAgB;QAClB;IACF;IACA,YAAY,SAAS,CAAC,kBAAkB,GAAG;QACzC,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1B,oEAAoE;QACpE,oEAAoE;QACpE,IAAI,gJAAA,CAAA,UAAG,CAAC,IAAI,IAAI,CAAC,CAAC,WAAW,QAAQ,GAAG,GAAG;YACzC,OAAO;QACT;QACA,IAAI,mBAAmB,IAAI,CAAC,UAAU,CAAC;QACvC,IAAI,kBAAkB;YACpB,IAAI,IAAI,CAAC,OAAO,GAAG,KAAK,KAAK,IAAI,CAAC,UAAU,CAAC,uBAAuB;gBAClE,mBAAmB;YACrB;QACF;QACA,OAAO,CAAC,CAAC;IACX;IACA,YAAY,SAAS,CAAC,WAAW,GAAG;QAClC,IAAI,CAAC,QAAQ,CAAC,KAAK;IACrB;IACA,YAAY,SAAS,CAAC,mBAAmB,GAAG,SAAU,IAAI,EAAE,KAAK,EAAE,eAAe;QAChF,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1B,UAAU;QACV,IAAI,QAAQ,8JAAA,CAAA,eAAY,CAAC,SAAS,CAAC,mBAAmB,CAAC,IAAI,CAAC,IAAI,EAAE,MAAM,OAAO;QAC/E,IAAI,CAAC,OAAO;YACV,QAAQ,QAAQ,mBAAmB,CAAC,MAAM,OAAO;QACnD;QACA,OAAO;IACT;IACA;;;GAGC,GACD,YAAY,SAAS,CAAC,iBAAiB,GAAG,SAAU,QAAQ;QAC1D,OAAO,IAAI,CAAC,UAAU,GAAG,gBAAgB,CAAC;IAC5C;IACA;;GAEC,GACD,YAAY,SAAS,CAAC,cAAc,GAAG;QACrC,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB;IACA;;GAEC,GACD,YAAY,SAAS,CAAC,uBAAuB,GAAG;QAC9C,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB;IACA,oCAAoC;IACpC,YAAY,SAAS,CAAC,MAAM,GAAG,SAAU,gBAAgB,EAAE,QAAQ;QACjE,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,OAAO,CAAC,WAAW;IAC5C;IACA,YAAY,SAAS,CAAC,QAAQ,GAAG,SAAU,gBAAgB,EAAE,QAAQ;QACnE,IAAI,cAAc,IAAI,CAAC,MAAM,CAAC,WAAW;QACzC,IAAI,CAAC,aAAa;YAChB;QACF;QACA,IAAI,eAAe,IAAI,CAAC,MAAM,CAAC,YAAY;QAC3C,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB,IAAI,iBAAiB,YAAY,gBAAgB,OAAO;YACtD,IAAI,CAAC,MAAM,CAAC,WAAW,GAAG,CAAC;YAC3B,IAAI,CAAC,uBAAuB,GAAG,CAAC;YAChC;QACF;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;YAChD,IAAI,YAAY,gBAAgB,CAAC,EAAE;YACnC,IAAI,WAAW,gBAAgB,MAAM;YACrC,WAAW,CAAC,SAAS,GAAG;YACxB,IAAI,CAAC,uBAAuB,CAAC,SAAS,GAAG,CAAC;QAC5C;IACF;IACA,YAAY,SAAS,CAAC,YAAY,GAAG,SAAU,gBAAgB,EAAE,QAAQ;QACvE,IAAI,SAAS,EAAE;QACf,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;YAChD,MAAM,CAAC,EAAE,GAAG,gBAAgB,CAAC,EAAE;YAC/B,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,EAAE,EAAE,YAAY,IAAI,CAAC,QAAQ,CAAC,QAAQ,YAAY,IAAI,CAAC,MAAM,CAAC,QAAQ;QACzG;IACF;IACA,YAAY,SAAS,CAAC,sBAAsB,GAAG;QAC7C,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,KAAK,OAAO;YACrC,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,GAAG,UAAU;QAChD;QACA,IAAI,yBAAyB,IAAI,CAAC,uBAAuB;QACzD,IAAI,YAAY,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE;QAC5B,IAAI,cAAc,EAAE;QACpB,IAAK,IAAI,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAK;YACzC,IAAI,YAAY,sBAAsB,CAAC,SAAS,CAAC,EAAE,CAAC;YACpD,IAAI,aAAa,GAAG;gBAClB,YAAY,IAAI,CAAC;YACnB;QACF;QACA,OAAO;IACT;IACA,YAAY,SAAS,CAAC,UAAU,GAAG,SAAU,SAAS,EAAE,QAAQ;QAC9D,IAAI,cAAc,IAAI,CAAC,MAAM,CAAC,WAAW;QACzC,IAAI,CAAC,aAAa;YAChB,OAAO;QACT;QACA,IAAI,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB,OAAO,CAAC,gBAAgB,SAAS,WAAW,CAAC,gBAAgB,MAAM,WAAW,KAAK,CAAC,KAAK,YAAY,CAAC,WAAW,GAAG,CAAC;YAAC;YAAU;SAAW;IAC7I;IACA,YAAY,SAAS,CAAC,4BAA4B,GAAG;QACnD,IAAI,IAAI,CAAC,iCAAiC,EAAE;YAC1C,OAAO;QACT;QACA,IAAI,yBAAyB,IAAI,CAAC,MAAM,CAAC,mBAAmB;QAC5D,eAAe;QACf,IAAI,CAAC,wBAAwB;YAC3B,OAAO;QACT;QACA,IAAI,2BAA2B,MAAM;YACnC,OAAO;QACT;QACA,4CAA4C;QAC5C,OAAO,0BAA0B,uBAAuB,OAAO;IACjE;IACA,YAAY,SAAS,CAAC,YAAY,GAAG,SAAU,IAAI,EAAE,gBAAgB;QACnE,IAAI,IAAI;QACR,IAAI,SAAS,IAAI,CAAC,MAAM;QACxB,IAAI,eAAe,OAAO,YAAY;QACtC,IAAI,MAAM,iBAAiB,MAAM;QACjC,IAAI,CAAC,gBAAgB,CAAC,KAAK;YACzB;QACF;QACA,IAAI,iBAAiB,UAAU;YAC7B,OAAO,WAAW,GAAG;QACvB,OAAO,IAAI,iBAAiB,YAAY;YACtC,IAAI,CAAC,CAAA,GAAA,iJAAA,CAAA,WAAe,AAAD,EAAE,OAAO,WAAW,GAAG;gBACxC,OAAO,WAAW,GAAG,CAAC;YACxB;YACA,IAAI,cAAc,OAAO,WAAW;YACpC,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,IAAK;gBAC5B,IAAI,YAAY,gBAAgB,CAAC,EAAE;gBACnC,kDAAkD;gBAClD,IAAI,WAAW,gBAAgB,MAAM;gBACrC,WAAW,CAAC,SAAS,GAAG;gBACxB,IAAI,CAAC,uBAAuB,CAAC,SAAS,GAAG,KAAK,WAAW,CAAC;YAC5D;QACF,OAAO,IAAI,iBAAiB,YAAY,iBAAiB,MAAM;YAC7D,IAAI,gBAAgB,gBAAgB,CAAC,MAAM,EAAE;YAC7C,IAAI,WAAW,gBAAgB,MAAM;YACrC,OAAO,WAAW,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,SAAS,GAAG,MAAM,EAAE;YACtD,IAAI,CAAC,uBAAuB,GAAG,CAAC,KAAK,CAAC,GAAG,EAAE,CAAC,SAAS,GAAG,KAAK,WAAW,CAAC,gBAAgB,EAAE;QAC7F;IACF;IACA,YAAY,SAAS,CAAC,wBAAwB,GAAG,SAAU,IAAI;QAC7D,oDAAoD;QACpD,+DAA+D;QAC/D,IAAI,IAAI,CAAC,MAAM,CAAC,WAAW,EAAE;YAC3B;QACF;QACA,IAAI,cAAc,EAAE;QACpB,IAAI,KAAK,aAAa,EAAE;YACtB,KAAK,IAAI,CAAC,SAAU,GAAG;gBACrB,IAAI,UAAU,KAAK,cAAc,CAAC;gBAClC,IAAI,WAAW,QAAQ,QAAQ,EAAE;oBAC/B,YAAY,IAAI,CAAC;gBACnB;YACF;QACF;QACA,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,IAAI,CAAC,YAAY,CAAC,MAAM;QAC1B;IACF;IACA,MAAM;IACN,4CAA4C;IAC5C,MAAM;IACN,0BAA0B;IAC1B,YAAY,aAAa,GAAG,SAAU,GAAG;QACvC,OAAO,uJAAA,CAAA,UAAc,CAAC,aAAa,CAAC;IACtC;IACA,YAAY,eAAe,GAAG;QAC5B,IAAI,QAAQ,YAAY,SAAS;QACjC,MAAM,IAAI,GAAG;QACb,MAAM,WAAW,GAAG;QACpB,MAAM,iBAAiB,GAAG;QAC1B,MAAM,eAAe,GAAG;QACxB,MAAM,aAAa,GAAG;QACtB,wCAAwC;QACxC,MAAM,qBAAqB,GAAG;QAC9B,MAAM,cAAc,GAAG;IACzB;IACA,OAAO;AACT,EAAE,uJAAA,CAAA,UAAc;AAChB,CAAA,GAAA,iJAAA,CAAA,QAAY,AAAD,EAAE,aAAa,iKAAA,CAAA,kBAAe;AACzC,CAAA,GAAA,iJAAA,CAAA,QAAY,AAAD,EAAE,aAAa,8JAAA,CAAA,eAAY;AACtC,CAAA,GAAA,kJAAA,CAAA,cAAW,AAAD,EAAE,aAAa,uJAAA,CAAA,UAAc;AACvC;;;;CAIC,GACD,SAAS,eAAe,WAAW;IACjC,kEAAkE;IAClE,0CAA0C;IAC1C,IAAI,OAAO,YAAY,IAAI;IAC3B,IAAI,CAAC,CAAA,GAAA,kJAAA,CAAA,kBAAyB,AAAD,EAAE,cAAc;QAC3C,YAAY,IAAI,GAAG,kBAAkB,gBAAgB;IACvD;AACF;AACA,SAAS,kBAAkB,WAAW;IACpC,IAAI,OAAO,YAAY,UAAU;IACjC,IAAI,WAAW,KAAK,gBAAgB,CAAC;IACrC,IAAI,UAAU,EAAE;IAChB,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,UAAU,SAAU,OAAO;QACrC,IAAI,UAAU,KAAK,gBAAgB,CAAC;QACpC,QAAQ,WAAW,IAAI,QAAQ,IAAI,CAAC,QAAQ,WAAW;IACzD;IACA,OAAO,QAAQ,IAAI,CAAC;AACtB;AACA,SAAS,cAAc,OAAO;IAC5B,OAAO,QAAQ,KAAK,CAAC,UAAU,GAAG,KAAK;AACzC;AACA,SAAS,cAAc,OAAO;IAC5B,IAAI,cAAc,QAAQ,KAAK;IAC/B,YAAY,OAAO,CAAC,YAAY,UAAU,GAAG,YAAY;IACzD,OAAO;AACT;AACA,SAAS,iBAAiB,KAAK,EAAE,OAAO;IACtC,6DAA6D;IAC7D,IAAI,QAAQ,UAAU,IAAI,MAAM,GAAG,GAAG,QAAQ,UAAU,CAAC,KAAK,IAAI;QAChE,QAAQ,KAAK,CAAC,UAAU,GAAG,YAAY,CAAC,QAAQ,UAAU;IAC5D;AACF;AACA,gBAAgB;AAChB,SAAS,SAAS,IAAI,EAAE,WAAW;IACjC,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,CAAA,GAAA,iJAAA,CAAA,cAAkB,AAAD,EAAE,KAAK,iBAAiB,EAAE,KAAK,kBAAkB,GAAG,SAAU,UAAU;QACnG,KAAK,UAAU,CAAC,YAAY,CAAA,GAAA,iJAAA,CAAA,QAAY,AAAD,EAAE,cAAc;IACzD;AACF;AACA,SAAS,aAAa,WAAW,EAAE,OAAO;IACxC,IAAI,OAAO,eAAe;IAC1B,IAAI,MAAM;QACR,qCAAqC;QACrC,KAAK,YAAY,CAAC,CAAC,WAAW,IAAI,EAAE,KAAK;IAC3C;IACA,OAAO;AACT;AACA,SAAS,eAAe,WAAW;IACjC,IAAI,YAAY,CAAC,YAAY,OAAO,IAAI,CAAC,CAAC,EAAE,SAAS;IACrD,IAAI,WAAW,aAAa,UAAU,WAAW,CAAC,YAAY,GAAG;IACjE,IAAI,UAAU;QACZ,wDAAwD;QACxD,qBAAqB;QACrB,IAAI,OAAO,SAAS,WAAW;QAC/B,IAAI,MAAM;YACR,IAAI,eAAe,KAAK,YAAY;YACpC,IAAI,cAAc;gBAChB,OAAO,aAAa,GAAG,CAAC,YAAY,GAAG;YACzC;QACF;QACA,OAAO;IACT;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3153, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/model/referHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/**\r\n * Helper for model references.\r\n * There are many manners to refer axis/coordSys.\r\n */\n// TODO\n// merge relevant logic to this file?\n// check: \"modelHelper\" of tooltip and \"BrushTargetManager\".\nimport { createHashMap, retrieve, each } from 'zrender/lib/core/util.js';\nimport { SINGLE_REFERRING } from '../util/model.js';\n/**\r\n * @class\r\n * For example:\r\n * {\r\n *     coordSysName: 'cartesian2d',\r\n *     coordSysDims: ['x', 'y', ...],\r\n *     axisMap: HashMap({\r\n *         x: xAxisModel,\r\n *         y: yAxisModel\r\n *     }),\r\n *     categoryAxisMap: HashMap({\r\n *         x: xAxisModel,\r\n *         y: undefined\r\n *     }),\r\n *     // The index of the first category axis in `coordSysDims`.\r\n *     // `null/undefined` means no category axis exists.\r\n *     firstCategoryDimIndex: 1,\r\n *     // To replace user specified encode.\r\n * }\r\n */\nvar CoordSysInfo = /** @class */function () {\n  function CoordSysInfo(coordSysName) {\n    this.coordSysDims = [];\n    this.axisMap = createHashMap();\n    this.categoryAxisMap = createHashMap();\n    this.coordSysName = coordSysName;\n  }\n  return CoordSysInfo;\n}();\nexport function getCoordSysInfoBySeries(seriesModel) {\n  var coordSysName = seriesModel.get('coordinateSystem');\n  var result = new CoordSysInfo(coordSysName);\n  var fetch = fetchers[coordSysName];\n  if (fetch) {\n    fetch(seriesModel, result, result.axisMap, result.categoryAxisMap);\n    return result;\n  }\n}\nvar fetchers = {\n  cartesian2d: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var xAxisModel = seriesModel.getReferringComponents('xAxis', SINGLE_REFERRING).models[0];\n    var yAxisModel = seriesModel.getReferringComponents('yAxis', SINGLE_REFERRING).models[0];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!xAxisModel) {\n        throw new Error('xAxis \"' + retrieve(seriesModel.get('xAxisIndex'), seriesModel.get('xAxisId'), 0) + '\" not found');\n      }\n      if (!yAxisModel) {\n        throw new Error('yAxis \"' + retrieve(seriesModel.get('xAxisIndex'), seriesModel.get('yAxisId'), 0) + '\" not found');\n      }\n    }\n    result.coordSysDims = ['x', 'y'];\n    axisMap.set('x', xAxisModel);\n    axisMap.set('y', yAxisModel);\n    if (isCategory(xAxisModel)) {\n      categoryAxisMap.set('x', xAxisModel);\n      result.firstCategoryDimIndex = 0;\n    }\n    if (isCategory(yAxisModel)) {\n      categoryAxisMap.set('y', yAxisModel);\n      result.firstCategoryDimIndex == null && (result.firstCategoryDimIndex = 1);\n    }\n  },\n  singleAxis: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var singleAxisModel = seriesModel.getReferringComponents('singleAxis', SINGLE_REFERRING).models[0];\n    if (process.env.NODE_ENV !== 'production') {\n      if (!singleAxisModel) {\n        throw new Error('singleAxis should be specified.');\n      }\n    }\n    result.coordSysDims = ['single'];\n    axisMap.set('single', singleAxisModel);\n    if (isCategory(singleAxisModel)) {\n      categoryAxisMap.set('single', singleAxisModel);\n      result.firstCategoryDimIndex = 0;\n    }\n  },\n  polar: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var polarModel = seriesModel.getReferringComponents('polar', SINGLE_REFERRING).models[0];\n    var radiusAxisModel = polarModel.findAxisModel('radiusAxis');\n    var angleAxisModel = polarModel.findAxisModel('angleAxis');\n    if (process.env.NODE_ENV !== 'production') {\n      if (!angleAxisModel) {\n        throw new Error('angleAxis option not found');\n      }\n      if (!radiusAxisModel) {\n        throw new Error('radiusAxis option not found');\n      }\n    }\n    result.coordSysDims = ['radius', 'angle'];\n    axisMap.set('radius', radiusAxisModel);\n    axisMap.set('angle', angleAxisModel);\n    if (isCategory(radiusAxisModel)) {\n      categoryAxisMap.set('radius', radiusAxisModel);\n      result.firstCategoryDimIndex = 0;\n    }\n    if (isCategory(angleAxisModel)) {\n      categoryAxisMap.set('angle', angleAxisModel);\n      result.firstCategoryDimIndex == null && (result.firstCategoryDimIndex = 1);\n    }\n  },\n  geo: function (seriesModel, result, axisMap, categoryAxisMap) {\n    result.coordSysDims = ['lng', 'lat'];\n  },\n  parallel: function (seriesModel, result, axisMap, categoryAxisMap) {\n    var ecModel = seriesModel.ecModel;\n    var parallelModel = ecModel.getComponent('parallel', seriesModel.get('parallelIndex'));\n    var coordSysDims = result.coordSysDims = parallelModel.dimensions.slice();\n    each(parallelModel.parallelAxisIndex, function (axisIndex, index) {\n      var axisModel = ecModel.getComponent('parallelAxis', axisIndex);\n      var axisDim = coordSysDims[index];\n      axisMap.set(axisDim, axisModel);\n      if (isCategory(axisModel)) {\n        categoryAxisMap.set(axisDim, axisModel);\n        if (result.firstCategoryDimIndex == null) {\n          result.firstCategoryDimIndex = index;\n        }\n      }\n    });\n  }\n};\nfunction isCategory(axisModel) {\n  return axisModel.get('type') === 'category';\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA,GACA;;;CAGC,GACD,OAAO;AACP,qCAAqC;AACrC,4DAA4D;;;;AA6CpD;AA5CR;AACA;;;AACA;;;;;;;;;;;;;;;;;;;CAmBC,GACD,IAAI,eAAe,WAAW,GAAE;IAC9B,SAAS,aAAa,YAAY;QAChC,IAAI,CAAC,YAAY,GAAG,EAAE;QACtB,IAAI,CAAC,OAAO,GAAG,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;QAC3B,IAAI,CAAC,eAAe,GAAG,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;QACnC,IAAI,CAAC,YAAY,GAAG;IACtB;IACA,OAAO;AACT;AACO,SAAS,wBAAwB,WAAW;IACjD,IAAI,eAAe,YAAY,GAAG,CAAC;IACnC,IAAI,SAAS,IAAI,aAAa;IAC9B,IAAI,QAAQ,QAAQ,CAAC,aAAa;IAClC,IAAI,OAAO;QACT,MAAM,aAAa,QAAQ,OAAO,OAAO,EAAE,OAAO,eAAe;QACjE,OAAO;IACT;AACF;AACA,IAAI,WAAW;IACb,aAAa,SAAU,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe;QAClE,IAAI,aAAa,YAAY,sBAAsB,CAAC,SAAS,kJAAA,CAAA,mBAAgB,EAAE,MAAM,CAAC,EAAE;QACxF,IAAI,aAAa,YAAY,sBAAsB,CAAC,SAAS,kJAAA,CAAA,mBAAgB,EAAE,MAAM,CAAC,EAAE;QACxF,wCAA2C;YACzC,IAAI,CAAC,YAAY;gBACf,MAAM,IAAI,MAAM,YAAY,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,GAAG,CAAC,eAAe,YAAY,GAAG,CAAC,YAAY,KAAK;YACvG;YACA,IAAI,CAAC,YAAY;gBACf,MAAM,IAAI,MAAM,YAAY,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,GAAG,CAAC,eAAe,YAAY,GAAG,CAAC,YAAY,KAAK;YACvG;QACF;QACA,OAAO,YAAY,GAAG;YAAC;YAAK;SAAI;QAChC,QAAQ,GAAG,CAAC,KAAK;QACjB,QAAQ,GAAG,CAAC,KAAK;QACjB,IAAI,WAAW,aAAa;YAC1B,gBAAgB,GAAG,CAAC,KAAK;YACzB,OAAO,qBAAqB,GAAG;QACjC;QACA,IAAI,WAAW,aAAa;YAC1B,gBAAgB,GAAG,CAAC,KAAK;YACzB,OAAO,qBAAqB,IAAI,QAAQ,CAAC,OAAO,qBAAqB,GAAG,CAAC;QAC3E;IACF;IACA,YAAY,SAAU,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe;QACjE,IAAI,kBAAkB,YAAY,sBAAsB,CAAC,cAAc,kJAAA,CAAA,mBAAgB,EAAE,MAAM,CAAC,EAAE;QAClG,wCAA2C;YACzC,IAAI,CAAC,iBAAiB;gBACpB,MAAM,IAAI,MAAM;YAClB;QACF;QACA,OAAO,YAAY,GAAG;YAAC;SAAS;QAChC,QAAQ,GAAG,CAAC,UAAU;QACtB,IAAI,WAAW,kBAAkB;YAC/B,gBAAgB,GAAG,CAAC,UAAU;YAC9B,OAAO,qBAAqB,GAAG;QACjC;IACF;IACA,OAAO,SAAU,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe;QAC5D,IAAI,aAAa,YAAY,sBAAsB,CAAC,SAAS,kJAAA,CAAA,mBAAgB,EAAE,MAAM,CAAC,EAAE;QACxF,IAAI,kBAAkB,WAAW,aAAa,CAAC;QAC/C,IAAI,iBAAiB,WAAW,aAAa,CAAC;QAC9C,wCAA2C;YACzC,IAAI,CAAC,gBAAgB;gBACnB,MAAM,IAAI,MAAM;YAClB;YACA,IAAI,CAAC,iBAAiB;gBACpB,MAAM,IAAI,MAAM;YAClB;QACF;QACA,OAAO,YAAY,GAAG;YAAC;YAAU;SAAQ;QACzC,QAAQ,GAAG,CAAC,UAAU;QACtB,QAAQ,GAAG,CAAC,SAAS;QACrB,IAAI,WAAW,kBAAkB;YAC/B,gBAAgB,GAAG,CAAC,UAAU;YAC9B,OAAO,qBAAqB,GAAG;QACjC;QACA,IAAI,WAAW,iBAAiB;YAC9B,gBAAgB,GAAG,CAAC,SAAS;YAC7B,OAAO,qBAAqB,IAAI,QAAQ,CAAC,OAAO,qBAAqB,GAAG,CAAC;QAC3E;IACF;IACA,KAAK,SAAU,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe;QAC1D,OAAO,YAAY,GAAG;YAAC;YAAO;SAAM;IACtC;IACA,UAAU,SAAU,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe;QAC/D,IAAI,UAAU,YAAY,OAAO;QACjC,IAAI,gBAAgB,QAAQ,YAAY,CAAC,YAAY,YAAY,GAAG,CAAC;QACrE,IAAI,eAAe,OAAO,YAAY,GAAG,cAAc,UAAU,CAAC,KAAK;QACvE,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,cAAc,iBAAiB,EAAE,SAAU,SAAS,EAAE,KAAK;YAC9D,IAAI,YAAY,QAAQ,YAAY,CAAC,gBAAgB;YACrD,IAAI,UAAU,YAAY,CAAC,MAAM;YACjC,QAAQ,GAAG,CAAC,SAAS;YACrB,IAAI,WAAW,YAAY;gBACzB,gBAAgB,GAAG,CAAC,SAAS;gBAC7B,IAAI,OAAO,qBAAqB,IAAI,MAAM;oBACxC,OAAO,qBAAqB,GAAG;gBACjC;YACF;QACF;IACF;AACF;AACA,SAAS,WAAW,SAAS;IAC3B,OAAO,UAAU,GAAG,CAAC,YAAY;AACnC", "ignoreList": [0], "debugId": null}}]}