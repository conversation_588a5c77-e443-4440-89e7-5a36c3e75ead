{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/visualMap/VisualMapModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport visualDefault from '../../visual/visualDefault.js';\nimport VisualMapping from '../../visual/VisualMapping.js';\nimport * as visualSolution from '../../visual/visualSolution.js';\nimport * as modelUtil from '../../util/model.js';\nimport * as numberUtil from '../../util/number.js';\nimport ComponentModel from '../../model/Component.js';\nvar mapVisual = VisualMapping.mapVisual;\nvar eachVisual = VisualMapping.eachVisual;\nvar isArray = zrUtil.isArray;\nvar each = zrUtil.each;\nvar asc = numberUtil.asc;\nvar linearMap = numberUtil.linearMap;\nvar VisualMapModel = /** @class */function (_super) {\n  __extends(VisualMapModel, _super);\n  function VisualMapModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = VisualMapModel.type;\n    _this.stateList = ['inRange', 'outOfRange'];\n    _this.replacableOptionKeys = ['inRange', 'outOfRange', 'target', 'controller', 'color'];\n    _this.layoutMode = {\n      type: 'box',\n      ignoreSize: true\n    };\n    /**\r\n     * [lowerBound, upperBound]\r\n     */\n    _this.dataBound = [-Infinity, Infinity];\n    _this.targetVisuals = {};\n    _this.controllerVisuals = {};\n    return _this;\n  }\n  VisualMapModel.prototype.init = function (option, parentModel, ecModel) {\n    this.mergeDefaultAndTheme(option, ecModel);\n  };\n  /**\r\n   * @protected\r\n   */\n  VisualMapModel.prototype.optionUpdated = function (newOption, isInit) {\n    var thisOption = this.option;\n    !isInit && visualSolution.replaceVisualOption(thisOption, newOption, this.replacableOptionKeys);\n    this.textStyleModel = this.getModel('textStyle');\n    this.resetItemSize();\n    this.completeVisualOption();\n  };\n  /**\r\n   * @protected\r\n   */\n  VisualMapModel.prototype.resetVisual = function (supplementVisualOption) {\n    var stateList = this.stateList;\n    supplementVisualOption = zrUtil.bind(supplementVisualOption, this);\n    this.controllerVisuals = visualSolution.createVisualMappings(this.option.controller, stateList, supplementVisualOption);\n    this.targetVisuals = visualSolution.createVisualMappings(this.option.target, stateList, supplementVisualOption);\n  };\n  /**\r\n   * @public\r\n   */\n  VisualMapModel.prototype.getItemSymbol = function () {\n    return null;\n  };\n  /**\r\n   * @protected\r\n   * @return {Array.<number>} An array of series indices.\r\n   */\n  VisualMapModel.prototype.getTargetSeriesIndices = function () {\n    var optionSeriesIndex = this.option.seriesIndex;\n    var seriesIndices = [];\n    if (optionSeriesIndex == null || optionSeriesIndex === 'all') {\n      this.ecModel.eachSeries(function (seriesModel, index) {\n        seriesIndices.push(index);\n      });\n    } else {\n      seriesIndices = modelUtil.normalizeToArray(optionSeriesIndex);\n    }\n    return seriesIndices;\n  };\n  /**\r\n   * @public\r\n   */\n  VisualMapModel.prototype.eachTargetSeries = function (callback, context) {\n    zrUtil.each(this.getTargetSeriesIndices(), function (seriesIndex) {\n      var seriesModel = this.ecModel.getSeriesByIndex(seriesIndex);\n      if (seriesModel) {\n        callback.call(context, seriesModel);\n      }\n    }, this);\n  };\n  /**\r\n   * @pubilc\r\n   */\n  VisualMapModel.prototype.isTargetSeries = function (seriesModel) {\n    var is = false;\n    this.eachTargetSeries(function (model) {\n      model === seriesModel && (is = true);\n    });\n    return is;\n  };\n  /**\r\n   * @example\r\n   * this.formatValueText(someVal); // format single numeric value to text.\r\n   * this.formatValueText(someVal, true); // format single category value to text.\r\n   * this.formatValueText([min, max]); // format numeric min-max to text.\r\n   * this.formatValueText([this.dataBound[0], max]); // using data lower bound.\r\n   * this.formatValueText([min, this.dataBound[1]]); // using data upper bound.\r\n   *\r\n   * @param value Real value, or this.dataBound[0 or 1].\r\n   * @param isCategory Only available when value is number.\r\n   * @param edgeSymbols Open-close symbol when value is interval.\r\n   * @protected\r\n   */\n  VisualMapModel.prototype.formatValueText = function (value, isCategory, edgeSymbols) {\n    var option = this.option;\n    var precision = option.precision;\n    var dataBound = this.dataBound;\n    var formatter = option.formatter;\n    var isMinMax;\n    edgeSymbols = edgeSymbols || ['<', '>'];\n    if (zrUtil.isArray(value)) {\n      value = value.slice();\n      isMinMax = true;\n    }\n    var textValue = isCategory ? value // Value is string when isCategory\n    : isMinMax ? [toFixed(value[0]), toFixed(value[1])] : toFixed(value);\n    if (zrUtil.isString(formatter)) {\n      return formatter.replace('{value}', isMinMax ? textValue[0] : textValue).replace('{value2}', isMinMax ? textValue[1] : textValue);\n    } else if (zrUtil.isFunction(formatter)) {\n      return isMinMax ? formatter(value[0], value[1]) : formatter(value);\n    }\n    if (isMinMax) {\n      if (value[0] === dataBound[0]) {\n        return edgeSymbols[0] + ' ' + textValue[1];\n      } else if (value[1] === dataBound[1]) {\n        return edgeSymbols[1] + ' ' + textValue[0];\n      } else {\n        return textValue[0] + ' - ' + textValue[1];\n      }\n    } else {\n      // Format single value (includes category case).\n      return textValue;\n    }\n    function toFixed(val) {\n      return val === dataBound[0] ? 'min' : val === dataBound[1] ? 'max' : (+val).toFixed(Math.min(precision, 20));\n    }\n  };\n  /**\r\n   * @protected\r\n   */\n  VisualMapModel.prototype.resetExtent = function () {\n    var thisOption = this.option;\n    // Can not calculate data extent by data here.\n    // Because series and data may be modified in processing stage.\n    // So we do not support the feature \"auto min/max\".\n    var extent = asc([thisOption.min, thisOption.max]);\n    this._dataExtent = extent;\n  };\n  /**\r\n   * PENDING:\r\n   * delete this method if no outer usage.\r\n   *\r\n   * Return  Concrete dimension. If null/undefined is returned, no dimension is used.\r\n   */\n  // getDataDimension(data: SeriesData) {\n  //     const optDim = this.option.dimension;\n  //     if (optDim != null) {\n  //         return data.getDimension(optDim);\n  //     }\n  //     const dimNames = data.dimensions;\n  //     for (let i = dimNames.length - 1; i >= 0; i--) {\n  //         const dimName = dimNames[i];\n  //         const dimInfo = data.getDimensionInfo(dimName);\n  //         if (!dimInfo.isCalculationCoord) {\n  //             return dimName;\n  //         }\n  //     }\n  // }\n  VisualMapModel.prototype.getDataDimensionIndex = function (data) {\n    var optDim = this.option.dimension;\n    if (optDim != null) {\n      return data.getDimensionIndex(optDim);\n    }\n    var dimNames = data.dimensions;\n    for (var i = dimNames.length - 1; i >= 0; i--) {\n      var dimName = dimNames[i];\n      var dimInfo = data.getDimensionInfo(dimName);\n      if (!dimInfo.isCalculationCoord) {\n        return dimInfo.storeDimIndex;\n      }\n    }\n  };\n  VisualMapModel.prototype.getExtent = function () {\n    return this._dataExtent.slice();\n  };\n  VisualMapModel.prototype.completeVisualOption = function () {\n    var ecModel = this.ecModel;\n    var thisOption = this.option;\n    var base = {\n      inRange: thisOption.inRange,\n      outOfRange: thisOption.outOfRange\n    };\n    var target = thisOption.target || (thisOption.target = {});\n    var controller = thisOption.controller || (thisOption.controller = {});\n    zrUtil.merge(target, base); // Do not override\n    zrUtil.merge(controller, base); // Do not override\n    var isCategory = this.isCategory();\n    completeSingle.call(this, target);\n    completeSingle.call(this, controller);\n    completeInactive.call(this, target, 'inRange', 'outOfRange');\n    // completeInactive.call(this, target, 'outOfRange', 'inRange');\n    completeController.call(this, controller);\n    function completeSingle(base) {\n      // Compatible with ec2 dataRange.color.\n      // The mapping order of dataRange.color is: [high value, ..., low value]\n      // whereas inRange.color and outOfRange.color is [low value, ..., high value]\n      // Notice: ec2 has no inverse.\n      if (isArray(thisOption.color)\n      // If there has been inRange: {symbol: ...}, adding color is a mistake.\n      // So adding color only when no inRange defined.\n      && !base.inRange) {\n        base.inRange = {\n          color: thisOption.color.slice().reverse()\n        };\n      }\n      // Compatible with previous logic, always give a default color, otherwise\n      // simple config with no inRange and outOfRange will not work.\n      // Originally we use visualMap.color as the default color, but setOption at\n      // the second time the default color will be erased. So we change to use\n      // constant DEFAULT_COLOR.\n      // If user do not want the default color, set inRange: {color: null}.\n      base.inRange = base.inRange || {\n        color: ecModel.get('gradientColor')\n      };\n    }\n    function completeInactive(base, stateExist, stateAbsent) {\n      var optExist = base[stateExist];\n      var optAbsent = base[stateAbsent];\n      if (optExist && !optAbsent) {\n        optAbsent = base[stateAbsent] = {};\n        each(optExist, function (visualData, visualType) {\n          if (!VisualMapping.isValidType(visualType)) {\n            return;\n          }\n          var defa = visualDefault.get(visualType, 'inactive', isCategory);\n          if (defa != null) {\n            optAbsent[visualType] = defa;\n            // Compatibable with ec2:\n            // Only inactive color to rgba(0,0,0,0) can not\n            // make label transparent, so use opacity also.\n            if (visualType === 'color' && !optAbsent.hasOwnProperty('opacity') && !optAbsent.hasOwnProperty('colorAlpha')) {\n              optAbsent.opacity = [0, 0];\n            }\n          }\n        });\n      }\n    }\n    function completeController(controller) {\n      var symbolExists = (controller.inRange || {}).symbol || (controller.outOfRange || {}).symbol;\n      var symbolSizeExists = (controller.inRange || {}).symbolSize || (controller.outOfRange || {}).symbolSize;\n      var inactiveColor = this.get('inactiveColor');\n      var itemSymbol = this.getItemSymbol();\n      var defaultSymbol = itemSymbol || 'roundRect';\n      each(this.stateList, function (state) {\n        var itemSize = this.itemSize;\n        var visuals = controller[state];\n        // Set inactive color for controller if no other color\n        // attr (like colorAlpha) specified.\n        if (!visuals) {\n          visuals = controller[state] = {\n            color: isCategory ? inactiveColor : [inactiveColor]\n          };\n        }\n        // Consistent symbol and symbolSize if not specified.\n        if (visuals.symbol == null) {\n          visuals.symbol = symbolExists && zrUtil.clone(symbolExists) || (isCategory ? defaultSymbol : [defaultSymbol]);\n        }\n        if (visuals.symbolSize == null) {\n          visuals.symbolSize = symbolSizeExists && zrUtil.clone(symbolSizeExists) || (isCategory ? itemSize[0] : [itemSize[0], itemSize[0]]);\n        }\n        // Filter none\n        visuals.symbol = mapVisual(visuals.symbol, function (symbol) {\n          return symbol === 'none' ? defaultSymbol : symbol;\n        });\n        // Normalize symbolSize\n        var symbolSize = visuals.symbolSize;\n        if (symbolSize != null) {\n          var max_1 = -Infinity;\n          // symbolSize can be object when categories defined.\n          eachVisual(symbolSize, function (value) {\n            value > max_1 && (max_1 = value);\n          });\n          visuals.symbolSize = mapVisual(symbolSize, function (value) {\n            return linearMap(value, [0, max_1], [0, itemSize[0]], true);\n          });\n        }\n      }, this);\n    }\n  };\n  VisualMapModel.prototype.resetItemSize = function () {\n    this.itemSize = [parseFloat(this.get('itemWidth')), parseFloat(this.get('itemHeight'))];\n  };\n  VisualMapModel.prototype.isCategory = function () {\n    return !!this.option.categories;\n  };\n  /**\r\n   * @public\r\n   * @abstract\r\n   */\n  VisualMapModel.prototype.setSelected = function (selected) {};\n  VisualMapModel.prototype.getSelected = function () {\n    return null;\n  };\n  /**\r\n   * @public\r\n   * @abstract\r\n   */\n  VisualMapModel.prototype.getValueState = function (value) {\n    return null;\n  };\n  /**\r\n   * FIXME\r\n   * Do not publish to thirt-part-dev temporarily\r\n   * util the interface is stable. (Should it return\r\n   * a function but not visual meta?)\r\n   *\r\n   * @pubilc\r\n   * @abstract\r\n   * @param getColorVisual\r\n   *        params: value, valueState\r\n   *        return: color\r\n   * @return {Object} visualMeta\r\n   *        should includes {stops, outerColors}\r\n   *        outerColor means [colorBeyondMinValue, colorBeyondMaxValue]\r\n   */\n  VisualMapModel.prototype.getVisualMeta = function (getColorVisual) {\n    return null;\n  };\n  VisualMapModel.type = 'visualMap';\n  VisualMapModel.dependencies = ['series'];\n  VisualMapModel.defaultOption = {\n    show: true,\n    // zlevel: 0,\n    z: 4,\n    seriesIndex: 'all',\n    min: 0,\n    max: 200,\n    left: 0,\n    right: null,\n    top: null,\n    bottom: 0,\n    itemWidth: null,\n    itemHeight: null,\n    inverse: false,\n    orient: 'vertical',\n    backgroundColor: 'rgba(0,0,0,0)',\n    borderColor: '#ccc',\n    contentColor: '#5793f3',\n    inactiveColor: '#aaa',\n    borderWidth: 0,\n    padding: 5,\n    // 接受数组分别设定上右下左边距，同css\n    textGap: 10,\n    precision: 0,\n    textStyle: {\n      color: '#333' // 值域文字颜色\n    }\n  };\n  return VisualMapModel;\n}(ComponentModel);\nexport default VisualMapModel;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACA,IAAI,YAAY,4JAAA,CAAA,UAAa,CAAC,SAAS;AACvC,IAAI,aAAa,4JAAA,CAAA,UAAa,CAAC,UAAU;AACzC,IAAI,UAAU,iJAAA,CAAA,UAAc;AAC5B,IAAI,OAAO,iJAAA,CAAA,OAAW;AACtB,IAAI,MAAM,mJAAA,CAAA,MAAc;AACxB,IAAI,YAAY,mJAAA,CAAA,YAAoB;AACpC,IAAI,iBAAiB,WAAW,GAAE,SAAU,MAAM;IAChD,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;IAC1B,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,eAAe,IAAI;QAChC,MAAM,SAAS,GAAG;YAAC;YAAW;SAAa;QAC3C,MAAM,oBAAoB,GAAG;YAAC;YAAW;YAAc;YAAU;YAAc;SAAQ;QACvF,MAAM,UAAU,GAAG;YACjB,MAAM;YACN,YAAY;QACd;QACA;;KAEC,GACD,MAAM,SAAS,GAAG;YAAC,CAAC;YAAU;SAAS;QACvC,MAAM,aAAa,GAAG,CAAC;QACvB,MAAM,iBAAiB,GAAG,CAAC;QAC3B,OAAO;IACT;IACA,eAAe,SAAS,CAAC,IAAI,GAAG,SAAU,MAAM,EAAE,WAAW,EAAE,OAAO;QACpE,IAAI,CAAC,oBAAoB,CAAC,QAAQ;IACpC;IACA;;GAEC,GACD,eAAe,SAAS,CAAC,aAAa,GAAG,SAAU,SAAS,EAAE,MAAM;QAClE,IAAI,aAAa,IAAI,CAAC,MAAM;QAC5B,CAAC,UAAU,CAAA,GAAA,6JAAA,CAAA,sBAAkC,AAAD,EAAE,YAAY,WAAW,IAAI,CAAC,oBAAoB;QAC9F,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,QAAQ,CAAC;QACpC,IAAI,CAAC,aAAa;QAClB,IAAI,CAAC,oBAAoB;IAC3B;IACA;;GAEC,GACD,eAAe,SAAS,CAAC,WAAW,GAAG,SAAU,sBAAsB;QACrE,IAAI,YAAY,IAAI,CAAC,SAAS;QAC9B,yBAAyB,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,wBAAwB,IAAI;QACjE,IAAI,CAAC,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,uBAAmC,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,UAAU,EAAE,WAAW;QAChG,IAAI,CAAC,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,uBAAmC,AAAD,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM,EAAE,WAAW;IAC1F;IACA;;GAEC,GACD,eAAe,SAAS,CAAC,aAAa,GAAG;QACvC,OAAO;IACT;IACA;;;GAGC,GACD,eAAe,SAAS,CAAC,sBAAsB,GAAG;QAChD,IAAI,oBAAoB,IAAI,CAAC,MAAM,CAAC,WAAW;QAC/C,IAAI,gBAAgB,EAAE;QACtB,IAAI,qBAAqB,QAAQ,sBAAsB,OAAO;YAC5D,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,SAAU,WAAW,EAAE,KAAK;gBAClD,cAAc,IAAI,CAAC;YACrB;QACF,OAAO;YACL,gBAAgB,CAAA,GAAA,kJAAA,CAAA,mBAA0B,AAAD,EAAE;QAC7C;QACA,OAAO;IACT;IACA;;GAEC,GACD,eAAe,SAAS,CAAC,gBAAgB,GAAG,SAAU,QAAQ,EAAE,OAAO;QACrE,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,IAAI,CAAC,sBAAsB,IAAI,SAAU,WAAW;YAC9D,IAAI,cAAc,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC;YAChD,IAAI,aAAa;gBACf,SAAS,IAAI,CAAC,SAAS;YACzB;QACF,GAAG,IAAI;IACT;IACA;;GAEC,GACD,eAAe,SAAS,CAAC,cAAc,GAAG,SAAU,WAAW;QAC7D,IAAI,KAAK;QACT,IAAI,CAAC,gBAAgB,CAAC,SAAU,KAAK;YACnC,UAAU,eAAe,CAAC,KAAK,IAAI;QACrC;QACA,OAAO;IACT;IACA;;;;;;;;;;;;GAYC,GACD,eAAe,SAAS,CAAC,eAAe,GAAG,SAAU,KAAK,EAAE,UAAU,EAAE,WAAW;QACjF,IAAI,SAAS,IAAI,CAAC,MAAM;QACxB,IAAI,YAAY,OAAO,SAAS;QAChC,IAAI,YAAY,IAAI,CAAC,SAAS;QAC9B,IAAI,YAAY,OAAO,SAAS;QAChC,IAAI;QACJ,cAAc,eAAe;YAAC;YAAK;SAAI;QACvC,IAAI,CAAA,GAAA,iJAAA,CAAA,UAAc,AAAD,EAAE,QAAQ;YACzB,QAAQ,MAAM,KAAK;YACnB,WAAW;QACb;QACA,IAAI,YAAY,aAAa,MAAM,kCAAkC;WACnE,WAAW;YAAC,QAAQ,KAAK,CAAC,EAAE;YAAG,QAAQ,KAAK,CAAC,EAAE;SAAE,GAAG,QAAQ;QAC9D,IAAI,CAAA,GAAA,iJAAA,CAAA,WAAe,AAAD,EAAE,YAAY;YAC9B,OAAO,UAAU,OAAO,CAAC,WAAW,WAAW,SAAS,CAAC,EAAE,GAAG,WAAW,OAAO,CAAC,YAAY,WAAW,SAAS,CAAC,EAAE,GAAG;QACzH,OAAO,IAAI,CAAA,GAAA,iJAAA,CAAA,aAAiB,AAAD,EAAE,YAAY;YACvC,OAAO,WAAW,UAAU,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,IAAI,UAAU;QAC9D;QACA,IAAI,UAAU;YACZ,IAAI,KAAK,CAAC,EAAE,KAAK,SAAS,CAAC,EAAE,EAAE;gBAC7B,OAAO,WAAW,CAAC,EAAE,GAAG,MAAM,SAAS,CAAC,EAAE;YAC5C,OAAO,IAAI,KAAK,CAAC,EAAE,KAAK,SAAS,CAAC,EAAE,EAAE;gBACpC,OAAO,WAAW,CAAC,EAAE,GAAG,MAAM,SAAS,CAAC,EAAE;YAC5C,OAAO;gBACL,OAAO,SAAS,CAAC,EAAE,GAAG,QAAQ,SAAS,CAAC,EAAE;YAC5C;QACF,OAAO;YACL,gDAAgD;YAChD,OAAO;QACT;;QACA,SAAS,QAAQ,GAAG;YAClB,OAAO,QAAQ,SAAS,CAAC,EAAE,GAAG,QAAQ,QAAQ,SAAS,CAAC,EAAE,GAAG,QAAQ,CAAC,CAAC,GAAG,EAAE,OAAO,CAAC,KAAK,GAAG,CAAC,WAAW;QAC1G;IACF;IACA;;GAEC,GACD,eAAe,SAAS,CAAC,WAAW,GAAG;QACrC,IAAI,aAAa,IAAI,CAAC,MAAM;QAC5B,8CAA8C;QAC9C,+DAA+D;QAC/D,mDAAmD;QACnD,IAAI,SAAS,IAAI;YAAC,WAAW,GAAG;YAAE,WAAW,GAAG;SAAC;QACjD,IAAI,CAAC,WAAW,GAAG;IACrB;IACA;;;;;GAKC,GACD,uCAAuC;IACvC,4CAA4C;IAC5C,4BAA4B;IAC5B,4CAA4C;IAC5C,QAAQ;IACR,wCAAwC;IACxC,uDAAuD;IACvD,uCAAuC;IACvC,0DAA0D;IAC1D,6CAA6C;IAC7C,8BAA8B;IAC9B,YAAY;IACZ,QAAQ;IACR,IAAI;IACJ,eAAe,SAAS,CAAC,qBAAqB,GAAG,SAAU,IAAI;QAC7D,IAAI,SAAS,IAAI,CAAC,MAAM,CAAC,SAAS;QAClC,IAAI,UAAU,MAAM;YAClB,OAAO,KAAK,iBAAiB,CAAC;QAChC;QACA,IAAI,WAAW,KAAK,UAAU;QAC9B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YAC7C,IAAI,UAAU,QAAQ,CAAC,EAAE;YACzB,IAAI,UAAU,KAAK,gBAAgB,CAAC;YACpC,IAAI,CAAC,QAAQ,kBAAkB,EAAE;gBAC/B,OAAO,QAAQ,aAAa;YAC9B;QACF;IACF;IACA,eAAe,SAAS,CAAC,SAAS,GAAG;QACnC,OAAO,IAAI,CAAC,WAAW,CAAC,KAAK;IAC/B;IACA,eAAe,SAAS,CAAC,oBAAoB,GAAG;QAC9C,IAAI,UAAU,IAAI,CAAC,OAAO;QAC1B,IAAI,aAAa,IAAI,CAAC,MAAM;QAC5B,IAAI,OAAO;YACT,SAAS,WAAW,OAAO;YAC3B,YAAY,WAAW,UAAU;QACnC;QACA,IAAI,SAAS,WAAW,MAAM,IAAI,CAAC,WAAW,MAAM,GAAG,CAAC,CAAC;QACzD,IAAI,aAAa,WAAW,UAAU,IAAI,CAAC,WAAW,UAAU,GAAG,CAAC,CAAC;QACrE,CAAA,GAAA,iJAAA,CAAA,QAAY,AAAD,EAAE,QAAQ,OAAO,kBAAkB;QAC9C,CAAA,GAAA,iJAAA,CAAA,QAAY,AAAD,EAAE,YAAY,OAAO,kBAAkB;QAClD,IAAI,aAAa,IAAI,CAAC,UAAU;QAChC,eAAe,IAAI,CAAC,IAAI,EAAE;QAC1B,eAAe,IAAI,CAAC,IAAI,EAAE;QAC1B,iBAAiB,IAAI,CAAC,IAAI,EAAE,QAAQ,WAAW;QAC/C,gEAAgE;QAChE,mBAAmB,IAAI,CAAC,IAAI,EAAE;QAC9B,SAAS,eAAe,IAAI;YAC1B,uCAAuC;YACvC,wEAAwE;YACxE,6EAA6E;YAC7E,8BAA8B;YAC9B,IAAI,QAAQ,WAAW,KAAK,KAGzB,CAAC,KAAK,OAAO,EAAE;gBAChB,KAAK,OAAO,GAAG;oBACb,OAAO,WAAW,KAAK,CAAC,KAAK,GAAG,OAAO;gBACzC;YACF;YACA,yEAAyE;YACzE,8DAA8D;YAC9D,2EAA2E;YAC3E,wEAAwE;YACxE,0BAA0B;YAC1B,qEAAqE;YACrE,KAAK,OAAO,GAAG,KAAK,OAAO,IAAI;gBAC7B,OAAO,QAAQ,GAAG,CAAC;YACrB;QACF;QACA,SAAS,iBAAiB,IAAI,EAAE,UAAU,EAAE,WAAW;YACrD,IAAI,WAAW,IAAI,CAAC,WAAW;YAC/B,IAAI,YAAY,IAAI,CAAC,YAAY;YACjC,IAAI,YAAY,CAAC,WAAW;gBAC1B,YAAY,IAAI,CAAC,YAAY,GAAG,CAAC;gBACjC,KAAK,UAAU,SAAU,UAAU,EAAE,UAAU;oBAC7C,IAAI,CAAC,4JAAA,CAAA,UAAa,CAAC,WAAW,CAAC,aAAa;wBAC1C;oBACF;oBACA,IAAI,OAAO,4JAAA,CAAA,UAAa,CAAC,GAAG,CAAC,YAAY,YAAY;oBACrD,IAAI,QAAQ,MAAM;wBAChB,SAAS,CAAC,WAAW,GAAG;wBACxB,yBAAyB;wBACzB,+CAA+C;wBAC/C,+CAA+C;wBAC/C,IAAI,eAAe,WAAW,CAAC,UAAU,cAAc,CAAC,cAAc,CAAC,UAAU,cAAc,CAAC,eAAe;4BAC7G,UAAU,OAAO,GAAG;gCAAC;gCAAG;6BAAE;wBAC5B;oBACF;gBACF;YACF;QACF;QACA,SAAS,mBAAmB,UAAU;YACpC,IAAI,eAAe,CAAC,WAAW,OAAO,IAAI,CAAC,CAAC,EAAE,MAAM,IAAI,CAAC,WAAW,UAAU,IAAI,CAAC,CAAC,EAAE,MAAM;YAC5F,IAAI,mBAAmB,CAAC,WAAW,OAAO,IAAI,CAAC,CAAC,EAAE,UAAU,IAAI,CAAC,WAAW,UAAU,IAAI,CAAC,CAAC,EAAE,UAAU;YACxG,IAAI,gBAAgB,IAAI,CAAC,GAAG,CAAC;YAC7B,IAAI,aAAa,IAAI,CAAC,aAAa;YACnC,IAAI,gBAAgB,cAAc;YAClC,KAAK,IAAI,CAAC,SAAS,EAAE,SAAU,KAAK;gBAClC,IAAI,WAAW,IAAI,CAAC,QAAQ;gBAC5B,IAAI,UAAU,UAAU,CAAC,MAAM;gBAC/B,sDAAsD;gBACtD,oCAAoC;gBACpC,IAAI,CAAC,SAAS;oBACZ,UAAU,UAAU,CAAC,MAAM,GAAG;wBAC5B,OAAO,aAAa,gBAAgB;4BAAC;yBAAc;oBACrD;gBACF;gBACA,qDAAqD;gBACrD,IAAI,QAAQ,MAAM,IAAI,MAAM;oBAC1B,QAAQ,MAAM,GAAG,gBAAgB,CAAA,GAAA,iJAAA,CAAA,QAAY,AAAD,EAAE,iBAAiB,CAAC,aAAa,gBAAgB;wBAAC;qBAAc;gBAC9G;gBACA,IAAI,QAAQ,UAAU,IAAI,MAAM;oBAC9B,QAAQ,UAAU,GAAG,oBAAoB,CAAA,GAAA,iJAAA,CAAA,QAAY,AAAD,EAAE,qBAAqB,CAAC,aAAa,QAAQ,CAAC,EAAE,GAAG;wBAAC,QAAQ,CAAC,EAAE;wBAAE,QAAQ,CAAC,EAAE;qBAAC;gBACnI;gBACA,cAAc;gBACd,QAAQ,MAAM,GAAG,UAAU,QAAQ,MAAM,EAAE,SAAU,MAAM;oBACzD,OAAO,WAAW,SAAS,gBAAgB;gBAC7C;gBACA,uBAAuB;gBACvB,IAAI,aAAa,QAAQ,UAAU;gBACnC,IAAI,cAAc,MAAM;oBACtB,IAAI,QAAQ,CAAC;oBACb,oDAAoD;oBACpD,WAAW,YAAY,SAAU,KAAK;wBACpC,QAAQ,SAAS,CAAC,QAAQ,KAAK;oBACjC;oBACA,QAAQ,UAAU,GAAG,UAAU,YAAY,SAAU,KAAK;wBACxD,OAAO,UAAU,OAAO;4BAAC;4BAAG;yBAAM,EAAE;4BAAC;4BAAG,QAAQ,CAAC,EAAE;yBAAC,EAAE;oBACxD;gBACF;YACF,GAAG,IAAI;QACT;IACF;IACA,eAAe,SAAS,CAAC,aAAa,GAAG;QACvC,IAAI,CAAC,QAAQ,GAAG;YAAC,WAAW,IAAI,CAAC,GAAG,CAAC;YAAe,WAAW,IAAI,CAAC,GAAG,CAAC;SAAe;IACzF;IACA,eAAe,SAAS,CAAC,UAAU,GAAG;QACpC,OAAO,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,UAAU;IACjC;IACA;;;GAGC,GACD,eAAe,SAAS,CAAC,WAAW,GAAG,SAAU,QAAQ,GAAG;IAC5D,eAAe,SAAS,CAAC,WAAW,GAAG;QACrC,OAAO;IACT;IACA;;;GAGC,GACD,eAAe,SAAS,CAAC,aAAa,GAAG,SAAU,KAAK;QACtD,OAAO;IACT;IACA;;;;;;;;;;;;;;GAcC,GACD,eAAe,SAAS,CAAC,aAAa,GAAG,SAAU,cAAc;QAC/D,OAAO;IACT;IACA,eAAe,IAAI,GAAG;IACtB,eAAe,YAAY,GAAG;QAAC;KAAS;IACxC,eAAe,aAAa,GAAG;QAC7B,MAAM;QACN,aAAa;QACb,GAAG;QACH,aAAa;QACb,KAAK;QACL,KAAK;QACL,MAAM;QACN,OAAO;QACP,KAAK;QACL,QAAQ;QACR,WAAW;QACX,YAAY;QACZ,SAAS;QACT,QAAQ;QACR,iBAAiB;QACjB,aAAa;QACb,cAAc;QACd,eAAe;QACf,aAAa;QACb,SAAS;QACT,sBAAsB;QACtB,SAAS;QACT,WAAW;QACX,WAAW;YACT,OAAO,OAAO,SAAS;QACzB;IACF;IACA,OAAO;AACT,EAAE,uJAAA,CAAA,UAAc;uCACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 456, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/visualMap/ContinuousModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport VisualMapModel from './VisualMapModel.js';\nimport * as numberUtil from '../../util/number.js';\nimport { inheritDefaultOption } from '../../util/component.js';\n// Constant\nvar DEFAULT_BAR_BOUND = [20, 140];\nvar ContinuousModel = /** @class */function (_super) {\n  __extends(ContinuousModel, _super);\n  function ContinuousModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ContinuousModel.type;\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  ContinuousModel.prototype.optionUpdated = function (newOption, isInit) {\n    _super.prototype.optionUpdated.apply(this, arguments);\n    this.resetExtent();\n    this.resetVisual(function (mappingOption) {\n      mappingOption.mappingMethod = 'linear';\n      mappingOption.dataExtent = this.getExtent();\n    });\n    this._resetRange();\n  };\n  /**\r\n   * @protected\r\n   * @override\r\n   */\n  ContinuousModel.prototype.resetItemSize = function () {\n    _super.prototype.resetItemSize.apply(this, arguments);\n    var itemSize = this.itemSize;\n    (itemSize[0] == null || isNaN(itemSize[0])) && (itemSize[0] = DEFAULT_BAR_BOUND[0]);\n    (itemSize[1] == null || isNaN(itemSize[1])) && (itemSize[1] = DEFAULT_BAR_BOUND[1]);\n  };\n  /**\r\n   * @private\r\n   */\n  ContinuousModel.prototype._resetRange = function () {\n    var dataExtent = this.getExtent();\n    var range = this.option.range;\n    if (!range || range.auto) {\n      // `range` should always be array (so we don't use other\n      // value like 'auto') for user-friend. (consider getOption).\n      dataExtent.auto = 1;\n      this.option.range = dataExtent;\n    } else if (zrUtil.isArray(range)) {\n      if (range[0] > range[1]) {\n        range.reverse();\n      }\n      range[0] = Math.max(range[0], dataExtent[0]);\n      range[1] = Math.min(range[1], dataExtent[1]);\n    }\n  };\n  /**\r\n   * @protected\r\n   * @override\r\n   */\n  ContinuousModel.prototype.completeVisualOption = function () {\n    _super.prototype.completeVisualOption.apply(this, arguments);\n    zrUtil.each(this.stateList, function (state) {\n      var symbolSize = this.option.controller[state].symbolSize;\n      if (symbolSize && symbolSize[0] !== symbolSize[1]) {\n        symbolSize[0] = symbolSize[1] / 3; // For good looking.\n      }\n    }, this);\n  };\n  /**\r\n   * @override\r\n   */\n  ContinuousModel.prototype.setSelected = function (selected) {\n    this.option.range = selected.slice();\n    this._resetRange();\n  };\n  /**\r\n   * @public\r\n   */\n  ContinuousModel.prototype.getSelected = function () {\n    var dataExtent = this.getExtent();\n    var dataInterval = numberUtil.asc((this.get('range') || []).slice());\n    // Clamp\n    dataInterval[0] > dataExtent[1] && (dataInterval[0] = dataExtent[1]);\n    dataInterval[1] > dataExtent[1] && (dataInterval[1] = dataExtent[1]);\n    dataInterval[0] < dataExtent[0] && (dataInterval[0] = dataExtent[0]);\n    dataInterval[1] < dataExtent[0] && (dataInterval[1] = dataExtent[0]);\n    return dataInterval;\n  };\n  /**\r\n   * @override\r\n   */\n  ContinuousModel.prototype.getValueState = function (value) {\n    var range = this.option.range;\n    var dataExtent = this.getExtent();\n    // When range[0] === dataExtent[0], any value larger than dataExtent[0] maps to 'inRange'.\n    // range[1] is processed likewise.\n    return (range[0] <= dataExtent[0] || range[0] <= value) && (range[1] >= dataExtent[1] || value <= range[1]) ? 'inRange' : 'outOfRange';\n  };\n  ContinuousModel.prototype.findTargetDataIndices = function (range) {\n    var result = [];\n    this.eachTargetSeries(function (seriesModel) {\n      var dataIndices = [];\n      var data = seriesModel.getData();\n      data.each(this.getDataDimensionIndex(data), function (value, dataIndex) {\n        range[0] <= value && value <= range[1] && dataIndices.push(dataIndex);\n      }, this);\n      result.push({\n        seriesId: seriesModel.id,\n        dataIndex: dataIndices\n      });\n    }, this);\n    return result;\n  };\n  /**\r\n   * @implement\r\n   */\n  ContinuousModel.prototype.getVisualMeta = function (getColorVisual) {\n    var oVals = getColorStopValues(this, 'outOfRange', this.getExtent());\n    var iVals = getColorStopValues(this, 'inRange', this.option.range.slice());\n    var stops = [];\n    function setStop(value, valueState) {\n      stops.push({\n        value: value,\n        color: getColorVisual(value, valueState)\n      });\n    }\n    // Format to: outOfRange -- inRange -- outOfRange.\n    var iIdx = 0;\n    var oIdx = 0;\n    var iLen = iVals.length;\n    var oLen = oVals.length;\n    for (; oIdx < oLen && (!iVals.length || oVals[oIdx] <= iVals[0]); oIdx++) {\n      // If oVal[oIdx] === iVals[iIdx], oVal[oIdx] should be ignored.\n      if (oVals[oIdx] < iVals[iIdx]) {\n        setStop(oVals[oIdx], 'outOfRange');\n      }\n    }\n    for (var first = 1; iIdx < iLen; iIdx++, first = 0) {\n      // If range is full, value beyond min, max will be clamped.\n      // make a singularity\n      first && stops.length && setStop(iVals[iIdx], 'outOfRange');\n      setStop(iVals[iIdx], 'inRange');\n    }\n    for (var first = 1; oIdx < oLen; oIdx++) {\n      if (!iVals.length || iVals[iVals.length - 1] < oVals[oIdx]) {\n        // make a singularity\n        if (first) {\n          stops.length && setStop(stops[stops.length - 1].value, 'outOfRange');\n          first = 0;\n        }\n        setStop(oVals[oIdx], 'outOfRange');\n      }\n    }\n    var stopsLen = stops.length;\n    return {\n      stops: stops,\n      outerColors: [stopsLen ? stops[0].color : 'transparent', stopsLen ? stops[stopsLen - 1].color : 'transparent']\n    };\n  };\n  ContinuousModel.type = 'visualMap.continuous';\n  ContinuousModel.defaultOption = inheritDefaultOption(VisualMapModel.defaultOption, {\n    align: 'auto',\n    calculable: false,\n    hoverLink: true,\n    realtime: true,\n    handleIcon: 'path://M-11.39,9.77h0a3.5,3.5,0,0,1-3.5,3.5h-22a3.5,3.5,0,0,1-3.5-3.5h0a3.5,3.5,0,0,1,3.5-3.5h22A3.5,3.5,0,0,1-11.39,9.77Z',\n    handleSize: '120%',\n    handleStyle: {\n      borderColor: '#fff',\n      borderWidth: 1\n    },\n    indicatorIcon: 'circle',\n    indicatorSize: '50%',\n    indicatorStyle: {\n      borderColor: '#fff',\n      borderWidth: 2,\n      shadowBlur: 2,\n      shadowOffsetX: 1,\n      shadowOffsetY: 1,\n      shadowColor: 'rgba(0,0,0,0.2)'\n    }\n    // emphasis: {\n    //     handleStyle: {\n    //         shadowBlur: 3,\n    //         shadowOffsetX: 1,\n    //         shadowOffsetY: 1,\n    //         shadowColor: 'rgba(0,0,0,0.2)'\n    //     }\n    // }\n  });\n  return ContinuousModel;\n}(VisualMapModel);\nfunction getColorStopValues(visualMapModel, valueState, dataExtent) {\n  if (dataExtent[0] === dataExtent[1]) {\n    return dataExtent.slice();\n  }\n  // When using colorHue mapping, it is not linear color any more.\n  // Moreover, canvas gradient seems not to be accurate linear.\n  // FIXME\n  // Should be arbitrary value 100? or based on pixel size?\n  var count = 200;\n  var step = (dataExtent[1] - dataExtent[0]) / count;\n  var value = dataExtent[0];\n  var stopValues = [];\n  for (var i = 0; i <= count && value < dataExtent[1]; i++) {\n    stopValues.push(value);\n    value += step;\n  }\n  stopValues.push(dataExtent[1]);\n  return stopValues;\n}\nexport default ContinuousModel;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;;;;;;AACA,WAAW;AACX,IAAI,oBAAoB;IAAC;IAAI;CAAI;AACjC,IAAI,kBAAkB,WAAW,GAAE,SAAU,MAAM;IACjD,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB;IAC3B,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,gBAAgB,IAAI;QACjC,OAAO;IACT;IACA;;GAEC,GACD,gBAAgB,SAAS,CAAC,aAAa,GAAG,SAAU,SAAS,EAAE,MAAM;QACnE,OAAO,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE;QAC3C,IAAI,CAAC,WAAW;QAChB,IAAI,CAAC,WAAW,CAAC,SAAU,aAAa;YACtC,cAAc,aAAa,GAAG;YAC9B,cAAc,UAAU,GAAG,IAAI,CAAC,SAAS;QAC3C;QACA,IAAI,CAAC,WAAW;IAClB;IACA;;;GAGC,GACD,gBAAgB,SAAS,CAAC,aAAa,GAAG;QACxC,OAAO,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE;QAC3C,IAAI,WAAW,IAAI,CAAC,QAAQ;QAC5B,CAAC,QAAQ,CAAC,EAAE,IAAI,QAAQ,MAAM,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,GAAG,iBAAiB,CAAC,EAAE;QAClF,CAAC,QAAQ,CAAC,EAAE,IAAI,QAAQ,MAAM,QAAQ,CAAC,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC,EAAE,GAAG,iBAAiB,CAAC,EAAE;IACpF;IACA;;GAEC,GACD,gBAAgB,SAAS,CAAC,WAAW,GAAG;QACtC,IAAI,aAAa,IAAI,CAAC,SAAS;QAC/B,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK;QAC7B,IAAI,CAAC,SAAS,MAAM,IAAI,EAAE;YACxB,wDAAwD;YACxD,4DAA4D;YAC5D,WAAW,IAAI,GAAG;YAClB,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG;QACtB,OAAO,IAAI,CAAA,GAAA,iJAAA,CAAA,UAAc,AAAD,EAAE,QAAQ;YAChC,IAAI,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,EAAE;gBACvB,MAAM,OAAO;YACf;YACA,KAAK,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,UAAU,CAAC,EAAE;YAC3C,KAAK,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,KAAK,CAAC,EAAE,EAAE,UAAU,CAAC,EAAE;QAC7C;IACF;IACA;;;GAGC,GACD,gBAAgB,SAAS,CAAC,oBAAoB,GAAG;QAC/C,OAAO,SAAS,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,EAAE;QAClD,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,IAAI,CAAC,SAAS,EAAE,SAAU,KAAK;YACzC,IAAI,aAAa,IAAI,CAAC,MAAM,CAAC,UAAU,CAAC,MAAM,CAAC,UAAU;YACzD,IAAI,cAAc,UAAU,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,EAAE;gBACjD,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,GAAG,GAAG,oBAAoB;YACzD;QACF,GAAG,IAAI;IACT;IACA;;GAEC,GACD,gBAAgB,SAAS,CAAC,WAAW,GAAG,SAAU,QAAQ;QACxD,IAAI,CAAC,MAAM,CAAC,KAAK,GAAG,SAAS,KAAK;QAClC,IAAI,CAAC,WAAW;IAClB;IACA;;GAEC,GACD,gBAAgB,SAAS,CAAC,WAAW,GAAG;QACtC,IAAI,aAAa,IAAI,CAAC,SAAS;QAC/B,IAAI,eAAe,CAAA,GAAA,mJAAA,CAAA,MAAc,AAAD,EAAE,CAAC,IAAI,CAAC,GAAG,CAAC,YAAY,EAAE,EAAE,KAAK;QACjE,QAAQ;QACR,YAAY,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE;QACnE,YAAY,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE;QACnE,YAAY,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE;QACnE,YAAY,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE;QACnE,OAAO;IACT;IACA;;GAEC,GACD,gBAAgB,SAAS,CAAC,aAAa,GAAG,SAAU,KAAK;QACvD,IAAI,QAAQ,IAAI,CAAC,MAAM,CAAC,KAAK;QAC7B,IAAI,aAAa,IAAI,CAAC,SAAS;QAC/B,0FAA0F;QAC1F,kCAAkC;QAClC,OAAO,CAAC,KAAK,CAAC,EAAE,IAAI,UAAU,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,KAAK,CAAC,KAAK,CAAC,EAAE,IAAI,UAAU,CAAC,EAAE,IAAI,SAAS,KAAK,CAAC,EAAE,IAAI,YAAY;IAC5H;IACA,gBAAgB,SAAS,CAAC,qBAAqB,GAAG,SAAU,KAAK;QAC/D,IAAI,SAAS,EAAE;QACf,IAAI,CAAC,gBAAgB,CAAC,SAAU,WAAW;YACzC,IAAI,cAAc,EAAE;YACpB,IAAI,OAAO,YAAY,OAAO;YAC9B,KAAK,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,SAAU,KAAK,EAAE,SAAS;gBACpE,KAAK,CAAC,EAAE,IAAI,SAAS,SAAS,KAAK,CAAC,EAAE,IAAI,YAAY,IAAI,CAAC;YAC7D,GAAG,IAAI;YACP,OAAO,IAAI,CAAC;gBACV,UAAU,YAAY,EAAE;gBACxB,WAAW;YACb;QACF,GAAG,IAAI;QACP,OAAO;IACT;IACA;;GAEC,GACD,gBAAgB,SAAS,CAAC,aAAa,GAAG,SAAU,cAAc;QAChE,IAAI,QAAQ,mBAAmB,IAAI,EAAE,cAAc,IAAI,CAAC,SAAS;QACjE,IAAI,QAAQ,mBAAmB,IAAI,EAAE,WAAW,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,KAAK;QACvE,IAAI,QAAQ,EAAE;QACd,SAAS,QAAQ,KAAK,EAAE,UAAU;YAChC,MAAM,IAAI,CAAC;gBACT,OAAO;gBACP,OAAO,eAAe,OAAO;YAC/B;QACF;QACA,kDAAkD;QAClD,IAAI,OAAO;QACX,IAAI,OAAO;QACX,IAAI,OAAO,MAAM,MAAM;QACvB,IAAI,OAAO,MAAM,MAAM;QACvB,MAAO,OAAO,QAAQ,CAAC,CAAC,MAAM,MAAM,IAAI,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,EAAE,GAAG,OAAQ;YACxE,+DAA+D;YAC/D,IAAI,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK,EAAE;gBAC7B,QAAQ,KAAK,CAAC,KAAK,EAAE;YACvB;QACF;QACA,IAAK,IAAI,QAAQ,GAAG,OAAO,MAAM,QAAQ,QAAQ,EAAG;YAClD,2DAA2D;YAC3D,qBAAqB;YACrB,SAAS,MAAM,MAAM,IAAI,QAAQ,KAAK,CAAC,KAAK,EAAE;YAC9C,QAAQ,KAAK,CAAC,KAAK,EAAE;QACvB;QACA,IAAK,IAAI,QAAQ,GAAG,OAAO,MAAM,OAAQ;YACvC,IAAI,CAAC,MAAM,MAAM,IAAI,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,GAAG,KAAK,CAAC,KAAK,EAAE;gBAC1D,qBAAqB;gBACrB,IAAI,OAAO;oBACT,MAAM,MAAM,IAAI,QAAQ,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE,CAAC,KAAK,EAAE;oBACvD,QAAQ;gBACV;gBACA,QAAQ,KAAK,CAAC,KAAK,EAAE;YACvB;QACF;QACA,IAAI,WAAW,MAAM,MAAM;QAC3B,OAAO;YACL,OAAO;YACP,aAAa;gBAAC,WAAW,KAAK,CAAC,EAAE,CAAC,KAAK,GAAG;gBAAe,WAAW,KAAK,CAAC,WAAW,EAAE,CAAC,KAAK,GAAG;aAAc;QAChH;IACF;IACA,gBAAgB,IAAI,GAAG;IACvB,gBAAgB,aAAa,GAAG,CAAA,GAAA,sJAAA,CAAA,uBAAoB,AAAD,EAAE,6KAAA,CAAA,UAAc,CAAC,aAAa,EAAE;QACjF,OAAO;QACP,YAAY;QACZ,WAAW;QACX,UAAU;QACV,YAAY;QACZ,YAAY;QACZ,aAAa;YACX,aAAa;YACb,aAAa;QACf;QACA,eAAe;QACf,eAAe;QACf,gBAAgB;YACd,aAAa;YACb,aAAa;YACb,YAAY;YACZ,eAAe;YACf,eAAe;YACf,aAAa;QACf;IASF;IACA,OAAO;AACT,EAAE,6KAAA,CAAA,UAAc;AAChB,SAAS,mBAAmB,cAAc,EAAE,UAAU,EAAE,UAAU;IAChE,IAAI,UAAU,CAAC,EAAE,KAAK,UAAU,CAAC,EAAE,EAAE;QACnC,OAAO,WAAW,KAAK;IACzB;IACA,gEAAgE;IAChE,6DAA6D;IAC7D,QAAQ;IACR,yDAAyD;IACzD,IAAI,QAAQ;IACZ,IAAI,OAAO,CAAC,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,IAAI;IAC7C,IAAI,QAAQ,UAAU,CAAC,EAAE;IACzB,IAAI,aAAa,EAAE;IACnB,IAAK,IAAI,IAAI,GAAG,KAAK,SAAS,QAAQ,UAAU,CAAC,EAAE,EAAE,IAAK;QACxD,WAAW,IAAI,CAAC;QAChB,SAAS;IACX;IACA,WAAW,IAAI,CAAC,UAAU,CAAC,EAAE;IAC7B,OAAO;AACT;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 708, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/visualMap/VisualMapView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { Rect } from '../../util/graphic.js';\nimport * as formatUtil from '../../util/format.js';\nimport * as layout from '../../util/layout.js';\nimport VisualMapping from '../../visual/VisualMapping.js';\nimport ComponentView from '../../view/Component.js';\nvar VisualMapView = /** @class */function (_super) {\n  __extends(VisualMapView, _super);\n  function VisualMapView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = VisualMapView.type;\n    _this.autoPositionValues = {\n      left: 1,\n      right: 1,\n      top: 1,\n      bottom: 1\n    };\n    return _this;\n  }\n  VisualMapView.prototype.init = function (ecModel, api) {\n    this.ecModel = ecModel;\n    this.api = api;\n  };\n  /**\r\n   * @protected\r\n   */\n  VisualMapView.prototype.render = function (visualMapModel, ecModel, api, payload // TODO: TYPE\n  ) {\n    this.visualMapModel = visualMapModel;\n    if (visualMapModel.get('show') === false) {\n      this.group.removeAll();\n      return;\n    }\n    this.doRender(visualMapModel, ecModel, api, payload);\n  };\n  /**\r\n   * @protected\r\n   */\n  VisualMapView.prototype.renderBackground = function (group) {\n    var visualMapModel = this.visualMapModel;\n    var padding = formatUtil.normalizeCssArray(visualMapModel.get('padding') || 0);\n    var rect = group.getBoundingRect();\n    group.add(new Rect({\n      z2: -1,\n      silent: true,\n      shape: {\n        x: rect.x - padding[3],\n        y: rect.y - padding[0],\n        width: rect.width + padding[3] + padding[1],\n        height: rect.height + padding[0] + padding[2]\n      },\n      style: {\n        fill: visualMapModel.get('backgroundColor'),\n        stroke: visualMapModel.get('borderColor'),\n        lineWidth: visualMapModel.get('borderWidth')\n      }\n    }));\n  };\n  /**\r\n   * @protected\r\n   * @param targetValue can be Infinity or -Infinity\r\n   * @param visualCluster Only can be 'color' 'opacity' 'symbol' 'symbolSize'\r\n   * @param opts\r\n   * @param opts.forceState Specify state, instead of using getValueState method.\r\n   * @param opts.convertOpacityToAlpha For color gradient in controller widget.\r\n   * @return {*} Visual value.\r\n   */\n  VisualMapView.prototype.getControllerVisual = function (targetValue, visualCluster, opts) {\n    opts = opts || {};\n    var forceState = opts.forceState;\n    var visualMapModel = this.visualMapModel;\n    var visualObj = {};\n    // Default values.\n    if (visualCluster === 'color') {\n      var defaultColor = visualMapModel.get('contentColor');\n      visualObj.color = defaultColor;\n    }\n    function getter(key) {\n      return visualObj[key];\n    }\n    function setter(key, value) {\n      visualObj[key] = value;\n    }\n    var mappings = visualMapModel.controllerVisuals[forceState || visualMapModel.getValueState(targetValue)];\n    var visualTypes = VisualMapping.prepareVisualTypes(mappings);\n    zrUtil.each(visualTypes, function (type) {\n      var visualMapping = mappings[type];\n      if (opts.convertOpacityToAlpha && type === 'opacity') {\n        type = 'colorAlpha';\n        visualMapping = mappings.__alphaForOpacity;\n      }\n      if (VisualMapping.dependsOn(type, visualCluster)) {\n        visualMapping && visualMapping.applyVisual(targetValue, getter, setter);\n      }\n    });\n    return visualObj[visualCluster];\n  };\n  VisualMapView.prototype.positionGroup = function (group) {\n    var model = this.visualMapModel;\n    var api = this.api;\n    layout.positionElement(group, model.getBoxLayoutParams(), {\n      width: api.getWidth(),\n      height: api.getHeight()\n    });\n  };\n  VisualMapView.prototype.doRender = function (visualMapModel, ecModel, api, payload) {};\n  VisualMapView.type = 'visualMap';\n  return VisualMapView;\n}(ComponentView);\nexport default VisualMapView;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACA,IAAI,gBAAgB,WAAW,GAAE,SAAU,MAAM;IAC/C,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,eAAe;IACzB,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,cAAc,IAAI;QAC/B,MAAM,kBAAkB,GAAG;YACzB,MAAM;YACN,OAAO;YACP,KAAK;YACL,QAAQ;QACV;QACA,OAAO;IACT;IACA,cAAc,SAAS,CAAC,IAAI,GAAG,SAAU,OAAO,EAAE,GAAG;QACnD,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,GAAG,GAAG;IACb;IACA;;GAEC,GACD,cAAc,SAAS,CAAC,MAAM,GAAG,SAAU,cAAc,EAAE,OAAO,EAAE,GAAG,EAAE,QAAQ,aAAa;IAAd;QAE9E,IAAI,CAAC,cAAc,GAAG;QACtB,IAAI,eAAe,GAAG,CAAC,YAAY,OAAO;YACxC,IAAI,CAAC,KAAK,CAAC,SAAS;YACpB;QACF;QACA,IAAI,CAAC,QAAQ,CAAC,gBAAgB,SAAS,KAAK;IAC9C;IACA;;GAEC,GACD,cAAc,SAAS,CAAC,gBAAgB,GAAG,SAAU,KAAK;QACxD,IAAI,iBAAiB,IAAI,CAAC,cAAc;QACxC,IAAI,UAAU,CAAA,GAAA,mKAAA,CAAA,oBAA4B,AAAD,EAAE,eAAe,GAAG,CAAC,cAAc;QAC5E,IAAI,OAAO,MAAM,eAAe;QAChC,MAAM,GAAG,CAAC,IAAI,gMAAA,CAAA,OAAI,CAAC;YACjB,IAAI,CAAC;YACL,QAAQ;YACR,OAAO;gBACL,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE;gBACtB,GAAG,KAAK,CAAC,GAAG,OAAO,CAAC,EAAE;gBACtB,OAAO,KAAK,KAAK,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;gBAC3C,QAAQ,KAAK,MAAM,GAAG,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;YAC/C;YACA,OAAO;gBACL,MAAM,eAAe,GAAG,CAAC;gBACzB,QAAQ,eAAe,GAAG,CAAC;gBAC3B,WAAW,eAAe,GAAG,CAAC;YAChC;QACF;IACF;IACA;;;;;;;;GAQC,GACD,cAAc,SAAS,CAAC,mBAAmB,GAAG,SAAU,WAAW,EAAE,aAAa,EAAE,IAAI;QACtF,OAAO,QAAQ,CAAC;QAChB,IAAI,aAAa,KAAK,UAAU;QAChC,IAAI,iBAAiB,IAAI,CAAC,cAAc;QACxC,IAAI,YAAY,CAAC;QACjB,kBAAkB;QAClB,IAAI,kBAAkB,SAAS;YAC7B,IAAI,eAAe,eAAe,GAAG,CAAC;YACtC,UAAU,KAAK,GAAG;QACpB;QACA,SAAS,OAAO,GAAG;YACjB,OAAO,SAAS,CAAC,IAAI;QACvB;QACA,SAAS,OAAO,GAAG,EAAE,KAAK;YACxB,SAAS,CAAC,IAAI,GAAG;QACnB;QACA,IAAI,WAAW,eAAe,iBAAiB,CAAC,cAAc,eAAe,aAAa,CAAC,aAAa;QACxG,IAAI,cAAc,4JAAA,CAAA,UAAa,CAAC,kBAAkB,CAAC;QACnD,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,aAAa,SAAU,IAAI;YACrC,IAAI,gBAAgB,QAAQ,CAAC,KAAK;YAClC,IAAI,KAAK,qBAAqB,IAAI,SAAS,WAAW;gBACpD,OAAO;gBACP,gBAAgB,SAAS,iBAAiB;YAC5C;YACA,IAAI,4JAAA,CAAA,UAAa,CAAC,SAAS,CAAC,MAAM,gBAAgB;gBAChD,iBAAiB,cAAc,WAAW,CAAC,aAAa,QAAQ;YAClE;QACF;QACA,OAAO,SAAS,CAAC,cAAc;IACjC;IACA,cAAc,SAAS,CAAC,aAAa,GAAG,SAAU,KAAK;QACrD,IAAI,QAAQ,IAAI,CAAC,cAAc;QAC/B,IAAI,MAAM,IAAI,CAAC,GAAG;QAClB,CAAA,GAAA,mJAAA,CAAA,kBAAsB,AAAD,EAAE,OAAO,MAAM,kBAAkB,IAAI;YACxD,OAAO,IAAI,QAAQ;YACnB,QAAQ,IAAI,SAAS;QACvB;IACF;IACA,cAAc,SAAS,CAAC,QAAQ,GAAG,SAAU,cAAc,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO,GAAG;IACrF,cAAc,IAAI,GAAG;IACrB,OAAO;AACT,EAAE,sJAAA,CAAA,UAAa;uCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 868, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/visualMap/helper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport { getLayoutRect } from '../../util/layout.js';\nvar paramsSet = [['left', 'right', 'width'], ['top', 'bottom', 'height']];\n/**\r\n * @param visualMapModel\r\n * @param api\r\n * @param itemSize always [short, long]\r\n * @return {string} 'left' or 'right' or 'top' or 'bottom'\r\n */\nexport function getItemAlign(visualMapModel, api, itemSize) {\n  var modelOption = visualMapModel.option;\n  var itemAlign = modelOption.align;\n  if (itemAlign != null && itemAlign !== 'auto') {\n    return itemAlign;\n  }\n  // Auto decision align.\n  var ecSize = {\n    width: api.getWidth(),\n    height: api.getHeight()\n  };\n  var realIndex = modelOption.orient === 'horizontal' ? 1 : 0;\n  var reals = paramsSet[realIndex];\n  var fakeValue = [0, null, 10];\n  var layoutInput = {};\n  for (var i = 0; i < 3; i++) {\n    layoutInput[paramsSet[1 - realIndex][i]] = fakeValue[i];\n    layoutInput[reals[i]] = i === 2 ? itemSize[0] : modelOption[reals[i]];\n  }\n  var rParam = [['x', 'width', 3], ['y', 'height', 0]][realIndex];\n  var rect = getLayoutRect(layoutInput, ecSize, modelOption.padding);\n  return reals[(rect.margin[rParam[2]] || 0) + rect[rParam[0]] + rect[rParam[1]] * 0.5 < ecSize[rParam[1]] * 0.5 ? 0 : 1];\n}\n/**\r\n * Prepare dataIndex for outside usage, where dataIndex means rawIndex, and\r\n * dataIndexInside means filtered index.\r\n */\n// TODO: TYPE more specified payload types.\nexport function makeHighDownBatch(batch, visualMapModel) {\n  zrUtil.each(batch || [], function (batchItem) {\n    if (batchItem.dataIndex != null) {\n      batchItem.dataIndexInside = batchItem.dataIndex;\n      batchItem.dataIndex = null;\n    }\n    batchItem.highlightKey = 'visualMap' + (visualMapModel ? visualMapModel.componentIndex : '');\n  });\n  return batch;\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;AACA;AACA;;;AACA,IAAI,YAAY;IAAC;QAAC;QAAQ;QAAS;KAAQ;IAAE;QAAC;QAAO;QAAU;KAAS;CAAC;AAOlE,SAAS,aAAa,cAAc,EAAE,GAAG,EAAE,QAAQ;IACxD,IAAI,cAAc,eAAe,MAAM;IACvC,IAAI,YAAY,YAAY,KAAK;IACjC,IAAI,aAAa,QAAQ,cAAc,QAAQ;QAC7C,OAAO;IACT;IACA,uBAAuB;IACvB,IAAI,SAAS;QACX,OAAO,IAAI,QAAQ;QACnB,QAAQ,IAAI,SAAS;IACvB;IACA,IAAI,YAAY,YAAY,MAAM,KAAK,eAAe,IAAI;IAC1D,IAAI,QAAQ,SAAS,CAAC,UAAU;IAChC,IAAI,YAAY;QAAC;QAAG;QAAM;KAAG;IAC7B,IAAI,cAAc,CAAC;IACnB,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,WAAW,CAAC,SAAS,CAAC,IAAI,UAAU,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE;QACvD,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,MAAM,IAAI,QAAQ,CAAC,EAAE,GAAG,WAAW,CAAC,KAAK,CAAC,EAAE,CAAC;IACvE;IACA,IAAI,SAAS;QAAC;YAAC;YAAK;YAAS;SAAE;QAAE;YAAC;YAAK;YAAU;SAAE;KAAC,CAAC,UAAU;IAC/D,IAAI,OAAO,CAAA,GAAA,mJAAA,CAAA,gBAAa,AAAD,EAAE,aAAa,QAAQ,YAAY,OAAO;IACjE,OAAO,KAAK,CAAC,CAAC,KAAK,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,MAAM,CAAC,MAAM,CAAC,EAAE,CAAC,GAAG,MAAM,IAAI,EAAE;AACzH;AAMO,SAAS,kBAAkB,KAAK,EAAE,cAAc;IACrD,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,SAAS,EAAE,EAAE,SAAU,SAAS;QAC1C,IAAI,UAAU,SAAS,IAAI,MAAM;YAC/B,UAAU,eAAe,GAAG,UAAU,SAAS;YAC/C,UAAU,SAAS,GAAG;QACxB;QACA,UAAU,YAAY,GAAG,cAAc,CAAC,iBAAiB,eAAe,cAAc,GAAG,EAAE;IAC7F;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 978, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/visualMap/ContinuousView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport LinearGradient from 'zrender/lib/graphic/LinearGradient.js';\nimport * as eventTool from 'zrender/lib/core/event.js';\nimport VisualMapView from './VisualMapView.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as numberUtil from '../../util/number.js';\nimport sliderMove from '../helper/sliderMove.js';\nimport * as helper from './helper.js';\nimport * as modelUtil from '../../util/model.js';\nimport { parsePercent } from 'zrender/lib/contain/text.js';\nimport { setAsHighDownDispatcher } from '../../util/states.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport ZRImage from 'zrender/lib/graphic/Image.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport { findEventDispatcher } from '../../util/event.js';\nvar linearMap = numberUtil.linearMap;\nvar each = zrUtil.each;\nvar mathMin = Math.min;\nvar mathMax = Math.max;\n// Arbitrary value\nvar HOVER_LINK_SIZE = 12;\nvar HOVER_LINK_OUT = 6;\n// Notice:\n// Any \"interval\" should be by the order of [low, high].\n// \"handle0\" (handleIndex === 0) maps to\n// low data value: this._dataInterval[0] and has low coord.\n// \"handle1\" (handleIndex === 1) maps to\n// high data value: this._dataInterval[1] and has high coord.\n// The logic of transform is implemented in this._createBarGroup.\nvar ContinuousView = /** @class */function (_super) {\n  __extends(ContinuousView, _super);\n  function ContinuousView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ContinuousView.type;\n    _this._shapes = {};\n    _this._dataInterval = [];\n    _this._handleEnds = [];\n    _this._hoverLinkDataIndices = [];\n    return _this;\n  }\n  ContinuousView.prototype.init = function (ecModel, api) {\n    _super.prototype.init.call(this, ecModel, api);\n    this._hoverLinkFromSeriesMouseOver = zrUtil.bind(this._hoverLinkFromSeriesMouseOver, this);\n    this._hideIndicator = zrUtil.bind(this._hideIndicator, this);\n  };\n  ContinuousView.prototype.doRender = function (visualMapModel, ecModel, api, payload) {\n    if (!payload || payload.type !== 'selectDataRange' || payload.from !== this.uid) {\n      this._buildView();\n    }\n  };\n  ContinuousView.prototype._buildView = function () {\n    this.group.removeAll();\n    var visualMapModel = this.visualMapModel;\n    var thisGroup = this.group;\n    this._orient = visualMapModel.get('orient');\n    this._useHandle = visualMapModel.get('calculable');\n    this._resetInterval();\n    this._renderBar(thisGroup);\n    var dataRangeText = visualMapModel.get('text');\n    this._renderEndsText(thisGroup, dataRangeText, 0);\n    this._renderEndsText(thisGroup, dataRangeText, 1);\n    // Do this for background size calculation.\n    this._updateView(true);\n    // After updating view, inner shapes is built completely,\n    // and then background can be rendered.\n    this.renderBackground(thisGroup);\n    // Real update view\n    this._updateView();\n    this._enableHoverLinkToSeries();\n    this._enableHoverLinkFromSeries();\n    this.positionGroup(thisGroup);\n  };\n  ContinuousView.prototype._renderEndsText = function (group, dataRangeText, endsIndex) {\n    if (!dataRangeText) {\n      return;\n    }\n    // Compatible with ec2, text[0] map to high value, text[1] map low value.\n    var text = dataRangeText[1 - endsIndex];\n    text = text != null ? text + '' : '';\n    var visualMapModel = this.visualMapModel;\n    var textGap = visualMapModel.get('textGap');\n    var itemSize = visualMapModel.itemSize;\n    var barGroup = this._shapes.mainGroup;\n    var position = this._applyTransform([itemSize[0] / 2, endsIndex === 0 ? -textGap : itemSize[1] + textGap], barGroup);\n    var align = this._applyTransform(endsIndex === 0 ? 'bottom' : 'top', barGroup);\n    var orient = this._orient;\n    var textStyleModel = this.visualMapModel.textStyleModel;\n    this.group.add(new graphic.Text({\n      style: createTextStyle(textStyleModel, {\n        x: position[0],\n        y: position[1],\n        verticalAlign: orient === 'horizontal' ? 'middle' : align,\n        align: orient === 'horizontal' ? align : 'center',\n        text: text\n      })\n    }));\n  };\n  ContinuousView.prototype._renderBar = function (targetGroup) {\n    var visualMapModel = this.visualMapModel;\n    var shapes = this._shapes;\n    var itemSize = visualMapModel.itemSize;\n    var orient = this._orient;\n    var useHandle = this._useHandle;\n    var itemAlign = helper.getItemAlign(visualMapModel, this.api, itemSize);\n    var mainGroup = shapes.mainGroup = this._createBarGroup(itemAlign);\n    var gradientBarGroup = new graphic.Group();\n    mainGroup.add(gradientBarGroup);\n    // Bar\n    gradientBarGroup.add(shapes.outOfRange = createPolygon());\n    gradientBarGroup.add(shapes.inRange = createPolygon(null, useHandle ? getCursor(this._orient) : null, zrUtil.bind(this._dragHandle, this, 'all', false), zrUtil.bind(this._dragHandle, this, 'all', true)));\n    // A border radius clip.\n    gradientBarGroup.setClipPath(new graphic.Rect({\n      shape: {\n        x: 0,\n        y: 0,\n        width: itemSize[0],\n        height: itemSize[1],\n        r: 3\n      }\n    }));\n    var textRect = visualMapModel.textStyleModel.getTextRect('国');\n    var textSize = mathMax(textRect.width, textRect.height);\n    // Handle\n    if (useHandle) {\n      shapes.handleThumbs = [];\n      shapes.handleLabels = [];\n      shapes.handleLabelPoints = [];\n      this._createHandle(visualMapModel, mainGroup, 0, itemSize, textSize, orient);\n      this._createHandle(visualMapModel, mainGroup, 1, itemSize, textSize, orient);\n    }\n    this._createIndicator(visualMapModel, mainGroup, itemSize, textSize, orient);\n    targetGroup.add(mainGroup);\n  };\n  ContinuousView.prototype._createHandle = function (visualMapModel, mainGroup, handleIndex, itemSize, textSize, orient) {\n    var onDrift = zrUtil.bind(this._dragHandle, this, handleIndex, false);\n    var onDragEnd = zrUtil.bind(this._dragHandle, this, handleIndex, true);\n    var handleSize = parsePercent(visualMapModel.get('handleSize'), itemSize[0]);\n    var handleThumb = createSymbol(visualMapModel.get('handleIcon'), -handleSize / 2, -handleSize / 2, handleSize, handleSize, null, true);\n    var cursor = getCursor(this._orient);\n    handleThumb.attr({\n      cursor: cursor,\n      draggable: true,\n      drift: onDrift,\n      ondragend: onDragEnd,\n      onmousemove: function (e) {\n        eventTool.stop(e.event);\n      }\n    });\n    handleThumb.x = itemSize[0] / 2;\n    handleThumb.useStyle(visualMapModel.getModel('handleStyle').getItemStyle());\n    handleThumb.setStyle({\n      strokeNoScale: true,\n      strokeFirst: true\n    });\n    handleThumb.style.lineWidth *= 2;\n    handleThumb.ensureState('emphasis').style = visualMapModel.getModel(['emphasis', 'handleStyle']).getItemStyle();\n    setAsHighDownDispatcher(handleThumb, true);\n    mainGroup.add(handleThumb);\n    // Text is always horizontal layout but should not be effected by\n    // transform (orient/inverse). So label is built separately but not\n    // use zrender/graphic/helper/RectText, and is located based on view\n    // group (according to handleLabelPoint) but not barGroup.\n    var textStyleModel = this.visualMapModel.textStyleModel;\n    var handleLabel = new graphic.Text({\n      cursor: cursor,\n      draggable: true,\n      drift: onDrift,\n      onmousemove: function (e) {\n        // For mobile device, prevent screen slider on the button.\n        eventTool.stop(e.event);\n      },\n      ondragend: onDragEnd,\n      style: createTextStyle(textStyleModel, {\n        x: 0,\n        y: 0,\n        text: ''\n      })\n    });\n    handleLabel.ensureState('blur').style = {\n      opacity: 0.1\n    };\n    handleLabel.stateTransition = {\n      duration: 200\n    };\n    this.group.add(handleLabel);\n    var handleLabelPoint = [handleSize, 0];\n    var shapes = this._shapes;\n    shapes.handleThumbs[handleIndex] = handleThumb;\n    shapes.handleLabelPoints[handleIndex] = handleLabelPoint;\n    shapes.handleLabels[handleIndex] = handleLabel;\n  };\n  ContinuousView.prototype._createIndicator = function (visualMapModel, mainGroup, itemSize, textSize, orient) {\n    var scale = parsePercent(visualMapModel.get('indicatorSize'), itemSize[0]);\n    var indicator = createSymbol(visualMapModel.get('indicatorIcon'), -scale / 2, -scale / 2, scale, scale, null, true);\n    indicator.attr({\n      cursor: 'move',\n      invisible: true,\n      silent: true,\n      x: itemSize[0] / 2\n    });\n    var indicatorStyle = visualMapModel.getModel('indicatorStyle').getItemStyle();\n    if (indicator instanceof ZRImage) {\n      var pathStyle = indicator.style;\n      indicator.useStyle(zrUtil.extend({\n        // TODO other properties like x, y ?\n        image: pathStyle.image,\n        x: pathStyle.x,\n        y: pathStyle.y,\n        width: pathStyle.width,\n        height: pathStyle.height\n      }, indicatorStyle));\n    } else {\n      indicator.useStyle(indicatorStyle);\n    }\n    mainGroup.add(indicator);\n    var textStyleModel = this.visualMapModel.textStyleModel;\n    var indicatorLabel = new graphic.Text({\n      silent: true,\n      invisible: true,\n      style: createTextStyle(textStyleModel, {\n        x: 0,\n        y: 0,\n        text: ''\n      })\n    });\n    this.group.add(indicatorLabel);\n    var indicatorLabelPoint = [(orient === 'horizontal' ? textSize / 2 : HOVER_LINK_OUT) + itemSize[0] / 2, 0];\n    var shapes = this._shapes;\n    shapes.indicator = indicator;\n    shapes.indicatorLabel = indicatorLabel;\n    shapes.indicatorLabelPoint = indicatorLabelPoint;\n    this._firstShowIndicator = true;\n  };\n  ContinuousView.prototype._dragHandle = function (handleIndex, isEnd,\n  // dx is event from ondragend if isEnd is true. It's not used\n  dx, dy) {\n    if (!this._useHandle) {\n      return;\n    }\n    this._dragging = !isEnd;\n    if (!isEnd) {\n      // Transform dx, dy to bar coordination.\n      var vertex = this._applyTransform([dx, dy], this._shapes.mainGroup, true);\n      this._updateInterval(handleIndex, vertex[1]);\n      this._hideIndicator();\n      // Considering realtime, update view should be executed\n      // before dispatch action.\n      this._updateView();\n    }\n    // dragEnd do not dispatch action when realtime.\n    if (isEnd === !this.visualMapModel.get('realtime')) {\n      // jshint ignore:line\n      this.api.dispatchAction({\n        type: 'selectDataRange',\n        from: this.uid,\n        visualMapId: this.visualMapModel.id,\n        selected: this._dataInterval.slice()\n      });\n    }\n    if (isEnd) {\n      !this._hovering && this._clearHoverLinkToSeries();\n    } else if (useHoverLinkOnHandle(this.visualMapModel)) {\n      this._doHoverLinkToSeries(this._handleEnds[handleIndex], false);\n    }\n  };\n  ContinuousView.prototype._resetInterval = function () {\n    var visualMapModel = this.visualMapModel;\n    var dataInterval = this._dataInterval = visualMapModel.getSelected();\n    var dataExtent = visualMapModel.getExtent();\n    var sizeExtent = [0, visualMapModel.itemSize[1]];\n    this._handleEnds = [linearMap(dataInterval[0], dataExtent, sizeExtent, true), linearMap(dataInterval[1], dataExtent, sizeExtent, true)];\n  };\n  /**\r\n   * @private\r\n   * @param {(number|string)} handleIndex 0 or 1 or 'all'\r\n   * @param {number} dx\r\n   * @param {number} dy\r\n   */\n  ContinuousView.prototype._updateInterval = function (handleIndex, delta) {\n    delta = delta || 0;\n    var visualMapModel = this.visualMapModel;\n    var handleEnds = this._handleEnds;\n    var sizeExtent = [0, visualMapModel.itemSize[1]];\n    sliderMove(delta, handleEnds, sizeExtent, handleIndex,\n    // cross is forbidden\n    0);\n    var dataExtent = visualMapModel.getExtent();\n    // Update data interval.\n    this._dataInterval = [linearMap(handleEnds[0], sizeExtent, dataExtent, true), linearMap(handleEnds[1], sizeExtent, dataExtent, true)];\n  };\n  ContinuousView.prototype._updateView = function (forSketch) {\n    var visualMapModel = this.visualMapModel;\n    var dataExtent = visualMapModel.getExtent();\n    var shapes = this._shapes;\n    var outOfRangeHandleEnds = [0, visualMapModel.itemSize[1]];\n    var inRangeHandleEnds = forSketch ? outOfRangeHandleEnds : this._handleEnds;\n    var visualInRange = this._createBarVisual(this._dataInterval, dataExtent, inRangeHandleEnds, 'inRange');\n    var visualOutOfRange = this._createBarVisual(dataExtent, dataExtent, outOfRangeHandleEnds, 'outOfRange');\n    shapes.inRange.setStyle({\n      fill: visualInRange.barColor\n      // opacity: visualInRange.opacity\n    }).setShape('points', visualInRange.barPoints);\n    shapes.outOfRange.setStyle({\n      fill: visualOutOfRange.barColor\n      // opacity: visualOutOfRange.opacity\n    }).setShape('points', visualOutOfRange.barPoints);\n    this._updateHandle(inRangeHandleEnds, visualInRange);\n  };\n  ContinuousView.prototype._createBarVisual = function (dataInterval, dataExtent, handleEnds, forceState) {\n    var opts = {\n      forceState: forceState,\n      convertOpacityToAlpha: true\n    };\n    var colorStops = this._makeColorGradient(dataInterval, opts);\n    var symbolSizes = [this.getControllerVisual(dataInterval[0], 'symbolSize', opts), this.getControllerVisual(dataInterval[1], 'symbolSize', opts)];\n    var barPoints = this._createBarPoints(handleEnds, symbolSizes);\n    return {\n      barColor: new LinearGradient(0, 0, 0, 1, colorStops),\n      barPoints: barPoints,\n      handlesColor: [colorStops[0].color, colorStops[colorStops.length - 1].color]\n    };\n  };\n  ContinuousView.prototype._makeColorGradient = function (dataInterval, opts) {\n    // Considering colorHue, which is not linear, so we have to sample\n    // to calculate gradient color stops, but not only calculate head\n    // and tail.\n    var sampleNumber = 100; // Arbitrary value.\n    var colorStops = [];\n    var step = (dataInterval[1] - dataInterval[0]) / sampleNumber;\n    colorStops.push({\n      color: this.getControllerVisual(dataInterval[0], 'color', opts),\n      offset: 0\n    });\n    for (var i = 1; i < sampleNumber; i++) {\n      var currValue = dataInterval[0] + step * i;\n      if (currValue > dataInterval[1]) {\n        break;\n      }\n      colorStops.push({\n        color: this.getControllerVisual(currValue, 'color', opts),\n        offset: i / sampleNumber\n      });\n    }\n    colorStops.push({\n      color: this.getControllerVisual(dataInterval[1], 'color', opts),\n      offset: 1\n    });\n    return colorStops;\n  };\n  ContinuousView.prototype._createBarPoints = function (handleEnds, symbolSizes) {\n    var itemSize = this.visualMapModel.itemSize;\n    return [[itemSize[0] - symbolSizes[0], handleEnds[0]], [itemSize[0], handleEnds[0]], [itemSize[0], handleEnds[1]], [itemSize[0] - symbolSizes[1], handleEnds[1]]];\n  };\n  ContinuousView.prototype._createBarGroup = function (itemAlign) {\n    var orient = this._orient;\n    var inverse = this.visualMapModel.get('inverse');\n    return new graphic.Group(orient === 'horizontal' && !inverse ? {\n      scaleX: itemAlign === 'bottom' ? 1 : -1,\n      rotation: Math.PI / 2\n    } : orient === 'horizontal' && inverse ? {\n      scaleX: itemAlign === 'bottom' ? -1 : 1,\n      rotation: -Math.PI / 2\n    } : orient === 'vertical' && !inverse ? {\n      scaleX: itemAlign === 'left' ? 1 : -1,\n      scaleY: -1\n    } : {\n      scaleX: itemAlign === 'left' ? 1 : -1\n    });\n  };\n  ContinuousView.prototype._updateHandle = function (handleEnds, visualInRange) {\n    if (!this._useHandle) {\n      return;\n    }\n    var shapes = this._shapes;\n    var visualMapModel = this.visualMapModel;\n    var handleThumbs = shapes.handleThumbs;\n    var handleLabels = shapes.handleLabels;\n    var itemSize = visualMapModel.itemSize;\n    var dataExtent = visualMapModel.getExtent();\n    var align = this._applyTransform('left', shapes.mainGroup);\n    each([0, 1], function (handleIndex) {\n      var handleThumb = handleThumbs[handleIndex];\n      handleThumb.setStyle('fill', visualInRange.handlesColor[handleIndex]);\n      handleThumb.y = handleEnds[handleIndex];\n      var val = linearMap(handleEnds[handleIndex], [0, itemSize[1]], dataExtent, true);\n      var symbolSize = this.getControllerVisual(val, 'symbolSize');\n      handleThumb.scaleX = handleThumb.scaleY = symbolSize / itemSize[0];\n      handleThumb.x = itemSize[0] - symbolSize / 2;\n      // Update handle label position.\n      var textPoint = graphic.applyTransform(shapes.handleLabelPoints[handleIndex], graphic.getTransform(handleThumb, this.group));\n      if (this._orient === 'horizontal') {\n        // If visualMap controls symbol size, an additional offset needs to be added to labels to avoid collision at minimum size.\n        // Offset reaches value of 0 at \"maximum\" position, so maximum position is not altered at all.\n        var minimumOffset = align === 'left' || align === 'top' ? (itemSize[0] - symbolSize) / 2 : (itemSize[0] - symbolSize) / -2;\n        textPoint[1] += minimumOffset;\n      }\n      handleLabels[handleIndex].setStyle({\n        x: textPoint[0],\n        y: textPoint[1],\n        text: visualMapModel.formatValueText(this._dataInterval[handleIndex]),\n        verticalAlign: 'middle',\n        align: this._orient === 'vertical' ? this._applyTransform('left', shapes.mainGroup) : 'center'\n      });\n    }, this);\n  };\n  ContinuousView.prototype._showIndicator = function (cursorValue, textValue, rangeSymbol, halfHoverLinkSize) {\n    var visualMapModel = this.visualMapModel;\n    var dataExtent = visualMapModel.getExtent();\n    var itemSize = visualMapModel.itemSize;\n    var sizeExtent = [0, itemSize[1]];\n    var shapes = this._shapes;\n    var indicator = shapes.indicator;\n    if (!indicator) {\n      return;\n    }\n    indicator.attr('invisible', false);\n    var opts = {\n      convertOpacityToAlpha: true\n    };\n    var color = this.getControllerVisual(cursorValue, 'color', opts);\n    var symbolSize = this.getControllerVisual(cursorValue, 'symbolSize');\n    var y = linearMap(cursorValue, dataExtent, sizeExtent, true);\n    var x = itemSize[0] - symbolSize / 2;\n    var oldIndicatorPos = {\n      x: indicator.x,\n      y: indicator.y\n    };\n    // Update handle label position.\n    indicator.y = y;\n    indicator.x = x;\n    var textPoint = graphic.applyTransform(shapes.indicatorLabelPoint, graphic.getTransform(indicator, this.group));\n    var indicatorLabel = shapes.indicatorLabel;\n    indicatorLabel.attr('invisible', false);\n    var align = this._applyTransform('left', shapes.mainGroup);\n    var orient = this._orient;\n    var isHorizontal = orient === 'horizontal';\n    indicatorLabel.setStyle({\n      text: (rangeSymbol ? rangeSymbol : '') + visualMapModel.formatValueText(textValue),\n      verticalAlign: isHorizontal ? align : 'middle',\n      align: isHorizontal ? 'center' : align\n    });\n    var indicatorNewProps = {\n      x: x,\n      y: y,\n      style: {\n        fill: color\n      }\n    };\n    var labelNewProps = {\n      style: {\n        x: textPoint[0],\n        y: textPoint[1]\n      }\n    };\n    if (visualMapModel.ecModel.isAnimationEnabled() && !this._firstShowIndicator) {\n      var animationCfg = {\n        duration: 100,\n        easing: 'cubicInOut',\n        additive: true\n      };\n      indicator.x = oldIndicatorPos.x;\n      indicator.y = oldIndicatorPos.y;\n      indicator.animateTo(indicatorNewProps, animationCfg);\n      indicatorLabel.animateTo(labelNewProps, animationCfg);\n    } else {\n      indicator.attr(indicatorNewProps);\n      indicatorLabel.attr(labelNewProps);\n    }\n    this._firstShowIndicator = false;\n    var handleLabels = this._shapes.handleLabels;\n    if (handleLabels) {\n      for (var i = 0; i < handleLabels.length; i++) {\n        // Fade out handle labels.\n        // NOTE: Must use api enter/leave on emphasis/blur/select state. Or the global states manager will change it.\n        this.api.enterBlur(handleLabels[i]);\n      }\n    }\n  };\n  ContinuousView.prototype._enableHoverLinkToSeries = function () {\n    var self = this;\n    this._shapes.mainGroup.on('mousemove', function (e) {\n      self._hovering = true;\n      if (!self._dragging) {\n        var itemSize = self.visualMapModel.itemSize;\n        var pos = self._applyTransform([e.offsetX, e.offsetY], self._shapes.mainGroup, true, true);\n        // For hover link show when hover handle, which might be\n        // below or upper than sizeExtent.\n        pos[1] = mathMin(mathMax(0, pos[1]), itemSize[1]);\n        self._doHoverLinkToSeries(pos[1], 0 <= pos[0] && pos[0] <= itemSize[0]);\n      }\n    }).on('mouseout', function () {\n      // When mouse is out of handle, hoverLink still need\n      // to be displayed when realtime is set as false.\n      self._hovering = false;\n      !self._dragging && self._clearHoverLinkToSeries();\n    });\n  };\n  ContinuousView.prototype._enableHoverLinkFromSeries = function () {\n    var zr = this.api.getZr();\n    if (this.visualMapModel.option.hoverLink) {\n      zr.on('mouseover', this._hoverLinkFromSeriesMouseOver, this);\n      zr.on('mouseout', this._hideIndicator, this);\n    } else {\n      this._clearHoverLinkFromSeries();\n    }\n  };\n  ContinuousView.prototype._doHoverLinkToSeries = function (cursorPos, hoverOnBar) {\n    var visualMapModel = this.visualMapModel;\n    var itemSize = visualMapModel.itemSize;\n    if (!visualMapModel.option.hoverLink) {\n      return;\n    }\n    var sizeExtent = [0, itemSize[1]];\n    var dataExtent = visualMapModel.getExtent();\n    // For hover link show when hover handle, which might be below or upper than sizeExtent.\n    cursorPos = mathMin(mathMax(sizeExtent[0], cursorPos), sizeExtent[1]);\n    var halfHoverLinkSize = getHalfHoverLinkSize(visualMapModel, dataExtent, sizeExtent);\n    var hoverRange = [cursorPos - halfHoverLinkSize, cursorPos + halfHoverLinkSize];\n    var cursorValue = linearMap(cursorPos, sizeExtent, dataExtent, true);\n    var valueRange = [linearMap(hoverRange[0], sizeExtent, dataExtent, true), linearMap(hoverRange[1], sizeExtent, dataExtent, true)];\n    // Consider data range is out of visualMap range, see test/visualMap-continuous.html,\n    // where china and india has very large population.\n    hoverRange[0] < sizeExtent[0] && (valueRange[0] = -Infinity);\n    hoverRange[1] > sizeExtent[1] && (valueRange[1] = Infinity);\n    // Do not show indicator when mouse is over handle,\n    // otherwise labels overlap, especially when dragging.\n    if (hoverOnBar) {\n      if (valueRange[0] === -Infinity) {\n        this._showIndicator(cursorValue, valueRange[1], '< ', halfHoverLinkSize);\n      } else if (valueRange[1] === Infinity) {\n        this._showIndicator(cursorValue, valueRange[0], '> ', halfHoverLinkSize);\n      } else {\n        this._showIndicator(cursorValue, cursorValue, '≈ ', halfHoverLinkSize);\n      }\n    }\n    // When realtime is set as false, handles, which are in barGroup,\n    // also trigger hoverLink, which help user to realize where they\n    // focus on when dragging. (see test/heatmap-large.html)\n    // When realtime is set as true, highlight will not show when hover\n    // handle, because the label on handle, which displays a exact value\n    // but not range, might mislead users.\n    var oldBatch = this._hoverLinkDataIndices;\n    var newBatch = [];\n    if (hoverOnBar || useHoverLinkOnHandle(visualMapModel)) {\n      newBatch = this._hoverLinkDataIndices = visualMapModel.findTargetDataIndices(valueRange);\n    }\n    var resultBatches = modelUtil.compressBatches(oldBatch, newBatch);\n    this._dispatchHighDown('downplay', helper.makeHighDownBatch(resultBatches[0], visualMapModel));\n    this._dispatchHighDown('highlight', helper.makeHighDownBatch(resultBatches[1], visualMapModel));\n  };\n  ContinuousView.prototype._hoverLinkFromSeriesMouseOver = function (e) {\n    var ecData;\n    findEventDispatcher(e.target, function (target) {\n      var currECData = getECData(target);\n      if (currECData.dataIndex != null) {\n        ecData = currECData;\n        return true;\n      }\n    }, true);\n    if (!ecData) {\n      return;\n    }\n    var dataModel = this.ecModel.getSeriesByIndex(ecData.seriesIndex);\n    var visualMapModel = this.visualMapModel;\n    if (!visualMapModel.isTargetSeries(dataModel)) {\n      return;\n    }\n    var data = dataModel.getData(ecData.dataType);\n    var value = data.getStore().get(visualMapModel.getDataDimensionIndex(data), ecData.dataIndex);\n    if (!isNaN(value)) {\n      this._showIndicator(value, value);\n    }\n  };\n  ContinuousView.prototype._hideIndicator = function () {\n    var shapes = this._shapes;\n    shapes.indicator && shapes.indicator.attr('invisible', true);\n    shapes.indicatorLabel && shapes.indicatorLabel.attr('invisible', true);\n    var handleLabels = this._shapes.handleLabels;\n    if (handleLabels) {\n      for (var i = 0; i < handleLabels.length; i++) {\n        // Fade out handle labels.\n        // NOTE: Must use api enter/leave on emphasis/blur/select state. Or the global states manager will change it.\n        this.api.leaveBlur(handleLabels[i]);\n      }\n    }\n  };\n  ContinuousView.prototype._clearHoverLinkToSeries = function () {\n    this._hideIndicator();\n    var indices = this._hoverLinkDataIndices;\n    this._dispatchHighDown('downplay', helper.makeHighDownBatch(indices, this.visualMapModel));\n    indices.length = 0;\n  };\n  ContinuousView.prototype._clearHoverLinkFromSeries = function () {\n    this._hideIndicator();\n    var zr = this.api.getZr();\n    zr.off('mouseover', this._hoverLinkFromSeriesMouseOver);\n    zr.off('mouseout', this._hideIndicator);\n  };\n  ContinuousView.prototype._applyTransform = function (vertex, element, inverse, global) {\n    var transform = graphic.getTransform(element, global ? null : this.group);\n    return zrUtil.isArray(vertex) ? graphic.applyTransform(vertex, transform, inverse) : graphic.transformDirection(vertex, transform, inverse);\n  };\n  // TODO: TYPE more specified payload types.\n  ContinuousView.prototype._dispatchHighDown = function (type, batch) {\n    batch && batch.length && this.api.dispatchAction({\n      type: type,\n      batch: batch\n    });\n  };\n  /**\r\n   * @override\r\n   */\n  ContinuousView.prototype.dispose = function () {\n    this._clearHoverLinkFromSeries();\n    this._clearHoverLinkToSeries();\n  };\n  ContinuousView.type = 'visualMap.continuous';\n  return ContinuousView;\n}(VisualMapView);\nfunction createPolygon(points, cursor, onDrift, onDragEnd) {\n  return new graphic.Polygon({\n    shape: {\n      points: points\n    },\n    draggable: !!onDrift,\n    cursor: cursor,\n    drift: onDrift,\n    onmousemove: function (e) {\n      // For mobile device, prevent screen slider on the button.\n      eventTool.stop(e.event);\n    },\n    ondragend: onDragEnd\n  });\n}\nfunction getHalfHoverLinkSize(visualMapModel, dataExtent, sizeExtent) {\n  var halfHoverLinkSize = HOVER_LINK_SIZE / 2;\n  var hoverLinkDataSize = visualMapModel.get('hoverLinkDataSize');\n  if (hoverLinkDataSize) {\n    halfHoverLinkSize = linearMap(hoverLinkDataSize, dataExtent, sizeExtent, true) / 2;\n  }\n  return halfHoverLinkSize;\n}\nfunction useHoverLinkOnHandle(visualMapModel) {\n  var hoverLinkOnHandle = visualMapModel.get('hoverLinkOnHandle');\n  return !!(hoverLinkOnHandle == null ? visualMapModel.get('realtime') : hoverLinkOnHandle);\n}\nfunction getCursor(orient) {\n  return orient === 'vertical' ? 'ns-resize' : 'ew-resize';\n}\nexport default ContinuousView;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;AACA,IAAI,YAAY,mJAAA,CAAA,YAAoB;AACpC,IAAI,OAAO,iJAAA,CAAA,OAAW;AACtB,IAAI,UAAU,KAAK,GAAG;AACtB,IAAI,UAAU,KAAK,GAAG;AACtB,kBAAkB;AAClB,IAAI,kBAAkB;AACtB,IAAI,iBAAiB;AACrB,UAAU;AACV,wDAAwD;AACxD,wCAAwC;AACxC,2DAA2D;AAC3D,wCAAwC;AACxC,6DAA6D;AAC7D,iEAAiE;AACjE,IAAI,iBAAiB,WAAW,GAAE,SAAU,MAAM;IAChD,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;IAC1B,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,eAAe,IAAI;QAChC,MAAM,OAAO,GAAG,CAAC;QACjB,MAAM,aAAa,GAAG,EAAE;QACxB,MAAM,WAAW,GAAG,EAAE;QACtB,MAAM,qBAAqB,GAAG,EAAE;QAChC,OAAO;IACT;IACA,eAAe,SAAS,CAAC,IAAI,GAAG,SAAU,OAAO,EAAE,GAAG;QACpD,OAAO,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,SAAS;QAC1C,IAAI,CAAC,6BAA6B,GAAG,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,IAAI,CAAC,6BAA6B,EAAE,IAAI;QACzF,IAAI,CAAC,cAAc,GAAG,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,IAAI,CAAC,cAAc,EAAE,IAAI;IAC7D;IACA,eAAe,SAAS,CAAC,QAAQ,GAAG,SAAU,cAAc,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO;QACjF,IAAI,CAAC,WAAW,QAAQ,IAAI,KAAK,qBAAqB,QAAQ,IAAI,KAAK,IAAI,CAAC,GAAG,EAAE;YAC/E,IAAI,CAAC,UAAU;QACjB;IACF;IACA,eAAe,SAAS,CAAC,UAAU,GAAG;QACpC,IAAI,CAAC,KAAK,CAAC,SAAS;QACpB,IAAI,iBAAiB,IAAI,CAAC,cAAc;QACxC,IAAI,YAAY,IAAI,CAAC,KAAK;QAC1B,IAAI,CAAC,OAAO,GAAG,eAAe,GAAG,CAAC;QAClC,IAAI,CAAC,UAAU,GAAG,eAAe,GAAG,CAAC;QACrC,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,UAAU,CAAC;QAChB,IAAI,gBAAgB,eAAe,GAAG,CAAC;QACvC,IAAI,CAAC,eAAe,CAAC,WAAW,eAAe;QAC/C,IAAI,CAAC,eAAe,CAAC,WAAW,eAAe;QAC/C,2CAA2C;QAC3C,IAAI,CAAC,WAAW,CAAC;QACjB,yDAAyD;QACzD,uCAAuC;QACvC,IAAI,CAAC,gBAAgB,CAAC;QACtB,mBAAmB;QACnB,IAAI,CAAC,WAAW;QAChB,IAAI,CAAC,wBAAwB;QAC7B,IAAI,CAAC,0BAA0B;QAC/B,IAAI,CAAC,aAAa,CAAC;IACrB;IACA,eAAe,SAAS,CAAC,eAAe,GAAG,SAAU,KAAK,EAAE,aAAa,EAAE,SAAS;QAClF,IAAI,CAAC,eAAe;YAClB;QACF;QACA,yEAAyE;QACzE,IAAI,OAAO,aAAa,CAAC,IAAI,UAAU;QACvC,OAAO,QAAQ,OAAO,OAAO,KAAK;QAClC,IAAI,iBAAiB,IAAI,CAAC,cAAc;QACxC,IAAI,UAAU,eAAe,GAAG,CAAC;QACjC,IAAI,WAAW,eAAe,QAAQ;QACtC,IAAI,WAAW,IAAI,CAAC,OAAO,CAAC,SAAS;QACrC,IAAI,WAAW,IAAI,CAAC,eAAe,CAAC;YAAC,QAAQ,CAAC,EAAE,GAAG;YAAG,cAAc,IAAI,CAAC,UAAU,QAAQ,CAAC,EAAE,GAAG;SAAQ,EAAE;QAC3G,IAAI,QAAQ,IAAI,CAAC,eAAe,CAAC,cAAc,IAAI,WAAW,OAAO;QACrE,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,iBAAiB,IAAI,CAAC,cAAc,CAAC,cAAc;QACvD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,uLAAA,CAAA,OAAY,CAAC;YAC9B,OAAO,CAAA,GAAA,wJAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB;gBACrC,GAAG,QAAQ,CAAC,EAAE;gBACd,GAAG,QAAQ,CAAC,EAAE;gBACd,eAAe,WAAW,eAAe,WAAW;gBACpD,OAAO,WAAW,eAAe,QAAQ;gBACzC,MAAM;YACR;QACF;IACF;IACA,eAAe,SAAS,CAAC,UAAU,GAAG,SAAU,WAAW;QACzD,IAAI,iBAAiB,IAAI,CAAC,cAAc;QACxC,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,WAAW,eAAe,QAAQ;QACtC,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,YAAY,IAAI,CAAC,UAAU;QAC/B,IAAI,YAAY,CAAA,GAAA,qKAAA,CAAA,eAAmB,AAAD,EAAE,gBAAgB,IAAI,CAAC,GAAG,EAAE;QAC9D,IAAI,YAAY,OAAO,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC;QACxD,IAAI,mBAAmB,IAAI,yLAAA,CAAA,QAAa;QACxC,UAAU,GAAG,CAAC;QACd,MAAM;QACN,iBAAiB,GAAG,CAAC,OAAO,UAAU,GAAG;QACzC,iBAAiB,GAAG,CAAC,OAAO,OAAO,GAAG,cAAc,MAAM,YAAY,UAAU,IAAI,CAAC,OAAO,IAAI,MAAM,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO,QAAQ,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,OAAO;QACpM,wBAAwB;QACxB,iBAAiB,WAAW,CAAC,IAAI,gMAAA,CAAA,OAAY,CAAC;YAC5C,OAAO;gBACL,GAAG;gBACH,GAAG;gBACH,OAAO,QAAQ,CAAC,EAAE;gBAClB,QAAQ,QAAQ,CAAC,EAAE;gBACnB,GAAG;YACL;QACF;QACA,IAAI,WAAW,eAAe,cAAc,CAAC,WAAW,CAAC;QACzD,IAAI,WAAW,QAAQ,SAAS,KAAK,EAAE,SAAS,MAAM;QACtD,SAAS;QACT,IAAI,WAAW;YACb,OAAO,YAAY,GAAG,EAAE;YACxB,OAAO,YAAY,GAAG,EAAE;YACxB,OAAO,iBAAiB,GAAG,EAAE;YAC7B,IAAI,CAAC,aAAa,CAAC,gBAAgB,WAAW,GAAG,UAAU,UAAU;YACrE,IAAI,CAAC,aAAa,CAAC,gBAAgB,WAAW,GAAG,UAAU,UAAU;QACvE;QACA,IAAI,CAAC,gBAAgB,CAAC,gBAAgB,WAAW,UAAU,UAAU;QACrE,YAAY,GAAG,CAAC;IAClB;IACA,eAAe,SAAS,CAAC,aAAa,GAAG,SAAU,cAAc,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM;QACnH,IAAI,UAAU,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,aAAa;QAC/D,IAAI,YAAY,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,IAAI,CAAC,WAAW,EAAE,IAAI,EAAE,aAAa;QACjE,IAAI,aAAa,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,eAAe,GAAG,CAAC,eAAe,QAAQ,CAAC,EAAE;QAC3E,IAAI,cAAc,CAAA,GAAA,mJAAA,CAAA,eAAY,AAAD,EAAE,eAAe,GAAG,CAAC,eAAe,CAAC,aAAa,GAAG,CAAC,aAAa,GAAG,YAAY,YAAY,MAAM;QACjI,IAAI,SAAS,UAAU,IAAI,CAAC,OAAO;QACnC,YAAY,IAAI,CAAC;YACf,QAAQ;YACR,WAAW;YACX,OAAO;YACP,WAAW;YACX,aAAa,SAAU,CAAC;gBACtB,CAAA,GAAA,kKAAA,CAAA,OAAc,AAAD,EAAE,EAAE,KAAK;YACxB;QACF;QACA,YAAY,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG;QAC9B,YAAY,QAAQ,CAAC,eAAe,QAAQ,CAAC,eAAe,YAAY;QACxE,YAAY,QAAQ,CAAC;YACnB,eAAe;YACf,aAAa;QACf;QACA,YAAY,KAAK,CAAC,SAAS,IAAI;QAC/B,YAAY,WAAW,CAAC,YAAY,KAAK,GAAG,eAAe,QAAQ,CAAC;YAAC;YAAY;SAAc,EAAE,YAAY;QAC7G,CAAA,GAAA,mJAAA,CAAA,0BAAuB,AAAD,EAAE,aAAa;QACrC,UAAU,GAAG,CAAC;QACd,iEAAiE;QACjE,mEAAmE;QACnE,oEAAoE;QACpE,0DAA0D;QAC1D,IAAI,iBAAiB,IAAI,CAAC,cAAc,CAAC,cAAc;QACvD,IAAI,cAAc,IAAI,uLAAA,CAAA,OAAY,CAAC;YACjC,QAAQ;YACR,WAAW;YACX,OAAO;YACP,aAAa,SAAU,CAAC;gBACtB,0DAA0D;gBAC1D,CAAA,GAAA,kKAAA,CAAA,OAAc,AAAD,EAAE,EAAE,KAAK;YACxB;YACA,WAAW;YACX,OAAO,CAAA,GAAA,wJAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB;gBACrC,GAAG;gBACH,GAAG;gBACH,MAAM;YACR;QACF;QACA,YAAY,WAAW,CAAC,QAAQ,KAAK,GAAG;YACtC,SAAS;QACX;QACA,YAAY,eAAe,GAAG;YAC5B,UAAU;QACZ;QACA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QACf,IAAI,mBAAmB;YAAC;YAAY;SAAE;QACtC,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,OAAO,YAAY,CAAC,YAAY,GAAG;QACnC,OAAO,iBAAiB,CAAC,YAAY,GAAG;QACxC,OAAO,YAAY,CAAC,YAAY,GAAG;IACrC;IACA,eAAe,SAAS,CAAC,gBAAgB,GAAG,SAAU,cAAc,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM;QACzG,IAAI,QAAQ,CAAA,GAAA,oJAAA,CAAA,eAAY,AAAD,EAAE,eAAe,GAAG,CAAC,kBAAkB,QAAQ,CAAC,EAAE;QACzE,IAAI,YAAY,CAAA,GAAA,mJAAA,CAAA,eAAY,AAAD,EAAE,eAAe,GAAG,CAAC,kBAAkB,CAAC,QAAQ,GAAG,CAAC,QAAQ,GAAG,OAAO,OAAO,MAAM;QAC9G,UAAU,IAAI,CAAC;YACb,QAAQ;YACR,WAAW;YACX,QAAQ;YACR,GAAG,QAAQ,CAAC,EAAE,GAAG;QACnB;QACA,IAAI,iBAAiB,eAAe,QAAQ,CAAC,kBAAkB,YAAY;QAC3E,IAAI,qBAAqB,qJAAA,CAAA,UAAO,EAAE;YAChC,IAAI,YAAY,UAAU,KAAK;YAC/B,UAAU,QAAQ,CAAC,CAAA,GAAA,iJAAA,CAAA,SAAa,AAAD,EAAE;gBAC/B,oCAAoC;gBACpC,OAAO,UAAU,KAAK;gBACtB,GAAG,UAAU,CAAC;gBACd,GAAG,UAAU,CAAC;gBACd,OAAO,UAAU,KAAK;gBACtB,QAAQ,UAAU,MAAM;YAC1B,GAAG;QACL,OAAO;YACL,UAAU,QAAQ,CAAC;QACrB;QACA,UAAU,GAAG,CAAC;QACd,IAAI,iBAAiB,IAAI,CAAC,cAAc,CAAC,cAAc;QACvD,IAAI,iBAAiB,IAAI,uLAAA,CAAA,OAAY,CAAC;YACpC,QAAQ;YACR,WAAW;YACX,OAAO,CAAA,GAAA,wJAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB;gBACrC,GAAG;gBACH,GAAG;gBACH,MAAM;YACR;QACF;QACA,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QACf,IAAI,sBAAsB;YAAC,CAAC,WAAW,eAAe,WAAW,IAAI,cAAc,IAAI,QAAQ,CAAC,EAAE,GAAG;YAAG;SAAE;QAC1G,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,OAAO,SAAS,GAAG;QACnB,OAAO,cAAc,GAAG;QACxB,OAAO,mBAAmB,GAAG;QAC7B,IAAI,CAAC,mBAAmB,GAAG;IAC7B;IACA,eAAe,SAAS,CAAC,WAAW,GAAG,SAAU,WAAW,EAAE,KAAK,EACnE,6DAA6D;IAC7D,EAAE,EAAE,EAAE;QACJ,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB;QACF;QACA,IAAI,CAAC,SAAS,GAAG,CAAC;QAClB,IAAI,CAAC,OAAO;YACV,wCAAwC;YACxC,IAAI,SAAS,IAAI,CAAC,eAAe,CAAC;gBAAC;gBAAI;aAAG,EAAE,IAAI,CAAC,OAAO,CAAC,SAAS,EAAE;YACpE,IAAI,CAAC,eAAe,CAAC,aAAa,MAAM,CAAC,EAAE;YAC3C,IAAI,CAAC,cAAc;YACnB,uDAAuD;YACvD,0BAA0B;YAC1B,IAAI,CAAC,WAAW;QAClB;QACA,gDAAgD;QAChD,IAAI,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC,aAAa;YAClD,qBAAqB;YACrB,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;gBACtB,MAAM;gBACN,MAAM,IAAI,CAAC,GAAG;gBACd,aAAa,IAAI,CAAC,cAAc,CAAC,EAAE;gBACnC,UAAU,IAAI,CAAC,aAAa,CAAC,KAAK;YACpC;QACF;QACA,IAAI,OAAO;YACT,CAAC,IAAI,CAAC,SAAS,IAAI,IAAI,CAAC,uBAAuB;QACjD,OAAO,IAAI,qBAAqB,IAAI,CAAC,cAAc,GAAG;YACpD,IAAI,CAAC,oBAAoB,CAAC,IAAI,CAAC,WAAW,CAAC,YAAY,EAAE;QAC3D;IACF;IACA,eAAe,SAAS,CAAC,cAAc,GAAG;QACxC,IAAI,iBAAiB,IAAI,CAAC,cAAc;QACxC,IAAI,eAAe,IAAI,CAAC,aAAa,GAAG,eAAe,WAAW;QAClE,IAAI,aAAa,eAAe,SAAS;QACzC,IAAI,aAAa;YAAC;YAAG,eAAe,QAAQ,CAAC,EAAE;SAAC;QAChD,IAAI,CAAC,WAAW,GAAG;YAAC,UAAU,YAAY,CAAC,EAAE,EAAE,YAAY,YAAY;YAAO,UAAU,YAAY,CAAC,EAAE,EAAE,YAAY,YAAY;SAAM;IACzI;IACA;;;;;GAKC,GACD,eAAe,SAAS,CAAC,eAAe,GAAG,SAAU,WAAW,EAAE,KAAK;QACrE,QAAQ,SAAS;QACjB,IAAI,iBAAiB,IAAI,CAAC,cAAc;QACxC,IAAI,aAAa,IAAI,CAAC,WAAW;QACjC,IAAI,aAAa;YAAC;YAAG,eAAe,QAAQ,CAAC,EAAE;SAAC;QAChD,CAAA,GAAA,sKAAA,CAAA,UAAU,AAAD,EAAE,OAAO,YAAY,YAAY,aAC1C,qBAAqB;QACrB;QACA,IAAI,aAAa,eAAe,SAAS;QACzC,wBAAwB;QACxB,IAAI,CAAC,aAAa,GAAG;YAAC,UAAU,UAAU,CAAC,EAAE,EAAE,YAAY,YAAY;YAAO,UAAU,UAAU,CAAC,EAAE,EAAE,YAAY,YAAY;SAAM;IACvI;IACA,eAAe,SAAS,CAAC,WAAW,GAAG,SAAU,SAAS;QACxD,IAAI,iBAAiB,IAAI,CAAC,cAAc;QACxC,IAAI,aAAa,eAAe,SAAS;QACzC,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,uBAAuB;YAAC;YAAG,eAAe,QAAQ,CAAC,EAAE;SAAC;QAC1D,IAAI,oBAAoB,YAAY,uBAAuB,IAAI,CAAC,WAAW;QAC3E,IAAI,gBAAgB,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,aAAa,EAAE,YAAY,mBAAmB;QAC7F,IAAI,mBAAmB,IAAI,CAAC,gBAAgB,CAAC,YAAY,YAAY,sBAAsB;QAC3F,OAAO,OAAO,CAAC,QAAQ,CAAC;YACtB,MAAM,cAAc,QAAQ;QAE9B,GAAG,QAAQ,CAAC,UAAU,cAAc,SAAS;QAC7C,OAAO,UAAU,CAAC,QAAQ,CAAC;YACzB,MAAM,iBAAiB,QAAQ;QAEjC,GAAG,QAAQ,CAAC,UAAU,iBAAiB,SAAS;QAChD,IAAI,CAAC,aAAa,CAAC,mBAAmB;IACxC;IACA,eAAe,SAAS,CAAC,gBAAgB,GAAG,SAAU,YAAY,EAAE,UAAU,EAAE,UAAU,EAAE,UAAU;QACpG,IAAI,OAAO;YACT,YAAY;YACZ,uBAAuB;QACzB;QACA,IAAI,aAAa,IAAI,CAAC,kBAAkB,CAAC,cAAc;QACvD,IAAI,cAAc;YAAC,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,EAAE,EAAE,cAAc;YAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,EAAE,EAAE,cAAc;SAAM;QAChJ,IAAI,YAAY,IAAI,CAAC,gBAAgB,CAAC,YAAY;QAClD,OAAO;YACL,UAAU,IAAI,8JAAA,CAAA,UAAc,CAAC,GAAG,GAAG,GAAG,GAAG;YACzC,WAAW;YACX,cAAc;gBAAC,UAAU,CAAC,EAAE,CAAC,KAAK;gBAAE,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,CAAC,KAAK;aAAC;QAC9E;IACF;IACA,eAAe,SAAS,CAAC,kBAAkB,GAAG,SAAU,YAAY,EAAE,IAAI;QACxE,kEAAkE;QAClE,iEAAiE;QACjE,YAAY;QACZ,IAAI,eAAe,KAAK,mBAAmB;QAC3C,IAAI,aAAa,EAAE;QACnB,IAAI,OAAO,CAAC,YAAY,CAAC,EAAE,GAAG,YAAY,CAAC,EAAE,IAAI;QACjD,WAAW,IAAI,CAAC;YACd,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,EAAE,EAAE,SAAS;YAC1D,QAAQ;QACV;QACA,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,IAAK;YACrC,IAAI,YAAY,YAAY,CAAC,EAAE,GAAG,OAAO;YACzC,IAAI,YAAY,YAAY,CAAC,EAAE,EAAE;gBAC/B;YACF;YACA,WAAW,IAAI,CAAC;gBACd,OAAO,IAAI,CAAC,mBAAmB,CAAC,WAAW,SAAS;gBACpD,QAAQ,IAAI;YACd;QACF;QACA,WAAW,IAAI,CAAC;YACd,OAAO,IAAI,CAAC,mBAAmB,CAAC,YAAY,CAAC,EAAE,EAAE,SAAS;YAC1D,QAAQ;QACV;QACA,OAAO;IACT;IACA,eAAe,SAAS,CAAC,gBAAgB,GAAG,SAAU,UAAU,EAAE,WAAW;QAC3E,IAAI,WAAW,IAAI,CAAC,cAAc,CAAC,QAAQ;QAC3C,OAAO;YAAC;gBAAC,QAAQ,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE;gBAAE,UAAU,CAAC,EAAE;aAAC;YAAE;gBAAC,QAAQ,CAAC,EAAE;gBAAE,UAAU,CAAC,EAAE;aAAC;YAAE;gBAAC,QAAQ,CAAC,EAAE;gBAAE,UAAU,CAAC,EAAE;aAAC;YAAE;gBAAC,QAAQ,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE;gBAAE,UAAU,CAAC,EAAE;aAAC;SAAC;IACnK;IACA,eAAe,SAAS,CAAC,eAAe,GAAG,SAAU,SAAS;QAC5D,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,UAAU,IAAI,CAAC,cAAc,CAAC,GAAG,CAAC;QACtC,OAAO,IAAI,yLAAA,CAAA,QAAa,CAAC,WAAW,gBAAgB,CAAC,UAAU;YAC7D,QAAQ,cAAc,WAAW,IAAI,CAAC;YACtC,UAAU,KAAK,EAAE,GAAG;QACtB,IAAI,WAAW,gBAAgB,UAAU;YACvC,QAAQ,cAAc,WAAW,CAAC,IAAI;YACtC,UAAU,CAAC,KAAK,EAAE,GAAG;QACvB,IAAI,WAAW,cAAc,CAAC,UAAU;YACtC,QAAQ,cAAc,SAAS,IAAI,CAAC;YACpC,QAAQ,CAAC;QACX,IAAI;YACF,QAAQ,cAAc,SAAS,IAAI,CAAC;QACtC;IACF;IACA,eAAe,SAAS,CAAC,aAAa,GAAG,SAAU,UAAU,EAAE,aAAa;QAC1E,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB;QACF;QACA,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,iBAAiB,IAAI,CAAC,cAAc;QACxC,IAAI,eAAe,OAAO,YAAY;QACtC,IAAI,eAAe,OAAO,YAAY;QACtC,IAAI,WAAW,eAAe,QAAQ;QACtC,IAAI,aAAa,eAAe,SAAS;QACzC,IAAI,QAAQ,IAAI,CAAC,eAAe,CAAC,QAAQ,OAAO,SAAS;QACzD,KAAK;YAAC;YAAG;SAAE,EAAE,SAAU,WAAW;YAChC,IAAI,cAAc,YAAY,CAAC,YAAY;YAC3C,YAAY,QAAQ,CAAC,QAAQ,cAAc,YAAY,CAAC,YAAY;YACpE,YAAY,CAAC,GAAG,UAAU,CAAC,YAAY;YACvC,IAAI,MAAM,UAAU,UAAU,CAAC,YAAY,EAAE;gBAAC;gBAAG,QAAQ,CAAC,EAAE;aAAC,EAAE,YAAY;YAC3E,IAAI,aAAa,IAAI,CAAC,mBAAmB,CAAC,KAAK;YAC/C,YAAY,MAAM,GAAG,YAAY,MAAM,GAAG,aAAa,QAAQ,CAAC,EAAE;YAClE,YAAY,CAAC,GAAG,QAAQ,CAAC,EAAE,GAAG,aAAa;YAC3C,gCAAgC;YAChC,IAAI,YAAY,CAAA,GAAA,oKAAA,CAAA,iBAAsB,AAAD,EAAE,OAAO,iBAAiB,CAAC,YAAY,EAAE,CAAA,GAAA,oKAAA,CAAA,eAAoB,AAAD,EAAE,aAAa,IAAI,CAAC,KAAK;YAC1H,IAAI,IAAI,CAAC,OAAO,KAAK,cAAc;gBACjC,0HAA0H;gBAC1H,8FAA8F;gBAC9F,IAAI,gBAAgB,UAAU,UAAU,UAAU,QAAQ,CAAC,QAAQ,CAAC,EAAE,GAAG,UAAU,IAAI,IAAI,CAAC,QAAQ,CAAC,EAAE,GAAG,UAAU,IAAI,CAAC;gBACzH,SAAS,CAAC,EAAE,IAAI;YAClB;YACA,YAAY,CAAC,YAAY,CAAC,QAAQ,CAAC;gBACjC,GAAG,SAAS,CAAC,EAAE;gBACf,GAAG,SAAS,CAAC,EAAE;gBACf,MAAM,eAAe,eAAe,CAAC,IAAI,CAAC,aAAa,CAAC,YAAY;gBACpE,eAAe;gBACf,OAAO,IAAI,CAAC,OAAO,KAAK,aAAa,IAAI,CAAC,eAAe,CAAC,QAAQ,OAAO,SAAS,IAAI;YACxF;QACF,GAAG,IAAI;IACT;IACA,eAAe,SAAS,CAAC,cAAc,GAAG,SAAU,WAAW,EAAE,SAAS,EAAE,WAAW,EAAE,iBAAiB;QACxG,IAAI,iBAAiB,IAAI,CAAC,cAAc;QACxC,IAAI,aAAa,eAAe,SAAS;QACzC,IAAI,WAAW,eAAe,QAAQ;QACtC,IAAI,aAAa;YAAC;YAAG,QAAQ,CAAC,EAAE;SAAC;QACjC,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,YAAY,OAAO,SAAS;QAChC,IAAI,CAAC,WAAW;YACd;QACF;QACA,UAAU,IAAI,CAAC,aAAa;QAC5B,IAAI,OAAO;YACT,uBAAuB;QACzB;QACA,IAAI,QAAQ,IAAI,CAAC,mBAAmB,CAAC,aAAa,SAAS;QAC3D,IAAI,aAAa,IAAI,CAAC,mBAAmB,CAAC,aAAa;QACvD,IAAI,IAAI,UAAU,aAAa,YAAY,YAAY;QACvD,IAAI,IAAI,QAAQ,CAAC,EAAE,GAAG,aAAa;QACnC,IAAI,kBAAkB;YACpB,GAAG,UAAU,CAAC;YACd,GAAG,UAAU,CAAC;QAChB;QACA,gCAAgC;QAChC,UAAU,CAAC,GAAG;QACd,UAAU,CAAC,GAAG;QACd,IAAI,YAAY,CAAA,GAAA,oKAAA,CAAA,iBAAsB,AAAD,EAAE,OAAO,mBAAmB,EAAE,CAAA,GAAA,oKAAA,CAAA,eAAoB,AAAD,EAAE,WAAW,IAAI,CAAC,KAAK;QAC7G,IAAI,iBAAiB,OAAO,cAAc;QAC1C,eAAe,IAAI,CAAC,aAAa;QACjC,IAAI,QAAQ,IAAI,CAAC,eAAe,CAAC,QAAQ,OAAO,SAAS;QACzD,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,eAAe,WAAW;QAC9B,eAAe,QAAQ,CAAC;YACtB,MAAM,CAAC,cAAc,cAAc,EAAE,IAAI,eAAe,eAAe,CAAC;YACxE,eAAe,eAAe,QAAQ;YACtC,OAAO,eAAe,WAAW;QACnC;QACA,IAAI,oBAAoB;YACtB,GAAG;YACH,GAAG;YACH,OAAO;gBACL,MAAM;YACR;QACF;QACA,IAAI,gBAAgB;YAClB,OAAO;gBACL,GAAG,SAAS,CAAC,EAAE;gBACf,GAAG,SAAS,CAAC,EAAE;YACjB;QACF;QACA,IAAI,eAAe,OAAO,CAAC,kBAAkB,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE;YAC5E,IAAI,eAAe;gBACjB,UAAU;gBACV,QAAQ;gBACR,UAAU;YACZ;YACA,UAAU,CAAC,GAAG,gBAAgB,CAAC;YAC/B,UAAU,CAAC,GAAG,gBAAgB,CAAC;YAC/B,UAAU,SAAS,CAAC,mBAAmB;YACvC,eAAe,SAAS,CAAC,eAAe;QAC1C,OAAO;YACL,UAAU,IAAI,CAAC;YACf,eAAe,IAAI,CAAC;QACtB;QACA,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,eAAe,IAAI,CAAC,OAAO,CAAC,YAAY;QAC5C,IAAI,cAAc;YAChB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;gBAC5C,0BAA0B;gBAC1B,6GAA6G;gBAC7G,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE;YACpC;QACF;IACF;IACA,eAAe,SAAS,CAAC,wBAAwB,GAAG;QAClD,IAAI,OAAO,IAAI;QACf,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,aAAa,SAAU,CAAC;YAChD,KAAK,SAAS,GAAG;YACjB,IAAI,CAAC,KAAK,SAAS,EAAE;gBACnB,IAAI,WAAW,KAAK,cAAc,CAAC,QAAQ;gBAC3C,IAAI,MAAM,KAAK,eAAe,CAAC;oBAAC,EAAE,OAAO;oBAAE,EAAE,OAAO;iBAAC,EAAE,KAAK,OAAO,CAAC,SAAS,EAAE,MAAM;gBACrF,wDAAwD;gBACxD,kCAAkC;gBAClC,GAAG,CAAC,EAAE,GAAG,QAAQ,QAAQ,GAAG,GAAG,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;gBAChD,KAAK,oBAAoB,CAAC,GAAG,CAAC,EAAE,EAAE,KAAK,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE;YACxE;QACF,GAAG,EAAE,CAAC,YAAY;YAChB,oDAAoD;YACpD,iDAAiD;YACjD,KAAK,SAAS,GAAG;YACjB,CAAC,KAAK,SAAS,IAAI,KAAK,uBAAuB;QACjD;IACF;IACA,eAAe,SAAS,CAAC,0BAA0B,GAAG;QACpD,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK;QACvB,IAAI,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC,SAAS,EAAE;YACxC,GAAG,EAAE,CAAC,aAAa,IAAI,CAAC,6BAA6B,EAAE,IAAI;YAC3D,GAAG,EAAE,CAAC,YAAY,IAAI,CAAC,cAAc,EAAE,IAAI;QAC7C,OAAO;YACL,IAAI,CAAC,yBAAyB;QAChC;IACF;IACA,eAAe,SAAS,CAAC,oBAAoB,GAAG,SAAU,SAAS,EAAE,UAAU;QAC7E,IAAI,iBAAiB,IAAI,CAAC,cAAc;QACxC,IAAI,WAAW,eAAe,QAAQ;QACtC,IAAI,CAAC,eAAe,MAAM,CAAC,SAAS,EAAE;YACpC;QACF;QACA,IAAI,aAAa;YAAC;YAAG,QAAQ,CAAC,EAAE;SAAC;QACjC,IAAI,aAAa,eAAe,SAAS;QACzC,wFAAwF;QACxF,YAAY,QAAQ,QAAQ,UAAU,CAAC,EAAE,EAAE,YAAY,UAAU,CAAC,EAAE;QACpE,IAAI,oBAAoB,qBAAqB,gBAAgB,YAAY;QACzE,IAAI,aAAa;YAAC,YAAY;YAAmB,YAAY;SAAkB;QAC/E,IAAI,cAAc,UAAU,WAAW,YAAY,YAAY;QAC/D,IAAI,aAAa;YAAC,UAAU,UAAU,CAAC,EAAE,EAAE,YAAY,YAAY;YAAO,UAAU,UAAU,CAAC,EAAE,EAAE,YAAY,YAAY;SAAM;QACjI,qFAAqF;QACrF,mDAAmD;QACnD,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG,CAAC,QAAQ;QAC3D,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG,QAAQ;QAC1D,mDAAmD;QACnD,sDAAsD;QACtD,IAAI,YAAY;YACd,IAAI,UAAU,CAAC,EAAE,KAAK,CAAC,UAAU;gBAC/B,IAAI,CAAC,cAAc,CAAC,aAAa,UAAU,CAAC,EAAE,EAAE,MAAM;YACxD,OAAO,IAAI,UAAU,CAAC,EAAE,KAAK,UAAU;gBACrC,IAAI,CAAC,cAAc,CAAC,aAAa,UAAU,CAAC,EAAE,EAAE,MAAM;YACxD,OAAO;gBACL,IAAI,CAAC,cAAc,CAAC,aAAa,aAAa,MAAM;YACtD;QACF;QACA,iEAAiE;QACjE,gEAAgE;QAChE,wDAAwD;QACxD,mEAAmE;QACnE,oEAAoE;QACpE,sCAAsC;QACtC,IAAI,WAAW,IAAI,CAAC,qBAAqB;QACzC,IAAI,WAAW,EAAE;QACjB,IAAI,cAAc,qBAAqB,iBAAiB;YACtD,WAAW,IAAI,CAAC,qBAAqB,GAAG,eAAe,qBAAqB,CAAC;QAC/E;QACA,IAAI,gBAAgB,CAAA,GAAA,kJAAA,CAAA,kBAAyB,AAAD,EAAE,UAAU;QACxD,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAA,GAAA,qKAAA,CAAA,oBAAwB,AAAD,EAAE,aAAa,CAAC,EAAE,EAAE;QAC9E,IAAI,CAAC,iBAAiB,CAAC,aAAa,CAAA,GAAA,qKAAA,CAAA,oBAAwB,AAAD,EAAE,aAAa,CAAC,EAAE,EAAE;IACjF;IACA,eAAe,SAAS,CAAC,6BAA6B,GAAG,SAAU,CAAC;QAClE,IAAI;QACJ,CAAA,GAAA,kJAAA,CAAA,sBAAmB,AAAD,EAAE,EAAE,MAAM,EAAE,SAAU,MAAM;YAC5C,IAAI,aAAa,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE;YAC3B,IAAI,WAAW,SAAS,IAAI,MAAM;gBAChC,SAAS;gBACT,OAAO;YACT;QACF,GAAG;QACH,IAAI,CAAC,QAAQ;YACX;QACF;QACA,IAAI,YAAY,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,OAAO,WAAW;QAChE,IAAI,iBAAiB,IAAI,CAAC,cAAc;QACxC,IAAI,CAAC,eAAe,cAAc,CAAC,YAAY;YAC7C;QACF;QACA,IAAI,OAAO,UAAU,OAAO,CAAC,OAAO,QAAQ;QAC5C,IAAI,QAAQ,KAAK,QAAQ,GAAG,GAAG,CAAC,eAAe,qBAAqB,CAAC,OAAO,OAAO,SAAS;QAC5F,IAAI,CAAC,MAAM,QAAQ;YACjB,IAAI,CAAC,cAAc,CAAC,OAAO;QAC7B;IACF;IACA,eAAe,SAAS,CAAC,cAAc,GAAG;QACxC,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,OAAO,SAAS,IAAI,OAAO,SAAS,CAAC,IAAI,CAAC,aAAa;QACvD,OAAO,cAAc,IAAI,OAAO,cAAc,CAAC,IAAI,CAAC,aAAa;QACjE,IAAI,eAAe,IAAI,CAAC,OAAO,CAAC,YAAY;QAC5C,IAAI,cAAc;YAChB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;gBAC5C,0BAA0B;gBAC1B,6GAA6G;gBAC7G,IAAI,CAAC,GAAG,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE;YACpC;QACF;IACF;IACA,eAAe,SAAS,CAAC,uBAAuB,GAAG;QACjD,IAAI,CAAC,cAAc;QACnB,IAAI,UAAU,IAAI,CAAC,qBAAqB;QACxC,IAAI,CAAC,iBAAiB,CAAC,YAAY,CAAA,GAAA,qKAAA,CAAA,oBAAwB,AAAD,EAAE,SAAS,IAAI,CAAC,cAAc;QACxF,QAAQ,MAAM,GAAG;IACnB;IACA,eAAe,SAAS,CAAC,yBAAyB,GAAG;QACnD,IAAI,CAAC,cAAc;QACnB,IAAI,KAAK,IAAI,CAAC,GAAG,CAAC,KAAK;QACvB,GAAG,GAAG,CAAC,aAAa,IAAI,CAAC,6BAA6B;QACtD,GAAG,GAAG,CAAC,YAAY,IAAI,CAAC,cAAc;IACxC;IACA,eAAe,SAAS,CAAC,eAAe,GAAG,SAAU,MAAM,EAAE,OAAO,EAAE,OAAO,EAAE,MAAM;QACnF,IAAI,YAAY,CAAA,GAAA,oKAAA,CAAA,eAAoB,AAAD,EAAE,SAAS,SAAS,OAAO,IAAI,CAAC,KAAK;QACxE,OAAO,CAAA,GAAA,iJAAA,CAAA,UAAc,AAAD,EAAE,UAAU,CAAA,GAAA,oKAAA,CAAA,iBAAsB,AAAD,EAAE,QAAQ,WAAW,WAAW,CAAA,GAAA,oKAAA,CAAA,qBAA0B,AAAD,EAAE,QAAQ,WAAW;IACrI;IACA,2CAA2C;IAC3C,eAAe,SAAS,CAAC,iBAAiB,GAAG,SAAU,IAAI,EAAE,KAAK;QAChE,SAAS,MAAM,MAAM,IAAI,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;YAC/C,MAAM;YACN,OAAO;QACT;IACF;IACA;;GAEC,GACD,eAAe,SAAS,CAAC,OAAO,GAAG;QACjC,IAAI,CAAC,yBAAyB;QAC9B,IAAI,CAAC,uBAAuB;IAC9B;IACA,eAAe,IAAI,GAAG;IACtB,OAAO;AACT,EAAE,4KAAA,CAAA,UAAa;AACf,SAAS,cAAc,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS;IACvD,OAAO,IAAI,sMAAA,CAAA,UAAe,CAAC;QACzB,OAAO;YACL,QAAQ;QACV;QACA,WAAW,CAAC,CAAC;QACb,QAAQ;QACR,OAAO;QACP,aAAa,SAAU,CAAC;YACtB,0DAA0D;YAC1D,CAAA,GAAA,kKAAA,CAAA,OAAc,AAAD,EAAE,EAAE,KAAK;QACxB;QACA,WAAW;IACb;AACF;AACA,SAAS,qBAAqB,cAAc,EAAE,UAAU,EAAE,UAAU;IAClE,IAAI,oBAAoB,kBAAkB;IAC1C,IAAI,oBAAoB,eAAe,GAAG,CAAC;IAC3C,IAAI,mBAAmB;QACrB,oBAAoB,UAAU,mBAAmB,YAAY,YAAY,QAAQ;IACnF;IACA,OAAO;AACT;AACA,SAAS,qBAAqB,cAAc;IAC1C,IAAI,oBAAoB,eAAe,GAAG,CAAC;IAC3C,OAAO,CAAC,CAAC,CAAC,qBAAqB,OAAO,eAAe,GAAG,CAAC,cAAc,iBAAiB;AAC1F;AACA,SAAS,UAAU,MAAM;IACvB,OAAO,WAAW,aAAa,cAAc;AAC/C;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1764, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/visualMap/visualMapAction.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nexport var visualMapActionInfo = {\n  type: 'selectDataRange',\n  event: 'dataRangeSelected',\n  // FIXME use updateView appears wrong\n  update: 'update'\n};\nexport var visualMapActionHander = function (payload, ecModel) {\n  ecModel.eachComponent({\n    mainType: 'visualMap',\n    query: payload\n  }, function (model) {\n    model.setSelected(payload.selected);\n  });\n};"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;AACO,IAAI,sBAAsB;IAC/B,MAAM;IACN,OAAO;IACP,qCAAqC;IACrC,QAAQ;AACV;AACO,IAAI,wBAAwB,SAAU,OAAO,EAAE,OAAO;IAC3D,QAAQ,aAAa,CAAC;QACpB,UAAU;QACV,OAAO;IACT,GAAG,SAAU,KAAK;QAChB,MAAM,WAAW,CAAC,QAAQ,QAAQ;IACpC;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1824, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/visualMap/visualEncoding.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as visualSolution from '../../visual/visualSolution.js';\nimport VisualMapping from '../../visual/VisualMapping.js';\nimport { getVisualFromData } from '../../visual/helper.js';\nexport var visualMapEncodingHandlers = [{\n  createOnAllSeries: true,\n  reset: function (seriesModel, ecModel) {\n    var resetDefines = [];\n    ecModel.eachComponent('visualMap', function (visualMapModel) {\n      var pipelineContext = seriesModel.pipelineContext;\n      if (!visualMapModel.isTargetSeries(seriesModel) || pipelineContext && pipelineContext.large) {\n        return;\n      }\n      resetDefines.push(visualSolution.incrementalApplyVisual(visualMapModel.stateList, visualMapModel.targetVisuals, zrUtil.bind(visualMapModel.getValueState, visualMapModel), visualMapModel.getDataDimensionIndex(seriesModel.getData())));\n    });\n    return resetDefines;\n  }\n},\n// Only support color.\n{\n  createOnAllSeries: true,\n  reset: function (seriesModel, ecModel) {\n    var data = seriesModel.getData();\n    var visualMetaList = [];\n    ecModel.eachComponent('visualMap', function (visualMapModel) {\n      if (visualMapModel.isTargetSeries(seriesModel)) {\n        var visualMeta = visualMapModel.getVisualMeta(zrUtil.bind(getColorVisual, null, seriesModel, visualMapModel)) || {\n          stops: [],\n          outerColors: []\n        };\n        var dimIdx = visualMapModel.getDataDimensionIndex(data);\n        if (dimIdx >= 0) {\n          // visualMeta.dimension should be dimension index, but not concrete dimension.\n          visualMeta.dimension = dimIdx;\n          visualMetaList.push(visualMeta);\n        }\n      }\n    });\n    // console.log(JSON.stringify(visualMetaList.map(a => a.stops)));\n    seriesModel.getData().setVisual('visualMeta', visualMetaList);\n  }\n}];\n// FIXME\n// performance and export for heatmap?\n// value can be Infinity or -Infinity\nfunction getColorVisual(seriesModel, visualMapModel, value, valueState) {\n  var mappings = visualMapModel.targetVisuals[valueState];\n  var visualTypes = VisualMapping.prepareVisualTypes(mappings);\n  var resultVisual = {\n    color: getVisualFromData(seriesModel.getData(), 'color') // default color.\n  };\n  for (var i = 0, len = visualTypes.length; i < len; i++) {\n    var type = visualTypes[i];\n    var mapping = mappings[type === 'opacity' ? '__alphaForOpacity' : type];\n    mapping && mapping.applyVisual(value, getVisual, setVisual);\n  }\n  return resultVisual.color;\n  function getVisual(key) {\n    return resultVisual[key];\n  }\n  function setVisual(key, value) {\n    resultVisual[key] = value;\n  }\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;;;;;AACO,IAAI,4BAA4B;IAAC;QACtC,mBAAmB;QACnB,OAAO,SAAU,WAAW,EAAE,OAAO;YACnC,IAAI,eAAe,EAAE;YACrB,QAAQ,aAAa,CAAC,aAAa,SAAU,cAAc;gBACzD,IAAI,kBAAkB,YAAY,eAAe;gBACjD,IAAI,CAAC,eAAe,cAAc,CAAC,gBAAgB,mBAAmB,gBAAgB,KAAK,EAAE;oBAC3F;gBACF;gBACA,aAAa,IAAI,CAAC,CAAA,GAAA,6JAAA,CAAA,yBAAqC,AAAD,EAAE,eAAe,SAAS,EAAE,eAAe,aAAa,EAAE,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,eAAe,aAAa,EAAE,iBAAiB,eAAe,qBAAqB,CAAC,YAAY,OAAO;YACrO;YACA,OAAO;QACT;IACF;IACA,sBAAsB;IACtB;QACE,mBAAmB;QACnB,OAAO,SAAU,WAAW,EAAE,OAAO;YACnC,IAAI,OAAO,YAAY,OAAO;YAC9B,IAAI,iBAAiB,EAAE;YACvB,QAAQ,aAAa,CAAC,aAAa,SAAU,cAAc;gBACzD,IAAI,eAAe,cAAc,CAAC,cAAc;oBAC9C,IAAI,aAAa,eAAe,aAAa,CAAC,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,gBAAgB,MAAM,aAAa,oBAAoB;wBAC/G,OAAO,EAAE;wBACT,aAAa,EAAE;oBACjB;oBACA,IAAI,SAAS,eAAe,qBAAqB,CAAC;oBAClD,IAAI,UAAU,GAAG;wBACf,8EAA8E;wBAC9E,WAAW,SAAS,GAAG;wBACvB,eAAe,IAAI,CAAC;oBACtB;gBACF;YACF;YACA,iEAAiE;YACjE,YAAY,OAAO,GAAG,SAAS,CAAC,cAAc;QAChD;IACF;CAAE;AACF,QAAQ;AACR,sCAAsC;AACtC,qCAAqC;AACrC,SAAS,eAAe,WAAW,EAAE,cAAc,EAAE,KAAK,EAAE,UAAU;IACpE,IAAI,WAAW,eAAe,aAAa,CAAC,WAAW;IACvD,IAAI,cAAc,4JAAA,CAAA,UAAa,CAAC,kBAAkB,CAAC;IACnD,IAAI,eAAe;QACjB,OAAO,CAAA,GAAA,qJAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY,OAAO,IAAI,SAAS,iBAAiB;IAC5E;IACA,IAAK,IAAI,IAAI,GAAG,MAAM,YAAY,MAAM,EAAE,IAAI,KAAK,IAAK;QACtD,IAAI,OAAO,WAAW,CAAC,EAAE;QACzB,IAAI,UAAU,QAAQ,CAAC,SAAS,YAAY,sBAAsB,KAAK;QACvE,WAAW,QAAQ,WAAW,CAAC,OAAO,WAAW;IACnD;IACA,OAAO,aAAa,KAAK;;IACzB,SAAS,UAAU,GAAG;QACpB,OAAO,YAAY,CAAC,IAAI;IAC1B;IACA,SAAS,UAAU,GAAG,EAAE,KAAK;QAC3B,YAAY,CAAC,IAAI,GAAG;IACtB;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1940, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/visualMap/preprocessor.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n// @ts-nocheck\nimport * as zrUtil from 'zrender/lib/core/util.js';\nvar each = zrUtil.each;\nexport default function visualMapPreprocessor(option) {\n  var visualMap = option && option.visualMap;\n  if (!zrUtil.isArray(visualMap)) {\n    visualMap = visualMap ? [visualMap] : [];\n  }\n  each(visualMap, function (opt) {\n    if (!opt) {\n      return;\n    }\n    // rename splitList to pieces\n    if (has(opt, 'splitList') && !has(opt, 'pieces')) {\n      opt.pieces = opt.splitList;\n      delete opt.splitList;\n    }\n    var pieces = opt.pieces;\n    if (pieces && zrUtil.isArray(pieces)) {\n      each(pieces, function (piece) {\n        if (zrUtil.isObject(piece)) {\n          if (has(piece, 'start') && !has(piece, 'min')) {\n            piece.min = piece.start;\n          }\n          if (has(piece, 'end') && !has(piece, 'max')) {\n            piece.max = piece.end;\n          }\n        }\n      });\n    }\n  });\n}\nfunction has(obj, name) {\n  return obj && obj.hasOwnProperty && obj.hasOwnProperty(name);\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA,GACA,cAAc;;;;AACd;;AACA,IAAI,OAAO,iJAAA,CAAA,OAAW;AACP,SAAS,sBAAsB,MAAM;IAClD,IAAI,YAAY,UAAU,OAAO,SAAS;IAC1C,IAAI,CAAC,CAAA,GAAA,iJAAA,CAAA,UAAc,AAAD,EAAE,YAAY;QAC9B,YAAY,YAAY;YAAC;SAAU,GAAG,EAAE;IAC1C;IACA,KAAK,WAAW,SAAU,GAAG;QAC3B,IAAI,CAAC,KAAK;YACR;QACF;QACA,6BAA6B;QAC7B,IAAI,IAAI,KAAK,gBAAgB,CAAC,IAAI,KAAK,WAAW;YAChD,IAAI,MAAM,GAAG,IAAI,SAAS;YAC1B,OAAO,IAAI,SAAS;QACtB;QACA,IAAI,SAAS,IAAI,MAAM;QACvB,IAAI,UAAU,CAAA,GAAA,iJAAA,CAAA,UAAc,AAAD,EAAE,SAAS;YACpC,KAAK,QAAQ,SAAU,KAAK;gBAC1B,IAAI,CAAA,GAAA,iJAAA,CAAA,WAAe,AAAD,EAAE,QAAQ;oBAC1B,IAAI,IAAI,OAAO,YAAY,CAAC,IAAI,OAAO,QAAQ;wBAC7C,MAAM,GAAG,GAAG,MAAM,KAAK;oBACzB;oBACA,IAAI,IAAI,OAAO,UAAU,CAAC,IAAI,OAAO,QAAQ;wBAC3C,MAAM,GAAG,GAAG,MAAM,GAAG;oBACvB;gBACF;YACF;QACF;IACF;AACF;AACA,SAAS,IAAI,GAAG,EAAE,IAAI;IACpB,OAAO,OAAO,IAAI,cAAc,IAAI,IAAI,cAAc,CAAC;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2023, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/visualMap/installCommon.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { visualMapActionInfo, visualMapActionHander } from './visualMapAction.js';\nimport { visualMapEncodingHandlers } from './visualEncoding.js';\nimport { each } from 'zrender/lib/core/util.js';\nimport preprocessor from './preprocessor.js';\nvar installed = false;\nexport default function installCommon(registers) {\n  if (installed) {\n    return;\n  }\n  installed = true;\n  registers.registerSubTypeDefaulter('visualMap', function (option) {\n    // Compatible with ec2, when splitNumber === 0, continuous visualMap will be used.\n    return !option.categories && (!(option.pieces ? option.pieces.length > 0 : option.splitNumber > 0) || option.calculable) ? 'continuous' : 'piecewise';\n  });\n  registers.registerAction(visualMapActionInfo, visualMapActionHander);\n  each(visualMapEncodingHandlers, function (handler) {\n    registers.registerVisual(registers.PRIORITY.VISUAL.COMPONENT, handler);\n  });\n  registers.registerPreprocessor(preprocessor);\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;;;;;AACA,IAAI,YAAY;AACD,SAAS,cAAc,SAAS;IAC7C,IAAI,WAAW;QACb;IACF;IACA,YAAY;IACZ,UAAU,wBAAwB,CAAC,aAAa,SAAU,MAAM;QAC9D,kFAAkF;QAClF,OAAO,CAAC,OAAO,UAAU,IAAI,CAAC,CAAC,CAAC,OAAO,MAAM,GAAG,OAAO,MAAM,CAAC,MAAM,GAAG,IAAI,OAAO,WAAW,GAAG,CAAC,KAAK,OAAO,UAAU,IAAI,eAAe;IAC5I;IACA,UAAU,cAAc,CAAC,8KAAA,CAAA,sBAAmB,EAAE,8KAAA,CAAA,wBAAqB;IACnE,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,6KAAA,CAAA,4BAAyB,EAAE,SAAU,OAAO;QAC/C,UAAU,cAAc,CAAC,UAAU,QAAQ,CAAC,MAAM,CAAC,SAAS,EAAE;IAChE;IACA,UAAU,oBAAoB,CAAC,2KAAA,CAAA,UAAY;AAC7C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2092, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/visualMap/installVisualMapContinuous.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport ContinuousModel from './ContinuousModel.js';\nimport ContinuousView from './ContinuousView.js';\nimport installCommon from './installCommon.js';\nexport function install(registers) {\n  registers.registerComponentModel(ContinuousModel);\n  registers.registerComponentView(ContinuousView);\n  installCommon(registers);\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;;;;AACO,SAAS,QAAQ,SAAS;IAC/B,UAAU,sBAAsB,CAAC,8KAAA,CAAA,UAAe;IAChD,UAAU,qBAAqB,CAAC,6KAAA,CAAA,UAAc;IAC9C,CAAA,GAAA,4KAAA,CAAA,UAAa,AAAD,EAAE;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/visualMap/PiecewiseModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport VisualMapModel from './VisualMapModel.js';\nimport VisualMapping from '../../visual/VisualMapping.js';\nimport visualDefault from '../../visual/visualDefault.js';\nimport { reformIntervals } from '../../util/number.js';\nimport { inheritDefaultOption } from '../../util/component.js';\nvar PiecewiseModel = /** @class */function (_super) {\n  __extends(PiecewiseModel, _super);\n  function PiecewiseModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = PiecewiseModel.type;\n    /**\r\n     * The order is always [low, ..., high].\r\n     * [{text: string, interval: Array.<number>}, ...]\r\n     */\n    _this._pieceList = [];\n    return _this;\n  }\n  PiecewiseModel.prototype.optionUpdated = function (newOption, isInit) {\n    _super.prototype.optionUpdated.apply(this, arguments);\n    this.resetExtent();\n    var mode = this._mode = this._determineMode();\n    this._pieceList = [];\n    resetMethods[this._mode].call(this, this._pieceList);\n    this._resetSelected(newOption, isInit);\n    var categories = this.option.categories;\n    this.resetVisual(function (mappingOption, state) {\n      if (mode === 'categories') {\n        mappingOption.mappingMethod = 'category';\n        mappingOption.categories = zrUtil.clone(categories);\n      } else {\n        mappingOption.dataExtent = this.getExtent();\n        mappingOption.mappingMethod = 'piecewise';\n        mappingOption.pieceList = zrUtil.map(this._pieceList, function (piece) {\n          piece = zrUtil.clone(piece);\n          if (state !== 'inRange') {\n            // FIXME\n            // outOfRange do not support special visual in pieces.\n            piece.visual = null;\n          }\n          return piece;\n        });\n      }\n    });\n  };\n  /**\r\n   * @protected\r\n   * @override\r\n   */\n  PiecewiseModel.prototype.completeVisualOption = function () {\n    // Consider this case:\n    // visualMap: {\n    //      pieces: [{symbol: 'circle', lt: 0}, {symbol: 'rect', gte: 0}]\n    // }\n    // where no inRange/outOfRange set but only pieces. So we should make\n    // default inRange/outOfRange for this case, otherwise visuals that only\n    // appear in `pieces` will not be taken into account in visual encoding.\n    var option = this.option;\n    var visualTypesInPieces = {};\n    var visualTypes = VisualMapping.listVisualTypes();\n    var isCategory = this.isCategory();\n    zrUtil.each(option.pieces, function (piece) {\n      zrUtil.each(visualTypes, function (visualType) {\n        if (piece.hasOwnProperty(visualType)) {\n          visualTypesInPieces[visualType] = 1;\n        }\n      });\n    });\n    zrUtil.each(visualTypesInPieces, function (v, visualType) {\n      var exists = false;\n      zrUtil.each(this.stateList, function (state) {\n        exists = exists || has(option, state, visualType) || has(option.target, state, visualType);\n      }, this);\n      !exists && zrUtil.each(this.stateList, function (state) {\n        (option[state] || (option[state] = {}))[visualType] = visualDefault.get(visualType, state === 'inRange' ? 'active' : 'inactive', isCategory);\n      });\n    }, this);\n    function has(obj, state, visualType) {\n      return obj && obj[state] && obj[state].hasOwnProperty(visualType);\n    }\n    _super.prototype.completeVisualOption.apply(this, arguments);\n  };\n  PiecewiseModel.prototype._resetSelected = function (newOption, isInit) {\n    var thisOption = this.option;\n    var pieceList = this._pieceList;\n    // Selected do not merge but all override.\n    var selected = (isInit ? thisOption : newOption).selected || {};\n    thisOption.selected = selected;\n    // Consider 'not specified' means true.\n    zrUtil.each(pieceList, function (piece, index) {\n      var key = this.getSelectedMapKey(piece);\n      if (!selected.hasOwnProperty(key)) {\n        selected[key] = true;\n      }\n    }, this);\n    if (thisOption.selectedMode === 'single') {\n      // Ensure there is only one selected.\n      var hasSel_1 = false;\n      zrUtil.each(pieceList, function (piece, index) {\n        var key = this.getSelectedMapKey(piece);\n        if (selected[key]) {\n          hasSel_1 ? selected[key] = false : hasSel_1 = true;\n        }\n      }, this);\n    }\n    // thisOption.selectedMode === 'multiple', default: all selected.\n  };\n  /**\r\n   * @public\r\n   */\n  PiecewiseModel.prototype.getItemSymbol = function () {\n    return this.get('itemSymbol');\n  };\n  /**\r\n   * @public\r\n   */\n  PiecewiseModel.prototype.getSelectedMapKey = function (piece) {\n    return this._mode === 'categories' ? piece.value + '' : piece.index + '';\n  };\n  /**\r\n   * @public\r\n   */\n  PiecewiseModel.prototype.getPieceList = function () {\n    return this._pieceList;\n  };\n  /**\r\n   * @return {string}\r\n   */\n  PiecewiseModel.prototype._determineMode = function () {\n    var option = this.option;\n    return option.pieces && option.pieces.length > 0 ? 'pieces' : this.option.categories ? 'categories' : 'splitNumber';\n  };\n  /**\r\n   * @override\r\n   */\n  PiecewiseModel.prototype.setSelected = function (selected) {\n    this.option.selected = zrUtil.clone(selected);\n  };\n  /**\r\n   * @override\r\n   */\n  PiecewiseModel.prototype.getValueState = function (value) {\n    var index = VisualMapping.findPieceIndex(value, this._pieceList);\n    return index != null ? this.option.selected[this.getSelectedMapKey(this._pieceList[index])] ? 'inRange' : 'outOfRange' : 'outOfRange';\n  };\n  /**\r\n   * @public\r\n   * @param pieceIndex piece index in visualMapModel.getPieceList()\r\n   */\n  PiecewiseModel.prototype.findTargetDataIndices = function (pieceIndex) {\n    var result = [];\n    var pieceList = this._pieceList;\n    this.eachTargetSeries(function (seriesModel) {\n      var dataIndices = [];\n      var data = seriesModel.getData();\n      data.each(this.getDataDimensionIndex(data), function (value, dataIndex) {\n        // Should always base on model pieceList, because it is order sensitive.\n        var pIdx = VisualMapping.findPieceIndex(value, pieceList);\n        pIdx === pieceIndex && dataIndices.push(dataIndex);\n      }, this);\n      result.push({\n        seriesId: seriesModel.id,\n        dataIndex: dataIndices\n      });\n    }, this);\n    return result;\n  };\n  /**\r\n   * @private\r\n   * @param piece piece.value or piece.interval is required.\r\n   * @return  Can be Infinity or -Infinity\r\n   */\n  PiecewiseModel.prototype.getRepresentValue = function (piece) {\n    var representValue;\n    if (this.isCategory()) {\n      representValue = piece.value;\n    } else {\n      if (piece.value != null) {\n        representValue = piece.value;\n      } else {\n        var pieceInterval = piece.interval || [];\n        representValue = pieceInterval[0] === -Infinity && pieceInterval[1] === Infinity ? 0 : (pieceInterval[0] + pieceInterval[1]) / 2;\n      }\n    }\n    return representValue;\n  };\n  PiecewiseModel.prototype.getVisualMeta = function (getColorVisual) {\n    // Do not support category. (category axis is ordinal, numerical)\n    if (this.isCategory()) {\n      return;\n    }\n    var stops = [];\n    var outerColors = ['', ''];\n    var visualMapModel = this;\n    function setStop(interval, valueState) {\n      var representValue = visualMapModel.getRepresentValue({\n        interval: interval\n      }); // Not category\n      if (!valueState) {\n        valueState = visualMapModel.getValueState(representValue);\n      }\n      var color = getColorVisual(representValue, valueState);\n      if (interval[0] === -Infinity) {\n        outerColors[0] = color;\n      } else if (interval[1] === Infinity) {\n        outerColors[1] = color;\n      } else {\n        stops.push({\n          value: interval[0],\n          color: color\n        }, {\n          value: interval[1],\n          color: color\n        });\n      }\n    }\n    // Suplement\n    var pieceList = this._pieceList.slice();\n    if (!pieceList.length) {\n      pieceList.push({\n        interval: [-Infinity, Infinity]\n      });\n    } else {\n      var edge = pieceList[0].interval[0];\n      edge !== -Infinity && pieceList.unshift({\n        interval: [-Infinity, edge]\n      });\n      edge = pieceList[pieceList.length - 1].interval[1];\n      edge !== Infinity && pieceList.push({\n        interval: [edge, Infinity]\n      });\n    }\n    var curr = -Infinity;\n    zrUtil.each(pieceList, function (piece) {\n      var interval = piece.interval;\n      if (interval) {\n        // Fulfill gap.\n        interval[0] > curr && setStop([curr, interval[0]], 'outOfRange');\n        setStop(interval.slice());\n        curr = interval[1];\n      }\n    }, this);\n    return {\n      stops: stops,\n      outerColors: outerColors\n    };\n  };\n  PiecewiseModel.type = 'visualMap.piecewise';\n  PiecewiseModel.defaultOption = inheritDefaultOption(VisualMapModel.defaultOption, {\n    selected: null,\n    minOpen: false,\n    maxOpen: false,\n    align: 'auto',\n    itemWidth: 20,\n    itemHeight: 14,\n    itemSymbol: 'roundRect',\n    pieces: null,\n    categories: null,\n    splitNumber: 5,\n    selectedMode: 'multiple',\n    itemGap: 10,\n    hoverLink: true // Enable hover highlight.\n  });\n  return PiecewiseModel;\n}(VisualMapModel);\n;\n/**\r\n * Key is this._mode\r\n * @type {Object}\r\n * @this {module:echarts/component/viusalMap/PiecewiseMode}\r\n */\nvar resetMethods = {\n  splitNumber: function (outPieceList) {\n    var thisOption = this.option;\n    var precision = Math.min(thisOption.precision, 20);\n    var dataExtent = this.getExtent();\n    var splitNumber = thisOption.splitNumber;\n    splitNumber = Math.max(parseInt(splitNumber, 10), 1);\n    thisOption.splitNumber = splitNumber;\n    var splitStep = (dataExtent[1] - dataExtent[0]) / splitNumber;\n    // Precision auto-adaption\n    while (+splitStep.toFixed(precision) !== splitStep && precision < 5) {\n      precision++;\n    }\n    thisOption.precision = precision;\n    splitStep = +splitStep.toFixed(precision);\n    if (thisOption.minOpen) {\n      outPieceList.push({\n        interval: [-Infinity, dataExtent[0]],\n        close: [0, 0]\n      });\n    }\n    for (var index = 0, curr = dataExtent[0]; index < splitNumber; curr += splitStep, index++) {\n      var max = index === splitNumber - 1 ? dataExtent[1] : curr + splitStep;\n      outPieceList.push({\n        interval: [curr, max],\n        close: [1, 1]\n      });\n    }\n    if (thisOption.maxOpen) {\n      outPieceList.push({\n        interval: [dataExtent[1], Infinity],\n        close: [0, 0]\n      });\n    }\n    reformIntervals(outPieceList);\n    zrUtil.each(outPieceList, function (piece, index) {\n      piece.index = index;\n      piece.text = this.formatValueText(piece.interval);\n    }, this);\n  },\n  categories: function (outPieceList) {\n    var thisOption = this.option;\n    zrUtil.each(thisOption.categories, function (cate) {\n      // FIXME category模式也使用pieceList，但在visualMapping中不是使用pieceList。\n      // 是否改一致。\n      outPieceList.push({\n        text: this.formatValueText(cate, true),\n        value: cate\n      });\n    }, this);\n    // See \"Order Rule\".\n    normalizeReverse(thisOption, outPieceList);\n  },\n  pieces: function (outPieceList) {\n    var thisOption = this.option;\n    zrUtil.each(thisOption.pieces, function (pieceListItem, index) {\n      if (!zrUtil.isObject(pieceListItem)) {\n        pieceListItem = {\n          value: pieceListItem\n        };\n      }\n      var item = {\n        text: '',\n        index: index\n      };\n      if (pieceListItem.label != null) {\n        item.text = pieceListItem.label;\n      }\n      if (pieceListItem.hasOwnProperty('value')) {\n        var value = item.value = pieceListItem.value;\n        item.interval = [value, value];\n        item.close = [1, 1];\n      } else {\n        // `min` `max` is legacy option.\n        // `lt` `gt` `lte` `gte` is recommended.\n        var interval = item.interval = [];\n        var close_1 = item.close = [0, 0];\n        var closeList = [1, 0, 1];\n        var infinityList = [-Infinity, Infinity];\n        var useMinMax = [];\n        for (var lg = 0; lg < 2; lg++) {\n          var names = [['gte', 'gt', 'min'], ['lte', 'lt', 'max']][lg];\n          for (var i = 0; i < 3 && interval[lg] == null; i++) {\n            interval[lg] = pieceListItem[names[i]];\n            close_1[lg] = closeList[i];\n            useMinMax[lg] = i === 2;\n          }\n          interval[lg] == null && (interval[lg] = infinityList[lg]);\n        }\n        useMinMax[0] && interval[1] === Infinity && (close_1[0] = 0);\n        useMinMax[1] && interval[0] === -Infinity && (close_1[1] = 0);\n        if (process.env.NODE_ENV !== 'production') {\n          if (interval[0] > interval[1]) {\n            console.warn('Piece ' + index + 'is illegal: ' + interval + ' lower bound should not greater then uppper bound.');\n          }\n        }\n        if (interval[0] === interval[1] && close_1[0] && close_1[1]) {\n          // Consider: [{min: 5, max: 5, visual: {...}}, {min: 0, max: 5}],\n          // we use value to lift the priority when min === max\n          item.value = interval[0];\n        }\n      }\n      item.visual = VisualMapping.retrieveVisuals(pieceListItem);\n      outPieceList.push(item);\n    }, this);\n    // See \"Order Rule\".\n    normalizeReverse(thisOption, outPieceList);\n    // Only pieces\n    reformIntervals(outPieceList);\n    zrUtil.each(outPieceList, function (piece) {\n      var close = piece.close;\n      var edgeSymbols = [['<', '≤'][close[1]], ['>', '≥'][close[0]]];\n      piece.text = piece.text || this.formatValueText(piece.value != null ? piece.value : piece.interval, false, edgeSymbols);\n    }, this);\n  }\n};\nfunction normalizeReverse(thisOption, pieceList) {\n  var inverse = thisOption.inverse;\n  if (thisOption.orient === 'vertical' ? !inverse : inverse) {\n    pieceList.reverse();\n  }\n}\nexport default PiecewiseModel;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AA4WY;AA3WZ;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACA,IAAI,iBAAiB,WAAW,GAAE,SAAU,MAAM;IAChD,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;IAC1B,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,eAAe,IAAI;QAChC;;;KAGC,GACD,MAAM,UAAU,GAAG,EAAE;QACrB,OAAO;IACT;IACA,eAAe,SAAS,CAAC,aAAa,GAAG,SAAU,SAAS,EAAE,MAAM;QAClE,OAAO,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE;QAC3C,IAAI,CAAC,WAAW;QAChB,IAAI,OAAO,IAAI,CAAC,KAAK,GAAG,IAAI,CAAC,cAAc;QAC3C,IAAI,CAAC,UAAU,GAAG,EAAE;QACpB,YAAY,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU;QACnD,IAAI,CAAC,cAAc,CAAC,WAAW;QAC/B,IAAI,aAAa,IAAI,CAAC,MAAM,CAAC,UAAU;QACvC,IAAI,CAAC,WAAW,CAAC,SAAU,aAAa,EAAE,KAAK;YAC7C,IAAI,SAAS,cAAc;gBACzB,cAAc,aAAa,GAAG;gBAC9B,cAAc,UAAU,GAAG,CAAA,GAAA,iJAAA,CAAA,QAAY,AAAD,EAAE;YAC1C,OAAO;gBACL,cAAc,UAAU,GAAG,IAAI,CAAC,SAAS;gBACzC,cAAc,aAAa,GAAG;gBAC9B,cAAc,SAAS,GAAG,CAAA,GAAA,iJAAA,CAAA,MAAU,AAAD,EAAE,IAAI,CAAC,UAAU,EAAE,SAAU,KAAK;oBACnE,QAAQ,CAAA,GAAA,iJAAA,CAAA,QAAY,AAAD,EAAE;oBACrB,IAAI,UAAU,WAAW;wBACvB,QAAQ;wBACR,sDAAsD;wBACtD,MAAM,MAAM,GAAG;oBACjB;oBACA,OAAO;gBACT;YACF;QACF;IACF;IACA;;;GAGC,GACD,eAAe,SAAS,CAAC,oBAAoB,GAAG;QAC9C,sBAAsB;QACtB,eAAe;QACf,qEAAqE;QACrE,IAAI;QACJ,qEAAqE;QACrE,wEAAwE;QACxE,wEAAwE;QACxE,IAAI,SAAS,IAAI,CAAC,MAAM;QACxB,IAAI,sBAAsB,CAAC;QAC3B,IAAI,cAAc,4JAAA,CAAA,UAAa,CAAC,eAAe;QAC/C,IAAI,aAAa,IAAI,CAAC,UAAU;QAChC,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,OAAO,MAAM,EAAE,SAAU,KAAK;YACxC,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,aAAa,SAAU,UAAU;gBAC3C,IAAI,MAAM,cAAc,CAAC,aAAa;oBACpC,mBAAmB,CAAC,WAAW,GAAG;gBACpC;YACF;QACF;QACA,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,qBAAqB,SAAU,CAAC,EAAE,UAAU;YACtD,IAAI,SAAS;YACb,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,IAAI,CAAC,SAAS,EAAE,SAAU,KAAK;gBACzC,SAAS,UAAU,IAAI,QAAQ,OAAO,eAAe,IAAI,OAAO,MAAM,EAAE,OAAO;YACjF,GAAG,IAAI;YACP,CAAC,UAAU,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,IAAI,CAAC,SAAS,EAAE,SAAU,KAAK;gBACpD,CAAC,MAAM,CAAC,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,WAAW,GAAG,4JAAA,CAAA,UAAa,CAAC,GAAG,CAAC,YAAY,UAAU,YAAY,WAAW,YAAY;YACnI;QACF,GAAG,IAAI;QACP,SAAS,IAAI,GAAG,EAAE,KAAK,EAAE,UAAU;YACjC,OAAO,OAAO,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,MAAM,CAAC,cAAc,CAAC;QACxD;QACA,OAAO,SAAS,CAAC,oBAAoB,CAAC,KAAK,CAAC,IAAI,EAAE;IACpD;IACA,eAAe,SAAS,CAAC,cAAc,GAAG,SAAU,SAAS,EAAE,MAAM;QACnE,IAAI,aAAa,IAAI,CAAC,MAAM;QAC5B,IAAI,YAAY,IAAI,CAAC,UAAU;QAC/B,0CAA0C;QAC1C,IAAI,WAAW,CAAC,SAAS,aAAa,SAAS,EAAE,QAAQ,IAAI,CAAC;QAC9D,WAAW,QAAQ,GAAG;QACtB,uCAAuC;QACvC,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,WAAW,SAAU,KAAK,EAAE,KAAK;YAC3C,IAAI,MAAM,IAAI,CAAC,iBAAiB,CAAC;YACjC,IAAI,CAAC,SAAS,cAAc,CAAC,MAAM;gBACjC,QAAQ,CAAC,IAAI,GAAG;YAClB;QACF,GAAG,IAAI;QACP,IAAI,WAAW,YAAY,KAAK,UAAU;YACxC,qCAAqC;YACrC,IAAI,WAAW;YACf,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,WAAW,SAAU,KAAK,EAAE,KAAK;gBAC3C,IAAI,MAAM,IAAI,CAAC,iBAAiB,CAAC;gBACjC,IAAI,QAAQ,CAAC,IAAI,EAAE;oBACjB,WAAW,QAAQ,CAAC,IAAI,GAAG,QAAQ,WAAW;gBAChD;YACF,GAAG,IAAI;QACT;IACA,iEAAiE;IACnE;IACA;;GAEC,GACD,eAAe,SAAS,CAAC,aAAa,GAAG;QACvC,OAAO,IAAI,CAAC,GAAG,CAAC;IAClB;IACA;;GAEC,GACD,eAAe,SAAS,CAAC,iBAAiB,GAAG,SAAU,KAAK;QAC1D,OAAO,IAAI,CAAC,KAAK,KAAK,eAAe,MAAM,KAAK,GAAG,KAAK,MAAM,KAAK,GAAG;IACxE;IACA;;GAEC,GACD,eAAe,SAAS,CAAC,YAAY,GAAG;QACtC,OAAO,IAAI,CAAC,UAAU;IACxB;IACA;;GAEC,GACD,eAAe,SAAS,CAAC,cAAc,GAAG;QACxC,IAAI,SAAS,IAAI,CAAC,MAAM;QACxB,OAAO,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC,MAAM,GAAG,IAAI,WAAW,IAAI,CAAC,MAAM,CAAC,UAAU,GAAG,eAAe;IACxG;IACA;;GAEC,GACD,eAAe,SAAS,CAAC,WAAW,GAAG,SAAU,QAAQ;QACvD,IAAI,CAAC,MAAM,CAAC,QAAQ,GAAG,CAAA,GAAA,iJAAA,CAAA,QAAY,AAAD,EAAE;IACtC;IACA;;GAEC,GACD,eAAe,SAAS,CAAC,aAAa,GAAG,SAAU,KAAK;QACtD,IAAI,QAAQ,4JAAA,CAAA,UAAa,CAAC,cAAc,CAAC,OAAO,IAAI,CAAC,UAAU;QAC/D,OAAO,SAAS,OAAO,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,IAAI,CAAC,UAAU,CAAC,MAAM,EAAE,GAAG,YAAY,eAAe;IAC3H;IACA;;;GAGC,GACD,eAAe,SAAS,CAAC,qBAAqB,GAAG,SAAU,UAAU;QACnE,IAAI,SAAS,EAAE;QACf,IAAI,YAAY,IAAI,CAAC,UAAU;QAC/B,IAAI,CAAC,gBAAgB,CAAC,SAAU,WAAW;YACzC,IAAI,cAAc,EAAE;YACpB,IAAI,OAAO,YAAY,OAAO;YAC9B,KAAK,IAAI,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO,SAAU,KAAK,EAAE,SAAS;gBACpE,wEAAwE;gBACxE,IAAI,OAAO,4JAAA,CAAA,UAAa,CAAC,cAAc,CAAC,OAAO;gBAC/C,SAAS,cAAc,YAAY,IAAI,CAAC;YAC1C,GAAG,IAAI;YACP,OAAO,IAAI,CAAC;gBACV,UAAU,YAAY,EAAE;gBACxB,WAAW;YACb;QACF,GAAG,IAAI;QACP,OAAO;IACT;IACA;;;;GAIC,GACD,eAAe,SAAS,CAAC,iBAAiB,GAAG,SAAU,KAAK;QAC1D,IAAI;QACJ,IAAI,IAAI,CAAC,UAAU,IAAI;YACrB,iBAAiB,MAAM,KAAK;QAC9B,OAAO;YACL,IAAI,MAAM,KAAK,IAAI,MAAM;gBACvB,iBAAiB,MAAM,KAAK;YAC9B,OAAO;gBACL,IAAI,gBAAgB,MAAM,QAAQ,IAAI,EAAE;gBACxC,iBAAiB,aAAa,CAAC,EAAE,KAAK,CAAC,YAAY,aAAa,CAAC,EAAE,KAAK,WAAW,IAAI,CAAC,aAAa,CAAC,EAAE,GAAG,aAAa,CAAC,EAAE,IAAI;YACjI;QACF;QACA,OAAO;IACT;IACA,eAAe,SAAS,CAAC,aAAa,GAAG,SAAU,cAAc;QAC/D,iEAAiE;QACjE,IAAI,IAAI,CAAC,UAAU,IAAI;YACrB;QACF;QACA,IAAI,QAAQ,EAAE;QACd,IAAI,cAAc;YAAC;YAAI;SAAG;QAC1B,IAAI,iBAAiB,IAAI;QACzB,SAAS,QAAQ,QAAQ,EAAE,UAAU;YACnC,IAAI,iBAAiB,eAAe,iBAAiB,CAAC;gBACpD,UAAU;YACZ,IAAI,eAAe;YACnB,IAAI,CAAC,YAAY;gBACf,aAAa,eAAe,aAAa,CAAC;YAC5C;YACA,IAAI,QAAQ,eAAe,gBAAgB;YAC3C,IAAI,QAAQ,CAAC,EAAE,KAAK,CAAC,UAAU;gBAC7B,WAAW,CAAC,EAAE,GAAG;YACnB,OAAO,IAAI,QAAQ,CAAC,EAAE,KAAK,UAAU;gBACnC,WAAW,CAAC,EAAE,GAAG;YACnB,OAAO;gBACL,MAAM,IAAI,CAAC;oBACT,OAAO,QAAQ,CAAC,EAAE;oBAClB,OAAO;gBACT,GAAG;oBACD,OAAO,QAAQ,CAAC,EAAE;oBAClB,OAAO;gBACT;YACF;QACF;QACA,YAAY;QACZ,IAAI,YAAY,IAAI,CAAC,UAAU,CAAC,KAAK;QACrC,IAAI,CAAC,UAAU,MAAM,EAAE;YACrB,UAAU,IAAI,CAAC;gBACb,UAAU;oBAAC,CAAC;oBAAU;iBAAS;YACjC;QACF,OAAO;YACL,IAAI,OAAO,SAAS,CAAC,EAAE,CAAC,QAAQ,CAAC,EAAE;YACnC,SAAS,CAAC,YAAY,UAAU,OAAO,CAAC;gBACtC,UAAU;oBAAC,CAAC;oBAAU;iBAAK;YAC7B;YACA,OAAO,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE;YAClD,SAAS,YAAY,UAAU,IAAI,CAAC;gBAClC,UAAU;oBAAC;oBAAM;iBAAS;YAC5B;QACF;QACA,IAAI,OAAO,CAAC;QACZ,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,WAAW,SAAU,KAAK;YACpC,IAAI,WAAW,MAAM,QAAQ;YAC7B,IAAI,UAAU;gBACZ,eAAe;gBACf,QAAQ,CAAC,EAAE,GAAG,QAAQ,QAAQ;oBAAC;oBAAM,QAAQ,CAAC,EAAE;iBAAC,EAAE;gBACnD,QAAQ,SAAS,KAAK;gBACtB,OAAO,QAAQ,CAAC,EAAE;YACpB;QACF,GAAG,IAAI;QACP,OAAO;YACL,OAAO;YACP,aAAa;QACf;IACF;IACA,eAAe,IAAI,GAAG;IACtB,eAAe,aAAa,GAAG,CAAA,GAAA,sJAAA,CAAA,uBAAoB,AAAD,EAAE,6KAAA,CAAA,UAAc,CAAC,aAAa,EAAE;QAChF,UAAU;QACV,SAAS;QACT,SAAS;QACT,OAAO;QACP,WAAW;QACX,YAAY;QACZ,YAAY;QACZ,QAAQ;QACR,YAAY;QACZ,aAAa;QACb,cAAc;QACd,SAAS;QACT,WAAW,KAAK,0BAA0B;IAC5C;IACA,OAAO;AACT,EAAE,6KAAA,CAAA,UAAc;;AAEhB;;;;CAIC,GACD,IAAI,eAAe;IACjB,aAAa,SAAU,YAAY;QACjC,IAAI,aAAa,IAAI,CAAC,MAAM;QAC5B,IAAI,YAAY,KAAK,GAAG,CAAC,WAAW,SAAS,EAAE;QAC/C,IAAI,aAAa,IAAI,CAAC,SAAS;QAC/B,IAAI,cAAc,WAAW,WAAW;QACxC,cAAc,KAAK,GAAG,CAAC,SAAS,aAAa,KAAK;QAClD,WAAW,WAAW,GAAG;QACzB,IAAI,YAAY,CAAC,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,IAAI;QAClD,0BAA0B;QAC1B,MAAO,CAAC,UAAU,OAAO,CAAC,eAAe,aAAa,YAAY,EAAG;YACnE;QACF;QACA,WAAW,SAAS,GAAG;QACvB,YAAY,CAAC,UAAU,OAAO,CAAC;QAC/B,IAAI,WAAW,OAAO,EAAE;YACtB,aAAa,IAAI,CAAC;gBAChB,UAAU;oBAAC,CAAC;oBAAU,UAAU,CAAC,EAAE;iBAAC;gBACpC,OAAO;oBAAC;oBAAG;iBAAE;YACf;QACF;QACA,IAAK,IAAI,QAAQ,GAAG,OAAO,UAAU,CAAC,EAAE,EAAE,QAAQ,aAAa,QAAQ,WAAW,QAAS;YACzF,IAAI,MAAM,UAAU,cAAc,IAAI,UAAU,CAAC,EAAE,GAAG,OAAO;YAC7D,aAAa,IAAI,CAAC;gBAChB,UAAU;oBAAC;oBAAM;iBAAI;gBACrB,OAAO;oBAAC;oBAAG;iBAAE;YACf;QACF;QACA,IAAI,WAAW,OAAO,EAAE;YACtB,aAAa,IAAI,CAAC;gBAChB,UAAU;oBAAC,UAAU,CAAC,EAAE;oBAAE;iBAAS;gBACnC,OAAO;oBAAC;oBAAG;iBAAE;YACf;QACF;QACA,CAAA,GAAA,mJAAA,CAAA,kBAAe,AAAD,EAAE;QAChB,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,cAAc,SAAU,KAAK,EAAE,KAAK;YAC9C,MAAM,KAAK,GAAG;YACd,MAAM,IAAI,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,QAAQ;QAClD,GAAG,IAAI;IACT;IACA,YAAY,SAAU,YAAY;QAChC,IAAI,aAAa,IAAI,CAAC,MAAM;QAC5B,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,WAAW,UAAU,EAAE,SAAU,IAAI;YAC/C,8DAA8D;YAC9D,SAAS;YACT,aAAa,IAAI,CAAC;gBAChB,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM;gBACjC,OAAO;YACT;QACF,GAAG,IAAI;QACP,oBAAoB;QACpB,iBAAiB,YAAY;IAC/B;IACA,QAAQ,SAAU,YAAY;QAC5B,IAAI,aAAa,IAAI,CAAC,MAAM;QAC5B,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,WAAW,MAAM,EAAE,SAAU,aAAa,EAAE,KAAK;YAC3D,IAAI,CAAC,CAAA,GAAA,iJAAA,CAAA,WAAe,AAAD,EAAE,gBAAgB;gBACnC,gBAAgB;oBACd,OAAO;gBACT;YACF;YACA,IAAI,OAAO;gBACT,MAAM;gBACN,OAAO;YACT;YACA,IAAI,cAAc,KAAK,IAAI,MAAM;gBAC/B,KAAK,IAAI,GAAG,cAAc,KAAK;YACjC;YACA,IAAI,cAAc,cAAc,CAAC,UAAU;gBACzC,IAAI,QAAQ,KAAK,KAAK,GAAG,cAAc,KAAK;gBAC5C,KAAK,QAAQ,GAAG;oBAAC;oBAAO;iBAAM;gBAC9B,KAAK,KAAK,GAAG;oBAAC;oBAAG;iBAAE;YACrB,OAAO;gBACL,gCAAgC;gBAChC,wCAAwC;gBACxC,IAAI,WAAW,KAAK,QAAQ,GAAG,EAAE;gBACjC,IAAI,UAAU,KAAK,KAAK,GAAG;oBAAC;oBAAG;iBAAE;gBACjC,IAAI,YAAY;oBAAC;oBAAG;oBAAG;iBAAE;gBACzB,IAAI,eAAe;oBAAC,CAAC;oBAAU;iBAAS;gBACxC,IAAI,YAAY,EAAE;gBAClB,IAAK,IAAI,KAAK,GAAG,KAAK,GAAG,KAAM;oBAC7B,IAAI,QAAQ;wBAAC;4BAAC;4BAAO;4BAAM;yBAAM;wBAAE;4BAAC;4BAAO;4BAAM;yBAAM;qBAAC,CAAC,GAAG;oBAC5D,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,QAAQ,CAAC,GAAG,IAAI,MAAM,IAAK;wBAClD,QAAQ,CAAC,GAAG,GAAG,aAAa,CAAC,KAAK,CAAC,EAAE,CAAC;wBACtC,OAAO,CAAC,GAAG,GAAG,SAAS,CAAC,EAAE;wBAC1B,SAAS,CAAC,GAAG,GAAG,MAAM;oBACxB;oBACA,QAAQ,CAAC,GAAG,IAAI,QAAQ,CAAC,QAAQ,CAAC,GAAG,GAAG,YAAY,CAAC,GAAG;gBAC1D;gBACA,SAAS,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,KAAK,YAAY,CAAC,OAAO,CAAC,EAAE,GAAG,CAAC;gBAC3D,SAAS,CAAC,EAAE,IAAI,QAAQ,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,OAAO,CAAC,EAAE,GAAG,CAAC;gBAC5D,wCAA2C;oBACzC,IAAI,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE,EAAE;wBAC7B,QAAQ,IAAI,CAAC,WAAW,QAAQ,iBAAiB,WAAW;oBAC9D;gBACF;gBACA,IAAI,QAAQ,CAAC,EAAE,KAAK,QAAQ,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,IAAI,OAAO,CAAC,EAAE,EAAE;oBAC3D,iEAAiE;oBACjE,qDAAqD;oBACrD,KAAK,KAAK,GAAG,QAAQ,CAAC,EAAE;gBAC1B;YACF;YACA,KAAK,MAAM,GAAG,4JAAA,CAAA,UAAa,CAAC,eAAe,CAAC;YAC5C,aAAa,IAAI,CAAC;QACpB,GAAG,IAAI;QACP,oBAAoB;QACpB,iBAAiB,YAAY;QAC7B,cAAc;QACd,CAAA,GAAA,mJAAA,CAAA,kBAAe,AAAD,EAAE;QAChB,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,cAAc,SAAU,KAAK;YACvC,IAAI,QAAQ,MAAM,KAAK;YACvB,IAAI,cAAc;gBAAC;oBAAC;oBAAK;iBAAI,CAAC,KAAK,CAAC,EAAE,CAAC;gBAAE;oBAAC;oBAAK;iBAAI,CAAC,KAAK,CAAC,EAAE,CAAC;aAAC;YAC9D,MAAM,IAAI,GAAG,MAAM,IAAI,IAAI,IAAI,CAAC,eAAe,CAAC,MAAM,KAAK,IAAI,OAAO,MAAM,KAAK,GAAG,MAAM,QAAQ,EAAE,OAAO;QAC7G,GAAG,IAAI;IACT;AACF;AACA,SAAS,iBAAiB,UAAU,EAAE,SAAS;IAC7C,IAAI,UAAU,WAAW,OAAO;IAChC,IAAI,WAAW,MAAM,KAAK,aAAa,CAAC,UAAU,SAAS;QACzD,UAAU,OAAO;IACnB;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2654, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/visualMap/PiecewiseView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport VisualMapView from './VisualMapView.js';\nimport * as graphic from '../../util/graphic.js';\nimport { createSymbol } from '../../util/symbol.js';\nimport * as layout from '../../util/layout.js';\nimport * as helper from './helper.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nvar PiecewiseVisualMapView = /** @class */function (_super) {\n  __extends(PiecewiseVisualMapView, _super);\n  function PiecewiseVisualMapView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = PiecewiseVisualMapView.type;\n    return _this;\n  }\n  PiecewiseVisualMapView.prototype.doRender = function () {\n    var thisGroup = this.group;\n    thisGroup.removeAll();\n    var visualMapModel = this.visualMapModel;\n    var textGap = visualMapModel.get('textGap');\n    var textStyleModel = visualMapModel.textStyleModel;\n    var textFont = textStyleModel.getFont();\n    var textFill = textStyleModel.getTextColor();\n    var itemAlign = this._getItemAlign();\n    var itemSize = visualMapModel.itemSize;\n    var viewData = this._getViewData();\n    var endsText = viewData.endsText;\n    var showLabel = zrUtil.retrieve(visualMapModel.get('showLabel', true), !endsText);\n    var silent = !visualMapModel.get('selectedMode');\n    endsText && this._renderEndsText(thisGroup, endsText[0], itemSize, showLabel, itemAlign);\n    zrUtil.each(viewData.viewPieceList, function (item) {\n      var piece = item.piece;\n      var itemGroup = new graphic.Group();\n      itemGroup.onclick = zrUtil.bind(this._onItemClick, this, piece);\n      this._enableHoverLink(itemGroup, item.indexInModelPieceList);\n      // TODO Category\n      var representValue = visualMapModel.getRepresentValue(piece);\n      this._createItemSymbol(itemGroup, representValue, [0, 0, itemSize[0], itemSize[1]], silent);\n      if (showLabel) {\n        var visualState = this.visualMapModel.getValueState(representValue);\n        itemGroup.add(new graphic.Text({\n          style: {\n            x: itemAlign === 'right' ? -textGap : itemSize[0] + textGap,\n            y: itemSize[1] / 2,\n            text: piece.text,\n            verticalAlign: 'middle',\n            align: itemAlign,\n            font: textFont,\n            fill: textFill,\n            opacity: visualState === 'outOfRange' ? 0.5 : 1\n          },\n          silent: silent\n        }));\n      }\n      thisGroup.add(itemGroup);\n    }, this);\n    endsText && this._renderEndsText(thisGroup, endsText[1], itemSize, showLabel, itemAlign);\n    layout.box(visualMapModel.get('orient'), thisGroup, visualMapModel.get('itemGap'));\n    this.renderBackground(thisGroup);\n    this.positionGroup(thisGroup);\n  };\n  PiecewiseVisualMapView.prototype._enableHoverLink = function (itemGroup, pieceIndex) {\n    var _this = this;\n    itemGroup.on('mouseover', function () {\n      return onHoverLink('highlight');\n    }).on('mouseout', function () {\n      return onHoverLink('downplay');\n    });\n    var onHoverLink = function (method) {\n      var visualMapModel = _this.visualMapModel;\n      // TODO: TYPE More detailed action types\n      visualMapModel.option.hoverLink && _this.api.dispatchAction({\n        type: method,\n        batch: helper.makeHighDownBatch(visualMapModel.findTargetDataIndices(pieceIndex), visualMapModel)\n      });\n    };\n  };\n  PiecewiseVisualMapView.prototype._getItemAlign = function () {\n    var visualMapModel = this.visualMapModel;\n    var modelOption = visualMapModel.option;\n    if (modelOption.orient === 'vertical') {\n      return helper.getItemAlign(visualMapModel, this.api, visualMapModel.itemSize);\n    } else {\n      // horizontal, most case left unless specifying right.\n      var align = modelOption.align;\n      if (!align || align === 'auto') {\n        align = 'left';\n      }\n      return align;\n    }\n  };\n  PiecewiseVisualMapView.prototype._renderEndsText = function (group, text, itemSize, showLabel, itemAlign) {\n    if (!text) {\n      return;\n    }\n    var itemGroup = new graphic.Group();\n    var textStyleModel = this.visualMapModel.textStyleModel;\n    itemGroup.add(new graphic.Text({\n      style: createTextStyle(textStyleModel, {\n        x: showLabel ? itemAlign === 'right' ? itemSize[0] : 0 : itemSize[0] / 2,\n        y: itemSize[1] / 2,\n        verticalAlign: 'middle',\n        align: showLabel ? itemAlign : 'center',\n        text: text\n      })\n    }));\n    group.add(itemGroup);\n  };\n  /**\r\n   * @private\r\n   * @return {Object} {peiceList, endsText} The order is the same as screen pixel order.\r\n   */\n  PiecewiseVisualMapView.prototype._getViewData = function () {\n    var visualMapModel = this.visualMapModel;\n    var viewPieceList = zrUtil.map(visualMapModel.getPieceList(), function (piece, index) {\n      return {\n        piece: piece,\n        indexInModelPieceList: index\n      };\n    });\n    var endsText = visualMapModel.get('text');\n    // Consider orient and inverse.\n    var orient = visualMapModel.get('orient');\n    var inverse = visualMapModel.get('inverse');\n    // Order of model pieceList is always [low, ..., high]\n    if (orient === 'horizontal' ? inverse : !inverse) {\n      viewPieceList.reverse();\n    }\n    // Origin order of endsText is [high, low]\n    else if (endsText) {\n      endsText = endsText.slice().reverse();\n    }\n    return {\n      viewPieceList: viewPieceList,\n      endsText: endsText\n    };\n  };\n  PiecewiseVisualMapView.prototype._createItemSymbol = function (group, representValue, shapeParam, silent) {\n    var itemSymbol = createSymbol(\n    // symbol will be string\n    this.getControllerVisual(representValue, 'symbol'), shapeParam[0], shapeParam[1], shapeParam[2], shapeParam[3],\n    // color will be string\n    this.getControllerVisual(representValue, 'color'));\n    itemSymbol.silent = silent;\n    group.add(itemSymbol);\n  };\n  PiecewiseVisualMapView.prototype._onItemClick = function (piece) {\n    var visualMapModel = this.visualMapModel;\n    var option = visualMapModel.option;\n    var selectedMode = option.selectedMode;\n    if (!selectedMode) {\n      return;\n    }\n    var selected = zrUtil.clone(option.selected);\n    var newKey = visualMapModel.getSelectedMapKey(piece);\n    if (selectedMode === 'single' || selectedMode === true) {\n      selected[newKey] = true;\n      zrUtil.each(selected, function (o, key) {\n        selected[key] = key === newKey;\n      });\n    } else {\n      selected[newKey] = !selected[newKey];\n    }\n    this.api.dispatchAction({\n      type: 'selectDataRange',\n      from: this.uid,\n      visualMapId: this.visualMapModel.id,\n      selected: selected\n    });\n  };\n  PiecewiseVisualMapView.type = 'visualMap.piecewise';\n  return PiecewiseVisualMapView;\n}(VisualMapView);\nexport default PiecewiseVisualMapView;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;;;;;;;AACA,IAAI,yBAAyB,WAAW,GAAE,SAAU,MAAM;IACxD,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,wBAAwB;IAClC,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,uBAAuB,IAAI;QACxC,OAAO;IACT;IACA,uBAAuB,SAAS,CAAC,QAAQ,GAAG;QAC1C,IAAI,YAAY,IAAI,CAAC,KAAK;QAC1B,UAAU,SAAS;QACnB,IAAI,iBAAiB,IAAI,CAAC,cAAc;QACxC,IAAI,UAAU,eAAe,GAAG,CAAC;QACjC,IAAI,iBAAiB,eAAe,cAAc;QAClD,IAAI,WAAW,eAAe,OAAO;QACrC,IAAI,WAAW,eAAe,YAAY;QAC1C,IAAI,YAAY,IAAI,CAAC,aAAa;QAClC,IAAI,WAAW,eAAe,QAAQ;QACtC,IAAI,WAAW,IAAI,CAAC,YAAY;QAChC,IAAI,WAAW,SAAS,QAAQ;QAChC,IAAI,YAAY,CAAA,GAAA,iJAAA,CAAA,WAAe,AAAD,EAAE,eAAe,GAAG,CAAC,aAAa,OAAO,CAAC;QACxE,IAAI,SAAS,CAAC,eAAe,GAAG,CAAC;QACjC,YAAY,IAAI,CAAC,eAAe,CAAC,WAAW,QAAQ,CAAC,EAAE,EAAE,UAAU,WAAW;QAC9E,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,SAAS,aAAa,EAAE,SAAU,IAAI;YAChD,IAAI,QAAQ,KAAK,KAAK;YACtB,IAAI,YAAY,IAAI,yLAAA,CAAA,QAAa;YACjC,UAAU,OAAO,GAAG,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,IAAI,CAAC,YAAY,EAAE,IAAI,EAAE;YACzD,IAAI,CAAC,gBAAgB,CAAC,WAAW,KAAK,qBAAqB;YAC3D,gBAAgB;YAChB,IAAI,iBAAiB,eAAe,iBAAiB,CAAC;YACtD,IAAI,CAAC,iBAAiB,CAAC,WAAW,gBAAgB;gBAAC;gBAAG;gBAAG,QAAQ,CAAC,EAAE;gBAAE,QAAQ,CAAC,EAAE;aAAC,EAAE;YACpF,IAAI,WAAW;gBACb,IAAI,cAAc,IAAI,CAAC,cAAc,CAAC,aAAa,CAAC;gBACpD,UAAU,GAAG,CAAC,IAAI,uLAAA,CAAA,OAAY,CAAC;oBAC7B,OAAO;wBACL,GAAG,cAAc,UAAU,CAAC,UAAU,QAAQ,CAAC,EAAE,GAAG;wBACpD,GAAG,QAAQ,CAAC,EAAE,GAAG;wBACjB,MAAM,MAAM,IAAI;wBAChB,eAAe;wBACf,OAAO;wBACP,MAAM;wBACN,MAAM;wBACN,SAAS,gBAAgB,eAAe,MAAM;oBAChD;oBACA,QAAQ;gBACV;YACF;YACA,UAAU,GAAG,CAAC;QAChB,GAAG,IAAI;QACP,YAAY,IAAI,CAAC,eAAe,CAAC,WAAW,QAAQ,CAAC,EAAE,EAAE,UAAU,WAAW;QAC9E,CAAA,GAAA,mJAAA,CAAA,MAAU,AAAD,EAAE,eAAe,GAAG,CAAC,WAAW,WAAW,eAAe,GAAG,CAAC;QACvE,IAAI,CAAC,gBAAgB,CAAC;QACtB,IAAI,CAAC,aAAa,CAAC;IACrB;IACA,uBAAuB,SAAS,CAAC,gBAAgB,GAAG,SAAU,SAAS,EAAE,UAAU;QACjF,IAAI,QAAQ,IAAI;QAChB,UAAU,EAAE,CAAC,aAAa;YACxB,OAAO,YAAY;QACrB,GAAG,EAAE,CAAC,YAAY;YAChB,OAAO,YAAY;QACrB;QACA,IAAI,cAAc,SAAU,MAAM;YAChC,IAAI,iBAAiB,MAAM,cAAc;YACzC,wCAAwC;YACxC,eAAe,MAAM,CAAC,SAAS,IAAI,MAAM,GAAG,CAAC,cAAc,CAAC;gBAC1D,MAAM;gBACN,OAAO,CAAA,GAAA,qKAAA,CAAA,oBAAwB,AAAD,EAAE,eAAe,qBAAqB,CAAC,aAAa;YACpF;QACF;IACF;IACA,uBAAuB,SAAS,CAAC,aAAa,GAAG;QAC/C,IAAI,iBAAiB,IAAI,CAAC,cAAc;QACxC,IAAI,cAAc,eAAe,MAAM;QACvC,IAAI,YAAY,MAAM,KAAK,YAAY;YACrC,OAAO,CAAA,GAAA,qKAAA,CAAA,eAAmB,AAAD,EAAE,gBAAgB,IAAI,CAAC,GAAG,EAAE,eAAe,QAAQ;QAC9E,OAAO;YACL,sDAAsD;YACtD,IAAI,QAAQ,YAAY,KAAK;YAC7B,IAAI,CAAC,SAAS,UAAU,QAAQ;gBAC9B,QAAQ;YACV;YACA,OAAO;QACT;IACF;IACA,uBAAuB,SAAS,CAAC,eAAe,GAAG,SAAU,KAAK,EAAE,IAAI,EAAE,QAAQ,EAAE,SAAS,EAAE,SAAS;QACtG,IAAI,CAAC,MAAM;YACT;QACF;QACA,IAAI,YAAY,IAAI,yLAAA,CAAA,QAAa;QACjC,IAAI,iBAAiB,IAAI,CAAC,cAAc,CAAC,cAAc;QACvD,UAAU,GAAG,CAAC,IAAI,uLAAA,CAAA,OAAY,CAAC;YAC7B,OAAO,CAAA,GAAA,wJAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB;gBACrC,GAAG,YAAY,cAAc,UAAU,QAAQ,CAAC,EAAE,GAAG,IAAI,QAAQ,CAAC,EAAE,GAAG;gBACvE,GAAG,QAAQ,CAAC,EAAE,GAAG;gBACjB,eAAe;gBACf,OAAO,YAAY,YAAY;gBAC/B,MAAM;YACR;QACF;QACA,MAAM,GAAG,CAAC;IACZ;IACA;;;GAGC,GACD,uBAAuB,SAAS,CAAC,YAAY,GAAG;QAC9C,IAAI,iBAAiB,IAAI,CAAC,cAAc;QACxC,IAAI,gBAAgB,CAAA,GAAA,iJAAA,CAAA,MAAU,AAAD,EAAE,eAAe,YAAY,IAAI,SAAU,KAAK,EAAE,KAAK;YAClF,OAAO;gBACL,OAAO;gBACP,uBAAuB;YACzB;QACF;QACA,IAAI,WAAW,eAAe,GAAG,CAAC;QAClC,+BAA+B;QAC/B,IAAI,SAAS,eAAe,GAAG,CAAC;QAChC,IAAI,UAAU,eAAe,GAAG,CAAC;QACjC,sDAAsD;QACtD,IAAI,WAAW,eAAe,UAAU,CAAC,SAAS;YAChD,cAAc,OAAO;QACvB,OAEK,IAAI,UAAU;YACjB,WAAW,SAAS,KAAK,GAAG,OAAO;QACrC;QACA,OAAO;YACL,eAAe;YACf,UAAU;QACZ;IACF;IACA,uBAAuB,SAAS,CAAC,iBAAiB,GAAG,SAAU,KAAK,EAAE,cAAc,EAAE,UAAU,EAAE,MAAM;QACtG,IAAI,aAAa,CAAA,GAAA,mJAAA,CAAA,eAAY,AAAD,EAC5B,wBAAwB;QACxB,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,WAAW,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,EAAE,EAC9G,uBAAuB;QACvB,IAAI,CAAC,mBAAmB,CAAC,gBAAgB;QACzC,WAAW,MAAM,GAAG;QACpB,MAAM,GAAG,CAAC;IACZ;IACA,uBAAuB,SAAS,CAAC,YAAY,GAAG,SAAU,KAAK;QAC7D,IAAI,iBAAiB,IAAI,CAAC,cAAc;QACxC,IAAI,SAAS,eAAe,MAAM;QAClC,IAAI,eAAe,OAAO,YAAY;QACtC,IAAI,CAAC,cAAc;YACjB;QACF;QACA,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,QAAY,AAAD,EAAE,OAAO,QAAQ;QAC3C,IAAI,SAAS,eAAe,iBAAiB,CAAC;QAC9C,IAAI,iBAAiB,YAAY,iBAAiB,MAAM;YACtD,QAAQ,CAAC,OAAO,GAAG;YACnB,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,UAAU,SAAU,CAAC,EAAE,GAAG;gBACpC,QAAQ,CAAC,IAAI,GAAG,QAAQ;YAC1B;QACF,OAAO;YACL,QAAQ,CAAC,OAAO,GAAG,CAAC,QAAQ,CAAC,OAAO;QACtC;QACA,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;YACtB,MAAM;YACN,MAAM,IAAI,CAAC,GAAG;YACd,aAAa,IAAI,CAAC,cAAc,CAAC,EAAE;YACnC,UAAU;QACZ;IACF;IACA,uBAAuB,IAAI,GAAG;IAC9B,OAAO;AACT,EAAE,4KAAA,CAAA,UAAa;uCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2882, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/visualMap/installVisualMapPiecewise.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport PiecewiseModel from './PiecewiseModel.js';\nimport PiecewiseView from './PiecewiseView.js';\nimport installCommon from './installCommon.js';\nexport function install(registers) {\n  registers.registerComponentModel(PiecewiseModel);\n  registers.registerComponentView(PiecewiseView);\n  installCommon(registers);\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;;;;AACO,SAAS,QAAQ,SAAS;IAC/B,UAAU,sBAAsB,CAAC,6KAAA,CAAA,UAAc;IAC/C,UAAU,qBAAqB,CAAC,4KAAA,CAAA,UAAa;IAC7C,CAAA,GAAA,4KAAA,CAAA,UAAa,AAAD,EAAE;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2938, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/visualMap/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { use } from '../../extension.js';\nimport { install as installVisualMapContinuous } from './installVisualMapContinuous.js';\nimport { install as installVisualMapPiecewise } from './installVisualMapPiecewise.js';\nexport function install(registers) {\n  use(installVisualMapContinuous);\n  use(installVisualMapPiecewise);\n  // Do not install './dataZoomSelect',\n  // since it only work for toolbox dataZoom.\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;;;;AACO,SAAS,QAAQ,SAAS;IAC/B,CAAA,GAAA,8IAAA,CAAA,MAAG,AAAD,EAAE,yLAAA,CAAA,UAA0B;IAC9B,CAAA,GAAA,8IAAA,CAAA,MAAG,AAAD,EAAE,wLAAA,CAAA,UAAyB;AAC7B,qCAAqC;AACrC,2CAA2C;AAC7C", "ignoreList": [0], "debugId": null}}]}