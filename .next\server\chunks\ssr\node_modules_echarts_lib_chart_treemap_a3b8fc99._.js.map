{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/chart/treemap/treemapAction.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as helper from '../helper/treeHelper.js';\nimport { noop } from 'zrender/lib/core/util.js';\nvar actionTypes = ['treemapZoomToNode', 'treemapRender', 'treemapMove'];\nexport function installTreemapAction(registers) {\n  for (var i = 0; i < actionTypes.length; i++) {\n    registers.registerAction({\n      type: actionTypes[i],\n      update: 'updateView'\n    }, noop);\n  }\n  registers.registerAction({\n    type: 'treemapRootToNode',\n    update: 'updateView'\n  }, function (payload, ecModel) {\n    ecModel.eachComponent({\n      mainType: 'series',\n      subType: 'treemap',\n      query: payload\n    }, handleRootToNode);\n    function handleRootToNode(model, index) {\n      var types = ['treemapZoomToNode', 'treemapRootToNode'];\n      var targetInfo = helper.retrieveTargetInfo(payload, types, model);\n      if (targetInfo) {\n        var originViewRoot = model.getViewRoot();\n        if (originViewRoot) {\n          payload.direction = helper.aboveViewRoot(originViewRoot, targetInfo.node) ? 'rollUp' : 'drillDown';\n        }\n        model.resetViewRoot(targetInfo.node);\n      }\n    }\n  });\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;;;AACA,IAAI,cAAc;IAAC;IAAqB;IAAiB;CAAc;AAChE,SAAS,qBAAqB,SAAS;IAC5C,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;QAC3C,UAAU,cAAc,CAAC;YACvB,MAAM,WAAW,CAAC,EAAE;YACpB,QAAQ;QACV,GAAG,8IAAA,CAAA,OAAI;IACT;IACA,UAAU,cAAc,CAAC;QACvB,MAAM;QACN,QAAQ;IACV,GAAG,SAAU,OAAO,EAAE,OAAO;QAC3B,QAAQ,aAAa,CAAC;YACpB,UAAU;YACV,SAAS;YACT,OAAO;QACT,GAAG;QACH,SAAS,iBAAiB,KAAK,EAAE,KAAK;YACpC,IAAI,QAAQ;gBAAC;gBAAqB;aAAoB;YACtD,IAAI,aAAa,CAAA,GAAA,+JAAA,CAAA,qBAAyB,AAAD,EAAE,SAAS,OAAO;YAC3D,IAAI,YAAY;gBACd,IAAI,iBAAiB,MAAM,WAAW;gBACtC,IAAI,gBAAgB;oBAClB,QAAQ,SAAS,GAAG,CAAA,GAAA,+JAAA,CAAA,gBAAoB,AAAD,EAAE,gBAAgB,WAAW,IAAI,IAAI,WAAW;gBACzF;gBACA,MAAM,aAAa,CAAC,WAAW,IAAI;YACrC;QACF;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/chart/treemap/TreemapSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport SeriesModel from '../../model/Series.js';\nimport Tree from '../../data/Tree.js';\nimport Model from '../../model/Model.js';\nimport { wrapTreePathInfo } from '../helper/treeHelper.js';\nimport { normalizeToArray } from '../../util/model.js';\nimport { createTooltipMarkup } from '../../component/tooltip/tooltipMarkup.js';\nimport enableAriaDecalForTree from '../helper/enableAriaDecalForTree.js';\nvar TreemapSeriesModel = /** @class */function (_super) {\n  __extends(TreemapSeriesModel, _super);\n  function TreemapSeriesModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TreemapSeriesModel.type;\n    _this.preventUsingHoverLayer = true;\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  TreemapSeriesModel.prototype.getInitialData = function (option, ecModel) {\n    // Create a virtual root.\n    var root = {\n      name: option.name,\n      children: option.data\n    };\n    completeTreeValue(root);\n    var levels = option.levels || [];\n    // Used in \"visual priority\" in `treemapVisual.js`.\n    // This way is a little tricky, must satisfy the precondition:\n    //   1. There is no `treeNode.getModel('itemStyle.xxx')` used.\n    //   2. The `Model.prototype.getModel()` will not use any clone-like way.\n    var designatedVisualItemStyle = this.designatedVisualItemStyle = {};\n    var designatedVisualModel = new Model({\n      itemStyle: designatedVisualItemStyle\n    }, this, ecModel);\n    levels = option.levels = setDefault(levels, ecModel);\n    var levelModels = zrUtil.map(levels || [], function (levelDefine) {\n      return new Model(levelDefine, designatedVisualModel, ecModel);\n    }, this);\n    // Make sure always a new tree is created when setOption,\n    // in TreemapView, we check whether oldTree === newTree\n    // to choose mappings approach among old shapes and new shapes.\n    var tree = Tree.createTree(root, this, beforeLink);\n    function beforeLink(nodeData) {\n      nodeData.wrapMethod('getItemModel', function (model, idx) {\n        var node = tree.getNodeByDataIndex(idx);\n        var levelModel = node ? levelModels[node.depth] : null;\n        // If no levelModel, we also need `designatedVisualModel`.\n        model.parentModel = levelModel || designatedVisualModel;\n        return model;\n      });\n    }\n    return tree.data;\n  };\n  TreemapSeriesModel.prototype.optionUpdated = function () {\n    this.resetViewRoot();\n  };\n  /**\r\n   * @override\r\n   * @param {number} dataIndex\r\n   * @param {boolean} [mutipleSeries=false]\r\n   */\n  TreemapSeriesModel.prototype.formatTooltip = function (dataIndex, multipleSeries, dataType) {\n    var data = this.getData();\n    var value = this.getRawValue(dataIndex);\n    var name = data.getName(dataIndex);\n    return createTooltipMarkup('nameValue', {\n      name: name,\n      value: value\n    });\n  };\n  /**\r\n   * Add tree path to tooltip param\r\n   *\r\n   * @override\r\n   * @param {number} dataIndex\r\n   * @return {Object}\r\n   */\n  TreemapSeriesModel.prototype.getDataParams = function (dataIndex) {\n    var params = _super.prototype.getDataParams.apply(this, arguments);\n    var node = this.getData().tree.getNodeByDataIndex(dataIndex);\n    params.treeAncestors = wrapTreePathInfo(node, this);\n    // compatitable the previous code.\n    params.treePathInfo = params.treeAncestors;\n    return params;\n  };\n  /**\r\n   * @public\r\n   * @param {Object} layoutInfo {\r\n   *                                x: containerGroup x\r\n   *                                y: containerGroup y\r\n   *                                width: containerGroup width\r\n   *                                height: containerGroup height\r\n   *                            }\r\n   */\n  TreemapSeriesModel.prototype.setLayoutInfo = function (layoutInfo) {\n    /**\r\n     * @readOnly\r\n     * @type {Object}\r\n     */\n    this.layoutInfo = this.layoutInfo || {};\n    zrUtil.extend(this.layoutInfo, layoutInfo);\n  };\n  /**\r\n   * @param  {string} id\r\n   * @return {number} index\r\n   */\n  TreemapSeriesModel.prototype.mapIdToIndex = function (id) {\n    // A feature is implemented:\n    // index is monotone increasing with the sequence of\n    // input id at the first time.\n    // This feature can make sure that each data item and its\n    // mapped color have the same index between data list and\n    // color list at the beginning, which is useful for user\n    // to adjust data-color mapping.\n    /**\r\n     * @private\r\n     * @type {Object}\r\n     */\n    var idIndexMap = this._idIndexMap;\n    if (!idIndexMap) {\n      idIndexMap = this._idIndexMap = zrUtil.createHashMap();\n      /**\r\n       * @private\r\n       * @type {number}\r\n       */\n      this._idIndexMapCount = 0;\n    }\n    var index = idIndexMap.get(id);\n    if (index == null) {\n      idIndexMap.set(id, index = this._idIndexMapCount++);\n    }\n    return index;\n  };\n  TreemapSeriesModel.prototype.getViewRoot = function () {\n    return this._viewRoot;\n  };\n  TreemapSeriesModel.prototype.resetViewRoot = function (viewRoot) {\n    viewRoot ? this._viewRoot = viewRoot : viewRoot = this._viewRoot;\n    var root = this.getRawData().tree.root;\n    if (!viewRoot || viewRoot !== root && !root.contains(viewRoot)) {\n      this._viewRoot = root;\n    }\n  };\n  TreemapSeriesModel.prototype.enableAriaDecal = function () {\n    enableAriaDecalForTree(this);\n  };\n  TreemapSeriesModel.type = 'series.treemap';\n  TreemapSeriesModel.layoutMode = 'box';\n  TreemapSeriesModel.defaultOption = {\n    // Disable progressive rendering\n    progressive: 0,\n    // size: ['80%', '80%'],            // deprecated, compatible with ec2.\n    left: 'center',\n    top: 'middle',\n    width: '80%',\n    height: '80%',\n    sort: true,\n    clipWindow: 'origin',\n    squareRatio: 0.5 * (1 + Math.sqrt(5)),\n    leafDepth: null,\n    drillDownIcon: '▶',\n    // to align specialized icon. ▷▶❒❐▼✚\n    zoomToNodeRatio: 0.32 * 0.32,\n    scaleLimit: null,\n    roam: true,\n    nodeClick: 'zoomToNode',\n    animation: true,\n    animationDurationUpdate: 900,\n    animationEasing: 'quinticInOut',\n    breadcrumb: {\n      show: true,\n      height: 22,\n      left: 'center',\n      top: 'bottom',\n      // right\n      // bottom\n      emptyItemWidth: 25,\n      itemStyle: {\n        color: 'rgba(0,0,0,0.7)',\n        textStyle: {\n          color: '#fff'\n        }\n      },\n      emphasis: {\n        itemStyle: {\n          color: 'rgba(0,0,0,0.9)' // '#5793f3',\n        }\n      }\n    },\n    label: {\n      show: true,\n      // Do not use textDistance, for ellipsis rect just the same as treemap node rect.\n      distance: 0,\n      padding: 5,\n      position: 'inside',\n      // formatter: null,\n      color: '#fff',\n      overflow: 'truncate'\n      // align\n      // verticalAlign\n    },\n    upperLabel: {\n      show: false,\n      position: [0, '50%'],\n      height: 20,\n      // formatter: null,\n      // color: '#fff',\n      overflow: 'truncate',\n      // align: null,\n      verticalAlign: 'middle'\n    },\n    itemStyle: {\n      color: null,\n      colorAlpha: null,\n      colorSaturation: null,\n      borderWidth: 0,\n      gapWidth: 0,\n      borderColor: '#fff',\n      borderColorSaturation: null // If specified, borderColor will be ineffective, and the\n      // border color is evaluated by color of current node and\n      // borderColorSaturation.\n    },\n    emphasis: {\n      upperLabel: {\n        show: true,\n        position: [0, '50%'],\n        overflow: 'truncate',\n        verticalAlign: 'middle'\n      }\n    },\n    visualDimension: 0,\n    visualMin: null,\n    visualMax: null,\n    color: [],\n    // level[n].color (if necessary).\n    // + Specify color list of each level. level[0].color would be global\n    // color list if not specified. (see method `setDefault`).\n    // + But set as a empty array to forbid fetch color from global palette\n    // when using nodeModel.get('color'), otherwise nodes on deep level\n    // will always has color palette set and are not able to inherit color\n    // from parent node.\n    // + TreemapSeries.color can not be set as 'none', otherwise effect\n    // legend color fetching (see seriesColor.js).\n    colorAlpha: null,\n    colorSaturation: null,\n    colorMappingBy: 'index',\n    visibleMin: 10,\n    // be rendered. Only works when sort is 'asc' or 'desc'.\n    childrenVisibleMin: null,\n    // grandchildren will not show.\n    // Why grandchildren? If not grandchildren but children,\n    // some siblings show children and some not,\n    // the appearance may be mess and not consistent,\n    levels: [] // Each item: {\n    //     visibleMin, itemStyle, visualDimension, label\n    // }\n  };\n  return TreemapSeriesModel;\n}(SeriesModel);\n/**\r\n * @param {Object} dataNode\r\n */\nfunction completeTreeValue(dataNode) {\n  // Postorder travel tree.\n  // If value of none-leaf node is not set,\n  // calculate it by suming up the value of all children.\n  var sum = 0;\n  zrUtil.each(dataNode.children, function (child) {\n    completeTreeValue(child);\n    var childValue = child.value;\n    zrUtil.isArray(childValue) && (childValue = childValue[0]);\n    sum += childValue;\n  });\n  var thisValue = dataNode.value;\n  if (zrUtil.isArray(thisValue)) {\n    thisValue = thisValue[0];\n  }\n  if (thisValue == null || isNaN(thisValue)) {\n    thisValue = sum;\n  }\n  // Value should not less than 0.\n  if (thisValue < 0) {\n    thisValue = 0;\n  }\n  zrUtil.isArray(dataNode.value) ? dataNode.value[0] = thisValue : dataNode.value = thisValue;\n}\n/**\r\n * set default to level configuration\r\n */\nfunction setDefault(levels, ecModel) {\n  var globalColorList = normalizeToArray(ecModel.get('color'));\n  var globalDecalList = normalizeToArray(ecModel.get(['aria', 'decal', 'decals']));\n  if (!globalColorList) {\n    return;\n  }\n  levels = levels || [];\n  var hasColorDefine;\n  var hasDecalDefine;\n  zrUtil.each(levels, function (levelDefine) {\n    var model = new Model(levelDefine);\n    var modelColor = model.get('color');\n    var modelDecal = model.get('decal');\n    if (model.get(['itemStyle', 'color']) || modelColor && modelColor !== 'none') {\n      hasColorDefine = true;\n    }\n    if (model.get(['itemStyle', 'decal']) || modelDecal && modelDecal !== 'none') {\n      hasDecalDefine = true;\n    }\n  });\n  var level0 = levels[0] || (levels[0] = {});\n  if (!hasColorDefine) {\n    level0.color = globalColorList.slice();\n  }\n  if (!hasDecalDefine && globalDecalList) {\n    level0.decal = globalDecalList.slice();\n  }\n  return levels;\n}\nexport default TreemapSeriesModel;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;AACA,IAAI,qBAAqB,WAAW,GAAE,SAAU,MAAM;IACpD,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE,oBAAoB;IAC9B,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,mBAAmB,IAAI;QACpC,MAAM,sBAAsB,GAAG;QAC/B,OAAO;IACT;IACA;;GAEC,GACD,mBAAmB,SAAS,CAAC,cAAc,GAAG,SAAU,MAAM,EAAE,OAAO;QACrE,yBAAyB;QACzB,IAAI,OAAO;YACT,MAAM,OAAO,IAAI;YACjB,UAAU,OAAO,IAAI;QACvB;QACA,kBAAkB;QAClB,IAAI,SAAS,OAAO,MAAM,IAAI,EAAE;QAChC,mDAAmD;QACnD,8DAA8D;QAC9D,8DAA8D;QAC9D,yEAAyE;QACzE,IAAI,4BAA4B,IAAI,CAAC,yBAAyB,GAAG,CAAC;QAClE,IAAI,wBAAwB,IAAI,gJAAA,CAAA,UAAK,CAAC;YACpC,WAAW;QACb,GAAG,IAAI,EAAE;QACT,SAAS,OAAO,MAAM,GAAG,WAAW,QAAQ;QAC5C,IAAI,cAAc,CAAA,GAAA,8IAAA,CAAA,MAAU,AAAD,EAAE,UAAU,EAAE,EAAE,SAAU,WAAW;YAC9D,OAAO,IAAI,gJAAA,CAAA,UAAK,CAAC,aAAa,uBAAuB;QACvD,GAAG,IAAI;QACP,yDAAyD;QACzD,uDAAuD;QACvD,+DAA+D;QAC/D,IAAI,OAAO,8IAAA,CAAA,UAAI,CAAC,UAAU,CAAC,MAAM,IAAI,EAAE;QACvC,SAAS,WAAW,QAAQ;YAC1B,SAAS,UAAU,CAAC,gBAAgB,SAAU,KAAK,EAAE,GAAG;gBACtD,IAAI,OAAO,KAAK,kBAAkB,CAAC;gBACnC,IAAI,aAAa,OAAO,WAAW,CAAC,KAAK,KAAK,CAAC,GAAG;gBAClD,0DAA0D;gBAC1D,MAAM,WAAW,GAAG,cAAc;gBAClC,OAAO;YACT;QACF;QACA,OAAO,KAAK,IAAI;IAClB;IACA,mBAAmB,SAAS,CAAC,aAAa,GAAG;QAC3C,IAAI,CAAC,aAAa;IACpB;IACA;;;;GAIC,GACD,mBAAmB,SAAS,CAAC,aAAa,GAAG,SAAU,SAAS,EAAE,cAAc,EAAE,QAAQ;QACxF,IAAI,OAAO,IAAI,CAAC,OAAO;QACvB,IAAI,QAAQ,IAAI,CAAC,WAAW,CAAC;QAC7B,IAAI,OAAO,KAAK,OAAO,CAAC;QACxB,OAAO,CAAA,GAAA,uKAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;YACtC,MAAM;YACN,OAAO;QACT;IACF;IACA;;;;;;GAMC,GACD,mBAAmB,SAAS,CAAC,aAAa,GAAG,SAAU,SAAS;QAC9D,IAAI,SAAS,OAAO,SAAS,CAAC,aAAa,CAAC,KAAK,CAAC,IAAI,EAAE;QACxD,IAAI,OAAO,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,kBAAkB,CAAC;QAClD,OAAO,aAAa,GAAG,CAAA,GAAA,+JAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,IAAI;QAClD,kCAAkC;QAClC,OAAO,YAAY,GAAG,OAAO,aAAa;QAC1C,OAAO;IACT;IACA;;;;;;;;GAQC,GACD,mBAAmB,SAAS,CAAC,aAAa,GAAG,SAAU,UAAU;QAC/D;;;KAGC,GACD,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,UAAU,IAAI,CAAC;QACtC,CAAA,GAAA,8IAAA,CAAA,SAAa,AAAD,EAAE,IAAI,CAAC,UAAU,EAAE;IACjC;IACA;;;GAGC,GACD,mBAAmB,SAAS,CAAC,YAAY,GAAG,SAAU,EAAE;QACtD,4BAA4B;QAC5B,oDAAoD;QACpD,8BAA8B;QAC9B,yDAAyD;QACzD,yDAAyD;QACzD,wDAAwD;QACxD,gCAAgC;QAChC;;;KAGC,GACD,IAAI,aAAa,IAAI,CAAC,WAAW;QACjC,IAAI,CAAC,YAAY;YACf,aAAa,IAAI,CAAC,WAAW,GAAG,CAAA,GAAA,8IAAA,CAAA,gBAAoB,AAAD;YACnD;;;OAGC,GACD,IAAI,CAAC,gBAAgB,GAAG;QAC1B;QACA,IAAI,QAAQ,WAAW,GAAG,CAAC;QAC3B,IAAI,SAAS,MAAM;YACjB,WAAW,GAAG,CAAC,IAAI,QAAQ,IAAI,CAAC,gBAAgB;QAClD;QACA,OAAO;IACT;IACA,mBAAmB,SAAS,CAAC,WAAW,GAAG;QACzC,OAAO,IAAI,CAAC,SAAS;IACvB;IACA,mBAAmB,SAAS,CAAC,aAAa,GAAG,SAAU,QAAQ;QAC7D,WAAW,IAAI,CAAC,SAAS,GAAG,WAAW,WAAW,IAAI,CAAC,SAAS;QAChE,IAAI,OAAO,IAAI,CAAC,UAAU,GAAG,IAAI,CAAC,IAAI;QACtC,IAAI,CAAC,YAAY,aAAa,QAAQ,CAAC,KAAK,QAAQ,CAAC,WAAW;YAC9D,IAAI,CAAC,SAAS,GAAG;QACnB;IACF;IACA,mBAAmB,SAAS,CAAC,eAAe,GAAG;QAC7C,CAAA,GAAA,2KAAA,CAAA,UAAsB,AAAD,EAAE,IAAI;IAC7B;IACA,mBAAmB,IAAI,GAAG;IAC1B,mBAAmB,UAAU,GAAG;IAChC,mBAAmB,aAAa,GAAG;QACjC,gCAAgC;QAChC,aAAa;QACb,uEAAuE;QACvE,MAAM;QACN,KAAK;QACL,OAAO;QACP,QAAQ;QACR,MAAM;QACN,YAAY;QACZ,aAAa,MAAM,CAAC,IAAI,KAAK,IAAI,CAAC,EAAE;QACpC,WAAW;QACX,eAAe;QACf,oCAAoC;QACpC,iBAAiB,OAAO;QACxB,YAAY;QACZ,MAAM;QACN,WAAW;QACX,WAAW;QACX,yBAAyB;QACzB,iBAAiB;QACjB,YAAY;YACV,MAAM;YACN,QAAQ;YACR,MAAM;YACN,KAAK;YACL,QAAQ;YACR,SAAS;YACT,gBAAgB;YAChB,WAAW;gBACT,OAAO;gBACP,WAAW;oBACT,OAAO;gBACT;YACF;YACA,UAAU;gBACR,WAAW;oBACT,OAAO,kBAAkB,aAAa;gBACxC;YACF;QACF;QACA,OAAO;YACL,MAAM;YACN,iFAAiF;YACjF,UAAU;YACV,SAAS;YACT,UAAU;YACV,mBAAmB;YACnB,OAAO;YACP,UAAU;QAGZ;QACA,YAAY;YACV,MAAM;YACN,UAAU;gBAAC;gBAAG;aAAM;YACpB,QAAQ;YACR,mBAAmB;YACnB,iBAAiB;YACjB,UAAU;YACV,eAAe;YACf,eAAe;QACjB;QACA,WAAW;YACT,OAAO;YACP,YAAY;YACZ,iBAAiB;YACjB,aAAa;YACb,UAAU;YACV,aAAa;YACb,uBAAuB,KAAK,yDAAyD;QAGvF;QACA,UAAU;YACR,YAAY;gBACV,MAAM;gBACN,UAAU;oBAAC;oBAAG;iBAAM;gBACpB,UAAU;gBACV,eAAe;YACjB;QACF;QACA,iBAAiB;QACjB,WAAW;QACX,WAAW;QACX,OAAO,EAAE;QACT,iCAAiC;QACjC,qEAAqE;QACrE,0DAA0D;QAC1D,uEAAuE;QACvE,mEAAmE;QACnE,sEAAsE;QACtE,oBAAoB;QACpB,mEAAmE;QACnE,8CAA8C;QAC9C,YAAY;QACZ,iBAAiB;QACjB,gBAAgB;QAChB,YAAY;QACZ,wDAAwD;QACxD,oBAAoB;QACpB,+BAA+B;QAC/B,wDAAwD;QACxD,4CAA4C;QAC5C,iDAAiD;QACjD,QAAQ,EAAE,CAAC,eAAe;IAG5B;IACA,OAAO;AACT,EAAE,iJAAA,CAAA,UAAW;AACb;;CAEC,GACD,SAAS,kBAAkB,QAAQ;IACjC,yBAAyB;IACzB,yCAAyC;IACzC,uDAAuD;IACvD,IAAI,MAAM;IACV,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE,SAAS,QAAQ,EAAE,SAAU,KAAK;QAC5C,kBAAkB;QAClB,IAAI,aAAa,MAAM,KAAK;QAC5B,CAAA,GAAA,8IAAA,CAAA,UAAc,AAAD,EAAE,eAAe,CAAC,aAAa,UAAU,CAAC,EAAE;QACzD,OAAO;IACT;IACA,IAAI,YAAY,SAAS,KAAK;IAC9B,IAAI,CAAA,GAAA,8IAAA,CAAA,UAAc,AAAD,EAAE,YAAY;QAC7B,YAAY,SAAS,CAAC,EAAE;IAC1B;IACA,IAAI,aAAa,QAAQ,MAAM,YAAY;QACzC,YAAY;IACd;IACA,gCAAgC;IAChC,IAAI,YAAY,GAAG;QACjB,YAAY;IACd;IACA,CAAA,GAAA,8IAAA,CAAA,UAAc,AAAD,EAAE,SAAS,KAAK,IAAI,SAAS,KAAK,CAAC,EAAE,GAAG,YAAY,SAAS,KAAK,GAAG;AACpF;AACA;;CAEC,GACD,SAAS,WAAW,MAAM,EAAE,OAAO;IACjC,IAAI,kBAAkB,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,GAAG,CAAC;IACnD,IAAI,kBAAkB,CAAA,GAAA,+IAAA,CAAA,mBAAgB,AAAD,EAAE,QAAQ,GAAG,CAAC;QAAC;QAAQ;QAAS;KAAS;IAC9E,IAAI,CAAC,iBAAiB;QACpB;IACF;IACA,SAAS,UAAU,EAAE;IACrB,IAAI;IACJ,IAAI;IACJ,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE,QAAQ,SAAU,WAAW;QACvC,IAAI,QAAQ,IAAI,gJAAA,CAAA,UAAK,CAAC;QACtB,IAAI,aAAa,MAAM,GAAG,CAAC;QAC3B,IAAI,aAAa,MAAM,GAAG,CAAC;QAC3B,IAAI,MAAM,GAAG,CAAC;YAAC;YAAa;SAAQ,KAAK,cAAc,eAAe,QAAQ;YAC5E,iBAAiB;QACnB;QACA,IAAI,MAAM,GAAG,CAAC;YAAC;YAAa;SAAQ,KAAK,cAAc,eAAe,QAAQ;YAC5E,iBAAiB;QACnB;IACF;IACA,IAAI,SAAS,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,CAAC,EAAE,GAAG,CAAC,CAAC;IACzC,IAAI,CAAC,gBAAgB;QACnB,OAAO,KAAK,GAAG,gBAAgB,KAAK;IACtC;IACA,IAAI,CAAC,kBAAkB,iBAAiB;QACtC,OAAO,KAAK,GAAG,gBAAgB,KAAK;IACtC;IACA,OAAO;AACT;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 468, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/chart/treemap/Breadcrumb.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport * as layout from '../../util/layout.js';\nimport { wrapTreePathInfo } from '../helper/treeHelper.js';\nimport { curry, defaults } from 'zrender/lib/core/util.js';\nimport { convertOptionIdName } from '../../util/model.js';\nimport { toggleHoverEmphasis, Z2_EMPHASIS_LIFT } from '../../util/states.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nvar TEXT_PADDING = 8;\nvar ITEM_GAP = 8;\nvar ARRAY_LENGTH = 5;\nvar Breadcrumb = /** @class */function () {\n  function Breadcrumb(containerGroup) {\n    this.group = new graphic.Group();\n    containerGroup.add(this.group);\n  }\n  Breadcrumb.prototype.render = function (seriesModel, api, targetNode, onSelect) {\n    var model = seriesModel.getModel('breadcrumb');\n    var thisGroup = this.group;\n    thisGroup.removeAll();\n    if (!model.get('show') || !targetNode) {\n      return;\n    }\n    var normalStyleModel = model.getModel('itemStyle');\n    var emphasisModel = model.getModel('emphasis');\n    var textStyleModel = normalStyleModel.getModel('textStyle');\n    var emphasisTextStyleModel = emphasisModel.getModel(['itemStyle', 'textStyle']);\n    var layoutParam = {\n      pos: {\n        left: model.get('left'),\n        right: model.get('right'),\n        top: model.get('top'),\n        bottom: model.get('bottom')\n      },\n      box: {\n        width: api.getWidth(),\n        height: api.getHeight()\n      },\n      emptyItemWidth: model.get('emptyItemWidth'),\n      totalWidth: 0,\n      renderList: []\n    };\n    this._prepare(targetNode, layoutParam, textStyleModel);\n    this._renderContent(seriesModel, layoutParam, normalStyleModel, emphasisModel, textStyleModel, emphasisTextStyleModel, onSelect);\n    layout.positionElement(thisGroup, layoutParam.pos, layoutParam.box);\n  };\n  /**\r\n   * Prepare render list and total width\r\n   * @private\r\n   */\n  Breadcrumb.prototype._prepare = function (targetNode, layoutParam, textStyleModel) {\n    for (var node = targetNode; node; node = node.parentNode) {\n      var text = convertOptionIdName(node.getModel().get('name'), '');\n      var textRect = textStyleModel.getTextRect(text);\n      var itemWidth = Math.max(textRect.width + TEXT_PADDING * 2, layoutParam.emptyItemWidth);\n      layoutParam.totalWidth += itemWidth + ITEM_GAP;\n      layoutParam.renderList.push({\n        node: node,\n        text: text,\n        width: itemWidth\n      });\n    }\n  };\n  /**\r\n   * @private\r\n   */\n  Breadcrumb.prototype._renderContent = function (seriesModel, layoutParam, normalStyleModel, emphasisModel, textStyleModel, emphasisTextStyleModel, onSelect) {\n    // Start rendering.\n    var lastX = 0;\n    var emptyItemWidth = layoutParam.emptyItemWidth;\n    var height = seriesModel.get(['breadcrumb', 'height']);\n    var availableSize = layout.getAvailableSize(layoutParam.pos, layoutParam.box);\n    var totalWidth = layoutParam.totalWidth;\n    var renderList = layoutParam.renderList;\n    var emphasisItemStyle = emphasisModel.getModel('itemStyle').getItemStyle();\n    for (var i = renderList.length - 1; i >= 0; i--) {\n      var item = renderList[i];\n      var itemNode = item.node;\n      var itemWidth = item.width;\n      var text = item.text;\n      // Hdie text and shorten width if necessary.\n      if (totalWidth > availableSize.width) {\n        totalWidth -= itemWidth - emptyItemWidth;\n        itemWidth = emptyItemWidth;\n        text = null;\n      }\n      var el = new graphic.Polygon({\n        shape: {\n          points: makeItemPoints(lastX, 0, itemWidth, height, i === renderList.length - 1, i === 0)\n        },\n        style: defaults(normalStyleModel.getItemStyle(), {\n          lineJoin: 'bevel'\n        }),\n        textContent: new graphic.Text({\n          style: createTextStyle(textStyleModel, {\n            text: text\n          })\n        }),\n        textConfig: {\n          position: 'inside'\n        },\n        z2: Z2_EMPHASIS_LIFT * 1e4,\n        onclick: curry(onSelect, itemNode)\n      });\n      el.disableLabelAnimation = true;\n      el.getTextContent().ensureState('emphasis').style = createTextStyle(emphasisTextStyleModel, {\n        text: text\n      });\n      el.ensureState('emphasis').style = emphasisItemStyle;\n      toggleHoverEmphasis(el, emphasisModel.get('focus'), emphasisModel.get('blurScope'), emphasisModel.get('disabled'));\n      this.group.add(el);\n      packEventData(el, seriesModel, itemNode);\n      lastX += itemWidth + ITEM_GAP;\n    }\n  };\n  Breadcrumb.prototype.remove = function () {\n    this.group.removeAll();\n  };\n  return Breadcrumb;\n}();\nfunction makeItemPoints(x, y, itemWidth, itemHeight, head, tail) {\n  var points = [[head ? x : x - ARRAY_LENGTH, y], [x + itemWidth, y], [x + itemWidth, y + itemHeight], [head ? x : x - ARRAY_LENGTH, y + itemHeight]];\n  !tail && points.splice(2, 0, [x + itemWidth + ARRAY_LENGTH, y + itemHeight / 2]);\n  !head && points.push([x, y + itemHeight / 2]);\n  return points;\n}\n// Package custom mouse event.\nfunction packEventData(el, seriesModel, itemNode) {\n  getECData(el).eventData = {\n    componentType: 'series',\n    componentSubType: 'treemap',\n    componentIndex: seriesModel.componentIndex,\n    seriesIndex: seriesModel.seriesIndex,\n    seriesName: seriesModel.name,\n    seriesType: 'treemap',\n    selfType: 'breadcrumb',\n    nodeData: {\n      dataIndex: itemNode && itemNode.dataIndex,\n      name: itemNode && itemNode.name\n    },\n    treePathInfo: itemNode && wrapTreePathInfo(itemNode, seriesModel)\n  };\n}\nexport default Breadcrumb;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACA,IAAI,eAAe;AACnB,IAAI,WAAW;AACf,IAAI,eAAe;AACnB,IAAI,aAAa,WAAW,GAAE;IAC5B,SAAS,WAAW,cAAc;QAChC,IAAI,CAAC,KAAK,GAAG,IAAI,sLAAA,CAAA,QAAa;QAC9B,eAAe,GAAG,CAAC,IAAI,CAAC,KAAK;IAC/B;IACA,WAAW,SAAS,CAAC,MAAM,GAAG,SAAU,WAAW,EAAE,GAAG,EAAE,UAAU,EAAE,QAAQ;QAC5E,IAAI,QAAQ,YAAY,QAAQ,CAAC;QACjC,IAAI,YAAY,IAAI,CAAC,KAAK;QAC1B,UAAU,SAAS;QACnB,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,CAAC,YAAY;YACrC;QACF;QACA,IAAI,mBAAmB,MAAM,QAAQ,CAAC;QACtC,IAAI,gBAAgB,MAAM,QAAQ,CAAC;QACnC,IAAI,iBAAiB,iBAAiB,QAAQ,CAAC;QAC/C,IAAI,yBAAyB,cAAc,QAAQ,CAAC;YAAC;YAAa;SAAY;QAC9E,IAAI,cAAc;YAChB,KAAK;gBACH,MAAM,MAAM,GAAG,CAAC;gBAChB,OAAO,MAAM,GAAG,CAAC;gBACjB,KAAK,MAAM,GAAG,CAAC;gBACf,QAAQ,MAAM,GAAG,CAAC;YACpB;YACA,KAAK;gBACH,OAAO,IAAI,QAAQ;gBACnB,QAAQ,IAAI,SAAS;YACvB;YACA,gBAAgB,MAAM,GAAG,CAAC;YAC1B,YAAY;YACZ,YAAY,EAAE;QAChB;QACA,IAAI,CAAC,QAAQ,CAAC,YAAY,aAAa;QACvC,IAAI,CAAC,cAAc,CAAC,aAAa,aAAa,kBAAkB,eAAe,gBAAgB,wBAAwB;QACvH,CAAA,GAAA,gJAAA,CAAA,kBAAsB,AAAD,EAAE,WAAW,YAAY,GAAG,EAAE,YAAY,GAAG;IACpE;IACA;;;GAGC,GACD,WAAW,SAAS,CAAC,QAAQ,GAAG,SAAU,UAAU,EAAE,WAAW,EAAE,cAAc;QAC/E,IAAK,IAAI,OAAO,YAAY,MAAM,OAAO,KAAK,UAAU,CAAE;YACxD,IAAI,OAAO,CAAA,GAAA,+IAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,QAAQ,GAAG,GAAG,CAAC,SAAS;YAC5D,IAAI,WAAW,eAAe,WAAW,CAAC;YAC1C,IAAI,YAAY,KAAK,GAAG,CAAC,SAAS,KAAK,GAAG,eAAe,GAAG,YAAY,cAAc;YACtF,YAAY,UAAU,IAAI,YAAY;YACtC,YAAY,UAAU,CAAC,IAAI,CAAC;gBAC1B,MAAM;gBACN,MAAM;gBACN,OAAO;YACT;QACF;IACF;IACA;;GAEC,GACD,WAAW,SAAS,CAAC,cAAc,GAAG,SAAU,WAAW,EAAE,WAAW,EAAE,gBAAgB,EAAE,aAAa,EAAE,cAAc,EAAE,sBAAsB,EAAE,QAAQ;QACzJ,mBAAmB;QACnB,IAAI,QAAQ;QACZ,IAAI,iBAAiB,YAAY,cAAc;QAC/C,IAAI,SAAS,YAAY,GAAG,CAAC;YAAC;YAAc;SAAS;QACrD,IAAI,gBAAgB,CAAA,GAAA,gJAAA,CAAA,mBAAuB,AAAD,EAAE,YAAY,GAAG,EAAE,YAAY,GAAG;QAC5E,IAAI,aAAa,YAAY,UAAU;QACvC,IAAI,aAAa,YAAY,UAAU;QACvC,IAAI,oBAAoB,cAAc,QAAQ,CAAC,aAAa,YAAY;QACxE,IAAK,IAAI,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;YAC/C,IAAI,OAAO,UAAU,CAAC,EAAE;YACxB,IAAI,WAAW,KAAK,IAAI;YACxB,IAAI,YAAY,KAAK,KAAK;YAC1B,IAAI,OAAO,KAAK,IAAI;YACpB,4CAA4C;YAC5C,IAAI,aAAa,cAAc,KAAK,EAAE;gBACpC,cAAc,YAAY;gBAC1B,YAAY;gBACZ,OAAO;YACT;YACA,IAAI,KAAK,IAAI,mMAAA,CAAA,UAAe,CAAC;gBAC3B,OAAO;oBACL,QAAQ,eAAe,OAAO,GAAG,WAAW,QAAQ,MAAM,WAAW,MAAM,GAAG,GAAG,MAAM;gBACzF;gBACA,OAAO,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,iBAAiB,YAAY,IAAI;oBAC/C,UAAU;gBACZ;gBACA,aAAa,IAAI,oLAAA,CAAA,OAAY,CAAC;oBAC5B,OAAO,CAAA,GAAA,qJAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB;wBACrC,MAAM;oBACR;gBACF;gBACA,YAAY;oBACV,UAAU;gBACZ;gBACA,IAAI,gJAAA,CAAA,mBAAgB,GAAG;gBACvB,SAAS,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE,UAAU;YAC3B;YACA,GAAG,qBAAqB,GAAG;YAC3B,GAAG,cAAc,GAAG,WAAW,CAAC,YAAY,KAAK,GAAG,CAAA,GAAA,qJAAA,CAAA,kBAAe,AAAD,EAAE,wBAAwB;gBAC1F,MAAM;YACR;YACA,GAAG,WAAW,CAAC,YAAY,KAAK,GAAG;YACnC,CAAA,GAAA,gJAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,cAAc,GAAG,CAAC,UAAU,cAAc,GAAG,CAAC,cAAc,cAAc,GAAG,CAAC;YACtG,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;YACf,cAAc,IAAI,aAAa;YAC/B,SAAS,YAAY;QACvB;IACF;IACA,WAAW,SAAS,CAAC,MAAM,GAAG;QAC5B,IAAI,CAAC,KAAK,CAAC,SAAS;IACtB;IACA,OAAO;AACT;AACA,SAAS,eAAe,CAAC,EAAE,CAAC,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI;IAC7D,IAAI,SAAS;QAAC;YAAC,OAAO,IAAI,IAAI;YAAc;SAAE;QAAE;YAAC,IAAI;YAAW;SAAE;QAAE;YAAC,IAAI;YAAW,IAAI;SAAW;QAAE;YAAC,OAAO,IAAI,IAAI;YAAc,IAAI;SAAW;KAAC;IACnJ,CAAC,QAAQ,OAAO,MAAM,CAAC,GAAG,GAAG;QAAC,IAAI,YAAY;QAAc,IAAI,aAAa;KAAE;IAC/E,CAAC,QAAQ,OAAO,IAAI,CAAC;QAAC;QAAG,IAAI,aAAa;KAAE;IAC5C,OAAO;AACT;AACA,8BAA8B;AAC9B,SAAS,cAAc,EAAE,EAAE,WAAW,EAAE,QAAQ;IAC9C,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,IAAI,SAAS,GAAG;QACxB,eAAe;QACf,kBAAkB;QAClB,gBAAgB,YAAY,cAAc;QAC1C,aAAa,YAAY,WAAW;QACpC,YAAY,YAAY,IAAI;QAC5B,YAAY;QACZ,UAAU;QACV,UAAU;YACR,WAAW,YAAY,SAAS,SAAS;YACzC,MAAM,YAAY,SAAS,IAAI;QACjC;QACA,cAAc,YAAY,CAAA,GAAA,+JAAA,CAAA,mBAAgB,AAAD,EAAE,UAAU;IACvD;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 694, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/chart/treemap/TreemapView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport { bind, each, indexOf, curry, extend, normalizeCssArray, isFunction } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { isHighDownDispatcher, setAsHighDownDispatcher, setDefaultStateProxy, enableHoverFocus, Z2_EMPHASIS_LIFT } from '../../util/states.js';\nimport DataDiffer from '../../data/DataDiffer.js';\nimport * as helper from '../helper/treeHelper.js';\nimport Breadcrumb from './Breadcrumb.js';\nimport RoamController from '../../component/helper/RoamController.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport * as animationUtil from '../../util/animation.js';\nimport makeStyleMapper from '../../model/mixin/makeStyleMapper.js';\nimport ChartView from '../../view/Chart.js';\nimport Displayable from 'zrender/lib/graphic/Displayable.js';\nimport { makeInner, convertOptionIdName } from '../../util/model.js';\nimport { windowOpen } from '../../util/format.js';\nimport { setLabelStyle, getLabelStatesModels } from '../../label/labelStyle.js';\nvar Group = graphic.Group;\nvar Rect = graphic.Rect;\nvar DRAG_THRESHOLD = 3;\nvar PATH_LABEL_NOAMAL = 'label';\nvar PATH_UPPERLABEL_NORMAL = 'upperLabel';\n// Should larger than emphasis states lift z\nvar Z2_BASE = Z2_EMPHASIS_LIFT * 10; // Should bigger than every z2.\nvar Z2_BG = Z2_EMPHASIS_LIFT * 2;\nvar Z2_CONTENT = Z2_EMPHASIS_LIFT * 3;\nvar getStateItemStyle = makeStyleMapper([['fill', 'color'],\n// `borderColor` and `borderWidth` has been occupied,\n// so use `stroke` to indicate the stroke of the rect.\n['stroke', 'strokeColor'], ['lineWidth', 'strokeWidth'], ['shadowBlur'], ['shadowOffsetX'], ['shadowOffsetY'], ['shadowColor']\n// Option decal is in `DecalObject` but style.decal is in `PatternObject`.\n// So do not transfer decal directly.\n]);\nvar getItemStyleNormal = function (model) {\n  // Normal style props should include emphasis style props.\n  var itemStyle = getStateItemStyle(model);\n  // Clear styles set by emphasis.\n  itemStyle.stroke = itemStyle.fill = itemStyle.lineWidth = null;\n  return itemStyle;\n};\nvar inner = makeInner();\nvar TreemapView = /** @class */function (_super) {\n  __extends(TreemapView, _super);\n  function TreemapView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TreemapView.type;\n    _this._state = 'ready';\n    _this._storage = createStorage();\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  TreemapView.prototype.render = function (seriesModel, ecModel, api, payload) {\n    var models = ecModel.findComponents({\n      mainType: 'series',\n      subType: 'treemap',\n      query: payload\n    });\n    if (indexOf(models, seriesModel) < 0) {\n      return;\n    }\n    this.seriesModel = seriesModel;\n    this.api = api;\n    this.ecModel = ecModel;\n    var types = ['treemapZoomToNode', 'treemapRootToNode'];\n    var targetInfo = helper.retrieveTargetInfo(payload, types, seriesModel);\n    var payloadType = payload && payload.type;\n    var layoutInfo = seriesModel.layoutInfo;\n    var isInit = !this._oldTree;\n    var thisStorage = this._storage;\n    // Mark new root when action is treemapRootToNode.\n    var reRoot = payloadType === 'treemapRootToNode' && targetInfo && thisStorage ? {\n      rootNodeGroup: thisStorage.nodeGroup[targetInfo.node.getRawIndex()],\n      direction: payload.direction\n    } : null;\n    var containerGroup = this._giveContainerGroup(layoutInfo);\n    var hasAnimation = seriesModel.get('animation');\n    var renderResult = this._doRender(containerGroup, seriesModel, reRoot);\n    hasAnimation && !isInit && (!payloadType || payloadType === 'treemapZoomToNode' || payloadType === 'treemapRootToNode') ? this._doAnimation(containerGroup, renderResult, seriesModel, reRoot) : renderResult.renderFinally();\n    this._resetController(api);\n    this._renderBreadcrumb(seriesModel, api, targetInfo);\n  };\n  TreemapView.prototype._giveContainerGroup = function (layoutInfo) {\n    var containerGroup = this._containerGroup;\n    if (!containerGroup) {\n      // FIXME\n      // 加一层containerGroup是为了clip，但是现在clip功能并没有实现。\n      containerGroup = this._containerGroup = new Group();\n      this._initEvents(containerGroup);\n      this.group.add(containerGroup);\n    }\n    containerGroup.x = layoutInfo.x;\n    containerGroup.y = layoutInfo.y;\n    return containerGroup;\n  };\n  TreemapView.prototype._doRender = function (containerGroup, seriesModel, reRoot) {\n    var thisTree = seriesModel.getData().tree;\n    var oldTree = this._oldTree;\n    // Clear last shape records.\n    var lastsForAnimation = createStorage();\n    var thisStorage = createStorage();\n    var oldStorage = this._storage;\n    var willInvisibleEls = [];\n    function doRenderNode(thisNode, oldNode, parentGroup, depth) {\n      return renderNode(seriesModel, thisStorage, oldStorage, reRoot, lastsForAnimation, willInvisibleEls, thisNode, oldNode, parentGroup, depth);\n    }\n    // Notice: When thisTree and oldTree are the same tree (see list.cloneShallow),\n    // the oldTree is actually losted, so we cannot find all of the old graphic\n    // elements from tree. So we use this strategy: make element storage, move\n    // from old storage to new storage, clear old storage.\n    dualTravel(thisTree.root ? [thisTree.root] : [], oldTree && oldTree.root ? [oldTree.root] : [], containerGroup, thisTree === oldTree || !oldTree, 0);\n    // Process all removing.\n    var willDeleteEls = clearStorage(oldStorage);\n    this._oldTree = thisTree;\n    this._storage = thisStorage;\n    if (this._controllerHost) {\n      var _oldRootLayout = this.seriesModel.layoutInfo;\n      var rootLayout = thisTree.root.getLayout();\n      if (rootLayout.width === _oldRootLayout.width && rootLayout.height === _oldRootLayout.height) {\n        this._controllerHost.zoom = 1;\n      }\n    }\n    return {\n      lastsForAnimation: lastsForAnimation,\n      willDeleteEls: willDeleteEls,\n      renderFinally: renderFinally\n    };\n    function dualTravel(thisViewChildren, oldViewChildren, parentGroup, sameTree, depth) {\n      // When 'render' is triggered by action,\n      // 'this' and 'old' may be the same tree,\n      // we use rawIndex in that case.\n      if (sameTree) {\n        oldViewChildren = thisViewChildren;\n        each(thisViewChildren, function (child, index) {\n          !child.isRemoved() && processNode(index, index);\n        });\n      }\n      // Diff hierarchically (diff only in each subtree, but not whole).\n      // because, consistency of view is important.\n      else {\n        new DataDiffer(oldViewChildren, thisViewChildren, getKey, getKey).add(processNode).update(processNode).remove(curry(processNode, null)).execute();\n      }\n      function getKey(node) {\n        // Identify by name or raw index.\n        return node.getId();\n      }\n      function processNode(newIndex, oldIndex) {\n        var thisNode = newIndex != null ? thisViewChildren[newIndex] : null;\n        var oldNode = oldIndex != null ? oldViewChildren[oldIndex] : null;\n        var group = doRenderNode(thisNode, oldNode, parentGroup, depth);\n        group && dualTravel(thisNode && thisNode.viewChildren || [], oldNode && oldNode.viewChildren || [], group, sameTree, depth + 1);\n      }\n    }\n    function clearStorage(storage) {\n      var willDeleteEls = createStorage();\n      storage && each(storage, function (store, storageName) {\n        var delEls = willDeleteEls[storageName];\n        each(store, function (el) {\n          el && (delEls.push(el), inner(el).willDelete = true);\n        });\n      });\n      return willDeleteEls;\n    }\n    function renderFinally() {\n      each(willDeleteEls, function (els) {\n        each(els, function (el) {\n          el.parent && el.parent.remove(el);\n        });\n      });\n      each(willInvisibleEls, function (el) {\n        el.invisible = true;\n        // Setting invisible is for optimizing, so no need to set dirty,\n        // just mark as invisible.\n        el.dirty();\n      });\n    }\n  };\n  TreemapView.prototype._doAnimation = function (containerGroup, renderResult, seriesModel, reRoot) {\n    var durationOption = seriesModel.get('animationDurationUpdate');\n    var easingOption = seriesModel.get('animationEasing');\n    // TODO: do not support function until necessary.\n    var duration = (isFunction(durationOption) ? 0 : durationOption) || 0;\n    var easing = (isFunction(easingOption) ? null : easingOption) || 'cubicOut';\n    var animationWrap = animationUtil.createWrap();\n    // Make delete animations.\n    each(renderResult.willDeleteEls, function (store, storageName) {\n      each(store, function (el, rawIndex) {\n        if (el.invisible) {\n          return;\n        }\n        var parent = el.parent; // Always has parent, and parent is nodeGroup.\n        var target;\n        var innerStore = inner(parent);\n        if (reRoot && reRoot.direction === 'drillDown') {\n          target = parent === reRoot.rootNodeGroup\n          // This is the content element of view root.\n          // Only `content` will enter this branch, because\n          // `background` and `nodeGroup` will not be deleted.\n          ? {\n            shape: {\n              x: 0,\n              y: 0,\n              width: innerStore.nodeWidth,\n              height: innerStore.nodeHeight\n            },\n            style: {\n              opacity: 0\n            }\n          }\n          // Others.\n          : {\n            style: {\n              opacity: 0\n            }\n          };\n        } else {\n          var targetX = 0;\n          var targetY = 0;\n          if (!innerStore.willDelete) {\n            // Let node animate to right-bottom corner, cooperating with fadeout,\n            // which is appropriate for user understanding.\n            // Divided by 2 for reRoot rolling up effect.\n            targetX = innerStore.nodeWidth / 2;\n            targetY = innerStore.nodeHeight / 2;\n          }\n          target = storageName === 'nodeGroup' ? {\n            x: targetX,\n            y: targetY,\n            style: {\n              opacity: 0\n            }\n          } : {\n            shape: {\n              x: targetX,\n              y: targetY,\n              width: 0,\n              height: 0\n            },\n            style: {\n              opacity: 0\n            }\n          };\n        }\n        // TODO: do not support delay until necessary.\n        target && animationWrap.add(el, target, duration, 0, easing);\n      });\n    });\n    // Make other animations\n    each(this._storage, function (store, storageName) {\n      each(store, function (el, rawIndex) {\n        var last = renderResult.lastsForAnimation[storageName][rawIndex];\n        var target = {};\n        if (!last) {\n          return;\n        }\n        if (el instanceof graphic.Group) {\n          if (last.oldX != null) {\n            target.x = el.x;\n            target.y = el.y;\n            el.x = last.oldX;\n            el.y = last.oldY;\n          }\n        } else {\n          if (last.oldShape) {\n            target.shape = extend({}, el.shape);\n            el.setShape(last.oldShape);\n          }\n          if (last.fadein) {\n            el.setStyle('opacity', 0);\n            target.style = {\n              opacity: 1\n            };\n          }\n          // When animation is stopped for succedent animation starting,\n          // el.style.opacity might not be 1\n          else if (el.style.opacity !== 1) {\n            target.style = {\n              opacity: 1\n            };\n          }\n        }\n        animationWrap.add(el, target, duration, 0, easing);\n      });\n    }, this);\n    this._state = 'animating';\n    animationWrap.finished(bind(function () {\n      this._state = 'ready';\n      renderResult.renderFinally();\n    }, this)).start();\n  };\n  TreemapView.prototype._resetController = function (api) {\n    var controller = this._controller;\n    var controllerHost = this._controllerHost;\n    if (!controllerHost) {\n      this._controllerHost = {\n        target: this.group\n      };\n      controllerHost = this._controllerHost;\n    }\n    // Init controller.\n    if (!controller) {\n      controller = this._controller = new RoamController(api.getZr());\n      controller.enable(this.seriesModel.get('roam'));\n      controllerHost.zoomLimit = this.seriesModel.get('scaleLimit');\n      controllerHost.zoom = this.seriesModel.get('zoom');\n      controller.on('pan', bind(this._onPan, this));\n      controller.on('zoom', bind(this._onZoom, this));\n    }\n    var rect = new BoundingRect(0, 0, api.getWidth(), api.getHeight());\n    controller.setPointerChecker(function (e, x, y) {\n      return rect.contain(x, y);\n    });\n  };\n  TreemapView.prototype._clearController = function () {\n    var controller = this._controller;\n    this._controllerHost = null;\n    if (controller) {\n      controller.dispose();\n      controller = null;\n    }\n  };\n  TreemapView.prototype._onPan = function (e) {\n    if (this._state !== 'animating' && (Math.abs(e.dx) > DRAG_THRESHOLD || Math.abs(e.dy) > DRAG_THRESHOLD)) {\n      // These param must not be cached.\n      var root = this.seriesModel.getData().tree.root;\n      if (!root) {\n        return;\n      }\n      var rootLayout = root.getLayout();\n      if (!rootLayout) {\n        return;\n      }\n      this.api.dispatchAction({\n        type: 'treemapMove',\n        from: this.uid,\n        seriesId: this.seriesModel.id,\n        rootRect: {\n          x: rootLayout.x + e.dx,\n          y: rootLayout.y + e.dy,\n          width: rootLayout.width,\n          height: rootLayout.height\n        }\n      });\n    }\n  };\n  TreemapView.prototype._onZoom = function (e) {\n    var mouseX = e.originX;\n    var mouseY = e.originY;\n    var zoomDelta = e.scale;\n    if (this._state !== 'animating') {\n      // These param must not be cached.\n      var root = this.seriesModel.getData().tree.root;\n      if (!root) {\n        return;\n      }\n      var rootLayout = root.getLayout();\n      if (!rootLayout) {\n        return;\n      }\n      var rect = new BoundingRect(rootLayout.x, rootLayout.y, rootLayout.width, rootLayout.height);\n      // scaleLimit\n      var zoomLimit = null;\n      var _controllerHost = this._controllerHost;\n      zoomLimit = _controllerHost.zoomLimit;\n      var newZoom = _controllerHost.zoom = _controllerHost.zoom || 1;\n      newZoom *= zoomDelta;\n      if (zoomLimit) {\n        var zoomMin = zoomLimit.min || 0;\n        var zoomMax = zoomLimit.max || Infinity;\n        newZoom = Math.max(Math.min(zoomMax, newZoom), zoomMin);\n      }\n      var zoomScale = newZoom / _controllerHost.zoom;\n      _controllerHost.zoom = newZoom;\n      var layoutInfo = this.seriesModel.layoutInfo;\n      // Transform mouse coord from global to containerGroup.\n      mouseX -= layoutInfo.x;\n      mouseY -= layoutInfo.y;\n      // Scale root bounding rect.\n      var m = matrix.create();\n      matrix.translate(m, m, [-mouseX, -mouseY]);\n      matrix.scale(m, m, [zoomScale, zoomScale]);\n      matrix.translate(m, m, [mouseX, mouseY]);\n      rect.applyTransform(m);\n      this.api.dispatchAction({\n        type: 'treemapRender',\n        from: this.uid,\n        seriesId: this.seriesModel.id,\n        rootRect: {\n          x: rect.x,\n          y: rect.y,\n          width: rect.width,\n          height: rect.height\n        }\n      });\n    }\n  };\n  TreemapView.prototype._initEvents = function (containerGroup) {\n    var _this = this;\n    containerGroup.on('click', function (e) {\n      if (_this._state !== 'ready') {\n        return;\n      }\n      var nodeClick = _this.seriesModel.get('nodeClick', true);\n      if (!nodeClick) {\n        return;\n      }\n      var targetInfo = _this.findTarget(e.offsetX, e.offsetY);\n      if (!targetInfo) {\n        return;\n      }\n      var node = targetInfo.node;\n      if (node.getLayout().isLeafRoot) {\n        _this._rootToNode(targetInfo);\n      } else {\n        if (nodeClick === 'zoomToNode') {\n          _this._zoomToNode(targetInfo);\n        } else if (nodeClick === 'link') {\n          var itemModel = node.hostTree.data.getItemModel(node.dataIndex);\n          var link = itemModel.get('link', true);\n          var linkTarget = itemModel.get('target', true) || 'blank';\n          link && windowOpen(link, linkTarget);\n        }\n      }\n    }, this);\n  };\n  TreemapView.prototype._renderBreadcrumb = function (seriesModel, api, targetInfo) {\n    var _this = this;\n    if (!targetInfo) {\n      targetInfo = seriesModel.get('leafDepth', true) != null ? {\n        node: seriesModel.getViewRoot()\n      }\n      // FIXME\n      // better way?\n      // Find breadcrumb tail on center of containerGroup.\n      : this.findTarget(api.getWidth() / 2, api.getHeight() / 2);\n      if (!targetInfo) {\n        targetInfo = {\n          node: seriesModel.getData().tree.root\n        };\n      }\n    }\n    (this._breadcrumb || (this._breadcrumb = new Breadcrumb(this.group))).render(seriesModel, api, targetInfo.node, function (node) {\n      if (_this._state !== 'animating') {\n        helper.aboveViewRoot(seriesModel.getViewRoot(), node) ? _this._rootToNode({\n          node: node\n        }) : _this._zoomToNode({\n          node: node\n        });\n      }\n    });\n  };\n  /**\r\n   * @override\r\n   */\n  TreemapView.prototype.remove = function () {\n    this._clearController();\n    this._containerGroup && this._containerGroup.removeAll();\n    this._storage = createStorage();\n    this._state = 'ready';\n    this._breadcrumb && this._breadcrumb.remove();\n  };\n  TreemapView.prototype.dispose = function () {\n    this._clearController();\n  };\n  TreemapView.prototype._zoomToNode = function (targetInfo) {\n    this.api.dispatchAction({\n      type: 'treemapZoomToNode',\n      from: this.uid,\n      seriesId: this.seriesModel.id,\n      targetNode: targetInfo.node\n    });\n  };\n  TreemapView.prototype._rootToNode = function (targetInfo) {\n    this.api.dispatchAction({\n      type: 'treemapRootToNode',\n      from: this.uid,\n      seriesId: this.seriesModel.id,\n      targetNode: targetInfo.node\n    });\n  };\n  /**\r\n   * @public\r\n   * @param {number} x Global coord x.\r\n   * @param {number} y Global coord y.\r\n   * @return {Object} info If not found, return undefined;\r\n   * @return {number} info.node Target node.\r\n   * @return {number} info.offsetX x refer to target node.\r\n   * @return {number} info.offsetY y refer to target node.\r\n   */\n  TreemapView.prototype.findTarget = function (x, y) {\n    var targetInfo;\n    var viewRoot = this.seriesModel.getViewRoot();\n    viewRoot.eachNode({\n      attr: 'viewChildren',\n      order: 'preorder'\n    }, function (node) {\n      var bgEl = this._storage.background[node.getRawIndex()];\n      // If invisible, there might be no element.\n      if (bgEl) {\n        var point = bgEl.transformCoordToLocal(x, y);\n        var shape = bgEl.shape;\n        // For performance consideration, don't use 'getBoundingRect'.\n        if (shape.x <= point[0] && point[0] <= shape.x + shape.width && shape.y <= point[1] && point[1] <= shape.y + shape.height) {\n          targetInfo = {\n            node: node,\n            offsetX: point[0],\n            offsetY: point[1]\n          };\n        } else {\n          return false; // Suppress visit subtree.\n        }\n      }\n    }, this);\n    return targetInfo;\n  };\n  TreemapView.type = 'treemap';\n  return TreemapView;\n}(ChartView);\n/**\r\n * @inner\r\n */\nfunction createStorage() {\n  return {\n    nodeGroup: [],\n    background: [],\n    content: []\n  };\n}\n/**\r\n * @inner\r\n * @return Return undefined means do not travel further.\r\n */\nfunction renderNode(seriesModel, thisStorage, oldStorage, reRoot, lastsForAnimation, willInvisibleEls, thisNode, oldNode, parentGroup, depth) {\n  // Whether under viewRoot.\n  if (!thisNode) {\n    // Deleting nodes will be performed finally. This method just find\n    // element from old storage, or create new element, set them to new\n    // storage, and set styles.\n    return;\n  }\n  // -------------------------------------------------------------------\n  // Start of closure variables available in \"Procedures in renderNode\".\n  var thisLayout = thisNode.getLayout();\n  var data = seriesModel.getData();\n  var nodeModel = thisNode.getModel();\n  // Only for enabling highlight/downplay. Clear firstly.\n  // Because some node will not be rendered.\n  data.setItemGraphicEl(thisNode.dataIndex, null);\n  if (!thisLayout || !thisLayout.isInView) {\n    return;\n  }\n  var thisWidth = thisLayout.width;\n  var thisHeight = thisLayout.height;\n  var borderWidth = thisLayout.borderWidth;\n  var thisInvisible = thisLayout.invisible;\n  var thisRawIndex = thisNode.getRawIndex();\n  var oldRawIndex = oldNode && oldNode.getRawIndex();\n  var thisViewChildren = thisNode.viewChildren;\n  var upperHeight = thisLayout.upperHeight;\n  var isParent = thisViewChildren && thisViewChildren.length;\n  var itemStyleNormalModel = nodeModel.getModel('itemStyle');\n  var itemStyleEmphasisModel = nodeModel.getModel(['emphasis', 'itemStyle']);\n  var itemStyleBlurModel = nodeModel.getModel(['blur', 'itemStyle']);\n  var itemStyleSelectModel = nodeModel.getModel(['select', 'itemStyle']);\n  var borderRadius = itemStyleNormalModel.get('borderRadius') || 0;\n  // End of closure ariables available in \"Procedures in renderNode\".\n  // -----------------------------------------------------------------\n  // Node group\n  var group = giveGraphic('nodeGroup', Group);\n  if (!group) {\n    return;\n  }\n  parentGroup.add(group);\n  // x,y are not set when el is above view root.\n  group.x = thisLayout.x || 0;\n  group.y = thisLayout.y || 0;\n  group.markRedraw();\n  inner(group).nodeWidth = thisWidth;\n  inner(group).nodeHeight = thisHeight;\n  if (thisLayout.isAboveViewRoot) {\n    return group;\n  }\n  // Background\n  var bg = giveGraphic('background', Rect, depth, Z2_BG);\n  bg && renderBackground(group, bg, isParent && thisLayout.upperLabelHeight);\n  var emphasisModel = nodeModel.getModel('emphasis');\n  var focus = emphasisModel.get('focus');\n  var blurScope = emphasisModel.get('blurScope');\n  var isDisabled = emphasisModel.get('disabled');\n  var focusOrIndices = focus === 'ancestor' ? thisNode.getAncestorsIndices() : focus === 'descendant' ? thisNode.getDescendantIndices() : focus;\n  // No children, render content.\n  if (isParent) {\n    // Because of the implementation about \"traverse\" in graphic hover style, we\n    // can not set hover listener on the \"group\" of non-leaf node. Otherwise the\n    // hover event from the descendents will be listenered.\n    if (isHighDownDispatcher(group)) {\n      setAsHighDownDispatcher(group, false);\n    }\n    if (bg) {\n      setAsHighDownDispatcher(bg, !isDisabled);\n      // Only for enabling highlight/downplay.\n      data.setItemGraphicEl(thisNode.dataIndex, bg);\n      enableHoverFocus(bg, focusOrIndices, blurScope);\n    }\n  } else {\n    var content = giveGraphic('content', Rect, depth, Z2_CONTENT);\n    content && renderContent(group, content);\n    bg.disableMorphing = true;\n    if (bg && isHighDownDispatcher(bg)) {\n      setAsHighDownDispatcher(bg, false);\n    }\n    setAsHighDownDispatcher(group, !isDisabled);\n    // Only for enabling highlight/downplay.\n    data.setItemGraphicEl(thisNode.dataIndex, group);\n    var cursorStyle = nodeModel.getShallow('cursor');\n    cursorStyle && content.attr('cursor', cursorStyle);\n    enableHoverFocus(group, focusOrIndices, blurScope);\n  }\n  return group;\n  // ----------------------------\n  // | Procedures in renderNode |\n  // ----------------------------\n  function renderBackground(group, bg, useUpperLabel) {\n    var ecData = getECData(bg);\n    // For tooltip.\n    ecData.dataIndex = thisNode.dataIndex;\n    ecData.seriesIndex = seriesModel.seriesIndex;\n    bg.setShape({\n      x: 0,\n      y: 0,\n      width: thisWidth,\n      height: thisHeight,\n      r: borderRadius\n    });\n    if (thisInvisible) {\n      // If invisible, do not set visual, otherwise the element will\n      // change immediately before animation. We think it is OK to\n      // remain its origin color when moving out of the view window.\n      processInvisible(bg);\n    } else {\n      bg.invisible = false;\n      var style = thisNode.getVisual('style');\n      var visualBorderColor = style.stroke;\n      var normalStyle = getItemStyleNormal(itemStyleNormalModel);\n      normalStyle.fill = visualBorderColor;\n      var emphasisStyle = getStateItemStyle(itemStyleEmphasisModel);\n      emphasisStyle.fill = itemStyleEmphasisModel.get('borderColor');\n      var blurStyle = getStateItemStyle(itemStyleBlurModel);\n      blurStyle.fill = itemStyleBlurModel.get('borderColor');\n      var selectStyle = getStateItemStyle(itemStyleSelectModel);\n      selectStyle.fill = itemStyleSelectModel.get('borderColor');\n      if (useUpperLabel) {\n        var upperLabelWidth = thisWidth - 2 * borderWidth;\n        prepareText(\n        // PENDING: convert ZRColor to ColorString for text.\n        bg, visualBorderColor, style.opacity, {\n          x: borderWidth,\n          y: 0,\n          width: upperLabelWidth,\n          height: upperHeight\n        });\n      }\n      // For old bg.\n      else {\n        bg.removeTextContent();\n      }\n      bg.setStyle(normalStyle);\n      bg.ensureState('emphasis').style = emphasisStyle;\n      bg.ensureState('blur').style = blurStyle;\n      bg.ensureState('select').style = selectStyle;\n      setDefaultStateProxy(bg);\n    }\n    group.add(bg);\n  }\n  function renderContent(group, content) {\n    var ecData = getECData(content);\n    // For tooltip.\n    ecData.dataIndex = thisNode.dataIndex;\n    ecData.seriesIndex = seriesModel.seriesIndex;\n    var contentWidth = Math.max(thisWidth - 2 * borderWidth, 0);\n    var contentHeight = Math.max(thisHeight - 2 * borderWidth, 0);\n    content.culling = true;\n    content.setShape({\n      x: borderWidth,\n      y: borderWidth,\n      width: contentWidth,\n      height: contentHeight,\n      r: borderRadius\n    });\n    if (thisInvisible) {\n      // If invisible, do not set visual, otherwise the element will\n      // change immediately before animation. We think it is OK to\n      // remain its origin color when moving out of the view window.\n      processInvisible(content);\n    } else {\n      content.invisible = false;\n      var nodeStyle = thisNode.getVisual('style');\n      var visualColor = nodeStyle.fill;\n      var normalStyle = getItemStyleNormal(itemStyleNormalModel);\n      normalStyle.fill = visualColor;\n      normalStyle.decal = nodeStyle.decal;\n      var emphasisStyle = getStateItemStyle(itemStyleEmphasisModel);\n      var blurStyle = getStateItemStyle(itemStyleBlurModel);\n      var selectStyle = getStateItemStyle(itemStyleSelectModel);\n      // PENDING: convert ZRColor to ColorString for text.\n      prepareText(content, visualColor, nodeStyle.opacity, null);\n      content.setStyle(normalStyle);\n      content.ensureState('emphasis').style = emphasisStyle;\n      content.ensureState('blur').style = blurStyle;\n      content.ensureState('select').style = selectStyle;\n      setDefaultStateProxy(content);\n    }\n    group.add(content);\n  }\n  function processInvisible(element) {\n    // Delay invisible setting utill animation finished,\n    // avoid element vanish suddenly before animation.\n    !element.invisible && willInvisibleEls.push(element);\n  }\n  function prepareText(rectEl, visualColor, visualOpacity,\n  // Can be null/undefined\n  upperLabelRect) {\n    var normalLabelModel = nodeModel.getModel(upperLabelRect ? PATH_UPPERLABEL_NORMAL : PATH_LABEL_NOAMAL);\n    var defaultText = convertOptionIdName(nodeModel.get('name'), null);\n    var isShow = normalLabelModel.getShallow('show');\n    setLabelStyle(rectEl, getLabelStatesModels(nodeModel, upperLabelRect ? PATH_UPPERLABEL_NORMAL : PATH_LABEL_NOAMAL), {\n      defaultText: isShow ? defaultText : null,\n      inheritColor: visualColor,\n      defaultOpacity: visualOpacity,\n      labelFetcher: seriesModel,\n      labelDataIndex: thisNode.dataIndex\n    });\n    var textEl = rectEl.getTextContent();\n    if (!textEl) {\n      return;\n    }\n    var textStyle = textEl.style;\n    var textPadding = normalizeCssArray(textStyle.padding || 0);\n    if (upperLabelRect) {\n      rectEl.setTextConfig({\n        layoutRect: upperLabelRect\n      });\n      textEl.disableLabelLayout = true;\n    }\n    textEl.beforeUpdate = function () {\n      var width = Math.max((upperLabelRect ? upperLabelRect.width : rectEl.shape.width) - textPadding[1] - textPadding[3], 0);\n      var height = Math.max((upperLabelRect ? upperLabelRect.height : rectEl.shape.height) - textPadding[0] - textPadding[2], 0);\n      if (textStyle.width !== width || textStyle.height !== height) {\n        textEl.setStyle({\n          width: width,\n          height: height\n        });\n      }\n    };\n    textStyle.truncateMinChar = 2;\n    textStyle.lineOverflow = 'truncate';\n    addDrillDownIcon(textStyle, upperLabelRect, thisLayout);\n    var textEmphasisState = textEl.getState('emphasis');\n    addDrillDownIcon(textEmphasisState ? textEmphasisState.style : null, upperLabelRect, thisLayout);\n  }\n  function addDrillDownIcon(style, upperLabelRect, thisLayout) {\n    var text = style ? style.text : null;\n    if (!upperLabelRect && thisLayout.isLeafRoot && text != null) {\n      var iconChar = seriesModel.get('drillDownIcon', true);\n      style.text = iconChar ? iconChar + ' ' + text : text;\n    }\n  }\n  function giveGraphic(storageName, Ctor, depth, z) {\n    var element = oldRawIndex != null && oldStorage[storageName][oldRawIndex];\n    var lasts = lastsForAnimation[storageName];\n    if (element) {\n      // Remove from oldStorage\n      oldStorage[storageName][oldRawIndex] = null;\n      prepareAnimationWhenHasOld(lasts, element);\n    }\n    // If invisible and no old element, do not create new element (for optimizing).\n    else if (!thisInvisible) {\n      element = new Ctor();\n      if (element instanceof Displayable) {\n        element.z2 = calculateZ2(depth, z);\n      }\n      prepareAnimationWhenNoOld(lasts, element);\n    }\n    // Set to thisStorage\n    return thisStorage[storageName][thisRawIndex] = element;\n  }\n  function prepareAnimationWhenHasOld(lasts, element) {\n    var lastCfg = lasts[thisRawIndex] = {};\n    if (element instanceof Group) {\n      lastCfg.oldX = element.x;\n      lastCfg.oldY = element.y;\n    } else {\n      lastCfg.oldShape = extend({}, element.shape);\n    }\n  }\n  // If a element is new, we need to find the animation start point carefully,\n  // otherwise it will looks strange when 'zoomToNode'.\n  function prepareAnimationWhenNoOld(lasts, element) {\n    var lastCfg = lasts[thisRawIndex] = {};\n    var parentNode = thisNode.parentNode;\n    var isGroup = element instanceof graphic.Group;\n    if (parentNode && (!reRoot || reRoot.direction === 'drillDown')) {\n      var parentOldX = 0;\n      var parentOldY = 0;\n      // New nodes appear from right-bottom corner in 'zoomToNode' animation.\n      // For convenience, get old bounding rect from background.\n      var parentOldBg = lastsForAnimation.background[parentNode.getRawIndex()];\n      if (!reRoot && parentOldBg && parentOldBg.oldShape) {\n        parentOldX = parentOldBg.oldShape.width;\n        parentOldY = parentOldBg.oldShape.height;\n      }\n      // When no parent old shape found, its parent is new too,\n      // so we can just use {x:0, y:0}.\n      if (isGroup) {\n        lastCfg.oldX = 0;\n        lastCfg.oldY = parentOldY;\n      } else {\n        lastCfg.oldShape = {\n          x: parentOldX,\n          y: parentOldY,\n          width: 0,\n          height: 0\n        };\n      }\n    }\n    // Fade in, user can be aware that these nodes are new.\n    lastCfg.fadein = !isGroup;\n  }\n}\n// We cannot set all background with the same z, because the behaviour of\n// drill down and roll up differ background creation sequence from tree\n// hierarchy sequence, which cause lower background elements to overlap\n// upper ones. So we calculate z based on depth.\n// Moreover, we try to shrink down z interval to [0, 1] to avoid that\n// treemap with large z overlaps other components.\nfunction calculateZ2(depth, z2InLevel) {\n  return depth * Z2_BASE + z2InLevel;\n}\nexport default TreemapView;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;AACA,IAAI,QAAQ,sLAAA,CAAA,QAAa;AACzB,IAAI,OAAO,6LAAA,CAAA,OAAY;AACvB,IAAI,iBAAiB;AACrB,IAAI,oBAAoB;AACxB,IAAI,yBAAyB;AAC7B,4CAA4C;AAC5C,IAAI,UAAU,gJAAA,CAAA,mBAAgB,GAAG,IAAI,+BAA+B;AACpE,IAAI,QAAQ,gJAAA,CAAA,mBAAgB,GAAG;AAC/B,IAAI,aAAa,gJAAA,CAAA,mBAAgB,GAAG;AACpC,IAAI,oBAAoB,CAAA,GAAA,mKAAA,CAAA,UAAe,AAAD,EAAE;IAAC;QAAC;QAAQ;KAAQ;IAC1D,qDAAqD;IACrD,sDAAsD;IACtD;QAAC;QAAU;KAAc;IAAE;QAAC;QAAa;KAAc;IAAE;QAAC;KAAa;IAAE;QAAC;KAAgB;IAAE;QAAC;KAAgB;IAAE;QAAC;KAAc;CAG7H;AACD,IAAI,qBAAqB,SAAU,KAAK;IACtC,0DAA0D;IAC1D,IAAI,YAAY,kBAAkB;IAClC,gCAAgC;IAChC,UAAU,MAAM,GAAG,UAAU,IAAI,GAAG,UAAU,SAAS,GAAG;IAC1D,OAAO;AACT;AACA,IAAI,QAAQ,CAAA,GAAA,+IAAA,CAAA,YAAS,AAAD;AACpB,IAAI,cAAc,WAAW,GAAE,SAAU,MAAM;IAC7C,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE,aAAa;IACvB,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,YAAY,IAAI;QAC7B,MAAM,MAAM,GAAG;QACf,MAAM,QAAQ,GAAG;QACjB,OAAO;IACT;IACA;;GAEC,GACD,YAAY,SAAS,CAAC,MAAM,GAAG,SAAU,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO;QACzE,IAAI,SAAS,QAAQ,cAAc,CAAC;YAClC,UAAU;YACV,SAAS;YACT,OAAO;QACT;QACA,IAAI,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,QAAQ,eAAe,GAAG;YACpC;QACF;QACA,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,QAAQ;YAAC;YAAqB;SAAoB;QACtD,IAAI,aAAa,CAAA,GAAA,+JAAA,CAAA,qBAAyB,AAAD,EAAE,SAAS,OAAO;QAC3D,IAAI,cAAc,WAAW,QAAQ,IAAI;QACzC,IAAI,aAAa,YAAY,UAAU;QACvC,IAAI,SAAS,CAAC,IAAI,CAAC,QAAQ;QAC3B,IAAI,cAAc,IAAI,CAAC,QAAQ;QAC/B,kDAAkD;QAClD,IAAI,SAAS,gBAAgB,uBAAuB,cAAc,cAAc;YAC9E,eAAe,YAAY,SAAS,CAAC,WAAW,IAAI,CAAC,WAAW,GAAG;YACnE,WAAW,QAAQ,SAAS;QAC9B,IAAI;QACJ,IAAI,iBAAiB,IAAI,CAAC,mBAAmB,CAAC;QAC9C,IAAI,eAAe,YAAY,GAAG,CAAC;QACnC,IAAI,eAAe,IAAI,CAAC,SAAS,CAAC,gBAAgB,aAAa;QAC/D,gBAAgB,CAAC,UAAU,CAAC,CAAC,eAAe,gBAAgB,uBAAuB,gBAAgB,mBAAmB,IAAI,IAAI,CAAC,YAAY,CAAC,gBAAgB,cAAc,aAAa,UAAU,aAAa,aAAa;QAC3N,IAAI,CAAC,gBAAgB,CAAC;QACtB,IAAI,CAAC,iBAAiB,CAAC,aAAa,KAAK;IAC3C;IACA,YAAY,SAAS,CAAC,mBAAmB,GAAG,SAAU,UAAU;QAC9D,IAAI,iBAAiB,IAAI,CAAC,eAAe;QACzC,IAAI,CAAC,gBAAgB;YACnB,QAAQ;YACR,4CAA4C;YAC5C,iBAAiB,IAAI,CAAC,eAAe,GAAG,IAAI;YAC5C,IAAI,CAAC,WAAW,CAAC;YACjB,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QACjB;QACA,eAAe,CAAC,GAAG,WAAW,CAAC;QAC/B,eAAe,CAAC,GAAG,WAAW,CAAC;QAC/B,OAAO;IACT;IACA,YAAY,SAAS,CAAC,SAAS,GAAG,SAAU,cAAc,EAAE,WAAW,EAAE,MAAM;QAC7E,IAAI,WAAW,YAAY,OAAO,GAAG,IAAI;QACzC,IAAI,UAAU,IAAI,CAAC,QAAQ;QAC3B,4BAA4B;QAC5B,IAAI,oBAAoB;QACxB,IAAI,cAAc;QAClB,IAAI,aAAa,IAAI,CAAC,QAAQ;QAC9B,IAAI,mBAAmB,EAAE;QACzB,SAAS,aAAa,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK;YACzD,OAAO,WAAW,aAAa,aAAa,YAAY,QAAQ,mBAAmB,kBAAkB,UAAU,SAAS,aAAa;QACvI;QACA,+EAA+E;QAC/E,2EAA2E;QAC3E,0EAA0E;QAC1E,sDAAsD;QACtD,WAAW,SAAS,IAAI,GAAG;YAAC,SAAS,IAAI;SAAC,GAAG,EAAE,EAAE,WAAW,QAAQ,IAAI,GAAG;YAAC,QAAQ,IAAI;SAAC,GAAG,EAAE,EAAE,gBAAgB,aAAa,WAAW,CAAC,SAAS;QAClJ,wBAAwB;QACxB,IAAI,gBAAgB,aAAa;QACjC,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,IAAI,CAAC,eAAe,EAAE;YACxB,IAAI,iBAAiB,IAAI,CAAC,WAAW,CAAC,UAAU;YAChD,IAAI,aAAa,SAAS,IAAI,CAAC,SAAS;YACxC,IAAI,WAAW,KAAK,KAAK,eAAe,KAAK,IAAI,WAAW,MAAM,KAAK,eAAe,MAAM,EAAE;gBAC5F,IAAI,CAAC,eAAe,CAAC,IAAI,GAAG;YAC9B;QACF;QACA,OAAO;YACL,mBAAmB;YACnB,eAAe;YACf,eAAe;QACjB;;QACA,SAAS,WAAW,gBAAgB,EAAE,eAAe,EAAE,WAAW,EAAE,QAAQ,EAAE,KAAK;YACjF,wCAAwC;YACxC,yCAAyC;YACzC,gCAAgC;YAChC,IAAI,UAAU;gBACZ,kBAAkB;gBAClB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,kBAAkB,SAAU,KAAK,EAAE,KAAK;oBAC3C,CAAC,MAAM,SAAS,MAAM,YAAY,OAAO;gBAC3C;YACF,OAGK;gBACH,IAAI,oJAAA,CAAA,UAAU,CAAC,iBAAiB,kBAAkB,QAAQ,QAAQ,GAAG,CAAC,aAAa,MAAM,CAAC,aAAa,MAAM,CAAC,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE,aAAa,OAAO,OAAO;YACjJ;YACA,SAAS,OAAO,IAAI;gBAClB,iCAAiC;gBACjC,OAAO,KAAK,KAAK;YACnB;YACA,SAAS,YAAY,QAAQ,EAAE,QAAQ;gBACrC,IAAI,WAAW,YAAY,OAAO,gBAAgB,CAAC,SAAS,GAAG;gBAC/D,IAAI,UAAU,YAAY,OAAO,eAAe,CAAC,SAAS,GAAG;gBAC7D,IAAI,QAAQ,aAAa,UAAU,SAAS,aAAa;gBACzD,SAAS,WAAW,YAAY,SAAS,YAAY,IAAI,EAAE,EAAE,WAAW,QAAQ,YAAY,IAAI,EAAE,EAAE,OAAO,UAAU,QAAQ;YAC/H;QACF;QACA,SAAS,aAAa,OAAO;YAC3B,IAAI,gBAAgB;YACpB,WAAW,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,SAAS,SAAU,KAAK,EAAE,WAAW;gBACnD,IAAI,SAAS,aAAa,CAAC,YAAY;gBACvC,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,OAAO,SAAU,EAAE;oBACtB,MAAM,CAAC,OAAO,IAAI,CAAC,KAAK,MAAM,IAAI,UAAU,GAAG,IAAI;gBACrD;YACF;YACA,OAAO;QACT;QACA,SAAS;YACP,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,eAAe,SAAU,GAAG;gBAC/B,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,KAAK,SAAU,EAAE;oBACpB,GAAG,MAAM,IAAI,GAAG,MAAM,CAAC,MAAM,CAAC;gBAChC;YACF;YACA,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,kBAAkB,SAAU,EAAE;gBACjC,GAAG,SAAS,GAAG;gBACf,gEAAgE;gBAChE,0BAA0B;gBAC1B,GAAG,KAAK;YACV;QACF;IACF;IACA,YAAY,SAAS,CAAC,YAAY,GAAG,SAAU,cAAc,EAAE,YAAY,EAAE,WAAW,EAAE,MAAM;QAC9F,IAAI,iBAAiB,YAAY,GAAG,CAAC;QACrC,IAAI,eAAe,YAAY,GAAG,CAAC;QACnC,iDAAiD;QACjD,IAAI,WAAW,CAAC,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD,EAAE,kBAAkB,IAAI,cAAc,KAAK;QACpE,IAAI,SAAS,CAAC,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD,EAAE,gBAAgB,OAAO,YAAY,KAAK;QACjE,IAAI,gBAAgB,CAAA,GAAA,mJAAA,CAAA,aAAwB,AAAD;QAC3C,0BAA0B;QAC1B,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,aAAa,aAAa,EAAE,SAAU,KAAK,EAAE,WAAW;YAC3D,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,OAAO,SAAU,EAAE,EAAE,QAAQ;gBAChC,IAAI,GAAG,SAAS,EAAE;oBAChB;gBACF;gBACA,IAAI,SAAS,GAAG,MAAM,EAAE,8CAA8C;gBACtE,IAAI;gBACJ,IAAI,aAAa,MAAM;gBACvB,IAAI,UAAU,OAAO,SAAS,KAAK,aAAa;oBAC9C,SAAS,WAAW,OAAO,aAAa,GAItC;wBACA,OAAO;4BACL,GAAG;4BACH,GAAG;4BACH,OAAO,WAAW,SAAS;4BAC3B,QAAQ,WAAW,UAAU;wBAC/B;wBACA,OAAO;4BACL,SAAS;wBACX;oBACF,IAEE;wBACA,OAAO;4BACL,SAAS;wBACX;oBACF;gBACF,OAAO;oBACL,IAAI,UAAU;oBACd,IAAI,UAAU;oBACd,IAAI,CAAC,WAAW,UAAU,EAAE;wBAC1B,qEAAqE;wBACrE,+CAA+C;wBAC/C,6CAA6C;wBAC7C,UAAU,WAAW,SAAS,GAAG;wBACjC,UAAU,WAAW,UAAU,GAAG;oBACpC;oBACA,SAAS,gBAAgB,cAAc;wBACrC,GAAG;wBACH,GAAG;wBACH,OAAO;4BACL,SAAS;wBACX;oBACF,IAAI;wBACF,OAAO;4BACL,GAAG;4BACH,GAAG;4BACH,OAAO;4BACP,QAAQ;wBACV;wBACA,OAAO;4BACL,SAAS;wBACX;oBACF;gBACF;gBACA,8CAA8C;gBAC9C,UAAU,cAAc,GAAG,CAAC,IAAI,QAAQ,UAAU,GAAG;YACvD;QACF;QACA,wBAAwB;QACxB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,QAAQ,EAAE,SAAU,KAAK,EAAE,WAAW;YAC9C,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,OAAO,SAAU,EAAE,EAAE,QAAQ;gBAChC,IAAI,OAAO,aAAa,iBAAiB,CAAC,YAAY,CAAC,SAAS;gBAChE,IAAI,SAAS,CAAC;gBACd,IAAI,CAAC,MAAM;oBACT;gBACF;gBACA,IAAI,cAAc,sLAAA,CAAA,QAAa,EAAE;oBAC/B,IAAI,KAAK,IAAI,IAAI,MAAM;wBACrB,OAAO,CAAC,GAAG,GAAG,CAAC;wBACf,OAAO,CAAC,GAAG,GAAG,CAAC;wBACf,GAAG,CAAC,GAAG,KAAK,IAAI;wBAChB,GAAG,CAAC,GAAG,KAAK,IAAI;oBAClB;gBACF,OAAO;oBACL,IAAI,KAAK,QAAQ,EAAE;wBACjB,OAAO,KAAK,GAAG,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,GAAG,KAAK;wBAClC,GAAG,QAAQ,CAAC,KAAK,QAAQ;oBAC3B;oBACA,IAAI,KAAK,MAAM,EAAE;wBACf,GAAG,QAAQ,CAAC,WAAW;wBACvB,OAAO,KAAK,GAAG;4BACb,SAAS;wBACX;oBACF,OAGK,IAAI,GAAG,KAAK,CAAC,OAAO,KAAK,GAAG;wBAC/B,OAAO,KAAK,GAAG;4BACb,SAAS;wBACX;oBACF;gBACF;gBACA,cAAc,GAAG,CAAC,IAAI,QAAQ,UAAU,GAAG;YAC7C;QACF,GAAG,IAAI;QACP,IAAI,CAAC,MAAM,GAAG;QACd,cAAc,QAAQ,CAAC,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE;YAC1B,IAAI,CAAC,MAAM,GAAG;YACd,aAAa,aAAa;QAC5B,GAAG,IAAI,GAAG,KAAK;IACjB;IACA,YAAY,SAAS,CAAC,gBAAgB,GAAG,SAAU,GAAG;QACpD,IAAI,aAAa,IAAI,CAAC,WAAW;QACjC,IAAI,iBAAiB,IAAI,CAAC,eAAe;QACzC,IAAI,CAAC,gBAAgB;YACnB,IAAI,CAAC,eAAe,GAAG;gBACrB,QAAQ,IAAI,CAAC,KAAK;YACpB;YACA,iBAAiB,IAAI,CAAC,eAAe;QACvC;QACA,mBAAmB;QACnB,IAAI,CAAC,YAAY;YACf,aAAa,IAAI,CAAC,WAAW,GAAG,IAAI,uKAAA,CAAA,UAAc,CAAC,IAAI,KAAK;YAC5D,WAAW,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;YACvC,eAAe,SAAS,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;YAChD,eAAe,IAAI,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC;YAC3C,WAAW,EAAE,CAAC,OAAO,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI;YAC3C,WAAW,EAAE,CAAC,QAAQ,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,OAAO,EAAE,IAAI;QAC/C;QACA,IAAI,OAAO,IAAI,sJAAA,CAAA,UAAY,CAAC,GAAG,GAAG,IAAI,QAAQ,IAAI,IAAI,SAAS;QAC/D,WAAW,iBAAiB,CAAC,SAAU,CAAC,EAAE,CAAC,EAAE,CAAC;YAC5C,OAAO,KAAK,OAAO,CAAC,GAAG;QACzB;IACF;IACA,YAAY,SAAS,CAAC,gBAAgB,GAAG;QACvC,IAAI,aAAa,IAAI,CAAC,WAAW;QACjC,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,YAAY;YACd,WAAW,OAAO;YAClB,aAAa;QACf;IACF;IACA,YAAY,SAAS,CAAC,MAAM,GAAG,SAAU,CAAC;QACxC,IAAI,IAAI,CAAC,MAAM,KAAK,eAAe,CAAC,KAAK,GAAG,CAAC,EAAE,EAAE,IAAI,kBAAkB,KAAK,GAAG,CAAC,EAAE,EAAE,IAAI,cAAc,GAAG;YACvG,kCAAkC;YAClC,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI;YAC/C,IAAI,CAAC,MAAM;gBACT;YACF;YACA,IAAI,aAAa,KAAK,SAAS;YAC/B,IAAI,CAAC,YAAY;gBACf;YACF;YACA,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;gBACtB,MAAM;gBACN,MAAM,IAAI,CAAC,GAAG;gBACd,UAAU,IAAI,CAAC,WAAW,CAAC,EAAE;gBAC7B,UAAU;oBACR,GAAG,WAAW,CAAC,GAAG,EAAE,EAAE;oBACtB,GAAG,WAAW,CAAC,GAAG,EAAE,EAAE;oBACtB,OAAO,WAAW,KAAK;oBACvB,QAAQ,WAAW,MAAM;gBAC3B;YACF;QACF;IACF;IACA,YAAY,SAAS,CAAC,OAAO,GAAG,SAAU,CAAC;QACzC,IAAI,SAAS,EAAE,OAAO;QACtB,IAAI,SAAS,EAAE,OAAO;QACtB,IAAI,YAAY,EAAE,KAAK;QACvB,IAAI,IAAI,CAAC,MAAM,KAAK,aAAa;YAC/B,kCAAkC;YAClC,IAAI,OAAO,IAAI,CAAC,WAAW,CAAC,OAAO,GAAG,IAAI,CAAC,IAAI;YAC/C,IAAI,CAAC,MAAM;gBACT;YACF;YACA,IAAI,aAAa,KAAK,SAAS;YAC/B,IAAI,CAAC,YAAY;gBACf;YACF;YACA,IAAI,OAAO,IAAI,sJAAA,CAAA,UAAY,CAAC,WAAW,CAAC,EAAE,WAAW,CAAC,EAAE,WAAW,KAAK,EAAE,WAAW,MAAM;YAC3F,aAAa;YACb,IAAI,YAAY;YAChB,IAAI,kBAAkB,IAAI,CAAC,eAAe;YAC1C,YAAY,gBAAgB,SAAS;YACrC,IAAI,UAAU,gBAAgB,IAAI,GAAG,gBAAgB,IAAI,IAAI;YAC7D,WAAW;YACX,IAAI,WAAW;gBACb,IAAI,UAAU,UAAU,GAAG,IAAI;gBAC/B,IAAI,UAAU,UAAU,GAAG,IAAI;gBAC/B,UAAU,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,SAAS,UAAU;YACjD;YACA,IAAI,YAAY,UAAU,gBAAgB,IAAI;YAC9C,gBAAgB,IAAI,GAAG;YACvB,IAAI,aAAa,IAAI,CAAC,WAAW,CAAC,UAAU;YAC5C,uDAAuD;YACvD,UAAU,WAAW,CAAC;YACtB,UAAU,WAAW,CAAC;YACtB,4BAA4B;YAC5B,IAAI,IAAI,CAAA,GAAA,gJAAA,CAAA,SAAa,AAAD;YACpB,CAAA,GAAA,gJAAA,CAAA,YAAgB,AAAD,EAAE,GAAG,GAAG;gBAAC,CAAC;gBAAQ,CAAC;aAAO;YACzC,CAAA,GAAA,gJAAA,CAAA,QAAY,AAAD,EAAE,GAAG,GAAG;gBAAC;gBAAW;aAAU;YACzC,CAAA,GAAA,gJAAA,CAAA,YAAgB,AAAD,EAAE,GAAG,GAAG;gBAAC;gBAAQ;aAAO;YACvC,KAAK,cAAc,CAAC;YACpB,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;gBACtB,MAAM;gBACN,MAAM,IAAI,CAAC,GAAG;gBACd,UAAU,IAAI,CAAC,WAAW,CAAC,EAAE;gBAC7B,UAAU;oBACR,GAAG,KAAK,CAAC;oBACT,GAAG,KAAK,CAAC;oBACT,OAAO,KAAK,KAAK;oBACjB,QAAQ,KAAK,MAAM;gBACrB;YACF;QACF;IACF;IACA,YAAY,SAAS,CAAC,WAAW,GAAG,SAAU,cAAc;QAC1D,IAAI,QAAQ,IAAI;QAChB,eAAe,EAAE,CAAC,SAAS,SAAU,CAAC;YACpC,IAAI,MAAM,MAAM,KAAK,SAAS;gBAC5B;YACF;YACA,IAAI,YAAY,MAAM,WAAW,CAAC,GAAG,CAAC,aAAa;YACnD,IAAI,CAAC,WAAW;gBACd;YACF;YACA,IAAI,aAAa,MAAM,UAAU,CAAC,EAAE,OAAO,EAAE,EAAE,OAAO;YACtD,IAAI,CAAC,YAAY;gBACf;YACF;YACA,IAAI,OAAO,WAAW,IAAI;YAC1B,IAAI,KAAK,SAAS,GAAG,UAAU,EAAE;gBAC/B,MAAM,WAAW,CAAC;YACpB,OAAO;gBACL,IAAI,cAAc,cAAc;oBAC9B,MAAM,WAAW,CAAC;gBACpB,OAAO,IAAI,cAAc,QAAQ;oBAC/B,IAAI,YAAY,KAAK,QAAQ,CAAC,IAAI,CAAC,YAAY,CAAC,KAAK,SAAS;oBAC9D,IAAI,OAAO,UAAU,GAAG,CAAC,QAAQ;oBACjC,IAAI,aAAa,UAAU,GAAG,CAAC,UAAU,SAAS;oBAClD,QAAQ,CAAA,GAAA,gKAAA,CAAA,aAAU,AAAD,EAAE,MAAM;gBAC3B;YACF;QACF,GAAG,IAAI;IACT;IACA,YAAY,SAAS,CAAC,iBAAiB,GAAG,SAAU,WAAW,EAAE,GAAG,EAAE,UAAU;QAC9E,IAAI,QAAQ,IAAI;QAChB,IAAI,CAAC,YAAY;YACf,aAAa,YAAY,GAAG,CAAC,aAAa,SAAS,OAAO;gBACxD,MAAM,YAAY,WAAW;YAC/B,IAIE,IAAI,CAAC,UAAU,CAAC,IAAI,QAAQ,KAAK,GAAG,IAAI,SAAS,KAAK;YACxD,IAAI,CAAC,YAAY;gBACf,aAAa;oBACX,MAAM,YAAY,OAAO,GAAG,IAAI,CAAC,IAAI;gBACvC;YACF;QACF;QACA,CAAC,IAAI,CAAC,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,GAAG,IAAI,gKAAA,CAAA,UAAU,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,EAAE,MAAM,CAAC,aAAa,KAAK,WAAW,IAAI,EAAE,SAAU,IAAI;YAC5H,IAAI,MAAM,MAAM,KAAK,aAAa;gBAChC,CAAA,GAAA,+JAAA,CAAA,gBAAoB,AAAD,EAAE,YAAY,WAAW,IAAI,QAAQ,MAAM,WAAW,CAAC;oBACxE,MAAM;gBACR,KAAK,MAAM,WAAW,CAAC;oBACrB,MAAM;gBACR;YACF;QACF;IACF;IACA;;GAEC,GACD,YAAY,SAAS,CAAC,MAAM,GAAG;QAC7B,IAAI,CAAC,gBAAgB;QACrB,IAAI,CAAC,eAAe,IAAI,IAAI,CAAC,eAAe,CAAC,SAAS;QACtD,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,WAAW,IAAI,IAAI,CAAC,WAAW,CAAC,MAAM;IAC7C;IACA,YAAY,SAAS,CAAC,OAAO,GAAG;QAC9B,IAAI,CAAC,gBAAgB;IACvB;IACA,YAAY,SAAS,CAAC,WAAW,GAAG,SAAU,UAAU;QACtD,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;YACtB,MAAM;YACN,MAAM,IAAI,CAAC,GAAG;YACd,UAAU,IAAI,CAAC,WAAW,CAAC,EAAE;YAC7B,YAAY,WAAW,IAAI;QAC7B;IACF;IACA,YAAY,SAAS,CAAC,WAAW,GAAG,SAAU,UAAU;QACtD,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;YACtB,MAAM;YACN,MAAM,IAAI,CAAC,GAAG;YACd,UAAU,IAAI,CAAC,WAAW,CAAC,EAAE;YAC7B,YAAY,WAAW,IAAI;QAC7B;IACF;IACA;;;;;;;;GAQC,GACD,YAAY,SAAS,CAAC,UAAU,GAAG,SAAU,CAAC,EAAE,CAAC;QAC/C,IAAI;QACJ,IAAI,WAAW,IAAI,CAAC,WAAW,CAAC,WAAW;QAC3C,SAAS,QAAQ,CAAC;YAChB,MAAM;YACN,OAAO;QACT,GAAG,SAAU,IAAI;YACf,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,WAAW,GAAG;YACvD,2CAA2C;YAC3C,IAAI,MAAM;gBACR,IAAI,QAAQ,KAAK,qBAAqB,CAAC,GAAG;gBAC1C,IAAI,QAAQ,KAAK,KAAK;gBACtB,8DAA8D;gBAC9D,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI,MAAM,CAAC,GAAG,MAAM,KAAK,IAAI,MAAM,CAAC,IAAI,KAAK,CAAC,EAAE,IAAI,KAAK,CAAC,EAAE,IAAI,MAAM,CAAC,GAAG,MAAM,MAAM,EAAE;oBACzH,aAAa;wBACX,MAAM;wBACN,SAAS,KAAK,CAAC,EAAE;wBACjB,SAAS,KAAK,CAAC,EAAE;oBACnB;gBACF,OAAO;oBACL,OAAO,OAAO,0BAA0B;gBAC1C;YACF;QACF,GAAG,IAAI;QACP,OAAO;IACT;IACA,YAAY,IAAI,GAAG;IACnB,OAAO;AACT,EAAE,+IAAA,CAAA,UAAS;AACX;;CAEC,GACD,SAAS;IACP,OAAO;QACL,WAAW,EAAE;QACb,YAAY,EAAE;QACd,SAAS,EAAE;IACb;AACF;AACA;;;CAGC,GACD,SAAS,WAAW,WAAW,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,QAAQ,EAAE,OAAO,EAAE,WAAW,EAAE,KAAK;IAC1I,0BAA0B;IAC1B,IAAI,CAAC,UAAU;QACb,kEAAkE;QAClE,mEAAmE;QACnE,2BAA2B;QAC3B;IACF;IACA,sEAAsE;IACtE,sEAAsE;IACtE,IAAI,aAAa,SAAS,SAAS;IACnC,IAAI,OAAO,YAAY,OAAO;IAC9B,IAAI,YAAY,SAAS,QAAQ;IACjC,uDAAuD;IACvD,0CAA0C;IAC1C,KAAK,gBAAgB,CAAC,SAAS,SAAS,EAAE;IAC1C,IAAI,CAAC,cAAc,CAAC,WAAW,QAAQ,EAAE;QACvC;IACF;IACA,IAAI,YAAY,WAAW,KAAK;IAChC,IAAI,aAAa,WAAW,MAAM;IAClC,IAAI,cAAc,WAAW,WAAW;IACxC,IAAI,gBAAgB,WAAW,SAAS;IACxC,IAAI,eAAe,SAAS,WAAW;IACvC,IAAI,cAAc,WAAW,QAAQ,WAAW;IAChD,IAAI,mBAAmB,SAAS,YAAY;IAC5C,IAAI,cAAc,WAAW,WAAW;IACxC,IAAI,WAAW,oBAAoB,iBAAiB,MAAM;IAC1D,IAAI,uBAAuB,UAAU,QAAQ,CAAC;IAC9C,IAAI,yBAAyB,UAAU,QAAQ,CAAC;QAAC;QAAY;KAAY;IACzE,IAAI,qBAAqB,UAAU,QAAQ,CAAC;QAAC;QAAQ;KAAY;IACjE,IAAI,uBAAuB,UAAU,QAAQ,CAAC;QAAC;QAAU;KAAY;IACrE,IAAI,eAAe,qBAAqB,GAAG,CAAC,mBAAmB;IAC/D,mEAAmE;IACnE,oEAAoE;IACpE,aAAa;IACb,IAAI,QAAQ,YAAY,aAAa;IACrC,IAAI,CAAC,OAAO;QACV;IACF;IACA,YAAY,GAAG,CAAC;IAChB,8CAA8C;IAC9C,MAAM,CAAC,GAAG,WAAW,CAAC,IAAI;IAC1B,MAAM,CAAC,GAAG,WAAW,CAAC,IAAI;IAC1B,MAAM,UAAU;IAChB,MAAM,OAAO,SAAS,GAAG;IACzB,MAAM,OAAO,UAAU,GAAG;IAC1B,IAAI,WAAW,eAAe,EAAE;QAC9B,OAAO;IACT;IACA,aAAa;IACb,IAAI,KAAK,YAAY,cAAc,MAAM,OAAO;IAChD,MAAM,iBAAiB,OAAO,IAAI,YAAY,WAAW,gBAAgB;IACzE,IAAI,gBAAgB,UAAU,QAAQ,CAAC;IACvC,IAAI,QAAQ,cAAc,GAAG,CAAC;IAC9B,IAAI,YAAY,cAAc,GAAG,CAAC;IAClC,IAAI,aAAa,cAAc,GAAG,CAAC;IACnC,IAAI,iBAAiB,UAAU,aAAa,SAAS,mBAAmB,KAAK,UAAU,eAAe,SAAS,oBAAoB,KAAK;IACxI,+BAA+B;IAC/B,IAAI,UAAU;QACZ,4EAA4E;QAC5E,4EAA4E;QAC5E,uDAAuD;QACvD,IAAI,CAAA,GAAA,gJAAA,CAAA,uBAAoB,AAAD,EAAE,QAAQ;YAC/B,CAAA,GAAA,gJAAA,CAAA,0BAAuB,AAAD,EAAE,OAAO;QACjC;QACA,IAAI,IAAI;YACN,CAAA,GAAA,gJAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,CAAC;YAC7B,wCAAwC;YACxC,KAAK,gBAAgB,CAAC,SAAS,SAAS,EAAE;YAC1C,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,IAAI,gBAAgB;QACvC;IACF,OAAO;QACL,IAAI,UAAU,YAAY,WAAW,MAAM,OAAO;QAClD,WAAW,cAAc,OAAO;QAChC,GAAG,eAAe,GAAG;QACrB,IAAI,MAAM,CAAA,GAAA,gJAAA,CAAA,uBAAoB,AAAD,EAAE,KAAK;YAClC,CAAA,GAAA,gJAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI;QAC9B;QACA,CAAA,GAAA,gJAAA,CAAA,0BAAuB,AAAD,EAAE,OAAO,CAAC;QAChC,wCAAwC;QACxC,KAAK,gBAAgB,CAAC,SAAS,SAAS,EAAE;QAC1C,IAAI,cAAc,UAAU,UAAU,CAAC;QACvC,eAAe,QAAQ,IAAI,CAAC,UAAU;QACtC,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,OAAO,gBAAgB;IAC1C;IACA,OAAO;;IACP,+BAA+B;IAC/B,+BAA+B;IAC/B,+BAA+B;IAC/B,SAAS,iBAAiB,KAAK,EAAE,EAAE,EAAE,aAAa;QAChD,IAAI,SAAS,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE;QACvB,eAAe;QACf,OAAO,SAAS,GAAG,SAAS,SAAS;QACrC,OAAO,WAAW,GAAG,YAAY,WAAW;QAC5C,GAAG,QAAQ,CAAC;YACV,GAAG;YACH,GAAG;YACH,OAAO;YACP,QAAQ;YACR,GAAG;QACL;QACA,IAAI,eAAe;YACjB,8DAA8D;YAC9D,4DAA4D;YAC5D,8DAA8D;YAC9D,iBAAiB;QACnB,OAAO;YACL,GAAG,SAAS,GAAG;YACf,IAAI,QAAQ,SAAS,SAAS,CAAC;YAC/B,IAAI,oBAAoB,MAAM,MAAM;YACpC,IAAI,cAAc,mBAAmB;YACrC,YAAY,IAAI,GAAG;YACnB,IAAI,gBAAgB,kBAAkB;YACtC,cAAc,IAAI,GAAG,uBAAuB,GAAG,CAAC;YAChD,IAAI,YAAY,kBAAkB;YAClC,UAAU,IAAI,GAAG,mBAAmB,GAAG,CAAC;YACxC,IAAI,cAAc,kBAAkB;YACpC,YAAY,IAAI,GAAG,qBAAqB,GAAG,CAAC;YAC5C,IAAI,eAAe;gBACjB,IAAI,kBAAkB,YAAY,IAAI;gBACtC,YACA,oDAAoD;gBACpD,IAAI,mBAAmB,MAAM,OAAO,EAAE;oBACpC,GAAG;oBACH,GAAG;oBACH,OAAO;oBACP,QAAQ;gBACV;YACF,OAEK;gBACH,GAAG,iBAAiB;YACtB;YACA,GAAG,QAAQ,CAAC;YACZ,GAAG,WAAW,CAAC,YAAY,KAAK,GAAG;YACnC,GAAG,WAAW,CAAC,QAAQ,KAAK,GAAG;YAC/B,GAAG,WAAW,CAAC,UAAU,KAAK,GAAG;YACjC,CAAA,GAAA,gJAAA,CAAA,uBAAoB,AAAD,EAAE;QACvB;QACA,MAAM,GAAG,CAAC;IACZ;IACA,SAAS,cAAc,KAAK,EAAE,OAAO;QACnC,IAAI,SAAS,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE;QACvB,eAAe;QACf,OAAO,SAAS,GAAG,SAAS,SAAS;QACrC,OAAO,WAAW,GAAG,YAAY,WAAW;QAC5C,IAAI,eAAe,KAAK,GAAG,CAAC,YAAY,IAAI,aAAa;QACzD,IAAI,gBAAgB,KAAK,GAAG,CAAC,aAAa,IAAI,aAAa;QAC3D,QAAQ,OAAO,GAAG;QAClB,QAAQ,QAAQ,CAAC;YACf,GAAG;YACH,GAAG;YACH,OAAO;YACP,QAAQ;YACR,GAAG;QACL;QACA,IAAI,eAAe;YACjB,8DAA8D;YAC9D,4DAA4D;YAC5D,8DAA8D;YAC9D,iBAAiB;QACnB,OAAO;YACL,QAAQ,SAAS,GAAG;YACpB,IAAI,YAAY,SAAS,SAAS,CAAC;YACnC,IAAI,cAAc,UAAU,IAAI;YAChC,IAAI,cAAc,mBAAmB;YACrC,YAAY,IAAI,GAAG;YACnB,YAAY,KAAK,GAAG,UAAU,KAAK;YACnC,IAAI,gBAAgB,kBAAkB;YACtC,IAAI,YAAY,kBAAkB;YAClC,IAAI,cAAc,kBAAkB;YACpC,oDAAoD;YACpD,YAAY,SAAS,aAAa,UAAU,OAAO,EAAE;YACrD,QAAQ,QAAQ,CAAC;YACjB,QAAQ,WAAW,CAAC,YAAY,KAAK,GAAG;YACxC,QAAQ,WAAW,CAAC,QAAQ,KAAK,GAAG;YACpC,QAAQ,WAAW,CAAC,UAAU,KAAK,GAAG;YACtC,CAAA,GAAA,gJAAA,CAAA,uBAAoB,AAAD,EAAE;QACvB;QACA,MAAM,GAAG,CAAC;IACZ;IACA,SAAS,iBAAiB,OAAO;QAC/B,oDAAoD;QACpD,kDAAkD;QAClD,CAAC,QAAQ,SAAS,IAAI,iBAAiB,IAAI,CAAC;IAC9C;IACA,SAAS,YAAY,MAAM,EAAE,WAAW,EAAE,aAAa,EACvD,wBAAwB;IACxB,cAAc;QACZ,IAAI,mBAAmB,UAAU,QAAQ,CAAC,iBAAiB,yBAAyB;QACpF,IAAI,cAAc,CAAA,GAAA,+IAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU,GAAG,CAAC,SAAS;QAC7D,IAAI,SAAS,iBAAiB,UAAU,CAAC;QACzC,CAAA,GAAA,qJAAA,CAAA,gBAAa,AAAD,EAAE,QAAQ,CAAA,GAAA,qJAAA,CAAA,uBAAoB,AAAD,EAAE,WAAW,iBAAiB,yBAAyB,oBAAoB;YAClH,aAAa,SAAS,cAAc;YACpC,cAAc;YACd,gBAAgB;YAChB,cAAc;YACd,gBAAgB,SAAS,SAAS;QACpC;QACA,IAAI,SAAS,OAAO,cAAc;QAClC,IAAI,CAAC,QAAQ;YACX;QACF;QACA,IAAI,YAAY,OAAO,KAAK;QAC5B,IAAI,cAAc,CAAA,GAAA,8IAAA,CAAA,oBAAiB,AAAD,EAAE,UAAU,OAAO,IAAI;QACzD,IAAI,gBAAgB;YAClB,OAAO,aAAa,CAAC;gBACnB,YAAY;YACd;YACA,OAAO,kBAAkB,GAAG;QAC9B;QACA,OAAO,YAAY,GAAG;YACpB,IAAI,QAAQ,KAAK,GAAG,CAAC,CAAC,iBAAiB,eAAe,KAAK,GAAG,OAAO,KAAK,CAAC,KAAK,IAAI,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,EAAE;YACrH,IAAI,SAAS,KAAK,GAAG,CAAC,CAAC,iBAAiB,eAAe,MAAM,GAAG,OAAO,KAAK,CAAC,MAAM,IAAI,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,EAAE;YACxH,IAAI,UAAU,KAAK,KAAK,SAAS,UAAU,MAAM,KAAK,QAAQ;gBAC5D,OAAO,QAAQ,CAAC;oBACd,OAAO;oBACP,QAAQ;gBACV;YACF;QACF;QACA,UAAU,eAAe,GAAG;QAC5B,UAAU,YAAY,GAAG;QACzB,iBAAiB,WAAW,gBAAgB;QAC5C,IAAI,oBAAoB,OAAO,QAAQ,CAAC;QACxC,iBAAiB,oBAAoB,kBAAkB,KAAK,GAAG,MAAM,gBAAgB;IACvF;IACA,SAAS,iBAAiB,KAAK,EAAE,cAAc,EAAE,UAAU;QACzD,IAAI,OAAO,QAAQ,MAAM,IAAI,GAAG;QAChC,IAAI,CAAC,kBAAkB,WAAW,UAAU,IAAI,QAAQ,MAAM;YAC5D,IAAI,WAAW,YAAY,GAAG,CAAC,iBAAiB;YAChD,MAAM,IAAI,GAAG,WAAW,WAAW,MAAM,OAAO;QAClD;IACF;IACA,SAAS,YAAY,WAAW,EAAE,IAAI,EAAE,KAAK,EAAE,CAAC;QAC9C,IAAI,UAAU,eAAe,QAAQ,UAAU,CAAC,YAAY,CAAC,YAAY;QACzE,IAAI,QAAQ,iBAAiB,CAAC,YAAY;QAC1C,IAAI,SAAS;YACX,yBAAyB;YACzB,UAAU,CAAC,YAAY,CAAC,YAAY,GAAG;YACvC,2BAA2B,OAAO;QACpC,OAEK,IAAI,CAAC,eAAe;YACvB,UAAU,IAAI;YACd,IAAI,mBAAmB,wJAAA,CAAA,UAAW,EAAE;gBAClC,QAAQ,EAAE,GAAG,YAAY,OAAO;YAClC;YACA,0BAA0B,OAAO;QACnC;QACA,qBAAqB;QACrB,OAAO,WAAW,CAAC,YAAY,CAAC,aAAa,GAAG;IAClD;IACA,SAAS,2BAA2B,KAAK,EAAE,OAAO;QAChD,IAAI,UAAU,KAAK,CAAC,aAAa,GAAG,CAAC;QACrC,IAAI,mBAAmB,OAAO;YAC5B,QAAQ,IAAI,GAAG,QAAQ,CAAC;YACxB,QAAQ,IAAI,GAAG,QAAQ,CAAC;QAC1B,OAAO;YACL,QAAQ,QAAQ,GAAG,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,QAAQ,KAAK;QAC7C;IACF;IACA,4EAA4E;IAC5E,qDAAqD;IACrD,SAAS,0BAA0B,KAAK,EAAE,OAAO;QAC/C,IAAI,UAAU,KAAK,CAAC,aAAa,GAAG,CAAC;QACrC,IAAI,aAAa,SAAS,UAAU;QACpC,IAAI,UAAU,mBAAmB,sLAAA,CAAA,QAAa;QAC9C,IAAI,cAAc,CAAC,CAAC,UAAU,OAAO,SAAS,KAAK,WAAW,GAAG;YAC/D,IAAI,aAAa;YACjB,IAAI,aAAa;YACjB,uEAAuE;YACvE,0DAA0D;YAC1D,IAAI,cAAc,kBAAkB,UAAU,CAAC,WAAW,WAAW,GAAG;YACxE,IAAI,CAAC,UAAU,eAAe,YAAY,QAAQ,EAAE;gBAClD,aAAa,YAAY,QAAQ,CAAC,KAAK;gBACvC,aAAa,YAAY,QAAQ,CAAC,MAAM;YAC1C;YACA,yDAAyD;YACzD,iCAAiC;YACjC,IAAI,SAAS;gBACX,QAAQ,IAAI,GAAG;gBACf,QAAQ,IAAI,GAAG;YACjB,OAAO;gBACL,QAAQ,QAAQ,GAAG;oBACjB,GAAG;oBACH,GAAG;oBACH,OAAO;oBACP,QAAQ;gBACV;YACF;QACF;QACA,uDAAuD;QACvD,QAAQ,MAAM,GAAG,CAAC;IACpB;AACF;AACA,yEAAyE;AACzE,uEAAuE;AACvE,uEAAuE;AACvE,gDAAgD;AAChD,qEAAqE;AACrE,kDAAkD;AAClD,SAAS,YAAY,KAAK,EAAE,SAAS;IACnC,OAAO,QAAQ,UAAU;AAC3B;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1620, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/chart/treemap/treemapVisual.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport VisualMapping from '../../visual/VisualMapping.js';\nimport { each, extend, isArray } from 'zrender/lib/core/util.js';\nimport { modifyHSL, modifyAlpha } from 'zrender/lib/tool/color.js';\nimport { makeInner } from '../../util/model.js';\nvar ITEM_STYLE_NORMAL = 'itemStyle';\nvar inner = makeInner();\nexport default {\n  seriesType: 'treemap',\n  reset: function (seriesModel) {\n    var tree = seriesModel.getData().tree;\n    var root = tree.root;\n    if (root.isRemoved()) {\n      return;\n    }\n    travelTree(root,\n    // Visual should calculate from tree root but not view root.\n    {}, seriesModel.getViewRoot().getAncestors(), seriesModel);\n  }\n};\nfunction travelTree(node, designatedVisual, viewRootAncestors, seriesModel) {\n  var nodeModel = node.getModel();\n  var nodeLayout = node.getLayout();\n  var data = node.hostTree.data;\n  // Optimize\n  if (!nodeLayout || nodeLayout.invisible || !nodeLayout.isInView) {\n    return;\n  }\n  var nodeItemStyleModel = nodeModel.getModel(ITEM_STYLE_NORMAL);\n  var visuals = buildVisuals(nodeItemStyleModel, designatedVisual, seriesModel);\n  var existsStyle = data.ensureUniqueItemVisual(node.dataIndex, 'style');\n  // calculate border color\n  var borderColor = nodeItemStyleModel.get('borderColor');\n  var borderColorSaturation = nodeItemStyleModel.get('borderColorSaturation');\n  var thisNodeColor;\n  if (borderColorSaturation != null) {\n    // For performance, do not always execute 'calculateColor'.\n    thisNodeColor = calculateColor(visuals);\n    borderColor = calculateBorderColor(borderColorSaturation, thisNodeColor);\n  }\n  existsStyle.stroke = borderColor;\n  var viewChildren = node.viewChildren;\n  if (!viewChildren || !viewChildren.length) {\n    thisNodeColor = calculateColor(visuals);\n    // Apply visual to this node.\n    existsStyle.fill = thisNodeColor;\n  } else {\n    var mapping_1 = buildVisualMapping(node, nodeModel, nodeLayout, nodeItemStyleModel, visuals, viewChildren);\n    // Designate visual to children.\n    each(viewChildren, function (child, index) {\n      // If higher than viewRoot, only ancestors of viewRoot is needed to visit.\n      if (child.depth >= viewRootAncestors.length || child === viewRootAncestors[child.depth]) {\n        var childVisual = mapVisual(nodeModel, visuals, child, index, mapping_1, seriesModel);\n        travelTree(child, childVisual, viewRootAncestors, seriesModel);\n      }\n    });\n  }\n}\nfunction buildVisuals(nodeItemStyleModel, designatedVisual, seriesModel) {\n  var visuals = extend({}, designatedVisual);\n  var designatedVisualItemStyle = seriesModel.designatedVisualItemStyle;\n  each(['color', 'colorAlpha', 'colorSaturation'], function (visualName) {\n    // Priority: thisNode > thisLevel > parentNodeDesignated > seriesModel\n    designatedVisualItemStyle[visualName] = designatedVisual[visualName];\n    var val = nodeItemStyleModel.get(visualName);\n    designatedVisualItemStyle[visualName] = null;\n    val != null && (visuals[visualName] = val);\n  });\n  return visuals;\n}\nfunction calculateColor(visuals) {\n  var color = getValueVisualDefine(visuals, 'color');\n  if (color) {\n    var colorAlpha = getValueVisualDefine(visuals, 'colorAlpha');\n    var colorSaturation = getValueVisualDefine(visuals, 'colorSaturation');\n    if (colorSaturation) {\n      color = modifyHSL(color, null, null, colorSaturation);\n    }\n    if (colorAlpha) {\n      color = modifyAlpha(color, colorAlpha);\n    }\n    return color;\n  }\n}\nfunction calculateBorderColor(borderColorSaturation, thisNodeColor) {\n  return thisNodeColor != null\n  // Can only be string\n  ? modifyHSL(thisNodeColor, null, null, borderColorSaturation) : null;\n}\nfunction getValueVisualDefine(visuals, name) {\n  var value = visuals[name];\n  if (value != null && value !== 'none') {\n    return value;\n  }\n}\nfunction buildVisualMapping(node, nodeModel, nodeLayout, nodeItemStyleModel, visuals, viewChildren) {\n  if (!viewChildren || !viewChildren.length) {\n    return;\n  }\n  var rangeVisual = getRangeVisual(nodeModel, 'color') || visuals.color != null && visuals.color !== 'none' && (getRangeVisual(nodeModel, 'colorAlpha') || getRangeVisual(nodeModel, 'colorSaturation'));\n  if (!rangeVisual) {\n    return;\n  }\n  var visualMin = nodeModel.get('visualMin');\n  var visualMax = nodeModel.get('visualMax');\n  var dataExtent = nodeLayout.dataExtent.slice();\n  visualMin != null && visualMin < dataExtent[0] && (dataExtent[0] = visualMin);\n  visualMax != null && visualMax > dataExtent[1] && (dataExtent[1] = visualMax);\n  var colorMappingBy = nodeModel.get('colorMappingBy');\n  var opt = {\n    type: rangeVisual.name,\n    dataExtent: dataExtent,\n    visual: rangeVisual.range\n  };\n  if (opt.type === 'color' && (colorMappingBy === 'index' || colorMappingBy === 'id')) {\n    opt.mappingMethod = 'category';\n    opt.loop = true;\n    // categories is ordinal, so do not set opt.categories.\n  } else {\n    opt.mappingMethod = 'linear';\n  }\n  var mapping = new VisualMapping(opt);\n  inner(mapping).drColorMappingBy = colorMappingBy;\n  return mapping;\n}\n// Notice: If we don't have the attribute 'colorRange', but only use\n// attribute 'color' to represent both concepts of 'colorRange' and 'color',\n// (It means 'colorRange' when 'color' is Array, means 'color' when not array),\n// this problem will be encountered:\n// If a level-1 node doesn't have children, and its siblings have children,\n// and colorRange is set on level-1, then the node cannot be colored.\n// So we separate 'colorRange' and 'color' to different attributes.\nfunction getRangeVisual(nodeModel, name) {\n  // 'colorRange', 'colorARange', 'colorSRange'.\n  // If not exists on this node, fetch from levels and series.\n  var range = nodeModel.get(name);\n  return isArray(range) && range.length ? {\n    name: name,\n    range: range\n  } : null;\n}\nfunction mapVisual(nodeModel, visuals, child, index, mapping, seriesModel) {\n  var childVisuals = extend({}, visuals);\n  if (mapping) {\n    // Only support color, colorAlpha, colorSaturation.\n    var mappingType = mapping.type;\n    var colorMappingBy = mappingType === 'color' && inner(mapping).drColorMappingBy;\n    var value = colorMappingBy === 'index' ? index : colorMappingBy === 'id' ? seriesModel.mapIdToIndex(child.getId()) : child.getValue(nodeModel.get('visualDimension'));\n    childVisuals[mappingType] = mapping.mapValueToVisual(value);\n  }\n  return childVisuals;\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;;;;;AACA,IAAI,oBAAoB;AACxB,IAAI,QAAQ,CAAA,GAAA,+IAAA,CAAA,YAAS,AAAD;uCACL;IACb,YAAY;IACZ,OAAO,SAAU,WAAW;QAC1B,IAAI,OAAO,YAAY,OAAO,GAAG,IAAI;QACrC,IAAI,OAAO,KAAK,IAAI;QACpB,IAAI,KAAK,SAAS,IAAI;YACpB;QACF;QACA,WAAW,MACX,4DAA4D;QAC5D,CAAC,GAAG,YAAY,WAAW,GAAG,YAAY,IAAI;IAChD;AACF;AACA,SAAS,WAAW,IAAI,EAAE,gBAAgB,EAAE,iBAAiB,EAAE,WAAW;IACxE,IAAI,YAAY,KAAK,QAAQ;IAC7B,IAAI,aAAa,KAAK,SAAS;IAC/B,IAAI,OAAO,KAAK,QAAQ,CAAC,IAAI;IAC7B,WAAW;IACX,IAAI,CAAC,cAAc,WAAW,SAAS,IAAI,CAAC,WAAW,QAAQ,EAAE;QAC/D;IACF;IACA,IAAI,qBAAqB,UAAU,QAAQ,CAAC;IAC5C,IAAI,UAAU,aAAa,oBAAoB,kBAAkB;IACjE,IAAI,cAAc,KAAK,sBAAsB,CAAC,KAAK,SAAS,EAAE;IAC9D,yBAAyB;IACzB,IAAI,cAAc,mBAAmB,GAAG,CAAC;IACzC,IAAI,wBAAwB,mBAAmB,GAAG,CAAC;IACnD,IAAI;IACJ,IAAI,yBAAyB,MAAM;QACjC,2DAA2D;QAC3D,gBAAgB,eAAe;QAC/B,cAAc,qBAAqB,uBAAuB;IAC5D;IACA,YAAY,MAAM,GAAG;IACrB,IAAI,eAAe,KAAK,YAAY;IACpC,IAAI,CAAC,gBAAgB,CAAC,aAAa,MAAM,EAAE;QACzC,gBAAgB,eAAe;QAC/B,6BAA6B;QAC7B,YAAY,IAAI,GAAG;IACrB,OAAO;QACL,IAAI,YAAY,mBAAmB,MAAM,WAAW,YAAY,oBAAoB,SAAS;QAC7F,gCAAgC;QAChC,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,cAAc,SAAU,KAAK,EAAE,KAAK;YACvC,0EAA0E;YAC1E,IAAI,MAAM,KAAK,IAAI,kBAAkB,MAAM,IAAI,UAAU,iBAAiB,CAAC,MAAM,KAAK,CAAC,EAAE;gBACvF,IAAI,cAAc,UAAU,WAAW,SAAS,OAAO,OAAO,WAAW;gBACzE,WAAW,OAAO,aAAa,mBAAmB;YACpD;QACF;IACF;AACF;AACA,SAAS,aAAa,kBAAkB,EAAE,gBAAgB,EAAE,WAAW;IACrE,IAAI,UAAU,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG;IACzB,IAAI,4BAA4B,YAAY,yBAAyB;IACrE,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE;QAAC;QAAS;QAAc;KAAkB,EAAE,SAAU,UAAU;QACnE,sEAAsE;QACtE,yBAAyB,CAAC,WAAW,GAAG,gBAAgB,CAAC,WAAW;QACpE,IAAI,MAAM,mBAAmB,GAAG,CAAC;QACjC,yBAAyB,CAAC,WAAW,GAAG;QACxC,OAAO,QAAQ,CAAC,OAAO,CAAC,WAAW,GAAG,GAAG;IAC3C;IACA,OAAO;AACT;AACA,SAAS,eAAe,OAAO;IAC7B,IAAI,QAAQ,qBAAqB,SAAS;IAC1C,IAAI,OAAO;QACT,IAAI,aAAa,qBAAqB,SAAS;QAC/C,IAAI,kBAAkB,qBAAqB,SAAS;QACpD,IAAI,iBAAiB;YACnB,QAAQ,CAAA,GAAA,+IAAA,CAAA,YAAS,AAAD,EAAE,OAAO,MAAM,MAAM;QACvC;QACA,IAAI,YAAY;YACd,QAAQ,CAAA,GAAA,+IAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QAC7B;QACA,OAAO;IACT;AACF;AACA,SAAS,qBAAqB,qBAAqB,EAAE,aAAa;IAChE,OAAO,iBAAiB,OAEtB,CAAA,GAAA,+IAAA,CAAA,YAAS,AAAD,EAAE,eAAe,MAAM,MAAM,yBAAyB;AAClE;AACA,SAAS,qBAAqB,OAAO,EAAE,IAAI;IACzC,IAAI,QAAQ,OAAO,CAAC,KAAK;IACzB,IAAI,SAAS,QAAQ,UAAU,QAAQ;QACrC,OAAO;IACT;AACF;AACA,SAAS,mBAAmB,IAAI,EAAE,SAAS,EAAE,UAAU,EAAE,kBAAkB,EAAE,OAAO,EAAE,YAAY;IAChG,IAAI,CAAC,gBAAgB,CAAC,aAAa,MAAM,EAAE;QACzC;IACF;IACA,IAAI,cAAc,eAAe,WAAW,YAAY,QAAQ,KAAK,IAAI,QAAQ,QAAQ,KAAK,KAAK,UAAU,CAAC,eAAe,WAAW,iBAAiB,eAAe,WAAW,kBAAkB;IACrM,IAAI,CAAC,aAAa;QAChB;IACF;IACA,IAAI,YAAY,UAAU,GAAG,CAAC;IAC9B,IAAI,YAAY,UAAU,GAAG,CAAC;IAC9B,IAAI,aAAa,WAAW,UAAU,CAAC,KAAK;IAC5C,aAAa,QAAQ,YAAY,UAAU,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG,SAAS;IAC5E,aAAa,QAAQ,YAAY,UAAU,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG,SAAS;IAC5E,IAAI,iBAAiB,UAAU,GAAG,CAAC;IACnC,IAAI,MAAM;QACR,MAAM,YAAY,IAAI;QACtB,YAAY;QACZ,QAAQ,YAAY,KAAK;IAC3B;IACA,IAAI,IAAI,IAAI,KAAK,WAAW,CAAC,mBAAmB,WAAW,mBAAmB,IAAI,GAAG;QACnF,IAAI,aAAa,GAAG;QACpB,IAAI,IAAI,GAAG;IACX,uDAAuD;IACzD,OAAO;QACL,IAAI,aAAa,GAAG;IACtB;IACA,IAAI,UAAU,IAAI,yJAAA,CAAA,UAAa,CAAC;IAChC,MAAM,SAAS,gBAAgB,GAAG;IAClC,OAAO;AACT;AACA,oEAAoE;AACpE,4EAA4E;AAC5E,+EAA+E;AAC/E,oCAAoC;AACpC,2EAA2E;AAC3E,qEAAqE;AACrE,mEAAmE;AACnE,SAAS,eAAe,SAAS,EAAE,IAAI;IACrC,8CAA8C;IAC9C,4DAA4D;IAC5D,IAAI,QAAQ,UAAU,GAAG,CAAC;IAC1B,OAAO,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,UAAU,MAAM,MAAM,GAAG;QACtC,MAAM;QACN,OAAO;IACT,IAAI;AACN;AACA,SAAS,UAAU,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,WAAW;IACvE,IAAI,eAAe,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG;IAC9B,IAAI,SAAS;QACX,mDAAmD;QACnD,IAAI,cAAc,QAAQ,IAAI;QAC9B,IAAI,iBAAiB,gBAAgB,WAAW,MAAM,SAAS,gBAAgB;QAC/E,IAAI,QAAQ,mBAAmB,UAAU,QAAQ,mBAAmB,OAAO,YAAY,YAAY,CAAC,MAAM,KAAK,MAAM,MAAM,QAAQ,CAAC,UAAU,GAAG,CAAC;QAClJ,YAAY,CAAC,YAAY,GAAG,QAAQ,gBAAgB,CAAC;IACvD;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1821, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/chart/treemap/treemapLayout.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\n/*\r\n* A third-party license is embedded for some of the code in this file:\r\n* The treemap layout implementation was originally copied from\r\n* \"d3.js\" with some modifications made for this project.\r\n* (See more details in the comment of the method \"squarify\" below.)\r\n* The use of the source code of this file is also subject to the terms\r\n* and consitions of the license of \"d3.js\" (BSD-3Clause, see\r\n* </licenses/LICENSE-d3>).\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport BoundingRect from 'zrender/lib/core/BoundingRect.js';\nimport { parsePercent, MAX_SAFE_INTEGER } from '../../util/number.js';\nimport * as layout from '../../util/layout.js';\nimport * as helper from '../helper/treeHelper.js';\nvar mathMax = Math.max;\nvar mathMin = Math.min;\nvar retrieveValue = zrUtil.retrieve;\nvar each = zrUtil.each;\nvar PATH_BORDER_WIDTH = ['itemStyle', 'borderWidth'];\nvar PATH_GAP_WIDTH = ['itemStyle', 'gapWidth'];\nvar PATH_UPPER_LABEL_SHOW = ['upperLabel', 'show'];\nvar PATH_UPPER_LABEL_HEIGHT = ['upperLabel', 'height'];\n;\n/**\r\n * @public\r\n */\nexport default {\n  seriesType: 'treemap',\n  reset: function (seriesModel, ecModel, api, payload) {\n    // Layout result in each node:\n    // {x, y, width, height, area, borderWidth}\n    var ecWidth = api.getWidth();\n    var ecHeight = api.getHeight();\n    var seriesOption = seriesModel.option;\n    var layoutInfo = layout.getLayoutRect(seriesModel.getBoxLayoutParams(), {\n      width: api.getWidth(),\n      height: api.getHeight()\n    });\n    var size = seriesOption.size || []; // Compatible with ec2.\n    var containerWidth = parsePercent(retrieveValue(layoutInfo.width, size[0]), ecWidth);\n    var containerHeight = parsePercent(retrieveValue(layoutInfo.height, size[1]), ecHeight);\n    // Fetch payload info.\n    var payloadType = payload && payload.type;\n    var types = ['treemapZoomToNode', 'treemapRootToNode'];\n    var targetInfo = helper.retrieveTargetInfo(payload, types, seriesModel);\n    var rootRect = payloadType === 'treemapRender' || payloadType === 'treemapMove' ? payload.rootRect : null;\n    var viewRoot = seriesModel.getViewRoot();\n    var viewAbovePath = helper.getPathToRoot(viewRoot);\n    if (payloadType !== 'treemapMove') {\n      var rootSize = payloadType === 'treemapZoomToNode' ? estimateRootSize(seriesModel, targetInfo, viewRoot, containerWidth, containerHeight) : rootRect ? [rootRect.width, rootRect.height] : [containerWidth, containerHeight];\n      var sort_1 = seriesOption.sort;\n      if (sort_1 && sort_1 !== 'asc' && sort_1 !== 'desc') {\n        // Default to be desc order.\n        sort_1 = 'desc';\n      }\n      var options = {\n        squareRatio: seriesOption.squareRatio,\n        sort: sort_1,\n        leafDepth: seriesOption.leafDepth\n      };\n      // layout should be cleared because using updateView but not update.\n      viewRoot.hostTree.clearLayouts();\n      // TODO\n      // optimize: if out of view clip, do not layout.\n      // But take care that if do not render node out of view clip,\n      // how to calculate start po\n      var viewRootLayout_1 = {\n        x: 0,\n        y: 0,\n        width: rootSize[0],\n        height: rootSize[1],\n        area: rootSize[0] * rootSize[1]\n      };\n      viewRoot.setLayout(viewRootLayout_1);\n      squarify(viewRoot, options, false, 0);\n      // Supplement layout.\n      viewRootLayout_1 = viewRoot.getLayout();\n      each(viewAbovePath, function (node, index) {\n        var childValue = (viewAbovePath[index + 1] || viewRoot).getValue();\n        node.setLayout(zrUtil.extend({\n          dataExtent: [childValue, childValue],\n          borderWidth: 0,\n          upperHeight: 0\n        }, viewRootLayout_1));\n      });\n    }\n    var treeRoot = seriesModel.getData().tree.root;\n    treeRoot.setLayout(calculateRootPosition(layoutInfo, rootRect, targetInfo), true);\n    seriesModel.setLayoutInfo(layoutInfo);\n    // FIXME\n    // 现在没有clip功能，暂时取ec高宽。\n    prunning(treeRoot,\n    // Transform to base element coordinate system.\n    new BoundingRect(-layoutInfo.x, -layoutInfo.y, ecWidth, ecHeight), viewAbovePath, viewRoot, 0);\n  }\n};\n/**\r\n * Layout treemap with squarify algorithm.\r\n * The original presentation of this algorithm\r\n * was made by Mark Bruls, Kees Huizing, and Jarke J. van Wijk\r\n * <https://graphics.ethz.ch/teaching/scivis_common/Literature/squarifiedTreeMaps.pdf>.\r\n * The implementation of this algorithm was originally copied from \"d3.js\"\r\n * <https://github.com/d3/d3/blob/9cc9a875e636a1dcf36cc1e07bdf77e1ad6e2c74/src/layout/treemap.js>\r\n * with some modifications made for this program.\r\n * See the license statement at the head of this file.\r\n *\r\n * @protected\r\n * @param {module:echarts/data/Tree~TreeNode} node\r\n * @param {Object} options\r\n * @param {string} options.sort 'asc' or 'desc'\r\n * @param {number} options.squareRatio\r\n * @param {boolean} hideChildren\r\n * @param {number} depth\r\n */\nfunction squarify(node, options, hideChildren, depth) {\n  var width;\n  var height;\n  if (node.isRemoved()) {\n    return;\n  }\n  var thisLayout = node.getLayout();\n  width = thisLayout.width;\n  height = thisLayout.height;\n  // Considering border and gap\n  var nodeModel = node.getModel();\n  var borderWidth = nodeModel.get(PATH_BORDER_WIDTH);\n  var halfGapWidth = nodeModel.get(PATH_GAP_WIDTH) / 2;\n  var upperLabelHeight = getUpperLabelHeight(nodeModel);\n  var upperHeight = Math.max(borderWidth, upperLabelHeight);\n  var layoutOffset = borderWidth - halfGapWidth;\n  var layoutOffsetUpper = upperHeight - halfGapWidth;\n  node.setLayout({\n    borderWidth: borderWidth,\n    upperHeight: upperHeight,\n    upperLabelHeight: upperLabelHeight\n  }, true);\n  width = mathMax(width - 2 * layoutOffset, 0);\n  height = mathMax(height - layoutOffset - layoutOffsetUpper, 0);\n  var totalArea = width * height;\n  var viewChildren = initChildren(node, nodeModel, totalArea, options, hideChildren, depth);\n  if (!viewChildren.length) {\n    return;\n  }\n  var rect = {\n    x: layoutOffset,\n    y: layoutOffsetUpper,\n    width: width,\n    height: height\n  };\n  var rowFixedLength = mathMin(width, height);\n  var best = Infinity; // the best row score so far\n  var row = [];\n  row.area = 0;\n  for (var i = 0, len = viewChildren.length; i < len;) {\n    var child = viewChildren[i];\n    row.push(child);\n    row.area += child.getLayout().area;\n    var score = worst(row, rowFixedLength, options.squareRatio);\n    // continue with this orientation\n    if (score <= best) {\n      i++;\n      best = score;\n    }\n    // abort, and try a different orientation\n    else {\n      row.area -= row.pop().getLayout().area;\n      position(row, rowFixedLength, rect, halfGapWidth, false);\n      rowFixedLength = mathMin(rect.width, rect.height);\n      row.length = row.area = 0;\n      best = Infinity;\n    }\n  }\n  if (row.length) {\n    position(row, rowFixedLength, rect, halfGapWidth, true);\n  }\n  if (!hideChildren) {\n    var childrenVisibleMin = nodeModel.get('childrenVisibleMin');\n    if (childrenVisibleMin != null && totalArea < childrenVisibleMin) {\n      hideChildren = true;\n    }\n  }\n  for (var i = 0, len = viewChildren.length; i < len; i++) {\n    squarify(viewChildren[i], options, hideChildren, depth + 1);\n  }\n}\n/**\r\n * Set area to each child, and calculate data extent for visual coding.\r\n */\nfunction initChildren(node, nodeModel, totalArea, options, hideChildren, depth) {\n  var viewChildren = node.children || [];\n  var orderBy = options.sort;\n  orderBy !== 'asc' && orderBy !== 'desc' && (orderBy = null);\n  var overLeafDepth = options.leafDepth != null && options.leafDepth <= depth;\n  // leafDepth has higher priority.\n  if (hideChildren && !overLeafDepth) {\n    return node.viewChildren = [];\n  }\n  // Sort children, order by desc.\n  viewChildren = zrUtil.filter(viewChildren, function (child) {\n    return !child.isRemoved();\n  });\n  sort(viewChildren, orderBy);\n  var info = statistic(nodeModel, viewChildren, orderBy);\n  if (info.sum === 0) {\n    return node.viewChildren = [];\n  }\n  info.sum = filterByThreshold(nodeModel, totalArea, info.sum, orderBy, viewChildren);\n  if (info.sum === 0) {\n    return node.viewChildren = [];\n  }\n  // Set area to each child.\n  for (var i = 0, len = viewChildren.length; i < len; i++) {\n    var area = viewChildren[i].getValue() / info.sum * totalArea;\n    // Do not use setLayout({...}, true), because it is needed to clear last layout.\n    viewChildren[i].setLayout({\n      area: area\n    });\n  }\n  if (overLeafDepth) {\n    viewChildren.length && node.setLayout({\n      isLeafRoot: true\n    }, true);\n    viewChildren.length = 0;\n  }\n  node.viewChildren = viewChildren;\n  node.setLayout({\n    dataExtent: info.dataExtent\n  }, true);\n  return viewChildren;\n}\n/**\r\n * Consider 'visibleMin'. Modify viewChildren and get new sum.\r\n */\nfunction filterByThreshold(nodeModel, totalArea, sum, orderBy, orderedChildren) {\n  // visibleMin is not supported yet when no option.sort.\n  if (!orderBy) {\n    return sum;\n  }\n  var visibleMin = nodeModel.get('visibleMin');\n  var len = orderedChildren.length;\n  var deletePoint = len;\n  // Always travel from little value to big value.\n  for (var i = len - 1; i >= 0; i--) {\n    var value = orderedChildren[orderBy === 'asc' ? len - i - 1 : i].getValue();\n    if (value / sum * totalArea < visibleMin) {\n      deletePoint = i;\n      sum -= value;\n    }\n  }\n  orderBy === 'asc' ? orderedChildren.splice(0, len - deletePoint) : orderedChildren.splice(deletePoint, len - deletePoint);\n  return sum;\n}\n/**\r\n * Sort\r\n */\nfunction sort(viewChildren, orderBy) {\n  if (orderBy) {\n    viewChildren.sort(function (a, b) {\n      var diff = orderBy === 'asc' ? a.getValue() - b.getValue() : b.getValue() - a.getValue();\n      return diff === 0 ? orderBy === 'asc' ? a.dataIndex - b.dataIndex : b.dataIndex - a.dataIndex : diff;\n    });\n  }\n  return viewChildren;\n}\n/**\r\n * Statistic\r\n */\nfunction statistic(nodeModel, children, orderBy) {\n  // Calculate sum.\n  var sum = 0;\n  for (var i = 0, len = children.length; i < len; i++) {\n    sum += children[i].getValue();\n  }\n  // Statistic data extent for latter visual coding.\n  // Notice: data extent should be calculate based on raw children\n  // but not filtered view children, otherwise visual mapping will not\n  // be stable when zoom (where children is filtered by visibleMin).\n  var dimension = nodeModel.get('visualDimension');\n  var dataExtent;\n  // The same as area dimension.\n  if (!children || !children.length) {\n    dataExtent = [NaN, NaN];\n  } else if (dimension === 'value' && orderBy) {\n    dataExtent = [children[children.length - 1].getValue(), children[0].getValue()];\n    orderBy === 'asc' && dataExtent.reverse();\n  }\n  // Other dimension.\n  else {\n    dataExtent = [Infinity, -Infinity];\n    each(children, function (child) {\n      var value = child.getValue(dimension);\n      value < dataExtent[0] && (dataExtent[0] = value);\n      value > dataExtent[1] && (dataExtent[1] = value);\n    });\n  }\n  return {\n    sum: sum,\n    dataExtent: dataExtent\n  };\n}\n/**\r\n * Computes the score for the specified row,\r\n * as the worst aspect ratio.\r\n */\nfunction worst(row, rowFixedLength, ratio) {\n  var areaMax = 0;\n  var areaMin = Infinity;\n  for (var i = 0, area = void 0, len = row.length; i < len; i++) {\n    area = row[i].getLayout().area;\n    if (area) {\n      area < areaMin && (areaMin = area);\n      area > areaMax && (areaMax = area);\n    }\n  }\n  var squareArea = row.area * row.area;\n  var f = rowFixedLength * rowFixedLength * ratio;\n  return squareArea ? mathMax(f * areaMax / squareArea, squareArea / (f * areaMin)) : Infinity;\n}\n/**\r\n * Positions the specified row of nodes. Modifies `rect`.\r\n */\nfunction position(row, rowFixedLength, rect, halfGapWidth, flush) {\n  // When rowFixedLength === rect.width,\n  // it is horizontal subdivision,\n  // rowFixedLength is the width of the subdivision,\n  // rowOtherLength is the height of the subdivision,\n  // and nodes will be positioned from left to right.\n  // wh[idx0WhenH] means: when horizontal,\n  //      wh[idx0WhenH] => wh[0] => 'width'.\n  //      xy[idx1WhenH] => xy[1] => 'y'.\n  var idx0WhenH = rowFixedLength === rect.width ? 0 : 1;\n  var idx1WhenH = 1 - idx0WhenH;\n  var xy = ['x', 'y'];\n  var wh = ['width', 'height'];\n  var last = rect[xy[idx0WhenH]];\n  var rowOtherLength = rowFixedLength ? row.area / rowFixedLength : 0;\n  if (flush || rowOtherLength > rect[wh[idx1WhenH]]) {\n    rowOtherLength = rect[wh[idx1WhenH]]; // over+underflow\n  }\n  for (var i = 0, rowLen = row.length; i < rowLen; i++) {\n    var node = row[i];\n    var nodeLayout = {};\n    var step = rowOtherLength ? node.getLayout().area / rowOtherLength : 0;\n    var wh1 = nodeLayout[wh[idx1WhenH]] = mathMax(rowOtherLength - 2 * halfGapWidth, 0);\n    // We use Math.max/min to avoid negative width/height when considering gap width.\n    var remain = rect[xy[idx0WhenH]] + rect[wh[idx0WhenH]] - last;\n    var modWH = i === rowLen - 1 || remain < step ? remain : step;\n    var wh0 = nodeLayout[wh[idx0WhenH]] = mathMax(modWH - 2 * halfGapWidth, 0);\n    nodeLayout[xy[idx1WhenH]] = rect[xy[idx1WhenH]] + mathMin(halfGapWidth, wh1 / 2);\n    nodeLayout[xy[idx0WhenH]] = last + mathMin(halfGapWidth, wh0 / 2);\n    last += modWH;\n    node.setLayout(nodeLayout, true);\n  }\n  rect[xy[idx1WhenH]] += rowOtherLength;\n  rect[wh[idx1WhenH]] -= rowOtherLength;\n}\n// Return [containerWidth, containerHeight] as default.\nfunction estimateRootSize(seriesModel, targetInfo, viewRoot, containerWidth, containerHeight) {\n  // If targetInfo.node exists, we zoom to the node,\n  // so estimate whole width and height by target node.\n  var currNode = (targetInfo || {}).node;\n  var defaultSize = [containerWidth, containerHeight];\n  if (!currNode || currNode === viewRoot) {\n    return defaultSize;\n  }\n  var parent;\n  var viewArea = containerWidth * containerHeight;\n  var area = viewArea * seriesModel.option.zoomToNodeRatio;\n  while (parent = currNode.parentNode) {\n    // jshint ignore:line\n    var sum = 0;\n    var siblings = parent.children;\n    for (var i = 0, len = siblings.length; i < len; i++) {\n      sum += siblings[i].getValue();\n    }\n    var currNodeValue = currNode.getValue();\n    if (currNodeValue === 0) {\n      return defaultSize;\n    }\n    area *= sum / currNodeValue;\n    // Considering border, suppose aspect ratio is 1.\n    var parentModel = parent.getModel();\n    var borderWidth = parentModel.get(PATH_BORDER_WIDTH);\n    var upperHeight = Math.max(borderWidth, getUpperLabelHeight(parentModel));\n    area += 4 * borderWidth * borderWidth + (3 * borderWidth + upperHeight) * Math.pow(area, 0.5);\n    area > MAX_SAFE_INTEGER && (area = MAX_SAFE_INTEGER);\n    currNode = parent;\n  }\n  area < viewArea && (area = viewArea);\n  var scale = Math.pow(area / viewArea, 0.5);\n  return [containerWidth * scale, containerHeight * scale];\n}\n// Root position based on coord of containerGroup\nfunction calculateRootPosition(layoutInfo, rootRect, targetInfo) {\n  if (rootRect) {\n    return {\n      x: rootRect.x,\n      y: rootRect.y\n    };\n  }\n  var defaultPosition = {\n    x: 0,\n    y: 0\n  };\n  if (!targetInfo) {\n    return defaultPosition;\n  }\n  // If targetInfo is fetched by 'retrieveTargetInfo',\n  // old tree and new tree are the same tree,\n  // so the node still exists and we can visit it.\n  var targetNode = targetInfo.node;\n  var layout = targetNode.getLayout();\n  if (!layout) {\n    return defaultPosition;\n  }\n  // Transform coord from local to container.\n  var targetCenter = [layout.width / 2, layout.height / 2];\n  var node = targetNode;\n  while (node) {\n    var nodeLayout = node.getLayout();\n    targetCenter[0] += nodeLayout.x;\n    targetCenter[1] += nodeLayout.y;\n    node = node.parentNode;\n  }\n  return {\n    x: layoutInfo.width / 2 - targetCenter[0],\n    y: layoutInfo.height / 2 - targetCenter[1]\n  };\n}\n// Mark nodes visible for prunning when visual coding and rendering.\n// Prunning depends on layout and root position, so we have to do it after layout.\nfunction prunning(node, clipRect, viewAbovePath, viewRoot, depth) {\n  var nodeLayout = node.getLayout();\n  var nodeInViewAbovePath = viewAbovePath[depth];\n  var isAboveViewRoot = nodeInViewAbovePath && nodeInViewAbovePath === node;\n  if (nodeInViewAbovePath && !isAboveViewRoot || depth === viewAbovePath.length && node !== viewRoot) {\n    return;\n  }\n  node.setLayout({\n    // isInView means: viewRoot sub tree + viewAbovePath\n    isInView: true,\n    // invisible only means: outside view clip so that the node can not\n    // see but still layout for animation preparation but not render.\n    invisible: !isAboveViewRoot && !clipRect.intersect(nodeLayout),\n    isAboveViewRoot: isAboveViewRoot\n  }, true);\n  // Transform to child coordinate.\n  var childClipRect = new BoundingRect(clipRect.x - nodeLayout.x, clipRect.y - nodeLayout.y, clipRect.width, clipRect.height);\n  each(node.viewChildren || [], function (child) {\n    prunning(child, childClipRect, viewAbovePath, viewRoot, depth + 1);\n  });\n}\nfunction getUpperLabelHeight(model) {\n  return model.get(PATH_UPPER_LABEL_SHOW) ? model.get(PATH_UPPER_LABEL_HEIGHT) : 0;\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA,GACA;;;;;;;;AAQA;;;AACA;AACA;AACA;AACA;AACA;;;;;;AACA,IAAI,UAAU,KAAK,GAAG;AACtB,IAAI,UAAU,KAAK,GAAG;AACtB,IAAI,gBAAgB,8IAAA,CAAA,WAAe;AACnC,IAAI,OAAO,8IAAA,CAAA,OAAW;AACtB,IAAI,oBAAoB;IAAC;IAAa;CAAc;AACpD,IAAI,iBAAiB;IAAC;IAAa;CAAW;AAC9C,IAAI,wBAAwB;IAAC;IAAc;CAAO;AAClD,IAAI,0BAA0B;IAAC;IAAc;CAAS;;uCAKvC;IACb,YAAY;IACZ,OAAO,SAAU,WAAW,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO;QACjD,8BAA8B;QAC9B,2CAA2C;QAC3C,IAAI,UAAU,IAAI,QAAQ;QAC1B,IAAI,WAAW,IAAI,SAAS;QAC5B,IAAI,eAAe,YAAY,MAAM;QACrC,IAAI,aAAa,CAAA,GAAA,gJAAA,CAAA,gBAAoB,AAAD,EAAE,YAAY,kBAAkB,IAAI;YACtE,OAAO,IAAI,QAAQ;YACnB,QAAQ,IAAI,SAAS;QACvB;QACA,IAAI,OAAO,aAAa,IAAI,IAAI,EAAE,EAAE,uBAAuB;QAC3D,IAAI,iBAAiB,CAAA,GAAA,gJAAA,CAAA,eAAY,AAAD,EAAE,cAAc,WAAW,KAAK,EAAE,IAAI,CAAC,EAAE,GAAG;QAC5E,IAAI,kBAAkB,CAAA,GAAA,gJAAA,CAAA,eAAY,AAAD,EAAE,cAAc,WAAW,MAAM,EAAE,IAAI,CAAC,EAAE,GAAG;QAC9E,sBAAsB;QACtB,IAAI,cAAc,WAAW,QAAQ,IAAI;QACzC,IAAI,QAAQ;YAAC;YAAqB;SAAoB;QACtD,IAAI,aAAa,CAAA,GAAA,+JAAA,CAAA,qBAAyB,AAAD,EAAE,SAAS,OAAO;QAC3D,IAAI,WAAW,gBAAgB,mBAAmB,gBAAgB,gBAAgB,QAAQ,QAAQ,GAAG;QACrG,IAAI,WAAW,YAAY,WAAW;QACtC,IAAI,gBAAgB,CAAA,GAAA,+JAAA,CAAA,gBAAoB,AAAD,EAAE;QACzC,IAAI,gBAAgB,eAAe;YACjC,IAAI,WAAW,gBAAgB,sBAAsB,iBAAiB,aAAa,YAAY,UAAU,gBAAgB,mBAAmB,WAAW;gBAAC,SAAS,KAAK;gBAAE,SAAS,MAAM;aAAC,GAAG;gBAAC;gBAAgB;aAAgB;YAC5N,IAAI,SAAS,aAAa,IAAI;YAC9B,IAAI,UAAU,WAAW,SAAS,WAAW,QAAQ;gBACnD,4BAA4B;gBAC5B,SAAS;YACX;YACA,IAAI,UAAU;gBACZ,aAAa,aAAa,WAAW;gBACrC,MAAM;gBACN,WAAW,aAAa,SAAS;YACnC;YACA,oEAAoE;YACpE,SAAS,QAAQ,CAAC,YAAY;YAC9B,OAAO;YACP,gDAAgD;YAChD,6DAA6D;YAC7D,4BAA4B;YAC5B,IAAI,mBAAmB;gBACrB,GAAG;gBACH,GAAG;gBACH,OAAO,QAAQ,CAAC,EAAE;gBAClB,QAAQ,QAAQ,CAAC,EAAE;gBACnB,MAAM,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;YACjC;YACA,SAAS,SAAS,CAAC;YACnB,SAAS,UAAU,SAAS,OAAO;YACnC,qBAAqB;YACrB,mBAAmB,SAAS,SAAS;YACrC,KAAK,eAAe,SAAU,IAAI,EAAE,KAAK;gBACvC,IAAI,aAAa,CAAC,aAAa,CAAC,QAAQ,EAAE,IAAI,QAAQ,EAAE,QAAQ;gBAChE,KAAK,SAAS,CAAC,CAAA,GAAA,8IAAA,CAAA,SAAa,AAAD,EAAE;oBAC3B,YAAY;wBAAC;wBAAY;qBAAW;oBACpC,aAAa;oBACb,aAAa;gBACf,GAAG;YACL;QACF;QACA,IAAI,WAAW,YAAY,OAAO,GAAG,IAAI,CAAC,IAAI;QAC9C,SAAS,SAAS,CAAC,sBAAsB,YAAY,UAAU,aAAa;QAC5E,YAAY,aAAa,CAAC;QAC1B,QAAQ;QACR,sBAAsB;QACtB,SAAS,UACT,+CAA+C;QAC/C,IAAI,sJAAA,CAAA,UAAY,CAAC,CAAC,WAAW,CAAC,EAAE,CAAC,WAAW,CAAC,EAAE,SAAS,WAAW,eAAe,UAAU;IAC9F;AACF;AACA;;;;;;;;;;;;;;;;;CAiBC,GACD,SAAS,SAAS,IAAI,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK;IAClD,IAAI;IACJ,IAAI;IACJ,IAAI,KAAK,SAAS,IAAI;QACpB;IACF;IACA,IAAI,aAAa,KAAK,SAAS;IAC/B,QAAQ,WAAW,KAAK;IACxB,SAAS,WAAW,MAAM;IAC1B,6BAA6B;IAC7B,IAAI,YAAY,KAAK,QAAQ;IAC7B,IAAI,cAAc,UAAU,GAAG,CAAC;IAChC,IAAI,eAAe,UAAU,GAAG,CAAC,kBAAkB;IACnD,IAAI,mBAAmB,oBAAoB;IAC3C,IAAI,cAAc,KAAK,GAAG,CAAC,aAAa;IACxC,IAAI,eAAe,cAAc;IACjC,IAAI,oBAAoB,cAAc;IACtC,KAAK,SAAS,CAAC;QACb,aAAa;QACb,aAAa;QACb,kBAAkB;IACpB,GAAG;IACH,QAAQ,QAAQ,QAAQ,IAAI,cAAc;IAC1C,SAAS,QAAQ,SAAS,eAAe,mBAAmB;IAC5D,IAAI,YAAY,QAAQ;IACxB,IAAI,eAAe,aAAa,MAAM,WAAW,WAAW,SAAS,cAAc;IACnF,IAAI,CAAC,aAAa,MAAM,EAAE;QACxB;IACF;IACA,IAAI,OAAO;QACT,GAAG;QACH,GAAG;QACH,OAAO;QACP,QAAQ;IACV;IACA,IAAI,iBAAiB,QAAQ,OAAO;IACpC,IAAI,OAAO,UAAU,4BAA4B;IACjD,IAAI,MAAM,EAAE;IACZ,IAAI,IAAI,GAAG;IACX,IAAK,IAAI,IAAI,GAAG,MAAM,aAAa,MAAM,EAAE,IAAI,KAAM;QACnD,IAAI,QAAQ,YAAY,CAAC,EAAE;QAC3B,IAAI,IAAI,CAAC;QACT,IAAI,IAAI,IAAI,MAAM,SAAS,GAAG,IAAI;QAClC,IAAI,QAAQ,MAAM,KAAK,gBAAgB,QAAQ,WAAW;QAC1D,iCAAiC;QACjC,IAAI,SAAS,MAAM;YACjB;YACA,OAAO;QACT,OAEK;YACH,IAAI,IAAI,IAAI,IAAI,GAAG,GAAG,SAAS,GAAG,IAAI;YACtC,SAAS,KAAK,gBAAgB,MAAM,cAAc;YAClD,iBAAiB,QAAQ,KAAK,KAAK,EAAE,KAAK,MAAM;YAChD,IAAI,MAAM,GAAG,IAAI,IAAI,GAAG;YACxB,OAAO;QACT;IACF;IACA,IAAI,IAAI,MAAM,EAAE;QACd,SAAS,KAAK,gBAAgB,MAAM,cAAc;IACpD;IACA,IAAI,CAAC,cAAc;QACjB,IAAI,qBAAqB,UAAU,GAAG,CAAC;QACvC,IAAI,sBAAsB,QAAQ,YAAY,oBAAoB;YAChE,eAAe;QACjB;IACF;IACA,IAAK,IAAI,IAAI,GAAG,MAAM,aAAa,MAAM,EAAE,IAAI,KAAK,IAAK;QACvD,SAAS,YAAY,CAAC,EAAE,EAAE,SAAS,cAAc,QAAQ;IAC3D;AACF;AACA;;CAEC,GACD,SAAS,aAAa,IAAI,EAAE,SAAS,EAAE,SAAS,EAAE,OAAO,EAAE,YAAY,EAAE,KAAK;IAC5E,IAAI,eAAe,KAAK,QAAQ,IAAI,EAAE;IACtC,IAAI,UAAU,QAAQ,IAAI;IAC1B,YAAY,SAAS,YAAY,UAAU,CAAC,UAAU,IAAI;IAC1D,IAAI,gBAAgB,QAAQ,SAAS,IAAI,QAAQ,QAAQ,SAAS,IAAI;IACtE,iCAAiC;IACjC,IAAI,gBAAgB,CAAC,eAAe;QAClC,OAAO,KAAK,YAAY,GAAG,EAAE;IAC/B;IACA,gCAAgC;IAChC,eAAe,CAAA,GAAA,8IAAA,CAAA,SAAa,AAAD,EAAE,cAAc,SAAU,KAAK;QACxD,OAAO,CAAC,MAAM,SAAS;IACzB;IACA,KAAK,cAAc;IACnB,IAAI,OAAO,UAAU,WAAW,cAAc;IAC9C,IAAI,KAAK,GAAG,KAAK,GAAG;QAClB,OAAO,KAAK,YAAY,GAAG,EAAE;IAC/B;IACA,KAAK,GAAG,GAAG,kBAAkB,WAAW,WAAW,KAAK,GAAG,EAAE,SAAS;IACtE,IAAI,KAAK,GAAG,KAAK,GAAG;QAClB,OAAO,KAAK,YAAY,GAAG,EAAE;IAC/B;IACA,0BAA0B;IAC1B,IAAK,IAAI,IAAI,GAAG,MAAM,aAAa,MAAM,EAAE,IAAI,KAAK,IAAK;QACvD,IAAI,OAAO,YAAY,CAAC,EAAE,CAAC,QAAQ,KAAK,KAAK,GAAG,GAAG;QACnD,gFAAgF;QAChF,YAAY,CAAC,EAAE,CAAC,SAAS,CAAC;YACxB,MAAM;QACR;IACF;IACA,IAAI,eAAe;QACjB,aAAa,MAAM,IAAI,KAAK,SAAS,CAAC;YACpC,YAAY;QACd,GAAG;QACH,aAAa,MAAM,GAAG;IACxB;IACA,KAAK,YAAY,GAAG;IACpB,KAAK,SAAS,CAAC;QACb,YAAY,KAAK,UAAU;IAC7B,GAAG;IACH,OAAO;AACT;AACA;;CAEC,GACD,SAAS,kBAAkB,SAAS,EAAE,SAAS,EAAE,GAAG,EAAE,OAAO,EAAE,eAAe;IAC5E,uDAAuD;IACvD,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IACA,IAAI,aAAa,UAAU,GAAG,CAAC;IAC/B,IAAI,MAAM,gBAAgB,MAAM;IAChC,IAAI,cAAc;IAClB,gDAAgD;IAChD,IAAK,IAAI,IAAI,MAAM,GAAG,KAAK,GAAG,IAAK;QACjC,IAAI,QAAQ,eAAe,CAAC,YAAY,QAAQ,MAAM,IAAI,IAAI,EAAE,CAAC,QAAQ;QACzE,IAAI,QAAQ,MAAM,YAAY,YAAY;YACxC,cAAc;YACd,OAAO;QACT;IACF;IACA,YAAY,QAAQ,gBAAgB,MAAM,CAAC,GAAG,MAAM,eAAe,gBAAgB,MAAM,CAAC,aAAa,MAAM;IAC7G,OAAO;AACT;AACA;;CAEC,GACD,SAAS,KAAK,YAAY,EAAE,OAAO;IACjC,IAAI,SAAS;QACX,aAAa,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;YAC9B,IAAI,OAAO,YAAY,QAAQ,EAAE,QAAQ,KAAK,EAAE,QAAQ,KAAK,EAAE,QAAQ,KAAK,EAAE,QAAQ;YACtF,OAAO,SAAS,IAAI,YAAY,QAAQ,EAAE,SAAS,GAAG,EAAE,SAAS,GAAG,EAAE,SAAS,GAAG,EAAE,SAAS,GAAG;QAClG;IACF;IACA,OAAO;AACT;AACA;;CAEC,GACD,SAAS,UAAU,SAAS,EAAE,QAAQ,EAAE,OAAO;IAC7C,iBAAiB;IACjB,IAAI,MAAM;IACV,IAAK,IAAI,IAAI,GAAG,MAAM,SAAS,MAAM,EAAE,IAAI,KAAK,IAAK;QACnD,OAAO,QAAQ,CAAC,EAAE,CAAC,QAAQ;IAC7B;IACA,kDAAkD;IAClD,gEAAgE;IAChE,oEAAoE;IACpE,kEAAkE;IAClE,IAAI,YAAY,UAAU,GAAG,CAAC;IAC9B,IAAI;IACJ,8BAA8B;IAC9B,IAAI,CAAC,YAAY,CAAC,SAAS,MAAM,EAAE;QACjC,aAAa;YAAC;YAAK;SAAI;IACzB,OAAO,IAAI,cAAc,WAAW,SAAS;QAC3C,aAAa;YAAC,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE,CAAC,QAAQ;YAAI,QAAQ,CAAC,EAAE,CAAC,QAAQ;SAAG;QAC/E,YAAY,SAAS,WAAW,OAAO;IACzC,OAEK;QACH,aAAa;YAAC;YAAU,CAAC;SAAS;QAClC,KAAK,UAAU,SAAU,KAAK;YAC5B,IAAI,QAAQ,MAAM,QAAQ,CAAC;YAC3B,QAAQ,UAAU,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG,KAAK;YAC/C,QAAQ,UAAU,CAAC,EAAE,IAAI,CAAC,UAAU,CAAC,EAAE,GAAG,KAAK;QACjD;IACF;IACA,OAAO;QACL,KAAK;QACL,YAAY;IACd;AACF;AACA;;;CAGC,GACD,SAAS,MAAM,GAAG,EAAE,cAAc,EAAE,KAAK;IACvC,IAAI,UAAU;IACd,IAAI,UAAU;IACd,IAAK,IAAI,IAAI,GAAG,OAAO,KAAK,GAAG,MAAM,IAAI,MAAM,EAAE,IAAI,KAAK,IAAK;QAC7D,OAAO,GAAG,CAAC,EAAE,CAAC,SAAS,GAAG,IAAI;QAC9B,IAAI,MAAM;YACR,OAAO,WAAW,CAAC,UAAU,IAAI;YACjC,OAAO,WAAW,CAAC,UAAU,IAAI;QACnC;IACF;IACA,IAAI,aAAa,IAAI,IAAI,GAAG,IAAI,IAAI;IACpC,IAAI,IAAI,iBAAiB,iBAAiB;IAC1C,OAAO,aAAa,QAAQ,IAAI,UAAU,YAAY,aAAa,CAAC,IAAI,OAAO,KAAK;AACtF;AACA;;CAEC,GACD,SAAS,SAAS,GAAG,EAAE,cAAc,EAAE,IAAI,EAAE,YAAY,EAAE,KAAK;IAC9D,sCAAsC;IACtC,gCAAgC;IAChC,kDAAkD;IAClD,mDAAmD;IACnD,mDAAmD;IACnD,wCAAwC;IACxC,0CAA0C;IAC1C,sCAAsC;IACtC,IAAI,YAAY,mBAAmB,KAAK,KAAK,GAAG,IAAI;IACpD,IAAI,YAAY,IAAI;IACpB,IAAI,KAAK;QAAC;QAAK;KAAI;IACnB,IAAI,KAAK;QAAC;QAAS;KAAS;IAC5B,IAAI,OAAO,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC;IAC9B,IAAI,iBAAiB,iBAAiB,IAAI,IAAI,GAAG,iBAAiB;IAClE,IAAI,SAAS,iBAAiB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE;QACjD,iBAAiB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,iBAAiB;IACzD;IACA,IAAK,IAAI,IAAI,GAAG,SAAS,IAAI,MAAM,EAAE,IAAI,QAAQ,IAAK;QACpD,IAAI,OAAO,GAAG,CAAC,EAAE;QACjB,IAAI,aAAa,CAAC;QAClB,IAAI,OAAO,iBAAiB,KAAK,SAAS,GAAG,IAAI,GAAG,iBAAiB;QACrE,IAAI,MAAM,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,QAAQ,iBAAiB,IAAI,cAAc;QACjF,iFAAiF;QACjF,IAAI,SAAS,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG;QACzD,IAAI,QAAQ,MAAM,SAAS,KAAK,SAAS,OAAO,SAAS;QACzD,IAAI,MAAM,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,QAAQ,QAAQ,IAAI,cAAc;QACxE,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,QAAQ,cAAc,MAAM;QAC9E,UAAU,CAAC,EAAE,CAAC,UAAU,CAAC,GAAG,OAAO,QAAQ,cAAc,MAAM;QAC/D,QAAQ;QACR,KAAK,SAAS,CAAC,YAAY;IAC7B;IACA,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI;IACvB,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,IAAI;AACzB;AACA,uDAAuD;AACvD,SAAS,iBAAiB,WAAW,EAAE,UAAU,EAAE,QAAQ,EAAE,cAAc,EAAE,eAAe;IAC1F,kDAAkD;IAClD,qDAAqD;IACrD,IAAI,WAAW,CAAC,cAAc,CAAC,CAAC,EAAE,IAAI;IACtC,IAAI,cAAc;QAAC;QAAgB;KAAgB;IACnD,IAAI,CAAC,YAAY,aAAa,UAAU;QACtC,OAAO;IACT;IACA,IAAI;IACJ,IAAI,WAAW,iBAAiB;IAChC,IAAI,OAAO,WAAW,YAAY,MAAM,CAAC,eAAe;IACxD,MAAO,SAAS,SAAS,UAAU,CAAE;QACnC,qBAAqB;QACrB,IAAI,MAAM;QACV,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAK,IAAI,IAAI,GAAG,MAAM,SAAS,MAAM,EAAE,IAAI,KAAK,IAAK;YACnD,OAAO,QAAQ,CAAC,EAAE,CAAC,QAAQ;QAC7B;QACA,IAAI,gBAAgB,SAAS,QAAQ;QACrC,IAAI,kBAAkB,GAAG;YACvB,OAAO;QACT;QACA,QAAQ,MAAM;QACd,iDAAiD;QACjD,IAAI,cAAc,OAAO,QAAQ;QACjC,IAAI,cAAc,YAAY,GAAG,CAAC;QAClC,IAAI,cAAc,KAAK,GAAG,CAAC,aAAa,oBAAoB;QAC5D,QAAQ,IAAI,cAAc,cAAc,CAAC,IAAI,cAAc,WAAW,IAAI,KAAK,GAAG,CAAC,MAAM;QACzF,OAAO,gJAAA,CAAA,mBAAgB,IAAI,CAAC,OAAO,gJAAA,CAAA,mBAAgB;QACnD,WAAW;IACb;IACA,OAAO,YAAY,CAAC,OAAO,QAAQ;IACnC,IAAI,QAAQ,KAAK,GAAG,CAAC,OAAO,UAAU;IACtC,OAAO;QAAC,iBAAiB;QAAO,kBAAkB;KAAM;AAC1D;AACA,iDAAiD;AACjD,SAAS,sBAAsB,UAAU,EAAE,QAAQ,EAAE,UAAU;IAC7D,IAAI,UAAU;QACZ,OAAO;YACL,GAAG,SAAS,CAAC;YACb,GAAG,SAAS,CAAC;QACf;IACF;IACA,IAAI,kBAAkB;QACpB,GAAG;QACH,GAAG;IACL;IACA,IAAI,CAAC,YAAY;QACf,OAAO;IACT;IACA,oDAAoD;IACpD,2CAA2C;IAC3C,gDAAgD;IAChD,IAAI,aAAa,WAAW,IAAI;IAChC,IAAI,SAAS,WAAW,SAAS;IACjC,IAAI,CAAC,QAAQ;QACX,OAAO;IACT;IACA,2CAA2C;IAC3C,IAAI,eAAe;QAAC,OAAO,KAAK,GAAG;QAAG,OAAO,MAAM,GAAG;KAAE;IACxD,IAAI,OAAO;IACX,MAAO,KAAM;QACX,IAAI,aAAa,KAAK,SAAS;QAC/B,YAAY,CAAC,EAAE,IAAI,WAAW,CAAC;QAC/B,YAAY,CAAC,EAAE,IAAI,WAAW,CAAC;QAC/B,OAAO,KAAK,UAAU;IACxB;IACA,OAAO;QACL,GAAG,WAAW,KAAK,GAAG,IAAI,YAAY,CAAC,EAAE;QACzC,GAAG,WAAW,MAAM,GAAG,IAAI,YAAY,CAAC,EAAE;IAC5C;AACF;AACA,oEAAoE;AACpE,kFAAkF;AAClF,SAAS,SAAS,IAAI,EAAE,QAAQ,EAAE,aAAa,EAAE,QAAQ,EAAE,KAAK;IAC9D,IAAI,aAAa,KAAK,SAAS;IAC/B,IAAI,sBAAsB,aAAa,CAAC,MAAM;IAC9C,IAAI,kBAAkB,uBAAuB,wBAAwB;IACrE,IAAI,uBAAuB,CAAC,mBAAmB,UAAU,cAAc,MAAM,IAAI,SAAS,UAAU;QAClG;IACF;IACA,KAAK,SAAS,CAAC;QACb,oDAAoD;QACpD,UAAU;QACV,mEAAmE;QACnE,iEAAiE;QACjE,WAAW,CAAC,mBAAmB,CAAC,SAAS,SAAS,CAAC;QACnD,iBAAiB;IACnB,GAAG;IACH,iCAAiC;IACjC,IAAI,gBAAgB,IAAI,sJAAA,CAAA,UAAY,CAAC,SAAS,CAAC,GAAG,WAAW,CAAC,EAAE,SAAS,CAAC,GAAG,WAAW,CAAC,EAAE,SAAS,KAAK,EAAE,SAAS,MAAM;IAC1H,KAAK,KAAK,YAAY,IAAI,EAAE,EAAE,SAAU,KAAK;QAC3C,SAAS,OAAO,eAAe,eAAe,UAAU,QAAQ;IAClE;AACF;AACA,SAAS,oBAAoB,KAAK;IAChC,OAAO,MAAM,GAAG,CAAC,yBAAyB,MAAM,GAAG,CAAC,2BAA2B;AACjF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2358, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/chart/treemap/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { installTreemapAction } from './treemapAction.js';\nimport TreemapSeriesModel from './TreemapSeries.js';\nimport TreemapView from './TreemapView.js';\nimport treemapVisual from './treemapVisual.js';\nimport treemapLayout from './treemapLayout.js';\nexport function install(registers) {\n  registers.registerSeriesModel(TreemapSeriesModel);\n  registers.registerChartView(TreemapView);\n  registers.registerVisual(treemapVisual);\n  registers.registerLayout(treemapLayout);\n  installTreemapAction(registers);\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;;;;;;AACO,SAAS,QAAQ,SAAS;IAC/B,UAAU,mBAAmB,CAAC,mKAAA,CAAA,UAAkB;IAChD,UAAU,iBAAiB,CAAC,iKAAA,CAAA,UAAW;IACvC,UAAU,cAAc,CAAC,mKAAA,CAAA,UAAa;IACtC,UAAU,cAAc,CAAC,mKAAA,CAAA,UAAa;IACtC,CAAA,GAAA,mKAAA,CAAA,uBAAoB,AAAD,EAAE;AACvB", "ignoreList": [0], "debugId": null}}]}