'use client'

import { useEffect, useState } from 'react'
import ReactECharts from 'echarts-for-react'
import { TrendingUp, DollarSign, Package, Users, Calendar } from 'lucide-react'

import type { DashboardStats } from '@/types'

interface APIGraphingProps {
  stats: DashboardStats
}

export default function APIGraphing({ stats }: APIGraphingProps) {
  const [salesData, setSalesData] = useState<number[]>([])
  const [debtData, setDebtData] = useState<number[]>([])

  useEffect(() => {
    // Simulate API data for charts
    const generateSalesData = () => {
      const data = []
      for (let i = 0; i < 12; i++) {
        data.push(Math.floor(Math.random() * 50000) + 20000)
      }
      setSalesData(data)
    }

    const generateDebtData = () => {
      const data = []
      for (let i = 0; i < 7; i++) {
        data.push(Math.floor(Math.random() * 15000) + 5000)
      }
      setDebtData(data)
    }

    generateSalesData()
    generateDebtData()
  }, [])

  // Monthly Sales Chart
  const salesChartOption = {
    title: {
      text: 'Monthly Sales Revenue',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        return `${params[0].name}<br/>Revenue: ₱${params[0].value.toLocaleString()}`
      },
    },
    xAxis: {
      type: 'category',
      data: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '₱{value}',
      },
    },
    series: [
      {
        data: salesData,
        type: 'line',
        smooth: true,
        lineStyle: {
          color: '#22c55e',
          width: 3,
        },
        itemStyle: {
          color: '#22c55e',
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: 'rgba(34, 197, 94, 0.3)' },
              { offset: 1, color: 'rgba(34, 197, 94, 0.05)' },
            ],
          },
        },
      },
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
  }

  // Customer Debt Chart
  const debtChartOption = {
    title: {
      text: 'Weekly Customer Debt Trends',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any) => {
        return `${params[0].name}<br/>Total Debt: ₱${params[0].value.toLocaleString()}`
      },
    },
    xAxis: {
      type: 'category',
      data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '₱{value}',
      },
    },
    series: [
      {
        data: debtData,
        type: 'bar',
        itemStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              { offset: 0, color: '#facc15' },
              { offset: 1, color: '#eab308' },
            ],
          },
        },
        emphasis: {
          itemStyle: {
            color: '#f59e0b',
          },
        },
      },
    ],
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true,
    },
  }

  // Product Categories Pie Chart
  const categoryChartOption = {
    title: {
      text: 'Product Categories Distribution',
      textStyle: {
        fontSize: 16,
        fontWeight: 'bold',
      },
    },
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)',
    },
    series: [
      {
        name: 'Categories',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2,
        },
        label: {
          show: false,
          position: 'center',
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold',
          },
        },
        labelLine: {
          show: false,
        },
        data: [
          { value: 35, name: 'Snacks', itemStyle: { color: '#22c55e' } },
          { value: 25, name: 'Beverages', itemStyle: { color: '#facc15' } },
          { value: 20, name: 'Canned Goods', itemStyle: { color: '#3b82f6' } },
          { value: 12, name: 'Personal Care', itemStyle: { color: '#f59e0b' } },
          { value: 8, name: 'Others', itemStyle: { color: '#8b5cf6' } },
        ],
      },
    ],
  }

  const kpiCards = [
    {
      title: 'Total Revenue',
      value: '₱' + salesData.reduce((a, b) => a + b, 0).toLocaleString(),
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+12.5%',
      changeColor: 'text-green-600',
    },
    {
      title: 'Products Listed',
      value: stats.totalProducts.toString(),
      icon: Package,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+5.2%',
      changeColor: 'text-blue-600',
    },
    {
      title: 'Active Customers',
      value: stats.totalDebts.toString(),
      icon: Users,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+8.1%',
      changeColor: 'text-purple-600',
    },
    {
      title: 'Outstanding Debt',
      value: '₱' + stats.totalDebtAmount.toLocaleString(),
      icon: TrendingUp,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      change: '-3.2%',
      changeColor: 'text-red-600',
    },
  ]

  return (
    <div className="space-y-6">
      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {kpiCards.map((kpi, index) => (
          <div key={index} className="card p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {kpi.title}
                </p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white mt-1">
                  {kpi.value}
                </p>
                <p className={`text-sm mt-1 ${kpi.changeColor}`}>
                  {kpi.change} from last month
                </p>
              </div>
              <div className={`p-3 rounded-lg ${kpi.bgColor}`}>
                <kpi.icon className={`h-6 w-6 ${kpi.color}`} />
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Sales Chart */}
        <div className="card p-6">
          <ReactECharts option={salesChartOption} style={{ height: '400px' }} />
        </div>

        {/* Debt Chart */}
        <div className="card p-6">
          <ReactECharts option={debtChartOption} style={{ height: '400px' }} />
        </div>
      </div>

      {/* Category Distribution */}
      <div className="card p-6">
        <ReactECharts option={categoryChartOption} style={{ height: '400px' }} />
      </div>

      {/* Real-time Data Indicator */}
      <div className="card p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
              Real-time Data Updates
            </span>
          </div>
          <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400">
            <Calendar className="h-4 w-4" />
            <span>Last updated: {new Date().toLocaleTimeString()}</span>
          </div>
        </div>
      </div>
    </div>
  )
}
