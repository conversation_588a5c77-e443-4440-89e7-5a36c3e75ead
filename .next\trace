[{"name": "hot-reloader", "duration": 297, "timestamp": 459460705723, "id": 3, "tags": {"version": "15.3.5"}, "startTime": 1752231701421, "traceId": "b1e4b567caacd325"}, {"name": "setup-dev-bundler", "duration": 1687764, "timestamp": 459460140446, "id": 2, "parentId": 1, "tags": {}, "startTime": 1752231700855, "traceId": "b1e4b567caacd325"}, {"name": "run-instrumentation-hook", "duration": 359, "timestamp": 459462004085, "id": 4, "parentId": 1, "tags": {}, "startTime": 1752231702719, "traceId": "b1e4b567caacd325"}, {"name": "start-dev-server", "duration": 4116816, "timestamp": 459458027787, "id": 1, "tags": {"cpus": "4", "platform": "win32", "memory.freeMem": "396500992", "memory.totalMem": "4138561536", "memory.heapSizeLimit": "2119172096", "memory.rss": "172666880", "memory.heapTotal": "97988608", "memory.heapUsed": "73571736"}, "startTime": 1752231698743, "traceId": "b1e4b567caacd325"}, {"name": "compile-path", "duration": 11705762, "timestamp": 459499044053, "id": 7, "tags": {"trigger": "/"}, "startTime": 1752231739759, "traceId": "b1e4b567caacd325"}, {"name": "ensure-page", "duration": 11717169, "timestamp": 459499042093, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1752231739757, "traceId": "b1e4b567caacd325"}]