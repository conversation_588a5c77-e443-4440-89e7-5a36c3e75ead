{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axisPointer/modelHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport Model from '../../model/Model.js';\nimport { each, curry, clone, defaults, isArray, indexOf } from 'zrender/lib/core/util.js';\n// Build axisPointerModel, mergin tooltip.axisPointer model for each axis.\n// allAxesInfo should be updated when setOption performed.\nexport function collect(ecModel, api) {\n  var result = {\n    /**\r\n     * key: makeKey(axis.model)\r\n     * value: {\r\n     *      axis,\r\n     *      coordSys,\r\n     *      axisPointerModel,\r\n     *      triggerTooltip,\r\n     *      triggerEmphasis,\r\n     *      involveSeries,\r\n     *      snap,\r\n     *      seriesModels,\r\n     *      seriesDataCount\r\n     * }\r\n     */\n    axesInfo: {},\n    seriesInvolved: false,\n    /**\r\n     * key: makeKey(coordSys.model)\r\n     * value: Object: key makeKey(axis.model), value: axisInfo\r\n     */\n    coordSysAxesInfo: {},\n    coordSysMap: {}\n  };\n  collectAxesInfo(result, ecModel, api);\n  // Check seriesInvolved for performance, in case too many series in some chart.\n  result.seriesInvolved && collectSeriesInfo(result, ecModel);\n  return result;\n}\nfunction collectAxesInfo(result, ecModel, api) {\n  var globalTooltipModel = ecModel.getComponent('tooltip');\n  var globalAxisPointerModel = ecModel.getComponent('axisPointer');\n  // links can only be set on global.\n  var linksOption = globalAxisPointerModel.get('link', true) || [];\n  var linkGroups = [];\n  // Collect axes info.\n  each(api.getCoordinateSystems(), function (coordSys) {\n    // Some coordinate system do not support axes, like geo.\n    if (!coordSys.axisPointerEnabled) {\n      return;\n    }\n    var coordSysKey = makeKey(coordSys.model);\n    var axesInfoInCoordSys = result.coordSysAxesInfo[coordSysKey] = {};\n    result.coordSysMap[coordSysKey] = coordSys;\n    // Set tooltip (like 'cross') is a convenient way to show axisPointer\n    // for user. So we enable setting tooltip on coordSys model.\n    var coordSysModel = coordSys.model;\n    var baseTooltipModel = coordSysModel.getModel('tooltip', globalTooltipModel);\n    each(coordSys.getAxes(), curry(saveTooltipAxisInfo, false, null));\n    // If axis tooltip used, choose tooltip axis for each coordSys.\n    // Notice this case: coordSys is `grid` but not `cartesian2D` here.\n    if (coordSys.getTooltipAxes && globalTooltipModel\n    // If tooltip.showContent is set as false, tooltip will not\n    // show but axisPointer will show as normal.\n    && baseTooltipModel.get('show')) {\n      // Compatible with previous logic. But series.tooltip.trigger: 'axis'\n      // or series.data[n].tooltip.trigger: 'axis' are not support any more.\n      var triggerAxis = baseTooltipModel.get('trigger') === 'axis';\n      var cross = baseTooltipModel.get(['axisPointer', 'type']) === 'cross';\n      var tooltipAxes = coordSys.getTooltipAxes(baseTooltipModel.get(['axisPointer', 'axis']));\n      if (triggerAxis || cross) {\n        each(tooltipAxes.baseAxes, curry(saveTooltipAxisInfo, cross ? 'cross' : true, triggerAxis));\n      }\n      if (cross) {\n        each(tooltipAxes.otherAxes, curry(saveTooltipAxisInfo, 'cross', false));\n      }\n    }\n    // fromTooltip: true | false | 'cross'\n    // triggerTooltip: true | false | null\n    function saveTooltipAxisInfo(fromTooltip, triggerTooltip, axis) {\n      var axisPointerModel = axis.model.getModel('axisPointer', globalAxisPointerModel);\n      var axisPointerShow = axisPointerModel.get('show');\n      if (!axisPointerShow || axisPointerShow === 'auto' && !fromTooltip && !isHandleTrigger(axisPointerModel)) {\n        return;\n      }\n      if (triggerTooltip == null) {\n        triggerTooltip = axisPointerModel.get('triggerTooltip');\n      }\n      axisPointerModel = fromTooltip ? makeAxisPointerModel(axis, baseTooltipModel, globalAxisPointerModel, ecModel, fromTooltip, triggerTooltip) : axisPointerModel;\n      var snap = axisPointerModel.get('snap');\n      var triggerEmphasis = axisPointerModel.get('triggerEmphasis');\n      var axisKey = makeKey(axis.model);\n      var involveSeries = triggerTooltip || snap || axis.type === 'category';\n      // If result.axesInfo[key] exist, override it (tooltip has higher priority).\n      var axisInfo = result.axesInfo[axisKey] = {\n        key: axisKey,\n        axis: axis,\n        coordSys: coordSys,\n        axisPointerModel: axisPointerModel,\n        triggerTooltip: triggerTooltip,\n        triggerEmphasis: triggerEmphasis,\n        involveSeries: involveSeries,\n        snap: snap,\n        useHandle: isHandleTrigger(axisPointerModel),\n        seriesModels: [],\n        linkGroup: null\n      };\n      axesInfoInCoordSys[axisKey] = axisInfo;\n      result.seriesInvolved = result.seriesInvolved || involveSeries;\n      var groupIndex = getLinkGroupIndex(linksOption, axis);\n      if (groupIndex != null) {\n        var linkGroup = linkGroups[groupIndex] || (linkGroups[groupIndex] = {\n          axesInfo: {}\n        });\n        linkGroup.axesInfo[axisKey] = axisInfo;\n        linkGroup.mapper = linksOption[groupIndex].mapper;\n        axisInfo.linkGroup = linkGroup;\n      }\n    }\n  });\n}\nfunction makeAxisPointerModel(axis, baseTooltipModel, globalAxisPointerModel, ecModel, fromTooltip, triggerTooltip) {\n  var tooltipAxisPointerModel = baseTooltipModel.getModel('axisPointer');\n  var fields = ['type', 'snap', 'lineStyle', 'shadowStyle', 'label', 'animation', 'animationDurationUpdate', 'animationEasingUpdate', 'z'];\n  var volatileOption = {};\n  each(fields, function (field) {\n    volatileOption[field] = clone(tooltipAxisPointerModel.get(field));\n  });\n  // category axis do not auto snap, otherwise some tick that do not\n  // has value can not be hovered. value/time/log axis default snap if\n  // triggered from tooltip and trigger tooltip.\n  volatileOption.snap = axis.type !== 'category' && !!triggerTooltip;\n  // Compatible with previous behavior, tooltip axis does not show label by default.\n  // Only these properties can be overridden from tooltip to axisPointer.\n  if (tooltipAxisPointerModel.get('type') === 'cross') {\n    volatileOption.type = 'line';\n  }\n  var labelOption = volatileOption.label || (volatileOption.label = {});\n  // Follow the convention, do not show label when triggered by tooltip by default.\n  labelOption.show == null && (labelOption.show = false);\n  if (fromTooltip === 'cross') {\n    // When 'cross', both axes show labels.\n    var tooltipAxisPointerLabelShow = tooltipAxisPointerModel.get(['label', 'show']);\n    labelOption.show = tooltipAxisPointerLabelShow != null ? tooltipAxisPointerLabelShow : true;\n    // If triggerTooltip, this is a base axis, which should better not use cross style\n    // (cross style is dashed by default)\n    if (!triggerTooltip) {\n      var crossStyle = volatileOption.lineStyle = tooltipAxisPointerModel.get('crossStyle');\n      crossStyle && defaults(labelOption, crossStyle.textStyle);\n    }\n  }\n  return axis.model.getModel('axisPointer', new Model(volatileOption, globalAxisPointerModel, ecModel));\n}\nfunction collectSeriesInfo(result, ecModel) {\n  // Prepare data for axis trigger\n  ecModel.eachSeries(function (seriesModel) {\n    // Notice this case: this coordSys is `cartesian2D` but not `grid`.\n    var coordSys = seriesModel.coordinateSystem;\n    var seriesTooltipTrigger = seriesModel.get(['tooltip', 'trigger'], true);\n    var seriesTooltipShow = seriesModel.get(['tooltip', 'show'], true);\n    if (!coordSys || seriesTooltipTrigger === 'none' || seriesTooltipTrigger === false || seriesTooltipTrigger === 'item' || seriesTooltipShow === false || seriesModel.get(['axisPointer', 'show'], true) === false) {\n      return;\n    }\n    each(result.coordSysAxesInfo[makeKey(coordSys.model)], function (axisInfo) {\n      var axis = axisInfo.axis;\n      if (coordSys.getAxis(axis.dim) === axis) {\n        axisInfo.seriesModels.push(seriesModel);\n        axisInfo.seriesDataCount == null && (axisInfo.seriesDataCount = 0);\n        axisInfo.seriesDataCount += seriesModel.getData().count();\n      }\n    });\n  });\n}\n/**\r\n * For example:\r\n * {\r\n *     axisPointer: {\r\n *         links: [{\r\n *             xAxisIndex: [2, 4],\r\n *             yAxisIndex: 'all'\r\n *         }, {\r\n *             xAxisId: ['a5', 'a7'],\r\n *             xAxisName: 'xxx'\r\n *         }]\r\n *     }\r\n * }\r\n */\nfunction getLinkGroupIndex(linksOption, axis) {\n  var axisModel = axis.model;\n  var dim = axis.dim;\n  for (var i = 0; i < linksOption.length; i++) {\n    var linkOption = linksOption[i] || {};\n    if (checkPropInLink(linkOption[dim + 'AxisId'], axisModel.id) || checkPropInLink(linkOption[dim + 'AxisIndex'], axisModel.componentIndex) || checkPropInLink(linkOption[dim + 'AxisName'], axisModel.name)) {\n      return i;\n    }\n  }\n}\nfunction checkPropInLink(linkPropValue, axisPropValue) {\n  return linkPropValue === 'all' || isArray(linkPropValue) && indexOf(linkPropValue, axisPropValue) >= 0 || linkPropValue === axisPropValue;\n}\nexport function fixValue(axisModel) {\n  var axisInfo = getAxisInfo(axisModel);\n  if (!axisInfo) {\n    return;\n  }\n  var axisPointerModel = axisInfo.axisPointerModel;\n  var scale = axisInfo.axis.scale;\n  var option = axisPointerModel.option;\n  var status = axisPointerModel.get('status');\n  var value = axisPointerModel.get('value');\n  // Parse init value for category and time axis.\n  if (value != null) {\n    value = scale.parse(value);\n  }\n  var useHandle = isHandleTrigger(axisPointerModel);\n  // If `handle` used, `axisPointer` will always be displayed, so value\n  // and status should be initialized.\n  if (status == null) {\n    option.status = useHandle ? 'show' : 'hide';\n  }\n  var extent = scale.getExtent().slice();\n  extent[0] > extent[1] && extent.reverse();\n  if (\n  // Pick a value on axis when initializing.\n  value == null\n  // If both `handle` and `dataZoom` are used, value may be out of axis extent,\n  // where we should re-pick a value to keep `handle` displaying normally.\n  || value > extent[1]) {\n    // Make handle displayed on the end of the axis when init, which looks better.\n    value = extent[1];\n  }\n  if (value < extent[0]) {\n    value = extent[0];\n  }\n  option.value = value;\n  if (useHandle) {\n    option.status = axisInfo.axis.scale.isBlank() ? 'hide' : 'show';\n  }\n}\nexport function getAxisInfo(axisModel) {\n  var coordSysAxesInfo = (axisModel.ecModel.getComponent('axisPointer') || {}).coordSysAxesInfo;\n  return coordSysAxesInfo && coordSysAxesInfo.axesInfo[makeKey(axisModel)];\n}\nexport function getAxisPointerModel(axisModel) {\n  var axisInfo = getAxisInfo(axisModel);\n  return axisInfo && axisInfo.axisPointerModel;\n}\nfunction isHandleTrigger(axisPointerModel) {\n  return !!axisPointerModel.get(['handle', 'show']);\n}\n/**\r\n * @param {module:echarts/model/Model} model\r\n * @return {string} unique key\r\n */\nexport function makeKey(model) {\n  return model.type + '||' + model.id;\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;;;;AACA;AACA;;;AAGO,SAAS,QAAQ,OAAO,EAAE,GAAG;IAClC,IAAI,SAAS;QACX;;;;;;;;;;;;;KAaC,GACD,UAAU,CAAC;QACX,gBAAgB;QAChB;;;KAGC,GACD,kBAAkB,CAAC;QACnB,aAAa,CAAC;IAChB;IACA,gBAAgB,QAAQ,SAAS;IACjC,+EAA+E;IAC/E,OAAO,cAAc,IAAI,kBAAkB,QAAQ;IACnD,OAAO;AACT;AACA,SAAS,gBAAgB,MAAM,EAAE,OAAO,EAAE,GAAG;IAC3C,IAAI,qBAAqB,QAAQ,YAAY,CAAC;IAC9C,IAAI,yBAAyB,QAAQ,YAAY,CAAC;IAClD,mCAAmC;IACnC,IAAI,cAAc,uBAAuB,GAAG,CAAC,QAAQ,SAAS,EAAE;IAChE,IAAI,aAAa,EAAE;IACnB,qBAAqB;IACrB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,IAAI,oBAAoB,IAAI,SAAU,QAAQ;QACjD,wDAAwD;QACxD,IAAI,CAAC,SAAS,kBAAkB,EAAE;YAChC;QACF;QACA,IAAI,cAAc,QAAQ,SAAS,KAAK;QACxC,IAAI,qBAAqB,OAAO,gBAAgB,CAAC,YAAY,GAAG,CAAC;QACjE,OAAO,WAAW,CAAC,YAAY,GAAG;QAClC,qEAAqE;QACrE,4DAA4D;QAC5D,IAAI,gBAAgB,SAAS,KAAK;QAClC,IAAI,mBAAmB,cAAc,QAAQ,CAAC,WAAW;QACzD,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,SAAS,OAAO,IAAI,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE,qBAAqB,OAAO;QAC3D,+DAA+D;QAC/D,mEAAmE;QACnE,IAAI,SAAS,cAAc,IAAI,sBAG5B,iBAAiB,GAAG,CAAC,SAAS;YAC/B,qEAAqE;YACrE,sEAAsE;YACtE,IAAI,cAAc,iBAAiB,GAAG,CAAC,eAAe;YACtD,IAAI,QAAQ,iBAAiB,GAAG,CAAC;gBAAC;gBAAe;aAAO,MAAM;YAC9D,IAAI,cAAc,SAAS,cAAc,CAAC,iBAAiB,GAAG,CAAC;gBAAC;gBAAe;aAAO;YACtF,IAAI,eAAe,OAAO;gBACxB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,YAAY,QAAQ,EAAE,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE,qBAAqB,QAAQ,UAAU,MAAM;YAChF;YACA,IAAI,OAAO;gBACT,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,YAAY,SAAS,EAAE,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE,qBAAqB,SAAS;YAClE;QACF;QACA,sCAAsC;QACtC,sCAAsC;QACtC,SAAS,oBAAoB,WAAW,EAAE,cAAc,EAAE,IAAI;YAC5D,IAAI,mBAAmB,KAAK,KAAK,CAAC,QAAQ,CAAC,eAAe;YAC1D,IAAI,kBAAkB,iBAAiB,GAAG,CAAC;YAC3C,IAAI,CAAC,mBAAmB,oBAAoB,UAAU,CAAC,eAAe,CAAC,gBAAgB,mBAAmB;gBACxG;YACF;YACA,IAAI,kBAAkB,MAAM;gBAC1B,iBAAiB,iBAAiB,GAAG,CAAC;YACxC;YACA,mBAAmB,cAAc,qBAAqB,MAAM,kBAAkB,wBAAwB,SAAS,aAAa,kBAAkB;YAC9I,IAAI,OAAO,iBAAiB,GAAG,CAAC;YAChC,IAAI,kBAAkB,iBAAiB,GAAG,CAAC;YAC3C,IAAI,UAAU,QAAQ,KAAK,KAAK;YAChC,IAAI,gBAAgB,kBAAkB,QAAQ,KAAK,IAAI,KAAK;YAC5D,4EAA4E;YAC5E,IAAI,WAAW,OAAO,QAAQ,CAAC,QAAQ,GAAG;gBACxC,KAAK;gBACL,MAAM;gBACN,UAAU;gBACV,kBAAkB;gBAClB,gBAAgB;gBAChB,iBAAiB;gBACjB,eAAe;gBACf,MAAM;gBACN,WAAW,gBAAgB;gBAC3B,cAAc,EAAE;gBAChB,WAAW;YACb;YACA,kBAAkB,CAAC,QAAQ,GAAG;YAC9B,OAAO,cAAc,GAAG,OAAO,cAAc,IAAI;YACjD,IAAI,aAAa,kBAAkB,aAAa;YAChD,IAAI,cAAc,MAAM;gBACtB,IAAI,YAAY,UAAU,CAAC,WAAW,IAAI,CAAC,UAAU,CAAC,WAAW,GAAG;oBAClE,UAAU,CAAC;gBACb,CAAC;gBACD,UAAU,QAAQ,CAAC,QAAQ,GAAG;gBAC9B,UAAU,MAAM,GAAG,WAAW,CAAC,WAAW,CAAC,MAAM;gBACjD,SAAS,SAAS,GAAG;YACvB;QACF;IACF;AACF;AACA,SAAS,qBAAqB,IAAI,EAAE,gBAAgB,EAAE,sBAAsB,EAAE,OAAO,EAAE,WAAW,EAAE,cAAc;IAChH,IAAI,0BAA0B,iBAAiB,QAAQ,CAAC;IACxD,IAAI,SAAS;QAAC;QAAQ;QAAQ;QAAa;QAAe;QAAS;QAAa;QAA2B;QAAyB;KAAI;IACxI,IAAI,iBAAiB,CAAC;IACtB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,SAAU,KAAK;QAC1B,cAAc,CAAC,MAAM,GAAG,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE,wBAAwB,GAAG,CAAC;IAC5D;IACA,kEAAkE;IAClE,oEAAoE;IACpE,8CAA8C;IAC9C,eAAe,IAAI,GAAG,KAAK,IAAI,KAAK,cAAc,CAAC,CAAC;IACpD,kFAAkF;IAClF,uEAAuE;IACvE,IAAI,wBAAwB,GAAG,CAAC,YAAY,SAAS;QACnD,eAAe,IAAI,GAAG;IACxB;IACA,IAAI,cAAc,eAAe,KAAK,IAAI,CAAC,eAAe,KAAK,GAAG,CAAC,CAAC;IACpE,iFAAiF;IACjF,YAAY,IAAI,IAAI,QAAQ,CAAC,YAAY,IAAI,GAAG,KAAK;IACrD,IAAI,gBAAgB,SAAS;QAC3B,uCAAuC;QACvC,IAAI,8BAA8B,wBAAwB,GAAG,CAAC;YAAC;YAAS;SAAO;QAC/E,YAAY,IAAI,GAAG,+BAA+B,OAAO,8BAA8B;QACvF,kFAAkF;QAClF,qCAAqC;QACrC,IAAI,CAAC,gBAAgB;YACnB,IAAI,aAAa,eAAe,SAAS,GAAG,wBAAwB,GAAG,CAAC;YACxE,cAAc,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,aAAa,WAAW,SAAS;QAC1D;IACF;IACA,OAAO,KAAK,KAAK,CAAC,QAAQ,CAAC,eAAe,IAAI,gJAAA,CAAA,UAAK,CAAC,gBAAgB,wBAAwB;AAC9F;AACA,SAAS,kBAAkB,MAAM,EAAE,OAAO;IACxC,gCAAgC;IAChC,QAAQ,UAAU,CAAC,SAAU,WAAW;QACtC,mEAAmE;QACnE,IAAI,WAAW,YAAY,gBAAgB;QAC3C,IAAI,uBAAuB,YAAY,GAAG,CAAC;YAAC;YAAW;SAAU,EAAE;QACnE,IAAI,oBAAoB,YAAY,GAAG,CAAC;YAAC;YAAW;SAAO,EAAE;QAC7D,IAAI,CAAC,YAAY,yBAAyB,UAAU,yBAAyB,SAAS,yBAAyB,UAAU,sBAAsB,SAAS,YAAY,GAAG,CAAC;YAAC;YAAe;SAAO,EAAE,UAAU,OAAO;YAChN;QACF;QACA,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,OAAO,gBAAgB,CAAC,QAAQ,SAAS,KAAK,EAAE,EAAE,SAAU,QAAQ;YACvE,IAAI,OAAO,SAAS,IAAI;YACxB,IAAI,SAAS,OAAO,CAAC,KAAK,GAAG,MAAM,MAAM;gBACvC,SAAS,YAAY,CAAC,IAAI,CAAC;gBAC3B,SAAS,eAAe,IAAI,QAAQ,CAAC,SAAS,eAAe,GAAG,CAAC;gBACjE,SAAS,eAAe,IAAI,YAAY,OAAO,GAAG,KAAK;YACzD;QACF;IACF;AACF;AACA;;;;;;;;;;;;;CAaC,GACD,SAAS,kBAAkB,WAAW,EAAE,IAAI;IAC1C,IAAI,YAAY,KAAK,KAAK;IAC1B,IAAI,MAAM,KAAK,GAAG;IAClB,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;QAC3C,IAAI,aAAa,WAAW,CAAC,EAAE,IAAI,CAAC;QACpC,IAAI,gBAAgB,UAAU,CAAC,MAAM,SAAS,EAAE,UAAU,EAAE,KAAK,gBAAgB,UAAU,CAAC,MAAM,YAAY,EAAE,UAAU,cAAc,KAAK,gBAAgB,UAAU,CAAC,MAAM,WAAW,EAAE,UAAU,IAAI,GAAG;YAC1M,OAAO;QACT;IACF;AACF;AACA,SAAS,gBAAgB,aAAa,EAAE,aAAa;IACnD,OAAO,kBAAkB,SAAS,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,kBAAkB,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,eAAe,kBAAkB,KAAK,kBAAkB;AAC9H;AACO,SAAS,SAAS,SAAS;IAChC,IAAI,WAAW,YAAY;IAC3B,IAAI,CAAC,UAAU;QACb;IACF;IACA,IAAI,mBAAmB,SAAS,gBAAgB;IAChD,IAAI,QAAQ,SAAS,IAAI,CAAC,KAAK;IAC/B,IAAI,SAAS,iBAAiB,MAAM;IACpC,IAAI,SAAS,iBAAiB,GAAG,CAAC;IAClC,IAAI,QAAQ,iBAAiB,GAAG,CAAC;IACjC,+CAA+C;IAC/C,IAAI,SAAS,MAAM;QACjB,QAAQ,MAAM,KAAK,CAAC;IACtB;IACA,IAAI,YAAY,gBAAgB;IAChC,qEAAqE;IACrE,oCAAoC;IACpC,IAAI,UAAU,MAAM;QAClB,OAAO,MAAM,GAAG,YAAY,SAAS;IACvC;IACA,IAAI,SAAS,MAAM,SAAS,GAAG,KAAK;IACpC,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,IAAI,OAAO,OAAO;IACvC,IACA,0CAA0C;IAC1C,SAAS,QAGN,QAAQ,MAAM,CAAC,EAAE,EAAE;QACpB,8EAA8E;QAC9E,QAAQ,MAAM,CAAC,EAAE;IACnB;IACA,IAAI,QAAQ,MAAM,CAAC,EAAE,EAAE;QACrB,QAAQ,MAAM,CAAC,EAAE;IACnB;IACA,OAAO,KAAK,GAAG;IACf,IAAI,WAAW;QACb,OAAO,MAAM,GAAG,SAAS,IAAI,CAAC,KAAK,CAAC,OAAO,KAAK,SAAS;IAC3D;AACF;AACO,SAAS,YAAY,SAAS;IACnC,IAAI,mBAAmB,CAAC,UAAU,OAAO,CAAC,YAAY,CAAC,kBAAkB,CAAC,CAAC,EAAE,gBAAgB;IAC7F,OAAO,oBAAoB,iBAAiB,QAAQ,CAAC,QAAQ,WAAW;AAC1E;AACO,SAAS,oBAAoB,SAAS;IAC3C,IAAI,WAAW,YAAY;IAC3B,OAAO,YAAY,SAAS,gBAAgB;AAC9C;AACA,SAAS,gBAAgB,gBAAgB;IACvC,OAAO,CAAC,CAAC,iBAAiB,GAAG,CAAC;QAAC;QAAU;KAAO;AAClD;AAKO,SAAS,QAAQ,KAAK;IAC3B,OAAO,MAAM,IAAI,GAAG,OAAO,MAAM,EAAE;AACrC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 325, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axisPointer/BaseAxisPointer.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as axisPointerModelHelper from './modelHelper.js';\nimport * as eventTool from 'zrender/lib/core/event.js';\nimport * as throttleUtil from '../../util/throttle.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nvar clone = zrUtil.clone;\nvar bind = zrUtil.bind;\n/**\r\n * Base axis pointer class in 2D.\r\n */\nvar BaseAxisPointer = /** @class */function () {\n  function BaseAxisPointer() {\n    this._dragging = false;\n    /**\r\n     * In px, arbitrary value. Do not set too small,\r\n     * no animation is ok for most cases.\r\n     */\n    this.animationThreshold = 15;\n  }\n  /**\r\n   * @implement\r\n   */\n  BaseAxisPointer.prototype.render = function (axisModel, axisPointerModel, api, forceRender) {\n    var value = axisPointerModel.get('value');\n    var status = axisPointerModel.get('status');\n    // Bind them to `this`, not in closure, otherwise they will not\n    // be replaced when user calling setOption in not merge mode.\n    this._axisModel = axisModel;\n    this._axisPointerModel = axisPointerModel;\n    this._api = api;\n    // Optimize: `render` will be called repeatedly during mouse move.\n    // So it is power consuming if performing `render` each time,\n    // especially on mobile device.\n    if (!forceRender && this._lastValue === value && this._lastStatus === status) {\n      return;\n    }\n    this._lastValue = value;\n    this._lastStatus = status;\n    var group = this._group;\n    var handle = this._handle;\n    if (!status || status === 'hide') {\n      // Do not clear here, for animation better.\n      group && group.hide();\n      handle && handle.hide();\n      return;\n    }\n    group && group.show();\n    handle && handle.show();\n    // Otherwise status is 'show'\n    var elOption = {};\n    this.makeElOption(elOption, value, axisModel, axisPointerModel, api);\n    // Enable change axis pointer type.\n    var graphicKey = elOption.graphicKey;\n    if (graphicKey !== this._lastGraphicKey) {\n      this.clear(api);\n    }\n    this._lastGraphicKey = graphicKey;\n    var moveAnimation = this._moveAnimation = this.determineAnimation(axisModel, axisPointerModel);\n    if (!group) {\n      group = this._group = new graphic.Group();\n      this.createPointerEl(group, elOption, axisModel, axisPointerModel);\n      this.createLabelEl(group, elOption, axisModel, axisPointerModel);\n      api.getZr().add(group);\n    } else {\n      var doUpdateProps = zrUtil.curry(updateProps, axisPointerModel, moveAnimation);\n      this.updatePointerEl(group, elOption, doUpdateProps);\n      this.updateLabelEl(group, elOption, doUpdateProps, axisPointerModel);\n    }\n    updateMandatoryProps(group, axisPointerModel, true);\n    this._renderHandle(value);\n  };\n  /**\r\n   * @implement\r\n   */\n  BaseAxisPointer.prototype.remove = function (api) {\n    this.clear(api);\n  };\n  /**\r\n   * @implement\r\n   */\n  BaseAxisPointer.prototype.dispose = function (api) {\n    this.clear(api);\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.determineAnimation = function (axisModel, axisPointerModel) {\n    var animation = axisPointerModel.get('animation');\n    var axis = axisModel.axis;\n    var isCategoryAxis = axis.type === 'category';\n    var useSnap = axisPointerModel.get('snap');\n    // Value axis without snap always do not snap.\n    if (!useSnap && !isCategoryAxis) {\n      return false;\n    }\n    if (animation === 'auto' || animation == null) {\n      var animationThreshold = this.animationThreshold;\n      if (isCategoryAxis && axis.getBandWidth() > animationThreshold) {\n        return true;\n      }\n      // It is important to auto animation when snap used. Consider if there is\n      // a dataZoom, animation will be disabled when too many points exist, while\n      // it will be enabled for better visual effect when little points exist.\n      if (useSnap) {\n        var seriesDataCount = axisPointerModelHelper.getAxisInfo(axisModel).seriesDataCount;\n        var axisExtent = axis.getExtent();\n        // Approximate band width\n        return Math.abs(axisExtent[0] - axisExtent[1]) / seriesDataCount > animationThreshold;\n      }\n      return false;\n    }\n    return animation === true;\n  };\n  /**\r\n   * add {pointer, label, graphicKey} to elOption\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.makeElOption = function (elOption, value, axisModel, axisPointerModel, api) {\n    // Should be implemenented by sub-class.\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.createPointerEl = function (group, elOption, axisModel, axisPointerModel) {\n    var pointerOption = elOption.pointer;\n    if (pointerOption) {\n      var pointerEl = inner(group).pointerEl = new graphic[pointerOption.type](clone(elOption.pointer));\n      group.add(pointerEl);\n    }\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.createLabelEl = function (group, elOption, axisModel, axisPointerModel) {\n    if (elOption.label) {\n      var labelEl = inner(group).labelEl = new graphic.Text(clone(elOption.label));\n      group.add(labelEl);\n      updateLabelShowHide(labelEl, axisPointerModel);\n    }\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.updatePointerEl = function (group, elOption, updateProps) {\n    var pointerEl = inner(group).pointerEl;\n    if (pointerEl && elOption.pointer) {\n      pointerEl.setStyle(elOption.pointer.style);\n      updateProps(pointerEl, {\n        shape: elOption.pointer.shape\n      });\n    }\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.updateLabelEl = function (group, elOption, updateProps, axisPointerModel) {\n    var labelEl = inner(group).labelEl;\n    if (labelEl) {\n      labelEl.setStyle(elOption.label.style);\n      updateProps(labelEl, {\n        // Consider text length change in vertical axis, animation should\n        // be used on shape, otherwise the effect will be weird.\n        // TODOTODO\n        // shape: elOption.label.shape,\n        x: elOption.label.x,\n        y: elOption.label.y\n      });\n      updateLabelShowHide(labelEl, axisPointerModel);\n    }\n  };\n  /**\r\n   * @private\r\n   */\n  BaseAxisPointer.prototype._renderHandle = function (value) {\n    if (this._dragging || !this.updateHandleTransform) {\n      return;\n    }\n    var axisPointerModel = this._axisPointerModel;\n    var zr = this._api.getZr();\n    var handle = this._handle;\n    var handleModel = axisPointerModel.getModel('handle');\n    var status = axisPointerModel.get('status');\n    if (!handleModel.get('show') || !status || status === 'hide') {\n      handle && zr.remove(handle);\n      this._handle = null;\n      return;\n    }\n    var isInit;\n    if (!this._handle) {\n      isInit = true;\n      handle = this._handle = graphic.createIcon(handleModel.get('icon'), {\n        cursor: 'move',\n        draggable: true,\n        onmousemove: function (e) {\n          // For mobile device, prevent screen slider on the button.\n          eventTool.stop(e.event);\n        },\n        onmousedown: bind(this._onHandleDragMove, this, 0, 0),\n        drift: bind(this._onHandleDragMove, this),\n        ondragend: bind(this._onHandleDragEnd, this)\n      });\n      zr.add(handle);\n    }\n    updateMandatoryProps(handle, axisPointerModel, false);\n    // update style\n    handle.setStyle(handleModel.getItemStyle(null, ['color', 'borderColor', 'borderWidth', 'opacity', 'shadowColor', 'shadowBlur', 'shadowOffsetX', 'shadowOffsetY']));\n    // update position\n    var handleSize = handleModel.get('size');\n    if (!zrUtil.isArray(handleSize)) {\n      handleSize = [handleSize, handleSize];\n    }\n    handle.scaleX = handleSize[0] / 2;\n    handle.scaleY = handleSize[1] / 2;\n    throttleUtil.createOrUpdate(this, '_doDispatchAxisPointer', handleModel.get('throttle') || 0, 'fixRate');\n    this._moveHandleToValue(value, isInit);\n  };\n  BaseAxisPointer.prototype._moveHandleToValue = function (value, isInit) {\n    updateProps(this._axisPointerModel, !isInit && this._moveAnimation, this._handle, getHandleTransProps(this.getHandleTransform(value, this._axisModel, this._axisPointerModel)));\n  };\n  BaseAxisPointer.prototype._onHandleDragMove = function (dx, dy) {\n    var handle = this._handle;\n    if (!handle) {\n      return;\n    }\n    this._dragging = true;\n    // Persistent for throttle.\n    var trans = this.updateHandleTransform(getHandleTransProps(handle), [dx, dy], this._axisModel, this._axisPointerModel);\n    this._payloadInfo = trans;\n    handle.stopAnimation();\n    handle.attr(getHandleTransProps(trans));\n    inner(handle).lastProp = null;\n    this._doDispatchAxisPointer();\n  };\n  /**\r\n   * Throttled method.\r\n   */\n  BaseAxisPointer.prototype._doDispatchAxisPointer = function () {\n    var handle = this._handle;\n    if (!handle) {\n      return;\n    }\n    var payloadInfo = this._payloadInfo;\n    var axisModel = this._axisModel;\n    this._api.dispatchAction({\n      type: 'updateAxisPointer',\n      x: payloadInfo.cursorPoint[0],\n      y: payloadInfo.cursorPoint[1],\n      tooltipOption: payloadInfo.tooltipOption,\n      axesInfo: [{\n        axisDim: axisModel.axis.dim,\n        axisIndex: axisModel.componentIndex\n      }]\n    });\n  };\n  BaseAxisPointer.prototype._onHandleDragEnd = function () {\n    this._dragging = false;\n    var handle = this._handle;\n    if (!handle) {\n      return;\n    }\n    var value = this._axisPointerModel.get('value');\n    // Consider snap or categroy axis, handle may be not consistent with\n    // axisPointer. So move handle to align the exact value position when\n    // drag ended.\n    this._moveHandleToValue(value);\n    // For the effect: tooltip will be shown when finger holding on handle\n    // button, and will be hidden after finger left handle button.\n    this._api.dispatchAction({\n      type: 'hideTip'\n    });\n  };\n  /**\r\n   * @private\r\n   */\n  BaseAxisPointer.prototype.clear = function (api) {\n    this._lastValue = null;\n    this._lastStatus = null;\n    var zr = api.getZr();\n    var group = this._group;\n    var handle = this._handle;\n    if (zr && group) {\n      this._lastGraphicKey = null;\n      group && zr.remove(group);\n      handle && zr.remove(handle);\n      this._group = null;\n      this._handle = null;\n      this._payloadInfo = null;\n    }\n    throttleUtil.clear(this, '_doDispatchAxisPointer');\n  };\n  /**\r\n   * @protected\r\n   */\n  BaseAxisPointer.prototype.doClear = function () {\n    // Implemented by sub-class if necessary.\n  };\n  BaseAxisPointer.prototype.buildLabel = function (xy, wh, xDimIndex) {\n    xDimIndex = xDimIndex || 0;\n    return {\n      x: xy[xDimIndex],\n      y: xy[1 - xDimIndex],\n      width: wh[xDimIndex],\n      height: wh[1 - xDimIndex]\n    };\n  };\n  return BaseAxisPointer;\n}();\nfunction updateProps(animationModel, moveAnimation, el, props) {\n  // Animation optimize.\n  if (!propsEqual(inner(el).lastProp, props)) {\n    inner(el).lastProp = props;\n    moveAnimation ? graphic.updateProps(el, props, animationModel) : (el.stopAnimation(), el.attr(props));\n  }\n}\nfunction propsEqual(lastProps, newProps) {\n  if (zrUtil.isObject(lastProps) && zrUtil.isObject(newProps)) {\n    var equals_1 = true;\n    zrUtil.each(newProps, function (item, key) {\n      equals_1 = equals_1 && propsEqual(lastProps[key], item);\n    });\n    return !!equals_1;\n  } else {\n    return lastProps === newProps;\n  }\n}\nfunction updateLabelShowHide(labelEl, axisPointerModel) {\n  labelEl[axisPointerModel.get(['label', 'show']) ? 'show' : 'hide']();\n}\nfunction getHandleTransProps(trans) {\n  return {\n    x: trans.x || 0,\n    y: trans.y || 0,\n    rotation: trans.rotation || 0\n  };\n}\nfunction updateMandatoryProps(group, axisPointerModel, silent) {\n  var z = axisPointerModel.get('z');\n  var zlevel = axisPointerModel.get('zlevel');\n  group && group.traverse(function (el) {\n    if (el.type !== 'group') {\n      z != null && (el.z = z);\n      zlevel != null && (el.zlevel = zlevel);\n      el.silent = silent;\n    }\n  });\n}\nexport default BaseAxisPointer;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACA,IAAI,QAAQ,CAAA,GAAA,+IAAA,CAAA,YAAS,AAAD;AACpB,IAAI,QAAQ,8IAAA,CAAA,QAAY;AACxB,IAAI,OAAO,8IAAA,CAAA,OAAW;AACtB;;CAEC,GACD,IAAI,kBAAkB,WAAW,GAAE;IACjC,SAAS;QACP,IAAI,CAAC,SAAS,GAAG;QACjB;;;KAGC,GACD,IAAI,CAAC,kBAAkB,GAAG;IAC5B;IACA;;GAEC,GACD,gBAAgB,SAAS,CAAC,MAAM,GAAG,SAAU,SAAS,EAAE,gBAAgB,EAAE,GAAG,EAAE,WAAW;QACxF,IAAI,QAAQ,iBAAiB,GAAG,CAAC;QACjC,IAAI,SAAS,iBAAiB,GAAG,CAAC;QAClC,+DAA+D;QAC/D,6DAA6D;QAC7D,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,iBAAiB,GAAG;QACzB,IAAI,CAAC,IAAI,GAAG;QACZ,kEAAkE;QAClE,6DAA6D;QAC7D,+BAA+B;QAC/B,IAAI,CAAC,eAAe,IAAI,CAAC,UAAU,KAAK,SAAS,IAAI,CAAC,WAAW,KAAK,QAAQ;YAC5E;QACF;QACA,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,QAAQ,IAAI,CAAC,MAAM;QACvB,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,CAAC,UAAU,WAAW,QAAQ;YAChC,2CAA2C;YAC3C,SAAS,MAAM,IAAI;YACnB,UAAU,OAAO,IAAI;YACrB;QACF;QACA,SAAS,MAAM,IAAI;QACnB,UAAU,OAAO,IAAI;QACrB,6BAA6B;QAC7B,IAAI,WAAW,CAAC;QAChB,IAAI,CAAC,YAAY,CAAC,UAAU,OAAO,WAAW,kBAAkB;QAChE,mCAAmC;QACnC,IAAI,aAAa,SAAS,UAAU;QACpC,IAAI,eAAe,IAAI,CAAC,eAAe,EAAE;YACvC,IAAI,CAAC,KAAK,CAAC;QACb;QACA,IAAI,CAAC,eAAe,GAAG;QACvB,IAAI,gBAAgB,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,kBAAkB,CAAC,WAAW;QAC7E,IAAI,CAAC,OAAO;YACV,QAAQ,IAAI,CAAC,MAAM,GAAG,IAAI,kJAAQ,KAAK;YACvC,IAAI,CAAC,eAAe,CAAC,OAAO,UAAU,WAAW;YACjD,IAAI,CAAC,aAAa,CAAC,OAAO,UAAU,WAAW;YAC/C,IAAI,KAAK,GAAG,GAAG,CAAC;QAClB,OAAO;YACL,IAAI,gBAAgB,CAAA,GAAA,8IAAA,CAAA,QAAY,AAAD,EAAE,aAAa,kBAAkB;YAChE,IAAI,CAAC,eAAe,CAAC,OAAO,UAAU;YACtC,IAAI,CAAC,aAAa,CAAC,OAAO,UAAU,eAAe;QACrD;QACA,qBAAqB,OAAO,kBAAkB;QAC9C,IAAI,CAAC,aAAa,CAAC;IACrB;IACA;;GAEC,GACD,gBAAgB,SAAS,CAAC,MAAM,GAAG,SAAU,GAAG;QAC9C,IAAI,CAAC,KAAK,CAAC;IACb;IACA;;GAEC,GACD,gBAAgB,SAAS,CAAC,OAAO,GAAG,SAAU,GAAG;QAC/C,IAAI,CAAC,KAAK,CAAC;IACb;IACA;;GAEC,GACD,gBAAgB,SAAS,CAAC,kBAAkB,GAAG,SAAU,SAAS,EAAE,gBAAgB;QAClF,IAAI,YAAY,iBAAiB,GAAG,CAAC;QACrC,IAAI,OAAO,UAAU,IAAI;QACzB,IAAI,iBAAiB,KAAK,IAAI,KAAK;QACnC,IAAI,UAAU,iBAAiB,GAAG,CAAC;QACnC,8CAA8C;QAC9C,IAAI,CAAC,WAAW,CAAC,gBAAgB;YAC/B,OAAO;QACT;QACA,IAAI,cAAc,UAAU,aAAa,MAAM;YAC7C,IAAI,qBAAqB,IAAI,CAAC,kBAAkB;YAChD,IAAI,kBAAkB,KAAK,YAAY,KAAK,oBAAoB;gBAC9D,OAAO;YACT;YACA,yEAAyE;YACzE,2EAA2E;YAC3E,wEAAwE;YACxE,IAAI,SAAS;gBACX,IAAI,kBAAkB,CAAA,GAAA,yKAAA,CAAA,cAAkC,AAAD,EAAE,WAAW,eAAe;gBACnF,IAAI,aAAa,KAAK,SAAS;gBAC/B,yBAAyB;gBACzB,OAAO,KAAK,GAAG,CAAC,UAAU,CAAC,EAAE,GAAG,UAAU,CAAC,EAAE,IAAI,kBAAkB;YACrE;YACA,OAAO;QACT;QACA,OAAO,cAAc;IACvB;IACA;;;GAGC,GACD,gBAAgB,SAAS,CAAC,YAAY,GAAG,SAAU,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,gBAAgB,EAAE,GAAG;IAClG,wCAAwC;IAC1C;IACA;;GAEC,GACD,gBAAgB,SAAS,CAAC,eAAe,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,gBAAgB;QAChG,IAAI,gBAAgB,SAAS,OAAO;QACpC,IAAI,eAAe;YACjB,IAAI,YAAY,MAAM,OAAO,SAAS,GAAG,IAAI,iJAAO,CAAC,cAAc,IAAI,CAAC,CAAC,MAAM,SAAS,OAAO;YAC/F,MAAM,GAAG,CAAC;QACZ;IACF;IACA;;GAEC,GACD,gBAAgB,SAAS,CAAC,aAAa,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,gBAAgB;QAC9F,IAAI,SAAS,KAAK,EAAE;YAClB,IAAI,UAAU,MAAM,OAAO,OAAO,GAAG,IAAI,kJAAQ,IAAI,CAAC,MAAM,SAAS,KAAK;YAC1E,MAAM,GAAG,CAAC;YACV,oBAAoB,SAAS;QAC/B;IACF;IACA;;GAEC,GACD,gBAAgB,SAAS,CAAC,eAAe,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,WAAW;QAChF,IAAI,YAAY,MAAM,OAAO,SAAS;QACtC,IAAI,aAAa,SAAS,OAAO,EAAE;YACjC,UAAU,QAAQ,CAAC,SAAS,OAAO,CAAC,KAAK;YACzC,YAAY,WAAW;gBACrB,OAAO,SAAS,OAAO,CAAC,KAAK;YAC/B;QACF;IACF;IACA;;GAEC,GACD,gBAAgB,SAAS,CAAC,aAAa,GAAG,SAAU,KAAK,EAAE,QAAQ,EAAE,WAAW,EAAE,gBAAgB;QAChG,IAAI,UAAU,MAAM,OAAO,OAAO;QAClC,IAAI,SAAS;YACX,QAAQ,QAAQ,CAAC,SAAS,KAAK,CAAC,KAAK;YACrC,YAAY,SAAS;gBACnB,iEAAiE;gBACjE,wDAAwD;gBACxD,WAAW;gBACX,+BAA+B;gBAC/B,GAAG,SAAS,KAAK,CAAC,CAAC;gBACnB,GAAG,SAAS,KAAK,CAAC,CAAC;YACrB;YACA,oBAAoB,SAAS;QAC/B;IACF;IACA;;GAEC,GACD,gBAAgB,SAAS,CAAC,aAAa,GAAG,SAAU,KAAK;QACvD,IAAI,IAAI,CAAC,SAAS,IAAI,CAAC,IAAI,CAAC,qBAAqB,EAAE;YACjD;QACF;QACA,IAAI,mBAAmB,IAAI,CAAC,iBAAiB;QAC7C,IAAI,KAAK,IAAI,CAAC,IAAI,CAAC,KAAK;QACxB,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,cAAc,iBAAiB,QAAQ,CAAC;QAC5C,IAAI,SAAS,iBAAiB,GAAG,CAAC;QAClC,IAAI,CAAC,YAAY,GAAG,CAAC,WAAW,CAAC,UAAU,WAAW,QAAQ;YAC5D,UAAU,GAAG,MAAM,CAAC;YACpB,IAAI,CAAC,OAAO,GAAG;YACf;QACF;QACA,IAAI;QACJ,IAAI,CAAC,IAAI,CAAC,OAAO,EAAE;YACjB,SAAS;YACT,SAAS,IAAI,CAAC,OAAO,GAAG,kJAAQ,UAAU,CAAC,YAAY,GAAG,CAAC,SAAS;gBAClE,QAAQ;gBACR,WAAW;gBACX,aAAa,SAAU,CAAC;oBACtB,0DAA0D;oBAC1D,CAAA,GAAA,+JAAA,CAAA,OAAc,AAAD,EAAE,EAAE,KAAK;gBACxB;gBACA,aAAa,KAAK,IAAI,CAAC,iBAAiB,EAAE,IAAI,EAAE,GAAG;gBACnD,OAAO,KAAK,IAAI,CAAC,iBAAiB,EAAE,IAAI;gBACxC,WAAW,KAAK,IAAI,CAAC,gBAAgB,EAAE,IAAI;YAC7C;YACA,GAAG,GAAG,CAAC;QACT;QACA,qBAAqB,QAAQ,kBAAkB;QAC/C,eAAe;QACf,OAAO,QAAQ,CAAC,YAAY,YAAY,CAAC,MAAM;YAAC;YAAS;YAAe;YAAe;YAAW;YAAe;YAAc;YAAiB;SAAgB;QAChK,kBAAkB;QAClB,IAAI,aAAa,YAAY,GAAG,CAAC;QACjC,IAAI,CAAC,CAAA,GAAA,8IAAA,CAAA,UAAc,AAAD,EAAE,aAAa;YAC/B,aAAa;gBAAC;gBAAY;aAAW;QACvC;QACA,OAAO,MAAM,GAAG,UAAU,CAAC,EAAE,GAAG;QAChC,OAAO,MAAM,GAAG,UAAU,CAAC,EAAE,GAAG;QAChC,CAAA,GAAA,kJAAA,CAAA,iBAA2B,AAAD,EAAE,IAAI,EAAE,0BAA0B,YAAY,GAAG,CAAC,eAAe,GAAG;QAC9F,IAAI,CAAC,kBAAkB,CAAC,OAAO;IACjC;IACA,gBAAgB,SAAS,CAAC,kBAAkB,GAAG,SAAU,KAAK,EAAE,MAAM;QACpE,YAAY,IAAI,CAAC,iBAAiB,EAAE,CAAC,UAAU,IAAI,CAAC,cAAc,EAAE,IAAI,CAAC,OAAO,EAAE,oBAAoB,IAAI,CAAC,kBAAkB,CAAC,OAAO,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB;IAC9K;IACA,gBAAgB,SAAS,CAAC,iBAAiB,GAAG,SAAU,EAAE,EAAE,EAAE;QAC5D,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,CAAC,QAAQ;YACX;QACF;QACA,IAAI,CAAC,SAAS,GAAG;QACjB,2BAA2B;QAC3B,IAAI,QAAQ,IAAI,CAAC,qBAAqB,CAAC,oBAAoB,SAAS;YAAC;YAAI;SAAG,EAAE,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,iBAAiB;QACrH,IAAI,CAAC,YAAY,GAAG;QACpB,OAAO,aAAa;QACpB,OAAO,IAAI,CAAC,oBAAoB;QAChC,MAAM,QAAQ,QAAQ,GAAG;QACzB,IAAI,CAAC,sBAAsB;IAC7B;IACA;;GAEC,GACD,gBAAgB,SAAS,CAAC,sBAAsB,GAAG;QACjD,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,CAAC,QAAQ;YACX;QACF;QACA,IAAI,cAAc,IAAI,CAAC,YAAY;QACnC,IAAI,YAAY,IAAI,CAAC,UAAU;QAC/B,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC;YACvB,MAAM;YACN,GAAG,YAAY,WAAW,CAAC,EAAE;YAC7B,GAAG,YAAY,WAAW,CAAC,EAAE;YAC7B,eAAe,YAAY,aAAa;YACxC,UAAU;gBAAC;oBACT,SAAS,UAAU,IAAI,CAAC,GAAG;oBAC3B,WAAW,UAAU,cAAc;gBACrC;aAAE;QACJ;IACF;IACA,gBAAgB,SAAS,CAAC,gBAAgB,GAAG;QAC3C,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,CAAC,QAAQ;YACX;QACF;QACA,IAAI,QAAQ,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC;QACvC,oEAAoE;QACpE,qEAAqE;QACrE,cAAc;QACd,IAAI,CAAC,kBAAkB,CAAC;QACxB,sEAAsE;QACtE,8DAA8D;QAC9D,IAAI,CAAC,IAAI,CAAC,cAAc,CAAC;YACvB,MAAM;QACR;IACF;IACA;;GAEC,GACD,gBAAgB,SAAS,CAAC,KAAK,GAAG,SAAU,GAAG;QAC7C,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,KAAK,IAAI,KAAK;QAClB,IAAI,QAAQ,IAAI,CAAC,MAAM;QACvB,IAAI,SAAS,IAAI,CAAC,OAAO;QACzB,IAAI,MAAM,OAAO;YACf,IAAI,CAAC,eAAe,GAAG;YACvB,SAAS,GAAG,MAAM,CAAC;YACnB,UAAU,GAAG,MAAM,CAAC;YACpB,IAAI,CAAC,MAAM,GAAG;YACd,IAAI,CAAC,OAAO,GAAG;YACf,IAAI,CAAC,YAAY,GAAG;QACtB;QACA,CAAA,GAAA,kJAAA,CAAA,QAAkB,AAAD,EAAE,IAAI,EAAE;IAC3B;IACA;;GAEC,GACD,gBAAgB,SAAS,CAAC,OAAO,GAAG;IAClC,yCAAyC;IAC3C;IACA,gBAAgB,SAAS,CAAC,UAAU,GAAG,SAAU,EAAE,EAAE,EAAE,EAAE,SAAS;QAChE,YAAY,aAAa;QACzB,OAAO;YACL,GAAG,EAAE,CAAC,UAAU;YAChB,GAAG,EAAE,CAAC,IAAI,UAAU;YACpB,OAAO,EAAE,CAAC,UAAU;YACpB,QAAQ,EAAE,CAAC,IAAI,UAAU;QAC3B;IACF;IACA,OAAO;AACT;AACA,SAAS,YAAY,cAAc,EAAE,aAAa,EAAE,EAAE,EAAE,KAAK;IAC3D,sBAAsB;IACtB,IAAI,CAAC,WAAW,MAAM,IAAI,QAAQ,EAAE,QAAQ;QAC1C,MAAM,IAAI,QAAQ,GAAG;QACrB,gBAAgB,kJAAQ,WAAW,CAAC,IAAI,OAAO,kBAAkB,CAAC,GAAG,aAAa,IAAI,GAAG,IAAI,CAAC,MAAM;IACtG;AACF;AACA,SAAS,WAAW,SAAS,EAAE,QAAQ;IACrC,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAe,AAAD,EAAE,cAAc,CAAA,GAAA,8IAAA,CAAA,WAAe,AAAD,EAAE,WAAW;QAC3D,IAAI,WAAW;QACf,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE,UAAU,SAAU,IAAI,EAAE,GAAG;YACvC,WAAW,YAAY,WAAW,SAAS,CAAC,IAAI,EAAE;QACpD;QACA,OAAO,CAAC,CAAC;IACX,OAAO;QACL,OAAO,cAAc;IACvB;AACF;AACA,SAAS,oBAAoB,OAAO,EAAE,gBAAgB;IACpD,OAAO,CAAC,iBAAiB,GAAG,CAAC;QAAC;QAAS;KAAO,IAAI,SAAS,OAAO;AACpE;AACA,SAAS,oBAAoB,KAAK;IAChC,OAAO;QACL,GAAG,MAAM,CAAC,IAAI;QACd,GAAG,MAAM,CAAC,IAAI;QACd,UAAU,MAAM,QAAQ,IAAI;IAC9B;AACF;AACA,SAAS,qBAAqB,KAAK,EAAE,gBAAgB,EAAE,MAAM;IAC3D,IAAI,IAAI,iBAAiB,GAAG,CAAC;IAC7B,IAAI,SAAS,iBAAiB,GAAG,CAAC;IAClC,SAAS,MAAM,QAAQ,CAAC,SAAU,EAAE;QAClC,IAAI,GAAG,IAAI,KAAK,SAAS;YACvB,KAAK,QAAQ,CAAC,GAAG,CAAC,GAAG,CAAC;YACtB,UAAU,QAAQ,CAAC,GAAG,MAAM,GAAG,MAAM;YACrC,GAAG,MAAM,GAAG;QACd;IACF;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 730, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axisPointer/viewHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as textContain from 'zrender/lib/contain/text.js';\nimport * as formatUtil from '../../util/format.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport * as axisHelper from '../../coord/axisHelper.js';\nimport AxisBuilder from '../axis/AxisBuilder.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nexport function buildElStyle(axisPointerModel) {\n  var axisPointerType = axisPointerModel.get('type');\n  var styleModel = axisPointerModel.getModel(axisPointerType + 'Style');\n  var style;\n  if (axisPointerType === 'line') {\n    style = styleModel.getLineStyle();\n    style.fill = null;\n  } else if (axisPointerType === 'shadow') {\n    style = styleModel.getAreaStyle();\n    style.stroke = null;\n  }\n  return style;\n}\n/**\r\n * @param {Function} labelPos {align, verticalAlign, position}\r\n */\nexport function buildLabelElOption(elOption, axisModel, axisPointerModel, api, labelPos) {\n  var value = axisPointerModel.get('value');\n  var text = getValueLabel(value, axisModel.axis, axisModel.ecModel, axisPointerModel.get('seriesDataIndices'), {\n    precision: axisPointerModel.get(['label', 'precision']),\n    formatter: axisPointerModel.get(['label', 'formatter'])\n  });\n  var labelModel = axisPointerModel.getModel('label');\n  var paddings = formatUtil.normalizeCssArray(labelModel.get('padding') || 0);\n  var font = labelModel.getFont();\n  var textRect = textContain.getBoundingRect(text, font);\n  var position = labelPos.position;\n  var width = textRect.width + paddings[1] + paddings[3];\n  var height = textRect.height + paddings[0] + paddings[2];\n  // Adjust by align.\n  var align = labelPos.align;\n  align === 'right' && (position[0] -= width);\n  align === 'center' && (position[0] -= width / 2);\n  var verticalAlign = labelPos.verticalAlign;\n  verticalAlign === 'bottom' && (position[1] -= height);\n  verticalAlign === 'middle' && (position[1] -= height / 2);\n  // Not overflow ec container\n  confineInContainer(position, width, height, api);\n  var bgColor = labelModel.get('backgroundColor');\n  if (!bgColor || bgColor === 'auto') {\n    bgColor = axisModel.get(['axisLine', 'lineStyle', 'color']);\n  }\n  elOption.label = {\n    // shape: {x: 0, y: 0, width: width, height: height, r: labelModel.get('borderRadius')},\n    x: position[0],\n    y: position[1],\n    style: createTextStyle(labelModel, {\n      text: text,\n      font: font,\n      fill: labelModel.getTextColor(),\n      padding: paddings,\n      backgroundColor: bgColor\n    }),\n    // Label should be over axisPointer.\n    z2: 10\n  };\n}\n// Do not overflow ec container\nfunction confineInContainer(position, width, height, api) {\n  var viewWidth = api.getWidth();\n  var viewHeight = api.getHeight();\n  position[0] = Math.min(position[0] + width, viewWidth) - width;\n  position[1] = Math.min(position[1] + height, viewHeight) - height;\n  position[0] = Math.max(position[0], 0);\n  position[1] = Math.max(position[1], 0);\n}\nexport function getValueLabel(value, axis, ecModel, seriesDataIndices, opt) {\n  value = axis.scale.parse(value);\n  var text = axis.scale.getLabel({\n    value: value\n  }, {\n    // If `precision` is set, width can be fixed (like '12.00500'), which\n    // helps to debounce when when moving label.\n    precision: opt.precision\n  });\n  var formatter = opt.formatter;\n  if (formatter) {\n    var params_1 = {\n      value: axisHelper.getAxisRawValue(axis, {\n        value: value\n      }),\n      axisDimension: axis.dim,\n      axisIndex: axis.index,\n      seriesData: []\n    };\n    zrUtil.each(seriesDataIndices, function (idxItem) {\n      var series = ecModel.getSeriesByIndex(idxItem.seriesIndex);\n      var dataIndex = idxItem.dataIndexInside;\n      var dataParams = series && series.getDataParams(dataIndex);\n      dataParams && params_1.seriesData.push(dataParams);\n    });\n    if (zrUtil.isString(formatter)) {\n      text = formatter.replace('{value}', text);\n    } else if (zrUtil.isFunction(formatter)) {\n      text = formatter(params_1);\n    }\n  }\n  return text;\n}\nexport function getTransformedPosition(axis, value, layoutInfo) {\n  var transform = matrix.create();\n  matrix.rotate(transform, transform, layoutInfo.rotation);\n  matrix.translate(transform, transform, layoutInfo.position);\n  return graphic.applyTransform([axis.dataToCoord(value), (layoutInfo.labelOffset || 0) + (layoutInfo.labelDirection || 1) * (layoutInfo.labelMargin || 0)], transform);\n}\nexport function buildCartesianSingleLabelElOption(value, elOption, layoutInfo, axisModel, axisPointerModel, api) {\n  // @ts-ignore\n  var textLayout = AxisBuilder.innerTextLayout(layoutInfo.rotation, 0, layoutInfo.labelDirection);\n  layoutInfo.labelMargin = axisPointerModel.get(['label', 'margin']);\n  buildLabelElOption(elOption, axisModel, axisPointerModel, api, {\n    position: getTransformedPosition(axisModel.axis, value, layoutInfo),\n    align: textLayout.textAlign,\n    verticalAlign: textLayout.textVerticalAlign\n  });\n}\nexport function makeLineShape(p1, p2, xDimIndex) {\n  xDimIndex = xDimIndex || 0;\n  return {\n    x1: p1[xDimIndex],\n    y1: p1[1 - xDimIndex],\n    x2: p2[xDimIndex],\n    y2: p2[1 - xDimIndex]\n  };\n}\nexport function makeRectShape(xy, wh, xDimIndex) {\n  xDimIndex = xDimIndex || 0;\n  return {\n    x: xy[xDimIndex],\n    y: xy[1 - xDimIndex],\n    width: wh[xDimIndex],\n    height: wh[1 - xDimIndex]\n  };\n}\nexport function makeSectorShape(cx, cy, r0, r, startAngle, endAngle) {\n  return {\n    cx: cx,\n    cy: cy,\n    r0: r0,\n    r: r,\n    startAngle: startAngle,\n    endAngle: endAngle,\n    clockwise: true\n  };\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACO,SAAS,aAAa,gBAAgB;IAC3C,IAAI,kBAAkB,iBAAiB,GAAG,CAAC;IAC3C,IAAI,aAAa,iBAAiB,QAAQ,CAAC,kBAAkB;IAC7D,IAAI;IACJ,IAAI,oBAAoB,QAAQ;QAC9B,QAAQ,WAAW,YAAY;QAC/B,MAAM,IAAI,GAAG;IACf,OAAO,IAAI,oBAAoB,UAAU;QACvC,QAAQ,WAAW,YAAY;QAC/B,MAAM,MAAM,GAAG;IACjB;IACA,OAAO;AACT;AAIO,SAAS,mBAAmB,QAAQ,EAAE,SAAS,EAAE,gBAAgB,EAAE,GAAG,EAAE,QAAQ;IACrF,IAAI,QAAQ,iBAAiB,GAAG,CAAC;IACjC,IAAI,OAAO,cAAc,OAAO,UAAU,IAAI,EAAE,UAAU,OAAO,EAAE,iBAAiB,GAAG,CAAC,sBAAsB;QAC5G,WAAW,iBAAiB,GAAG,CAAC;YAAC;YAAS;SAAY;QACtD,WAAW,iBAAiB,GAAG,CAAC;YAAC;YAAS;SAAY;IACxD;IACA,IAAI,aAAa,iBAAiB,QAAQ,CAAC;IAC3C,IAAI,WAAW,CAAA,GAAA,gKAAA,CAAA,oBAA4B,AAAD,EAAE,WAAW,GAAG,CAAC,cAAc;IACzE,IAAI,OAAO,WAAW,OAAO;IAC7B,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,kBAA2B,AAAD,EAAE,MAAM;IACjD,IAAI,WAAW,SAAS,QAAQ;IAChC,IAAI,QAAQ,SAAS,KAAK,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;IACtD,IAAI,SAAS,SAAS,MAAM,GAAG,QAAQ,CAAC,EAAE,GAAG,QAAQ,CAAC,EAAE;IACxD,mBAAmB;IACnB,IAAI,QAAQ,SAAS,KAAK;IAC1B,UAAU,WAAW,CAAC,QAAQ,CAAC,EAAE,IAAI,KAAK;IAC1C,UAAU,YAAY,CAAC,QAAQ,CAAC,EAAE,IAAI,QAAQ,CAAC;IAC/C,IAAI,gBAAgB,SAAS,aAAa;IAC1C,kBAAkB,YAAY,CAAC,QAAQ,CAAC,EAAE,IAAI,MAAM;IACpD,kBAAkB,YAAY,CAAC,QAAQ,CAAC,EAAE,IAAI,SAAS,CAAC;IACxD,4BAA4B;IAC5B,mBAAmB,UAAU,OAAO,QAAQ;IAC5C,IAAI,UAAU,WAAW,GAAG,CAAC;IAC7B,IAAI,CAAC,WAAW,YAAY,QAAQ;QAClC,UAAU,UAAU,GAAG,CAAC;YAAC;YAAY;YAAa;SAAQ;IAC5D;IACA,SAAS,KAAK,GAAG;QACf,wFAAwF;QACxF,GAAG,QAAQ,CAAC,EAAE;QACd,GAAG,QAAQ,CAAC,EAAE;QACd,OAAO,CAAA,GAAA,qJAAA,CAAA,kBAAe,AAAD,EAAE,YAAY;YACjC,MAAM;YACN,MAAM;YACN,MAAM,WAAW,YAAY;YAC7B,SAAS;YACT,iBAAiB;QACnB;QACA,oCAAoC;QACpC,IAAI;IACN;AACF;AACA,+BAA+B;AAC/B,SAAS,mBAAmB,QAAQ,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG;IACtD,IAAI,YAAY,IAAI,QAAQ;IAC5B,IAAI,aAAa,IAAI,SAAS;IAC9B,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,OAAO,aAAa;IACzD,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,QAAQ,cAAc;IAC3D,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE;IACpC,QAAQ,CAAC,EAAE,GAAG,KAAK,GAAG,CAAC,QAAQ,CAAC,EAAE,EAAE;AACtC;AACO,SAAS,cAAc,KAAK,EAAE,IAAI,EAAE,OAAO,EAAE,iBAAiB,EAAE,GAAG;IACxE,QAAQ,KAAK,KAAK,CAAC,KAAK,CAAC;IACzB,IAAI,OAAO,KAAK,KAAK,CAAC,QAAQ,CAAC;QAC7B,OAAO;IACT,GAAG;QACD,qEAAqE;QACrE,4CAA4C;QAC5C,WAAW,IAAI,SAAS;IAC1B;IACA,IAAI,YAAY,IAAI,SAAS;IAC7B,IAAI,WAAW;QACb,IAAI,WAAW;YACb,OAAO,CAAA,GAAA,qJAAA,CAAA,kBAA0B,AAAD,EAAE,MAAM;gBACtC,OAAO;YACT;YACA,eAAe,KAAK,GAAG;YACvB,WAAW,KAAK,KAAK;YACrB,YAAY,EAAE;QAChB;QACA,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE,mBAAmB,SAAU,OAAO;YAC9C,IAAI,SAAS,QAAQ,gBAAgB,CAAC,QAAQ,WAAW;YACzD,IAAI,YAAY,QAAQ,eAAe;YACvC,IAAI,aAAa,UAAU,OAAO,aAAa,CAAC;YAChD,cAAc,SAAS,UAAU,CAAC,IAAI,CAAC;QACzC;QACA,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAe,AAAD,EAAE,YAAY;YAC9B,OAAO,UAAU,OAAO,CAAC,WAAW;QACtC,OAAO,IAAI,CAAA,GAAA,8IAAA,CAAA,aAAiB,AAAD,EAAE,YAAY;YACvC,OAAO,UAAU;QACnB;IACF;IACA,OAAO;AACT;AACO,SAAS,uBAAuB,IAAI,EAAE,KAAK,EAAE,UAAU;IAC5D,IAAI,YAAY,CAAA,GAAA,gJAAA,CAAA,SAAa,AAAD;IAC5B,CAAA,GAAA,gJAAA,CAAA,SAAa,AAAD,EAAE,WAAW,WAAW,WAAW,QAAQ;IACvD,CAAA,GAAA,gJAAA,CAAA,YAAgB,AAAD,EAAE,WAAW,WAAW,WAAW,QAAQ;IAC1D,OAAO,CAAA,GAAA,iKAAA,CAAA,iBAAsB,AAAD,EAAE;QAAC,KAAK,WAAW,CAAC;QAAQ,CAAC,WAAW,WAAW,IAAI,CAAC,IAAI,CAAC,WAAW,cAAc,IAAI,CAAC,IAAI,CAAC,WAAW,WAAW,IAAI,CAAC;KAAE,EAAE;AAC7J;AACO,SAAS,kCAAkC,KAAK,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,gBAAgB,EAAE,GAAG;IAC7G,aAAa;IACb,IAAI,aAAa,kKAAA,CAAA,UAAW,CAAC,eAAe,CAAC,WAAW,QAAQ,EAAE,GAAG,WAAW,cAAc;IAC9F,WAAW,WAAW,GAAG,iBAAiB,GAAG,CAAC;QAAC;QAAS;KAAS;IACjE,mBAAmB,UAAU,WAAW,kBAAkB,KAAK;QAC7D,UAAU,uBAAuB,UAAU,IAAI,EAAE,OAAO;QACxD,OAAO,WAAW,SAAS;QAC3B,eAAe,WAAW,iBAAiB;IAC7C;AACF;AACO,SAAS,cAAc,EAAE,EAAE,EAAE,EAAE,SAAS;IAC7C,YAAY,aAAa;IACzB,OAAO;QACL,IAAI,EAAE,CAAC,UAAU;QACjB,IAAI,EAAE,CAAC,IAAI,UAAU;QACrB,IAAI,EAAE,CAAC,UAAU;QACjB,IAAI,EAAE,CAAC,IAAI,UAAU;IACvB;AACF;AACO,SAAS,cAAc,EAAE,EAAE,EAAE,EAAE,SAAS;IAC7C,YAAY,aAAa;IACzB,OAAO;QACL,GAAG,EAAE,CAAC,UAAU;QAChB,GAAG,EAAE,CAAC,IAAI,UAAU;QACpB,OAAO,EAAE,CAAC,UAAU;QACpB,QAAQ,EAAE,CAAC,IAAI,UAAU;IAC3B;AACF;AACO,SAAS,gBAAgB,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,UAAU,EAAE,QAAQ;IACjE,OAAO;QACL,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,GAAG;QACH,YAAY;QACZ,UAAU;QACV,WAAW;IACb;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 955, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axisPointer/CartesianAxisPointer.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport BaseAxisPointer from './BaseAxisPointer.js';\nimport * as viewHelper from './viewHelper.js';\nimport * as cartesianAxisHelper from '../../coord/cartesian/cartesianAxisHelper.js';\nvar CartesianAxisPointer = /** @class */function (_super) {\n  __extends(CartesianAxisPointer, _super);\n  function CartesianAxisPointer() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  /**\r\n   * @override\r\n   */\n  CartesianAxisPointer.prototype.makeElOption = function (elOption, value, axisModel, axisPointerModel, api) {\n    var axis = axisModel.axis;\n    var grid = axis.grid;\n    var axisPointerType = axisPointerModel.get('type');\n    var otherExtent = getCartesian(grid, axis).getOtherAxis(axis).getGlobalExtent();\n    var pixelValue = axis.toGlobalCoord(axis.dataToCoord(value, true));\n    if (axisPointerType && axisPointerType !== 'none') {\n      var elStyle = viewHelper.buildElStyle(axisPointerModel);\n      var pointerOption = pointerShapeBuilder[axisPointerType](axis, pixelValue, otherExtent);\n      pointerOption.style = elStyle;\n      elOption.graphicKey = pointerOption.type;\n      elOption.pointer = pointerOption;\n    }\n    var layoutInfo = cartesianAxisHelper.layout(grid.model, axisModel);\n    viewHelper.buildCartesianSingleLabelElOption(\n    // @ts-ignore\n    value, elOption, layoutInfo, axisModel, axisPointerModel, api);\n  };\n  /**\r\n   * @override\r\n   */\n  CartesianAxisPointer.prototype.getHandleTransform = function (value, axisModel, axisPointerModel) {\n    var layoutInfo = cartesianAxisHelper.layout(axisModel.axis.grid.model, axisModel, {\n      labelInside: false\n    });\n    // @ts-ignore\n    layoutInfo.labelMargin = axisPointerModel.get(['handle', 'margin']);\n    var pos = viewHelper.getTransformedPosition(axisModel.axis, value, layoutInfo);\n    return {\n      x: pos[0],\n      y: pos[1],\n      rotation: layoutInfo.rotation + (layoutInfo.labelDirection < 0 ? Math.PI : 0)\n    };\n  };\n  /**\r\n   * @override\r\n   */\n  CartesianAxisPointer.prototype.updateHandleTransform = function (transform, delta, axisModel, axisPointerModel) {\n    var axis = axisModel.axis;\n    var grid = axis.grid;\n    var axisExtent = axis.getGlobalExtent(true);\n    var otherExtent = getCartesian(grid, axis).getOtherAxis(axis).getGlobalExtent();\n    var dimIndex = axis.dim === 'x' ? 0 : 1;\n    var currPosition = [transform.x, transform.y];\n    currPosition[dimIndex] += delta[dimIndex];\n    currPosition[dimIndex] = Math.min(axisExtent[1], currPosition[dimIndex]);\n    currPosition[dimIndex] = Math.max(axisExtent[0], currPosition[dimIndex]);\n    var cursorOtherValue = (otherExtent[1] + otherExtent[0]) / 2;\n    var cursorPoint = [cursorOtherValue, cursorOtherValue];\n    cursorPoint[dimIndex] = currPosition[dimIndex];\n    // Make tooltip do not overlap axisPointer and in the middle of the grid.\n    var tooltipOptions = [{\n      verticalAlign: 'middle'\n    }, {\n      align: 'center'\n    }];\n    return {\n      x: currPosition[0],\n      y: currPosition[1],\n      rotation: transform.rotation,\n      cursorPoint: cursorPoint,\n      tooltipOption: tooltipOptions[dimIndex]\n    };\n  };\n  return CartesianAxisPointer;\n}(BaseAxisPointer);\nfunction getCartesian(grid, axis) {\n  var opt = {};\n  opt[axis.dim + 'AxisIndex'] = axis.index;\n  return grid.getCartesian(opt);\n}\nvar pointerShapeBuilder = {\n  line: function (axis, pixelValue, otherExtent) {\n    var targetShape = viewHelper.makeLineShape([pixelValue, otherExtent[0]], [pixelValue, otherExtent[1]], getAxisDimIndex(axis));\n    return {\n      type: 'Line',\n      subPixelOptimize: true,\n      shape: targetShape\n    };\n  },\n  shadow: function (axis, pixelValue, otherExtent) {\n    var bandWidth = Math.max(1, axis.getBandWidth());\n    var span = otherExtent[1] - otherExtent[0];\n    return {\n      type: 'Rect',\n      shape: viewHelper.makeRectShape([pixelValue - bandWidth / 2, otherExtent[0]], [bandWidth, span], getAxisDimIndex(axis))\n    };\n  }\n};\nfunction getAxisDimIndex(axis) {\n  return axis.dim === 'x' ? 0 : 1;\n}\nexport default CartesianAxisPointer;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;;;;;AACA,IAAI,uBAAuB,WAAW,GAAE,SAAU,MAAM;IACtD,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE,sBAAsB;IAChC,SAAS;QACP,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACjE;IACA;;GAEC,GACD,qBAAqB,SAAS,CAAC,YAAY,GAAG,SAAU,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,gBAAgB,EAAE,GAAG;QACvG,IAAI,OAAO,UAAU,IAAI;QACzB,IAAI,OAAO,KAAK,IAAI;QACpB,IAAI,kBAAkB,iBAAiB,GAAG,CAAC;QAC3C,IAAI,cAAc,aAAa,MAAM,MAAM,YAAY,CAAC,MAAM,eAAe;QAC7E,IAAI,aAAa,KAAK,aAAa,CAAC,KAAK,WAAW,CAAC,OAAO;QAC5D,IAAI,mBAAmB,oBAAoB,QAAQ;YACjD,IAAI,UAAU,CAAA,GAAA,wKAAA,CAAA,eAAuB,AAAD,EAAE;YACtC,IAAI,gBAAgB,mBAAmB,CAAC,gBAAgB,CAAC,MAAM,YAAY;YAC3E,cAAc,KAAK,GAAG;YACtB,SAAS,UAAU,GAAG,cAAc,IAAI;YACxC,SAAS,OAAO,GAAG;QACrB;QACA,IAAI,aAAa,CAAA,GAAA,2KAAA,CAAA,SAA0B,AAAD,EAAE,KAAK,KAAK,EAAE;QACxD,CAAA,GAAA,wKAAA,CAAA,oCAA4C,AAAD,EAC3C,aAAa;QACb,OAAO,UAAU,YAAY,WAAW,kBAAkB;IAC5D;IACA;;GAEC,GACD,qBAAqB,SAAS,CAAC,kBAAkB,GAAG,SAAU,KAAK,EAAE,SAAS,EAAE,gBAAgB;QAC9F,IAAI,aAAa,CAAA,GAAA,2KAAA,CAAA,SAA0B,AAAD,EAAE,UAAU,IAAI,CAAC,IAAI,CAAC,KAAK,EAAE,WAAW;YAChF,aAAa;QACf;QACA,aAAa;QACb,WAAW,WAAW,GAAG,iBAAiB,GAAG,CAAC;YAAC;YAAU;SAAS;QAClE,IAAI,MAAM,CAAA,GAAA,wKAAA,CAAA,yBAAiC,AAAD,EAAE,UAAU,IAAI,EAAE,OAAO;QACnE,OAAO;YACL,GAAG,GAAG,CAAC,EAAE;YACT,GAAG,GAAG,CAAC,EAAE;YACT,UAAU,WAAW,QAAQ,GAAG,CAAC,WAAW,cAAc,GAAG,IAAI,KAAK,EAAE,GAAG,CAAC;QAC9E;IACF;IACA;;GAEC,GACD,qBAAqB,SAAS,CAAC,qBAAqB,GAAG,SAAU,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,gBAAgB;QAC5G,IAAI,OAAO,UAAU,IAAI;QACzB,IAAI,OAAO,KAAK,IAAI;QACpB,IAAI,aAAa,KAAK,eAAe,CAAC;QACtC,IAAI,cAAc,aAAa,MAAM,MAAM,YAAY,CAAC,MAAM,eAAe;QAC7E,IAAI,WAAW,KAAK,GAAG,KAAK,MAAM,IAAI;QACtC,IAAI,eAAe;YAAC,UAAU,CAAC;YAAE,UAAU,CAAC;SAAC;QAC7C,YAAY,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS;QACzC,YAAY,CAAC,SAAS,GAAG,KAAK,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,YAAY,CAAC,SAAS;QACvE,YAAY,CAAC,SAAS,GAAG,KAAK,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,YAAY,CAAC,SAAS;QACvE,IAAI,mBAAmB,CAAC,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,IAAI;QAC3D,IAAI,cAAc;YAAC;YAAkB;SAAiB;QACtD,WAAW,CAAC,SAAS,GAAG,YAAY,CAAC,SAAS;QAC9C,yEAAyE;QACzE,IAAI,iBAAiB;YAAC;gBACpB,eAAe;YACjB;YAAG;gBACD,OAAO;YACT;SAAE;QACF,OAAO;YACL,GAAG,YAAY,CAAC,EAAE;YAClB,GAAG,YAAY,CAAC,EAAE;YAClB,UAAU,UAAU,QAAQ;YAC5B,aAAa;YACb,eAAe,cAAc,CAAC,SAAS;QACzC;IACF;IACA,OAAO;AACT,EAAE,6KAAA,CAAA,UAAe;AACjB,SAAS,aAAa,IAAI,EAAE,IAAI;IAC9B,IAAI,MAAM,CAAC;IACX,GAAG,CAAC,KAAK,GAAG,GAAG,YAAY,GAAG,KAAK,KAAK;IACxC,OAAO,KAAK,YAAY,CAAC;AAC3B;AACA,IAAI,sBAAsB;IACxB,MAAM,SAAU,IAAI,EAAE,UAAU,EAAE,WAAW;QAC3C,IAAI,cAAc,CAAA,GAAA,wKAAA,CAAA,gBAAwB,AAAD,EAAE;YAAC;YAAY,WAAW,CAAC,EAAE;SAAC,EAAE;YAAC;YAAY,WAAW,CAAC,EAAE;SAAC,EAAE,gBAAgB;QACvH,OAAO;YACL,MAAM;YACN,kBAAkB;YAClB,OAAO;QACT;IACF;IACA,QAAQ,SAAU,IAAI,EAAE,UAAU,EAAE,WAAW;QAC7C,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,KAAK,YAAY;QAC7C,IAAI,OAAO,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE;QAC1C,OAAO;YACL,MAAM;YACN,OAAO,CAAA,GAAA,wKAAA,CAAA,gBAAwB,AAAD,EAAE;gBAAC,aAAa,YAAY;gBAAG,WAAW,CAAC,EAAE;aAAC,EAAE;gBAAC;gBAAW;aAAK,EAAE,gBAAgB;QACnH;IACF;AACF;AACA,SAAS,gBAAgB,IAAI;IAC3B,OAAO,KAAK,GAAG,KAAK,MAAM,IAAI;AAChC;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1129, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axisPointer/AxisPointerModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport ComponentModel from '../../model/Component.js';\nvar AxisPointerModel = /** @class */function (_super) {\n  __extends(AxisPointerModel, _super);\n  function AxisPointerModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = AxisPointerModel.type;\n    return _this;\n  }\n  AxisPointerModel.type = 'axisPointer';\n  AxisPointerModel.defaultOption = {\n    // 'auto' means that show when triggered by tooltip or handle.\n    show: 'auto',\n    // zlevel: 0,\n    z: 50,\n    type: 'line',\n    // axispointer triggered by tootip determine snap automatically,\n    // see `modelHelper`.\n    snap: false,\n    triggerTooltip: true,\n    triggerEmphasis: true,\n    value: null,\n    status: null,\n    link: [],\n    // Do not set 'auto' here, otherwise global animation: false\n    // will not effect at this axispointer.\n    animation: null,\n    animationDurationUpdate: 200,\n    lineStyle: {\n      color: '#B9BEC9',\n      width: 1,\n      type: 'dashed'\n    },\n    shadowStyle: {\n      color: 'rgba(210,219,238,0.2)'\n    },\n    label: {\n      show: true,\n      formatter: null,\n      precision: 'auto',\n      margin: 3,\n      color: '#fff',\n      padding: [5, 7, 5, 7],\n      backgroundColor: 'auto',\n      borderColor: null,\n      borderWidth: 0,\n      borderRadius: 3\n    },\n    handle: {\n      show: false,\n      // eslint-disable-next-line\n      icon: 'M10.7,11.9v-1.3H9.3v1.3c-4.9,0.3-8.8,4.4-8.8,9.4c0,5,3.9,9.1,8.8,9.4h1.3c4.9-0.3,8.8-4.4,8.8-9.4C19.5,16.3,15.6,12.2,10.7,11.9z M13.3,24.4H6.7v-1.2h6.6z M13.3,22H6.7v-1.2h6.6z M13.3,19.6H6.7v-1.2h6.6z',\n      size: 45,\n      // handle margin is from symbol center to axis, which is stable when circular move.\n      margin: 50,\n      // color: '#1b8bbd'\n      // color: '#2f4554'\n      color: '#333',\n      shadowBlur: 3,\n      shadowColor: '#aaa',\n      shadowOffsetX: 0,\n      shadowOffsetY: 2,\n      // For mobile performance\n      throttle: 40\n    }\n  };\n  return AxisPointerModel;\n}(ComponentModel);\nexport default AxisPointerModel;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;;;AACA,IAAI,mBAAmB,WAAW,GAAE,SAAU,MAAM;IAClD,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE,kBAAkB;IAC5B,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,iBAAiB,IAAI;QAClC,OAAO;IACT;IACA,iBAAiB,IAAI,GAAG;IACxB,iBAAiB,aAAa,GAAG;QAC/B,8DAA8D;QAC9D,MAAM;QACN,aAAa;QACb,GAAG;QACH,MAAM;QACN,gEAAgE;QAChE,qBAAqB;QACrB,MAAM;QACN,gBAAgB;QAChB,iBAAiB;QACjB,OAAO;QACP,QAAQ;QACR,MAAM,EAAE;QACR,4DAA4D;QAC5D,uCAAuC;QACvC,WAAW;QACX,yBAAyB;QACzB,WAAW;YACT,OAAO;YACP,OAAO;YACP,MAAM;QACR;QACA,aAAa;YACX,OAAO;QACT;QACA,OAAO;YACL,MAAM;YACN,WAAW;YACX,WAAW;YACX,QAAQ;YACR,OAAO;YACP,SAAS;gBAAC;gBAAG;gBAAG;gBAAG;aAAE;YACrB,iBAAiB;YACjB,aAAa;YACb,aAAa;YACb,cAAc;QAChB;QACA,QAAQ;YACN,MAAM;YACN,2BAA2B;YAC3B,MAAM;YACN,MAAM;YACN,mFAAmF;YACnF,QAAQ;YACR,mBAAmB;YACnB,mBAAmB;YACnB,OAAO;YACP,YAAY;YACZ,aAAa;YACb,eAAe;YACf,eAAe;YACf,yBAAyB;YACzB,UAAU;QACZ;IACF;IACA,OAAO;AACT,EAAE,oJAAA,CAAA,UAAc;uCACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1250, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axisPointer/globalListener.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nvar each = zrUtil.each;\n/**\r\n * @param {string} key\r\n * @param {module:echarts/ExtensionAPI} api\r\n * @param {Function} handler\r\n *      param: {string} currTrigger\r\n *      param: {Array.<number>} point\r\n */\nexport function register(key, api, handler) {\n  if (env.node) {\n    return;\n  }\n  var zr = api.getZr();\n  inner(zr).records || (inner(zr).records = {});\n  initGlobalListeners(zr, api);\n  var record = inner(zr).records[key] || (inner(zr).records[key] = {});\n  record.handler = handler;\n}\nfunction initGlobalListeners(zr, api) {\n  if (inner(zr).initialized) {\n    return;\n  }\n  inner(zr).initialized = true;\n  useHandler('click', zrUtil.curry(doEnter, 'click'));\n  useHandler('mousemove', zrUtil.curry(doEnter, 'mousemove'));\n  // useHandler('mouseout', onLeave);\n  useHandler('globalout', onLeave);\n  function useHandler(eventType, cb) {\n    zr.on(eventType, function (e) {\n      var dis = makeDispatchAction(api);\n      each(inner(zr).records, function (record) {\n        record && cb(record, e, dis.dispatchAction);\n      });\n      dispatchTooltipFinally(dis.pendings, api);\n    });\n  }\n}\nfunction dispatchTooltipFinally(pendings, api) {\n  var showLen = pendings.showTip.length;\n  var hideLen = pendings.hideTip.length;\n  var actuallyPayload;\n  if (showLen) {\n    actuallyPayload = pendings.showTip[showLen - 1];\n  } else if (hideLen) {\n    actuallyPayload = pendings.hideTip[hideLen - 1];\n  }\n  if (actuallyPayload) {\n    actuallyPayload.dispatchAction = null;\n    api.dispatchAction(actuallyPayload);\n  }\n}\nfunction onLeave(record, e, dispatchAction) {\n  record.handler('leave', null, dispatchAction);\n}\nfunction doEnter(currTrigger, record, e, dispatchAction) {\n  record.handler(currTrigger, e, dispatchAction);\n}\nfunction makeDispatchAction(api) {\n  var pendings = {\n    showTip: [],\n    hideTip: []\n  };\n  // FIXME\n  // better approach?\n  // 'showTip' and 'hideTip' can be triggered by axisPointer and tooltip,\n  // which may be conflict, (axisPointer call showTip but tooltip call hideTip);\n  // So we have to add \"final stage\" to merge those dispatched actions.\n  var dispatchAction = function (payload) {\n    var pendingList = pendings[payload.type];\n    if (pendingList) {\n      pendingList.push(payload);\n    } else {\n      payload.dispatchAction = dispatchAction;\n      api.dispatchAction(payload);\n    }\n  };\n  return {\n    dispatchAction: dispatchAction,\n    pendings: pendings\n  };\n}\nexport function unregister(key, api) {\n  if (env.node) {\n    return;\n  }\n  var zr = api.getZr();\n  var record = (inner(zr).records || {})[key];\n  if (record) {\n    inner(zr).records[key] = null;\n  }\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;AACA;AACA;AACA;;;;AACA,IAAI,QAAQ,CAAA,GAAA,+IAAA,CAAA,YAAS,AAAD;AACpB,IAAI,OAAO,8IAAA,CAAA,OAAW;AAQf,SAAS,SAAS,GAAG,EAAE,GAAG,EAAE,OAAO;IACxC,IAAI,6IAAA,CAAA,UAAG,CAAC,IAAI,EAAE;QACZ;IACF;IACA,IAAI,KAAK,IAAI,KAAK;IAClB,MAAM,IAAI,OAAO,IAAI,CAAC,MAAM,IAAI,OAAO,GAAG,CAAC,CAAC;IAC5C,oBAAoB,IAAI;IACxB,IAAI,SAAS,MAAM,IAAI,OAAO,CAAC,IAAI,IAAI,CAAC,MAAM,IAAI,OAAO,CAAC,IAAI,GAAG,CAAC,CAAC;IACnE,OAAO,OAAO,GAAG;AACnB;AACA,SAAS,oBAAoB,EAAE,EAAE,GAAG;IAClC,IAAI,MAAM,IAAI,WAAW,EAAE;QACzB;IACF;IACA,MAAM,IAAI,WAAW,GAAG;IACxB,WAAW,SAAS,CAAA,GAAA,8IAAA,CAAA,QAAY,AAAD,EAAE,SAAS;IAC1C,WAAW,aAAa,CAAA,GAAA,8IAAA,CAAA,QAAY,AAAD,EAAE,SAAS;IAC9C,mCAAmC;IACnC,WAAW,aAAa;IACxB,SAAS,WAAW,SAAS,EAAE,EAAE;QAC/B,GAAG,EAAE,CAAC,WAAW,SAAU,CAAC;YAC1B,IAAI,MAAM,mBAAmB;YAC7B,KAAK,MAAM,IAAI,OAAO,EAAE,SAAU,MAAM;gBACtC,UAAU,GAAG,QAAQ,GAAG,IAAI,cAAc;YAC5C;YACA,uBAAuB,IAAI,QAAQ,EAAE;QACvC;IACF;AACF;AACA,SAAS,uBAAuB,QAAQ,EAAE,GAAG;IAC3C,IAAI,UAAU,SAAS,OAAO,CAAC,MAAM;IACrC,IAAI,UAAU,SAAS,OAAO,CAAC,MAAM;IACrC,IAAI;IACJ,IAAI,SAAS;QACX,kBAAkB,SAAS,OAAO,CAAC,UAAU,EAAE;IACjD,OAAO,IAAI,SAAS;QAClB,kBAAkB,SAAS,OAAO,CAAC,UAAU,EAAE;IACjD;IACA,IAAI,iBAAiB;QACnB,gBAAgB,cAAc,GAAG;QACjC,IAAI,cAAc,CAAC;IACrB;AACF;AACA,SAAS,QAAQ,MAAM,EAAE,CAAC,EAAE,cAAc;IACxC,OAAO,OAAO,CAAC,SAAS,MAAM;AAChC;AACA,SAAS,QAAQ,WAAW,EAAE,MAAM,EAAE,CAAC,EAAE,cAAc;IACrD,OAAO,OAAO,CAAC,aAAa,GAAG;AACjC;AACA,SAAS,mBAAmB,GAAG;IAC7B,IAAI,WAAW;QACb,SAAS,EAAE;QACX,SAAS,EAAE;IACb;IACA,QAAQ;IACR,mBAAmB;IACnB,uEAAuE;IACvE,8EAA8E;IAC9E,qEAAqE;IACrE,IAAI,iBAAiB,SAAU,OAAO;QACpC,IAAI,cAAc,QAAQ,CAAC,QAAQ,IAAI,CAAC;QACxC,IAAI,aAAa;YACf,YAAY,IAAI,CAAC;QACnB,OAAO;YACL,QAAQ,cAAc,GAAG;YACzB,IAAI,cAAc,CAAC;QACrB;IACF;IACA,OAAO;QACL,gBAAgB;QAChB,UAAU;IACZ;AACF;AACO,SAAS,WAAW,GAAG,EAAE,GAAG;IACjC,IAAI,6IAAA,CAAA,UAAG,CAAC,IAAI,EAAE;QACZ;IACF;IACA,IAAI,KAAK,IAAI,KAAK;IAClB,IAAI,SAAS,CAAC,MAAM,IAAI,OAAO,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI;IAC3C,IAAI,QAAQ;QACV,MAAM,IAAI,OAAO,CAAC,IAAI,GAAG;IAC3B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1387, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axisPointer/AxisPointerView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as globalListener from './globalListener.js';\nimport ComponentView from '../../view/Component.js';\nvar AxisPointerView = /** @class */function (_super) {\n  __extends(AxisPointerView, _super);\n  function AxisPointerView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = AxisPointerView.type;\n    return _this;\n  }\n  AxisPointerView.prototype.render = function (globalAxisPointerModel, ecModel, api) {\n    var globalTooltipModel = ecModel.getComponent('tooltip');\n    var triggerOn = globalAxisPointerModel.get('triggerOn') || globalTooltipModel && globalTooltipModel.get('triggerOn') || 'mousemove|click';\n    // Register global listener in AxisPointerView to enable\n    // AxisPointerView to be independent to Tooltip.\n    globalListener.register('axisPointer', api, function (currTrigger, e, dispatchAction) {\n      // If 'none', it is not controlled by mouse totally.\n      if (triggerOn !== 'none' && (currTrigger === 'leave' || triggerOn.indexOf(currTrigger) >= 0)) {\n        dispatchAction({\n          type: 'updateAxisPointer',\n          currTrigger: currTrigger,\n          x: e && e.offsetX,\n          y: e && e.offsetY\n        });\n      }\n    });\n  };\n  AxisPointerView.prototype.remove = function (ecModel, api) {\n    globalListener.unregister('axisPointer', api);\n  };\n  AxisPointerView.prototype.dispose = function (ecModel, api) {\n    globalListener.unregister('axisPointer', api);\n  };\n  AxisPointerView.type = 'axisPointer';\n  return AxisPointerView;\n}(ComponentView);\nexport default AxisPointerView;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;;;;AACA,IAAI,kBAAkB,WAAW,GAAE,SAAU,MAAM;IACjD,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE,iBAAiB;IAC3B,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,gBAAgB,IAAI;QACjC,OAAO;IACT;IACA,gBAAgB,SAAS,CAAC,MAAM,GAAG,SAAU,sBAAsB,EAAE,OAAO,EAAE,GAAG;QAC/E,IAAI,qBAAqB,QAAQ,YAAY,CAAC;QAC9C,IAAI,YAAY,uBAAuB,GAAG,CAAC,gBAAgB,sBAAsB,mBAAmB,GAAG,CAAC,gBAAgB;QACxH,wDAAwD;QACxD,gDAAgD;QAChD,CAAA,GAAA,4KAAA,CAAA,WAAuB,AAAD,EAAE,eAAe,KAAK,SAAU,WAAW,EAAE,CAAC,EAAE,cAAc;YAClF,oDAAoD;YACpD,IAAI,cAAc,UAAU,CAAC,gBAAgB,WAAW,UAAU,OAAO,CAAC,gBAAgB,CAAC,GAAG;gBAC5F,eAAe;oBACb,MAAM;oBACN,aAAa;oBACb,GAAG,KAAK,EAAE,OAAO;oBACjB,GAAG,KAAK,EAAE,OAAO;gBACnB;YACF;QACF;IACF;IACA,gBAAgB,SAAS,CAAC,MAAM,GAAG,SAAU,OAAO,EAAE,GAAG;QACvD,CAAA,GAAA,4KAAA,CAAA,aAAyB,AAAD,EAAE,eAAe;IAC3C;IACA,gBAAgB,SAAS,CAAC,OAAO,GAAG,SAAU,OAAO,EAAE,GAAG;QACxD,CAAA,GAAA,4KAAA,CAAA,aAAyB,AAAD,EAAE,eAAe;IAC3C;IACA,gBAAgB,IAAI,GAAG;IACvB,OAAO;AACT,EAAE,mJAAA,CAAA,UAAa;uCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1472, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axisPointer/findPointFromSeries.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as modelUtil from '../../util/model.js';\n/**\r\n * @param finder contains {seriesIndex, dataIndex, dataIndexInside}\r\n * @param ecModel\r\n * @return  {point: [x, y], el: ...} point Will not be null.\r\n */\nexport default function findPointFromSeries(finder, ecModel) {\n  var point = [];\n  var seriesIndex = finder.seriesIndex;\n  var seriesModel;\n  if (seriesIndex == null || !(seriesModel = ecModel.getSeriesByIndex(seriesIndex))) {\n    return {\n      point: []\n    };\n  }\n  var data = seriesModel.getData();\n  var dataIndex = modelUtil.queryDataIndex(data, finder);\n  if (dataIndex == null || dataIndex < 0 || zrUtil.isArray(dataIndex)) {\n    return {\n      point: []\n    };\n  }\n  var el = data.getItemGraphicEl(dataIndex);\n  var coordSys = seriesModel.coordinateSystem;\n  if (seriesModel.getTooltipPosition) {\n    point = seriesModel.getTooltipPosition(dataIndex) || [];\n  } else if (coordSys && coordSys.dataToPoint) {\n    if (finder.isStacked) {\n      var baseAxis = coordSys.getBaseAxis();\n      var valueAxis = coordSys.getOtherAxis(baseAxis);\n      var valueAxisDim = valueAxis.dim;\n      var baseAxisDim = baseAxis.dim;\n      var baseDataOffset = valueAxisDim === 'x' || valueAxisDim === 'radius' ? 1 : 0;\n      var baseDim = data.mapDimension(baseAxisDim);\n      var stackedData = [];\n      stackedData[baseDataOffset] = data.get(baseDim, dataIndex);\n      stackedData[1 - baseDataOffset] = data.get(data.getCalculationInfo('stackResultDimension'), dataIndex);\n      point = coordSys.dataToPoint(stackedData) || [];\n    } else {\n      point = coordSys.dataToPoint(data.getValues(zrUtil.map(coordSys.dimensions, function (dim) {\n        return data.mapDimension(dim);\n      }), dataIndex)) || [];\n    }\n  } else if (el) {\n    // Use graphic bounding rect\n    var rect = el.getBoundingRect().clone();\n    rect.applyTransform(el.transform);\n    point = [rect.x + rect.width / 2, rect.y + rect.height / 2];\n  }\n  return {\n    point: point,\n    el: el\n  };\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;;;AAMe,SAAS,oBAAoB,MAAM,EAAE,OAAO;IACzD,IAAI,QAAQ,EAAE;IACd,IAAI,cAAc,OAAO,WAAW;IACpC,IAAI;IACJ,IAAI,eAAe,QAAQ,CAAC,CAAC,cAAc,QAAQ,gBAAgB,CAAC,YAAY,GAAG;QACjF,OAAO;YACL,OAAO,EAAE;QACX;IACF;IACA,IAAI,OAAO,YAAY,OAAO;IAC9B,IAAI,YAAY,CAAA,GAAA,+IAAA,CAAA,iBAAwB,AAAD,EAAE,MAAM;IAC/C,IAAI,aAAa,QAAQ,YAAY,KAAK,CAAA,GAAA,8IAAA,CAAA,UAAc,AAAD,EAAE,YAAY;QACnE,OAAO;YACL,OAAO,EAAE;QACX;IACF;IACA,IAAI,KAAK,KAAK,gBAAgB,CAAC;IAC/B,IAAI,WAAW,YAAY,gBAAgB;IAC3C,IAAI,YAAY,kBAAkB,EAAE;QAClC,QAAQ,YAAY,kBAAkB,CAAC,cAAc,EAAE;IACzD,OAAO,IAAI,YAAY,SAAS,WAAW,EAAE;QAC3C,IAAI,OAAO,SAAS,EAAE;YACpB,IAAI,WAAW,SAAS,WAAW;YACnC,IAAI,YAAY,SAAS,YAAY,CAAC;YACtC,IAAI,eAAe,UAAU,GAAG;YAChC,IAAI,cAAc,SAAS,GAAG;YAC9B,IAAI,iBAAiB,iBAAiB,OAAO,iBAAiB,WAAW,IAAI;YAC7E,IAAI,UAAU,KAAK,YAAY,CAAC;YAChC,IAAI,cAAc,EAAE;YACpB,WAAW,CAAC,eAAe,GAAG,KAAK,GAAG,CAAC,SAAS;YAChD,WAAW,CAAC,IAAI,eAAe,GAAG,KAAK,GAAG,CAAC,KAAK,kBAAkB,CAAC,yBAAyB;YAC5F,QAAQ,SAAS,WAAW,CAAC,gBAAgB,EAAE;QACjD,OAAO;YACL,QAAQ,SAAS,WAAW,CAAC,KAAK,SAAS,CAAC,CAAA,GAAA,8IAAA,CAAA,MAAU,AAAD,EAAE,SAAS,UAAU,EAAE,SAAU,GAAG;gBACvF,OAAO,KAAK,YAAY,CAAC;YAC3B,IAAI,eAAe,EAAE;QACvB;IACF,OAAO,IAAI,IAAI;QACb,4BAA4B;QAC5B,IAAI,OAAO,GAAG,eAAe,GAAG,KAAK;QACrC,KAAK,cAAc,CAAC,GAAG,SAAS;QAChC,QAAQ;YAAC,KAAK,CAAC,GAAG,KAAK,KAAK,GAAG;YAAG,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG;SAAE;IAC7D;IACA,OAAO;QACL,OAAO;QACP,IAAI;IACN;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1572, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axisPointer/axisTrigger.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { makeInner } from '../../util/model.js';\nimport * as modelHelper from './modelHelper.js';\nimport findPointFromSeries from './findPointFromSeries.js';\nimport { each, curry, bind, extend } from 'zrender/lib/core/util.js';\nvar inner = makeInner();\n/**\r\n * Basic logic: check all axis, if they do not demand show/highlight,\r\n * then hide/downplay them.\r\n *\r\n * @return content of event obj for echarts.connect.\r\n */\nexport default function axisTrigger(payload, ecModel, api) {\n  var currTrigger = payload.currTrigger;\n  var point = [payload.x, payload.y];\n  var finder = payload;\n  var dispatchAction = payload.dispatchAction || bind(api.dispatchAction, api);\n  var coordSysAxesInfo = ecModel.getComponent('axisPointer').coordSysAxesInfo;\n  // Pending\n  // See #6121. But we are not able to reproduce it yet.\n  if (!coordSysAxesInfo) {\n    return;\n  }\n  if (illegalPoint(point)) {\n    // Used in the default behavior of `connection`: use the sample seriesIndex\n    // and dataIndex. And also used in the tooltipView trigger.\n    point = findPointFromSeries({\n      seriesIndex: finder.seriesIndex,\n      // Do not use dataIndexInside from other ec instance.\n      // FIXME: auto detect it?\n      dataIndex: finder.dataIndex\n    }, ecModel).point;\n  }\n  var isIllegalPoint = illegalPoint(point);\n  // Axis and value can be specified when calling dispatchAction({type: 'updateAxisPointer'}).\n  // Notice: In this case, it is difficult to get the `point` (which is necessary to show\n  // tooltip, so if point is not given, we just use the point found by sample seriesIndex\n  // and dataIndex.\n  var inputAxesInfo = finder.axesInfo;\n  var axesInfo = coordSysAxesInfo.axesInfo;\n  var shouldHide = currTrigger === 'leave' || illegalPoint(point);\n  var outputPayload = {};\n  var showValueMap = {};\n  var dataByCoordSys = {\n    list: [],\n    map: {}\n  };\n  var updaters = {\n    showPointer: curry(showPointer, showValueMap),\n    showTooltip: curry(showTooltip, dataByCoordSys)\n  };\n  // Process for triggered axes.\n  each(coordSysAxesInfo.coordSysMap, function (coordSys, coordSysKey) {\n    // If a point given, it must be contained by the coordinate system.\n    var coordSysContainsPoint = isIllegalPoint || coordSys.containPoint(point);\n    each(coordSysAxesInfo.coordSysAxesInfo[coordSysKey], function (axisInfo, key) {\n      var axis = axisInfo.axis;\n      var inputAxisInfo = findInputAxisInfo(inputAxesInfo, axisInfo);\n      // If no inputAxesInfo, no axis is restricted.\n      if (!shouldHide && coordSysContainsPoint && (!inputAxesInfo || inputAxisInfo)) {\n        var val = inputAxisInfo && inputAxisInfo.value;\n        if (val == null && !isIllegalPoint) {\n          val = axis.pointToData(point);\n        }\n        val != null && processOnAxis(axisInfo, val, updaters, false, outputPayload);\n      }\n    });\n  });\n  // Process for linked axes.\n  var linkTriggers = {};\n  each(axesInfo, function (tarAxisInfo, tarKey) {\n    var linkGroup = tarAxisInfo.linkGroup;\n    // If axis has been triggered in the previous stage, it should not be triggered by link.\n    if (linkGroup && !showValueMap[tarKey]) {\n      each(linkGroup.axesInfo, function (srcAxisInfo, srcKey) {\n        var srcValItem = showValueMap[srcKey];\n        // If srcValItem exist, source axis is triggered, so link to target axis.\n        if (srcAxisInfo !== tarAxisInfo && srcValItem) {\n          var val = srcValItem.value;\n          linkGroup.mapper && (val = tarAxisInfo.axis.scale.parse(linkGroup.mapper(val, makeMapperParam(srcAxisInfo), makeMapperParam(tarAxisInfo))));\n          linkTriggers[tarAxisInfo.key] = val;\n        }\n      });\n    }\n  });\n  each(linkTriggers, function (val, tarKey) {\n    processOnAxis(axesInfo[tarKey], val, updaters, true, outputPayload);\n  });\n  updateModelActually(showValueMap, axesInfo, outputPayload);\n  dispatchTooltipActually(dataByCoordSys, point, payload, dispatchAction);\n  dispatchHighDownActually(axesInfo, dispatchAction, api);\n  return outputPayload;\n}\nfunction processOnAxis(axisInfo, newValue, updaters, noSnap, outputFinder) {\n  var axis = axisInfo.axis;\n  if (axis.scale.isBlank() || !axis.containData(newValue)) {\n    return;\n  }\n  if (!axisInfo.involveSeries) {\n    updaters.showPointer(axisInfo, newValue);\n    return;\n  }\n  // Heavy calculation. So put it after axis.containData checking.\n  var payloadInfo = buildPayloadsBySeries(newValue, axisInfo);\n  var payloadBatch = payloadInfo.payloadBatch;\n  var snapToValue = payloadInfo.snapToValue;\n  // Fill content of event obj for echarts.connect.\n  // By default use the first involved series data as a sample to connect.\n  if (payloadBatch[0] && outputFinder.seriesIndex == null) {\n    extend(outputFinder, payloadBatch[0]);\n  }\n  // If no linkSource input, this process is for collecting link\n  // target, where snap should not be accepted.\n  if (!noSnap && axisInfo.snap) {\n    if (axis.containData(snapToValue) && snapToValue != null) {\n      newValue = snapToValue;\n    }\n  }\n  updaters.showPointer(axisInfo, newValue, payloadBatch);\n  // Tooltip should always be snapToValue, otherwise there will be\n  // incorrect \"axis value ~ series value\" mapping displayed in tooltip.\n  updaters.showTooltip(axisInfo, payloadInfo, snapToValue);\n}\nfunction buildPayloadsBySeries(value, axisInfo) {\n  var axis = axisInfo.axis;\n  var dim = axis.dim;\n  var snapToValue = value;\n  var payloadBatch = [];\n  var minDist = Number.MAX_VALUE;\n  var minDiff = -1;\n  each(axisInfo.seriesModels, function (series, idx) {\n    var dataDim = series.getData().mapDimensionsAll(dim);\n    var seriesNestestValue;\n    var dataIndices;\n    if (series.getAxisTooltipData) {\n      var result = series.getAxisTooltipData(dataDim, value, axis);\n      dataIndices = result.dataIndices;\n      seriesNestestValue = result.nestestValue;\n    } else {\n      dataIndices = series.getData().indicesOfNearest(dataDim[0], value,\n      // Add a threshold to avoid find the wrong dataIndex\n      // when data length is not same.\n      // false,\n      axis.type === 'category' ? 0.5 : null);\n      if (!dataIndices.length) {\n        return;\n      }\n      seriesNestestValue = series.getData().get(dataDim[0], dataIndices[0]);\n    }\n    if (seriesNestestValue == null || !isFinite(seriesNestestValue)) {\n      return;\n    }\n    var diff = value - seriesNestestValue;\n    var dist = Math.abs(diff);\n    // Consider category case\n    if (dist <= minDist) {\n      if (dist < minDist || diff >= 0 && minDiff < 0) {\n        minDist = dist;\n        minDiff = diff;\n        snapToValue = seriesNestestValue;\n        payloadBatch.length = 0;\n      }\n      each(dataIndices, function (dataIndex) {\n        payloadBatch.push({\n          seriesIndex: series.seriesIndex,\n          dataIndexInside: dataIndex,\n          dataIndex: series.getData().getRawIndex(dataIndex)\n        });\n      });\n    }\n  });\n  return {\n    payloadBatch: payloadBatch,\n    snapToValue: snapToValue\n  };\n}\nfunction showPointer(showValueMap, axisInfo, value, payloadBatch) {\n  showValueMap[axisInfo.key] = {\n    value: value,\n    payloadBatch: payloadBatch\n  };\n}\nfunction showTooltip(dataByCoordSys, axisInfo, payloadInfo, value) {\n  var payloadBatch = payloadInfo.payloadBatch;\n  var axis = axisInfo.axis;\n  var axisModel = axis.model;\n  var axisPointerModel = axisInfo.axisPointerModel;\n  // If no data, do not create anything in dataByCoordSys,\n  // whose length will be used to judge whether dispatch action.\n  if (!axisInfo.triggerTooltip || !payloadBatch.length) {\n    return;\n  }\n  var coordSysModel = axisInfo.coordSys.model;\n  var coordSysKey = modelHelper.makeKey(coordSysModel);\n  var coordSysItem = dataByCoordSys.map[coordSysKey];\n  if (!coordSysItem) {\n    coordSysItem = dataByCoordSys.map[coordSysKey] = {\n      coordSysId: coordSysModel.id,\n      coordSysIndex: coordSysModel.componentIndex,\n      coordSysType: coordSysModel.type,\n      coordSysMainType: coordSysModel.mainType,\n      dataByAxis: []\n    };\n    dataByCoordSys.list.push(coordSysItem);\n  }\n  coordSysItem.dataByAxis.push({\n    axisDim: axis.dim,\n    axisIndex: axisModel.componentIndex,\n    axisType: axisModel.type,\n    axisId: axisModel.id,\n    value: value,\n    // Caustion: viewHelper.getValueLabel is actually on \"view stage\", which\n    // depends that all models have been updated. So it should not be performed\n    // here. Considering axisPointerModel used here is volatile, which is hard\n    // to be retrieve in TooltipView, we prepare parameters here.\n    valueLabelOpt: {\n      precision: axisPointerModel.get(['label', 'precision']),\n      formatter: axisPointerModel.get(['label', 'formatter'])\n    },\n    seriesDataIndices: payloadBatch.slice()\n  });\n}\nfunction updateModelActually(showValueMap, axesInfo, outputPayload) {\n  var outputAxesInfo = outputPayload.axesInfo = [];\n  // Basic logic: If no 'show' required, 'hide' this axisPointer.\n  each(axesInfo, function (axisInfo, key) {\n    var option = axisInfo.axisPointerModel.option;\n    var valItem = showValueMap[key];\n    if (valItem) {\n      !axisInfo.useHandle && (option.status = 'show');\n      option.value = valItem.value;\n      // For label formatter param and highlight.\n      option.seriesDataIndices = (valItem.payloadBatch || []).slice();\n    }\n    // When always show (e.g., handle used), remain\n    // original value and status.\n    else {\n      // If hide, value still need to be set, consider\n      // click legend to toggle axis blank.\n      !axisInfo.useHandle && (option.status = 'hide');\n    }\n    // If status is 'hide', should be no info in payload.\n    option.status === 'show' && outputAxesInfo.push({\n      axisDim: axisInfo.axis.dim,\n      axisIndex: axisInfo.axis.model.componentIndex,\n      value: option.value\n    });\n  });\n}\nfunction dispatchTooltipActually(dataByCoordSys, point, payload, dispatchAction) {\n  // Basic logic: If no showTip required, hideTip will be dispatched.\n  if (illegalPoint(point) || !dataByCoordSys.list.length) {\n    dispatchAction({\n      type: 'hideTip'\n    });\n    return;\n  }\n  // In most case only one axis (or event one series is used). It is\n  // convenient to fetch payload.seriesIndex and payload.dataIndex\n  // directly. So put the first seriesIndex and dataIndex of the first\n  // axis on the payload.\n  var sampleItem = ((dataByCoordSys.list[0].dataByAxis[0] || {}).seriesDataIndices || [])[0] || {};\n  dispatchAction({\n    type: 'showTip',\n    escapeConnect: true,\n    x: point[0],\n    y: point[1],\n    tooltipOption: payload.tooltipOption,\n    position: payload.position,\n    dataIndexInside: sampleItem.dataIndexInside,\n    dataIndex: sampleItem.dataIndex,\n    seriesIndex: sampleItem.seriesIndex,\n    dataByCoordSys: dataByCoordSys.list\n  });\n}\nfunction dispatchHighDownActually(axesInfo, dispatchAction, api) {\n  // FIXME\n  // highlight status modification should be a stage of main process?\n  // (Consider confilct (e.g., legend and axisPointer) and setOption)\n  var zr = api.getZr();\n  var highDownKey = 'axisPointerLastHighlights';\n  var lastHighlights = inner(zr)[highDownKey] || {};\n  var newHighlights = inner(zr)[highDownKey] = {};\n  // Update highlight/downplay status according to axisPointer model.\n  // Build hash map and remove duplicate incidentally.\n  each(axesInfo, function (axisInfo, key) {\n    var option = axisInfo.axisPointerModel.option;\n    option.status === 'show' && axisInfo.triggerEmphasis && each(option.seriesDataIndices, function (batchItem) {\n      var key = batchItem.seriesIndex + ' | ' + batchItem.dataIndex;\n      newHighlights[key] = batchItem;\n    });\n  });\n  // Diff.\n  var toHighlight = [];\n  var toDownplay = [];\n  each(lastHighlights, function (batchItem, key) {\n    !newHighlights[key] && toDownplay.push(batchItem);\n  });\n  each(newHighlights, function (batchItem, key) {\n    !lastHighlights[key] && toHighlight.push(batchItem);\n  });\n  toDownplay.length && api.dispatchAction({\n    type: 'downplay',\n    escapeConnect: true,\n    // Not blur others when highlight in axisPointer.\n    notBlur: true,\n    batch: toDownplay\n  });\n  toHighlight.length && api.dispatchAction({\n    type: 'highlight',\n    escapeConnect: true,\n    // Not blur others when highlight in axisPointer.\n    notBlur: true,\n    batch: toHighlight\n  });\n}\nfunction findInputAxisInfo(inputAxesInfo, axisInfo) {\n  for (var i = 0; i < (inputAxesInfo || []).length; i++) {\n    var inputAxisInfo = inputAxesInfo[i];\n    if (axisInfo.axis.dim === inputAxisInfo.axisDim && axisInfo.axis.model.componentIndex === inputAxisInfo.axisIndex) {\n      return inputAxisInfo;\n    }\n  }\n}\nfunction makeMapperParam(axisInfo) {\n  var axisModel = axisInfo.axis.model;\n  var item = {};\n  var dim = item.axisDim = axisInfo.axis.dim;\n  item.axisIndex = item[dim + 'AxisIndex'] = axisModel.componentIndex;\n  item.axisName = item[dim + 'AxisName'] = axisModel.name;\n  item.axisId = item[dim + 'AxisId'] = axisModel.id;\n  return item;\n}\nfunction illegalPoint(point) {\n  return !point || point[0] == null || isNaN(point[0]) || point[1] == null || isNaN(point[1]);\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;;;;;AACA,IAAI,QAAQ,CAAA,GAAA,+IAAA,CAAA,YAAS,AAAD;AAOL,SAAS,YAAY,OAAO,EAAE,OAAO,EAAE,GAAG;IACvD,IAAI,cAAc,QAAQ,WAAW;IACrC,IAAI,QAAQ;QAAC,QAAQ,CAAC;QAAE,QAAQ,CAAC;KAAC;IAClC,IAAI,SAAS;IACb,IAAI,iBAAiB,QAAQ,cAAc,IAAI,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,IAAI,cAAc,EAAE;IACxE,IAAI,mBAAmB,QAAQ,YAAY,CAAC,eAAe,gBAAgB;IAC3E,UAAU;IACV,sDAAsD;IACtD,IAAI,CAAC,kBAAkB;QACrB;IACF;IACA,IAAI,aAAa,QAAQ;QACvB,2EAA2E;QAC3E,2DAA2D;QAC3D,QAAQ,CAAA,GAAA,iLAAA,CAAA,UAAmB,AAAD,EAAE;YAC1B,aAAa,OAAO,WAAW;YAC/B,qDAAqD;YACrD,yBAAyB;YACzB,WAAW,OAAO,SAAS;QAC7B,GAAG,SAAS,KAAK;IACnB;IACA,IAAI,iBAAiB,aAAa;IAClC,4FAA4F;IAC5F,uFAAuF;IACvF,uFAAuF;IACvF,iBAAiB;IACjB,IAAI,gBAAgB,OAAO,QAAQ;IACnC,IAAI,WAAW,iBAAiB,QAAQ;IACxC,IAAI,aAAa,gBAAgB,WAAW,aAAa;IACzD,IAAI,gBAAgB,CAAC;IACrB,IAAI,eAAe,CAAC;IACpB,IAAI,iBAAiB;QACnB,MAAM,EAAE;QACR,KAAK,CAAC;IACR;IACA,IAAI,WAAW;QACb,aAAa,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE,aAAa;QAChC,aAAa,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE,aAAa;IAClC;IACA,8BAA8B;IAC9B,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,iBAAiB,WAAW,EAAE,SAAU,QAAQ,EAAE,WAAW;QAChE,mEAAmE;QACnE,IAAI,wBAAwB,kBAAkB,SAAS,YAAY,CAAC;QACpE,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,iBAAiB,gBAAgB,CAAC,YAAY,EAAE,SAAU,QAAQ,EAAE,GAAG;YAC1E,IAAI,OAAO,SAAS,IAAI;YACxB,IAAI,gBAAgB,kBAAkB,eAAe;YACrD,8CAA8C;YAC9C,IAAI,CAAC,cAAc,yBAAyB,CAAC,CAAC,iBAAiB,aAAa,GAAG;gBAC7E,IAAI,MAAM,iBAAiB,cAAc,KAAK;gBAC9C,IAAI,OAAO,QAAQ,CAAC,gBAAgB;oBAClC,MAAM,KAAK,WAAW,CAAC;gBACzB;gBACA,OAAO,QAAQ,cAAc,UAAU,KAAK,UAAU,OAAO;YAC/D;QACF;IACF;IACA,2BAA2B;IAC3B,IAAI,eAAe,CAAC;IACpB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,UAAU,SAAU,WAAW,EAAE,MAAM;QAC1C,IAAI,YAAY,YAAY,SAAS;QACrC,wFAAwF;QACxF,IAAI,aAAa,CAAC,YAAY,CAAC,OAAO,EAAE;YACtC,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,UAAU,QAAQ,EAAE,SAAU,WAAW,EAAE,MAAM;gBACpD,IAAI,aAAa,YAAY,CAAC,OAAO;gBACrC,yEAAyE;gBACzE,IAAI,gBAAgB,eAAe,YAAY;oBAC7C,IAAI,MAAM,WAAW,KAAK;oBAC1B,UAAU,MAAM,IAAI,CAAC,MAAM,YAAY,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,UAAU,MAAM,CAAC,KAAK,gBAAgB,cAAc,gBAAgB,cAAc;oBAC1I,YAAY,CAAC,YAAY,GAAG,CAAC,GAAG;gBAClC;YACF;QACF;IACF;IACA,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,cAAc,SAAU,GAAG,EAAE,MAAM;QACtC,cAAc,QAAQ,CAAC,OAAO,EAAE,KAAK,UAAU,MAAM;IACvD;IACA,oBAAoB,cAAc,UAAU;IAC5C,wBAAwB,gBAAgB,OAAO,SAAS;IACxD,yBAAyB,UAAU,gBAAgB;IACnD,OAAO;AACT;AACA,SAAS,cAAc,QAAQ,EAAE,QAAQ,EAAE,QAAQ,EAAE,MAAM,EAAE,YAAY;IACvE,IAAI,OAAO,SAAS,IAAI;IACxB,IAAI,KAAK,KAAK,CAAC,OAAO,MAAM,CAAC,KAAK,WAAW,CAAC,WAAW;QACvD;IACF;IACA,IAAI,CAAC,SAAS,aAAa,EAAE;QAC3B,SAAS,WAAW,CAAC,UAAU;QAC/B;IACF;IACA,gEAAgE;IAChE,IAAI,cAAc,sBAAsB,UAAU;IAClD,IAAI,eAAe,YAAY,YAAY;IAC3C,IAAI,cAAc,YAAY,WAAW;IACzC,iDAAiD;IACjD,wEAAwE;IACxE,IAAI,YAAY,CAAC,EAAE,IAAI,aAAa,WAAW,IAAI,MAAM;QACvD,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,cAAc,YAAY,CAAC,EAAE;IACtC;IACA,8DAA8D;IAC9D,6CAA6C;IAC7C,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE;QAC5B,IAAI,KAAK,WAAW,CAAC,gBAAgB,eAAe,MAAM;YACxD,WAAW;QACb;IACF;IACA,SAAS,WAAW,CAAC,UAAU,UAAU;IACzC,gEAAgE;IAChE,sEAAsE;IACtE,SAAS,WAAW,CAAC,UAAU,aAAa;AAC9C;AACA,SAAS,sBAAsB,KAAK,EAAE,QAAQ;IAC5C,IAAI,OAAO,SAAS,IAAI;IACxB,IAAI,MAAM,KAAK,GAAG;IAClB,IAAI,cAAc;IAClB,IAAI,eAAe,EAAE;IACrB,IAAI,UAAU,OAAO,SAAS;IAC9B,IAAI,UAAU,CAAC;IACf,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,SAAS,YAAY,EAAE,SAAU,MAAM,EAAE,GAAG;QAC/C,IAAI,UAAU,OAAO,OAAO,GAAG,gBAAgB,CAAC;QAChD,IAAI;QACJ,IAAI;QACJ,IAAI,OAAO,kBAAkB,EAAE;YAC7B,IAAI,SAAS,OAAO,kBAAkB,CAAC,SAAS,OAAO;YACvD,cAAc,OAAO,WAAW;YAChC,qBAAqB,OAAO,YAAY;QAC1C,OAAO;YACL,cAAc,OAAO,OAAO,GAAG,gBAAgB,CAAC,OAAO,CAAC,EAAE,EAAE,OAC5D,oDAAoD;YACpD,gCAAgC;YAChC,SAAS;YACT,KAAK,IAAI,KAAK,aAAa,MAAM;YACjC,IAAI,CAAC,YAAY,MAAM,EAAE;gBACvB;YACF;YACA,qBAAqB,OAAO,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE;QACtE;QACA,IAAI,sBAAsB,QAAQ,CAAC,SAAS,qBAAqB;YAC/D;QACF;QACA,IAAI,OAAO,QAAQ;QACnB,IAAI,OAAO,KAAK,GAAG,CAAC;QACpB,yBAAyB;QACzB,IAAI,QAAQ,SAAS;YACnB,IAAI,OAAO,WAAW,QAAQ,KAAK,UAAU,GAAG;gBAC9C,UAAU;gBACV,UAAU;gBACV,cAAc;gBACd,aAAa,MAAM,GAAG;YACxB;YACA,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,aAAa,SAAU,SAAS;gBACnC,aAAa,IAAI,CAAC;oBAChB,aAAa,OAAO,WAAW;oBAC/B,iBAAiB;oBACjB,WAAW,OAAO,OAAO,GAAG,WAAW,CAAC;gBAC1C;YACF;QACF;IACF;IACA,OAAO;QACL,cAAc;QACd,aAAa;IACf;AACF;AACA,SAAS,YAAY,YAAY,EAAE,QAAQ,EAAE,KAAK,EAAE,YAAY;IAC9D,YAAY,CAAC,SAAS,GAAG,CAAC,GAAG;QAC3B,OAAO;QACP,cAAc;IAChB;AACF;AACA,SAAS,YAAY,cAAc,EAAE,QAAQ,EAAE,WAAW,EAAE,KAAK;IAC/D,IAAI,eAAe,YAAY,YAAY;IAC3C,IAAI,OAAO,SAAS,IAAI;IACxB,IAAI,YAAY,KAAK,KAAK;IAC1B,IAAI,mBAAmB,SAAS,gBAAgB;IAChD,wDAAwD;IACxD,8DAA8D;IAC9D,IAAI,CAAC,SAAS,cAAc,IAAI,CAAC,aAAa,MAAM,EAAE;QACpD;IACF;IACA,IAAI,gBAAgB,SAAS,QAAQ,CAAC,KAAK;IAC3C,IAAI,cAAc,CAAA,GAAA,yKAAA,CAAA,UAAmB,AAAD,EAAE;IACtC,IAAI,eAAe,eAAe,GAAG,CAAC,YAAY;IAClD,IAAI,CAAC,cAAc;QACjB,eAAe,eAAe,GAAG,CAAC,YAAY,GAAG;YAC/C,YAAY,cAAc,EAAE;YAC5B,eAAe,cAAc,cAAc;YAC3C,cAAc,cAAc,IAAI;YAChC,kBAAkB,cAAc,QAAQ;YACxC,YAAY,EAAE;QAChB;QACA,eAAe,IAAI,CAAC,IAAI,CAAC;IAC3B;IACA,aAAa,UAAU,CAAC,IAAI,CAAC;QAC3B,SAAS,KAAK,GAAG;QACjB,WAAW,UAAU,cAAc;QACnC,UAAU,UAAU,IAAI;QACxB,QAAQ,UAAU,EAAE;QACpB,OAAO;QACP,wEAAwE;QACxE,2EAA2E;QAC3E,0EAA0E;QAC1E,6DAA6D;QAC7D,eAAe;YACb,WAAW,iBAAiB,GAAG,CAAC;gBAAC;gBAAS;aAAY;YACtD,WAAW,iBAAiB,GAAG,CAAC;gBAAC;gBAAS;aAAY;QACxD;QACA,mBAAmB,aAAa,KAAK;IACvC;AACF;AACA,SAAS,oBAAoB,YAAY,EAAE,QAAQ,EAAE,aAAa;IAChE,IAAI,iBAAiB,cAAc,QAAQ,GAAG,EAAE;IAChD,+DAA+D;IAC/D,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,UAAU,SAAU,QAAQ,EAAE,GAAG;QACpC,IAAI,SAAS,SAAS,gBAAgB,CAAC,MAAM;QAC7C,IAAI,UAAU,YAAY,CAAC,IAAI;QAC/B,IAAI,SAAS;YACX,CAAC,SAAS,SAAS,IAAI,CAAC,OAAO,MAAM,GAAG,MAAM;YAC9C,OAAO,KAAK,GAAG,QAAQ,KAAK;YAC5B,2CAA2C;YAC3C,OAAO,iBAAiB,GAAG,CAAC,QAAQ,YAAY,IAAI,EAAE,EAAE,KAAK;QAC/D,OAGK;YACH,gDAAgD;YAChD,qCAAqC;YACrC,CAAC,SAAS,SAAS,IAAI,CAAC,OAAO,MAAM,GAAG,MAAM;QAChD;QACA,qDAAqD;QACrD,OAAO,MAAM,KAAK,UAAU,eAAe,IAAI,CAAC;YAC9C,SAAS,SAAS,IAAI,CAAC,GAAG;YAC1B,WAAW,SAAS,IAAI,CAAC,KAAK,CAAC,cAAc;YAC7C,OAAO,OAAO,KAAK;QACrB;IACF;AACF;AACA,SAAS,wBAAwB,cAAc,EAAE,KAAK,EAAE,OAAO,EAAE,cAAc;IAC7E,mEAAmE;IACnE,IAAI,aAAa,UAAU,CAAC,eAAe,IAAI,CAAC,MAAM,EAAE;QACtD,eAAe;YACb,MAAM;QACR;QACA;IACF;IACA,kEAAkE;IAClE,gEAAgE;IAChE,oEAAoE;IACpE,uBAAuB;IACvB,IAAI,aAAa,CAAC,CAAC,eAAe,IAAI,CAAC,EAAE,CAAC,UAAU,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,iBAAiB,IAAI,EAAE,CAAC,CAAC,EAAE,IAAI,CAAC;IAC/F,eAAe;QACb,MAAM;QACN,eAAe;QACf,GAAG,KAAK,CAAC,EAAE;QACX,GAAG,KAAK,CAAC,EAAE;QACX,eAAe,QAAQ,aAAa;QACpC,UAAU,QAAQ,QAAQ;QAC1B,iBAAiB,WAAW,eAAe;QAC3C,WAAW,WAAW,SAAS;QAC/B,aAAa,WAAW,WAAW;QACnC,gBAAgB,eAAe,IAAI;IACrC;AACF;AACA,SAAS,yBAAyB,QAAQ,EAAE,cAAc,EAAE,GAAG;IAC7D,QAAQ;IACR,mEAAmE;IACnE,mEAAmE;IACnE,IAAI,KAAK,IAAI,KAAK;IAClB,IAAI,cAAc;IAClB,IAAI,iBAAiB,MAAM,GAAG,CAAC,YAAY,IAAI,CAAC;IAChD,IAAI,gBAAgB,MAAM,GAAG,CAAC,YAAY,GAAG,CAAC;IAC9C,mEAAmE;IACnE,oDAAoD;IACpD,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,UAAU,SAAU,QAAQ,EAAE,GAAG;QACpC,IAAI,SAAS,SAAS,gBAAgB,CAAC,MAAM;QAC7C,OAAO,MAAM,KAAK,UAAU,SAAS,eAAe,IAAI,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,OAAO,iBAAiB,EAAE,SAAU,SAAS;YACxG,IAAI,MAAM,UAAU,WAAW,GAAG,QAAQ,UAAU,SAAS;YAC7D,aAAa,CAAC,IAAI,GAAG;QACvB;IACF;IACA,QAAQ;IACR,IAAI,cAAc,EAAE;IACpB,IAAI,aAAa,EAAE;IACnB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB,SAAU,SAAS,EAAE,GAAG;QAC3C,CAAC,aAAa,CAAC,IAAI,IAAI,WAAW,IAAI,CAAC;IACzC;IACA,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,eAAe,SAAU,SAAS,EAAE,GAAG;QAC1C,CAAC,cAAc,CAAC,IAAI,IAAI,YAAY,IAAI,CAAC;IAC3C;IACA,WAAW,MAAM,IAAI,IAAI,cAAc,CAAC;QACtC,MAAM;QACN,eAAe;QACf,iDAAiD;QACjD,SAAS;QACT,OAAO;IACT;IACA,YAAY,MAAM,IAAI,IAAI,cAAc,CAAC;QACvC,MAAM;QACN,eAAe;QACf,iDAAiD;QACjD,SAAS;QACT,OAAO;IACT;AACF;AACA,SAAS,kBAAkB,aAAa,EAAE,QAAQ;IAChD,IAAK,IAAI,IAAI,GAAG,IAAI,CAAC,iBAAiB,EAAE,EAAE,MAAM,EAAE,IAAK;QACrD,IAAI,gBAAgB,aAAa,CAAC,EAAE;QACpC,IAAI,SAAS,IAAI,CAAC,GAAG,KAAK,cAAc,OAAO,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,cAAc,KAAK,cAAc,SAAS,EAAE;YACjH,OAAO;QACT;IACF;AACF;AACA,SAAS,gBAAgB,QAAQ;IAC/B,IAAI,YAAY,SAAS,IAAI,CAAC,KAAK;IACnC,IAAI,OAAO,CAAC;IACZ,IAAI,MAAM,KAAK,OAAO,GAAG,SAAS,IAAI,CAAC,GAAG;IAC1C,KAAK,SAAS,GAAG,IAAI,CAAC,MAAM,YAAY,GAAG,UAAU,cAAc;IACnE,KAAK,QAAQ,GAAG,IAAI,CAAC,MAAM,WAAW,GAAG,UAAU,IAAI;IACvD,KAAK,MAAM,GAAG,IAAI,CAAC,MAAM,SAAS,GAAG,UAAU,EAAE;IACjD,OAAO;AACT;AACA,SAAS,aAAa,KAAK;IACzB,OAAO,CAAC,SAAS,KAAK,CAAC,EAAE,IAAI,QAAQ,MAAM,KAAK,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,IAAI,QAAQ,MAAM,KAAK,CAAC,EAAE;AAC5F", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1955, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axisPointer/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport AxisView from '../axis/AxisView.js';\nimport CartesianAxisPointer from './CartesianAxisPointer.js';\nimport AxisPointerModel from './AxisPointerModel.js';\nimport AxisPointerView from './AxisPointerView.js';\nimport { isArray } from 'zrender/lib/core/util.js';\nimport { collect } from './modelHelper.js';\nimport axisTrigger from './axisTrigger.js';\nexport function install(registers) {\n  // CartesianAxisPointer is not supposed to be required here. But consider\n  // echarts.simple.js and online build tooltip, which only require gridSimple,\n  // CartesianAxisPointer should be able to required somewhere.\n  AxisView.registerAxisPointerClass('CartesianAxisPointer', CartesianAxisPointer);\n  registers.registerComponentModel(AxisPointerModel);\n  registers.registerComponentView(AxisPointerView);\n  registers.registerPreprocessor(function (option) {\n    // Always has a global axisPointerModel for default setting.\n    if (option) {\n      (!option.axisPointer || option.axisPointer.length === 0) && (option.axisPointer = {});\n      var link = option.axisPointer.link;\n      // Normalize to array to avoid object mergin. But if link\n      // is not set, remain null/undefined, otherwise it will\n      // override existent link setting.\n      if (link && !isArray(link)) {\n        option.axisPointer.link = [link];\n      }\n    }\n  });\n  // This process should proformed after coordinate systems created\n  // and series data processed. So put it on statistic processing stage.\n  registers.registerProcessor(registers.PRIORITY.PROCESSOR.STATISTIC, function (ecModel, api) {\n    // Build axisPointerModel, mergin tooltip.axisPointer model for each axis.\n    // allAxesInfo should be updated when setOption performed.\n    ecModel.getComponent('axisPointer').coordSysAxesInfo = collect(ecModel, api);\n  });\n  // Broadcast to all views.\n  registers.registerAction({\n    type: 'updateAxisPointer',\n    event: 'updateAxisPointer',\n    update: ':updateAxisPointer'\n  }, axisTrigger);\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACO,SAAS,QAAQ,SAAS;IAC/B,yEAAyE;IACzE,6EAA6E;IAC7E,6DAA6D;IAC7D,+JAAA,CAAA,UAAQ,CAAC,wBAAwB,CAAC,wBAAwB,kLAAA,CAAA,UAAoB;IAC9E,UAAU,sBAAsB,CAAC,8KAAA,CAAA,UAAgB;IACjD,UAAU,qBAAqB,CAAC,6KAAA,CAAA,UAAe;IAC/C,UAAU,oBAAoB,CAAC,SAAU,MAAM;QAC7C,4DAA4D;QAC5D,IAAI,QAAQ;YACV,CAAC,CAAC,OAAO,WAAW,IAAI,OAAO,WAAW,CAAC,MAAM,KAAK,CAAC,KAAK,CAAC,OAAO,WAAW,GAAG,CAAC,CAAC;YACpF,IAAI,OAAO,OAAO,WAAW,CAAC,IAAI;YAClC,yDAAyD;YACzD,uDAAuD;YACvD,kCAAkC;YAClC,IAAI,QAAQ,CAAC,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,OAAO;gBAC1B,OAAO,WAAW,CAAC,IAAI,GAAG;oBAAC;iBAAK;YAClC;QACF;IACF;IACA,iEAAiE;IACjE,sEAAsE;IACtE,UAAU,iBAAiB,CAAC,UAAU,QAAQ,CAAC,SAAS,CAAC,SAAS,EAAE,SAAU,OAAO,EAAE,GAAG;QACxF,0EAA0E;QAC1E,0DAA0D;QAC1D,QAAQ,YAAY,CAAC,eAAe,gBAAgB,GAAG,CAAA,GAAA,yKAAA,CAAA,UAAO,AAAD,EAAE,SAAS;IAC1E;IACA,0BAA0B;IAC1B,UAAU,cAAc,CAAC;QACvB,MAAM;QACN,OAAO;QACP,QAAQ;IACV,GAAG,yKAAA,CAAA,UAAW;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2050, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axisPointer/PolarAxisPointer.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport BaseAxisPointer from './BaseAxisPointer.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as viewHelper from './viewHelper.js';\nimport * as matrix from 'zrender/lib/core/matrix.js';\nimport AxisBuilder from '../axis/AxisBuilder.js';\nvar PolarAxisPointer = /** @class */function (_super) {\n  __extends(PolarAxisPointer, _super);\n  function PolarAxisPointer() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  /**\r\n   * @override\r\n   */\n  PolarAxisPointer.prototype.makeElOption = function (elOption, value, axisModel, axisPointerModel, api) {\n    var axis = axisModel.axis;\n    if (axis.dim === 'angle') {\n      this.animationThreshold = Math.PI / 18;\n    }\n    var polar = axis.polar;\n    var otherAxis = polar.getOtherAxis(axis);\n    var otherExtent = otherAxis.getExtent();\n    var coordValue = axis.dataToCoord(value);\n    var axisPointerType = axisPointerModel.get('type');\n    if (axisPointerType && axisPointerType !== 'none') {\n      var elStyle = viewHelper.buildElStyle(axisPointerModel);\n      var pointerOption = pointerShapeBuilder[axisPointerType](axis, polar, coordValue, otherExtent);\n      pointerOption.style = elStyle;\n      elOption.graphicKey = pointerOption.type;\n      elOption.pointer = pointerOption;\n    }\n    var labelMargin = axisPointerModel.get(['label', 'margin']);\n    var labelPos = getLabelPosition(value, axisModel, axisPointerModel, polar, labelMargin);\n    viewHelper.buildLabelElOption(elOption, axisModel, axisPointerModel, api, labelPos);\n  };\n  return PolarAxisPointer;\n}(BaseAxisPointer);\n;\nfunction getLabelPosition(value, axisModel, axisPointerModel, polar, labelMargin) {\n  var axis = axisModel.axis;\n  var coord = axis.dataToCoord(value);\n  var axisAngle = polar.getAngleAxis().getExtent()[0];\n  axisAngle = axisAngle / 180 * Math.PI;\n  var radiusExtent = polar.getRadiusAxis().getExtent();\n  var position;\n  var align;\n  var verticalAlign;\n  if (axis.dim === 'radius') {\n    var transform = matrix.create();\n    matrix.rotate(transform, transform, axisAngle);\n    matrix.translate(transform, transform, [polar.cx, polar.cy]);\n    position = graphic.applyTransform([coord, -labelMargin], transform);\n    var labelRotation = axisModel.getModel('axisLabel').get('rotate') || 0;\n    // @ts-ignore\n    var labelLayout = AxisBuilder.innerTextLayout(axisAngle, labelRotation * Math.PI / 180, -1);\n    align = labelLayout.textAlign;\n    verticalAlign = labelLayout.textVerticalAlign;\n  } else {\n    // angle axis\n    var r = radiusExtent[1];\n    position = polar.coordToPoint([r + labelMargin, coord]);\n    var cx = polar.cx;\n    var cy = polar.cy;\n    align = Math.abs(position[0] - cx) / r < 0.3 ? 'center' : position[0] > cx ? 'left' : 'right';\n    verticalAlign = Math.abs(position[1] - cy) / r < 0.3 ? 'middle' : position[1] > cy ? 'top' : 'bottom';\n  }\n  return {\n    position: position,\n    align: align,\n    verticalAlign: verticalAlign\n  };\n}\nvar pointerShapeBuilder = {\n  line: function (axis, polar, coordValue, otherExtent) {\n    return axis.dim === 'angle' ? {\n      type: 'Line',\n      shape: viewHelper.makeLineShape(polar.coordToPoint([otherExtent[0], coordValue]), polar.coordToPoint([otherExtent[1], coordValue]))\n    } : {\n      type: 'Circle',\n      shape: {\n        cx: polar.cx,\n        cy: polar.cy,\n        r: coordValue\n      }\n    };\n  },\n  shadow: function (axis, polar, coordValue, otherExtent) {\n    var bandWidth = Math.max(1, axis.getBandWidth());\n    var radian = Math.PI / 180;\n    return axis.dim === 'angle' ? {\n      type: 'Sector',\n      shape: viewHelper.makeSectorShape(polar.cx, polar.cy, otherExtent[0], otherExtent[1],\n      // In ECharts y is negative if angle is positive\n      (-coordValue - bandWidth / 2) * radian, (-coordValue + bandWidth / 2) * radian)\n    } : {\n      type: 'Sector',\n      shape: viewHelper.makeSectorShape(polar.cx, polar.cy, coordValue - bandWidth / 2, coordValue + bandWidth / 2, 0, Math.PI * 2)\n    };\n  }\n};\nexport default PolarAxisPointer;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;AACA,IAAI,mBAAmB,WAAW,GAAE,SAAU,MAAM;IAClD,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE,kBAAkB;IAC5B,SAAS;QACP,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACjE;IACA;;GAEC,GACD,iBAAiB,SAAS,CAAC,YAAY,GAAG,SAAU,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,gBAAgB,EAAE,GAAG;QACnG,IAAI,OAAO,UAAU,IAAI;QACzB,IAAI,KAAK,GAAG,KAAK,SAAS;YACxB,IAAI,CAAC,kBAAkB,GAAG,KAAK,EAAE,GAAG;QACtC;QACA,IAAI,QAAQ,KAAK,KAAK;QACtB,IAAI,YAAY,MAAM,YAAY,CAAC;QACnC,IAAI,cAAc,UAAU,SAAS;QACrC,IAAI,aAAa,KAAK,WAAW,CAAC;QAClC,IAAI,kBAAkB,iBAAiB,GAAG,CAAC;QAC3C,IAAI,mBAAmB,oBAAoB,QAAQ;YACjD,IAAI,UAAU,CAAA,GAAA,wKAAA,CAAA,eAAuB,AAAD,EAAE;YACtC,IAAI,gBAAgB,mBAAmB,CAAC,gBAAgB,CAAC,MAAM,OAAO,YAAY;YAClF,cAAc,KAAK,GAAG;YACtB,SAAS,UAAU,GAAG,cAAc,IAAI;YACxC,SAAS,OAAO,GAAG;QACrB;QACA,IAAI,cAAc,iBAAiB,GAAG,CAAC;YAAC;YAAS;SAAS;QAC1D,IAAI,WAAW,iBAAiB,OAAO,WAAW,kBAAkB,OAAO;QAC3E,CAAA,GAAA,wKAAA,CAAA,qBAA6B,AAAD,EAAE,UAAU,WAAW,kBAAkB,KAAK;IAC5E;IACA,OAAO;AACT,EAAE,6KAAA,CAAA,UAAe;;AAEjB,SAAS,iBAAiB,KAAK,EAAE,SAAS,EAAE,gBAAgB,EAAE,KAAK,EAAE,WAAW;IAC9E,IAAI,OAAO,UAAU,IAAI;IACzB,IAAI,QAAQ,KAAK,WAAW,CAAC;IAC7B,IAAI,YAAY,MAAM,YAAY,GAAG,SAAS,EAAE,CAAC,EAAE;IACnD,YAAY,YAAY,MAAM,KAAK,EAAE;IACrC,IAAI,eAAe,MAAM,aAAa,GAAG,SAAS;IAClD,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,KAAK,GAAG,KAAK,UAAU;QACzB,IAAI,YAAY,CAAA,GAAA,gJAAA,CAAA,SAAa,AAAD;QAC5B,CAAA,GAAA,gJAAA,CAAA,SAAa,AAAD,EAAE,WAAW,WAAW;QACpC,CAAA,GAAA,gJAAA,CAAA,YAAgB,AAAD,EAAE,WAAW,WAAW;YAAC,MAAM,EAAE;YAAE,MAAM,EAAE;SAAC;QAC3D,WAAW,CAAA,GAAA,iKAAA,CAAA,iBAAsB,AAAD,EAAE;YAAC;YAAO,CAAC;SAAY,EAAE;QACzD,IAAI,gBAAgB,UAAU,QAAQ,CAAC,aAAa,GAAG,CAAC,aAAa;QACrE,aAAa;QACb,IAAI,cAAc,kKAAA,CAAA,UAAW,CAAC,eAAe,CAAC,WAAW,gBAAgB,KAAK,EAAE,GAAG,KAAK,CAAC;QACzF,QAAQ,YAAY,SAAS;QAC7B,gBAAgB,YAAY,iBAAiB;IAC/C,OAAO;QACL,aAAa;QACb,IAAI,IAAI,YAAY,CAAC,EAAE;QACvB,WAAW,MAAM,YAAY,CAAC;YAAC,IAAI;YAAa;SAAM;QACtD,IAAI,KAAK,MAAM,EAAE;QACjB,IAAI,KAAK,MAAM,EAAE;QACjB,QAAQ,KAAK,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,MAAM,IAAI,MAAM,WAAW,QAAQ,CAAC,EAAE,GAAG,KAAK,SAAS;QACtF,gBAAgB,KAAK,GAAG,CAAC,QAAQ,CAAC,EAAE,GAAG,MAAM,IAAI,MAAM,WAAW,QAAQ,CAAC,EAAE,GAAG,KAAK,QAAQ;IAC/F;IACA,OAAO;QACL,UAAU;QACV,OAAO;QACP,eAAe;IACjB;AACF;AACA,IAAI,sBAAsB;IACxB,MAAM,SAAU,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW;QAClD,OAAO,KAAK,GAAG,KAAK,UAAU;YAC5B,MAAM;YACN,OAAO,CAAA,GAAA,wKAAA,CAAA,gBAAwB,AAAD,EAAE,MAAM,YAAY,CAAC;gBAAC,WAAW,CAAC,EAAE;gBAAE;aAAW,GAAG,MAAM,YAAY,CAAC;gBAAC,WAAW,CAAC,EAAE;gBAAE;aAAW;QACnI,IAAI;YACF,MAAM;YACN,OAAO;gBACL,IAAI,MAAM,EAAE;gBACZ,IAAI,MAAM,EAAE;gBACZ,GAAG;YACL;QACF;IACF;IACA,QAAQ,SAAU,IAAI,EAAE,KAAK,EAAE,UAAU,EAAE,WAAW;QACpD,IAAI,YAAY,KAAK,GAAG,CAAC,GAAG,KAAK,YAAY;QAC7C,IAAI,SAAS,KAAK,EAAE,GAAG;QACvB,OAAO,KAAK,GAAG,KAAK,UAAU;YAC5B,MAAM;YACN,OAAO,CAAA,GAAA,wKAAA,CAAA,kBAA0B,AAAD,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,WAAW,CAAC,EAAE,EAAE,WAAW,CAAC,EAAE,EACpF,gDAAgD;YAChD,CAAC,CAAC,aAAa,YAAY,CAAC,IAAI,QAAQ,CAAC,CAAC,aAAa,YAAY,CAAC,IAAI;QAC1E,IAAI;YACF,MAAM;YACN,OAAO,CAAA,GAAA,wKAAA,CAAA,kBAA0B,AAAD,EAAE,MAAM,EAAE,EAAE,MAAM,EAAE,EAAE,aAAa,YAAY,GAAG,aAAa,YAAY,GAAG,GAAG,KAAK,EAAE,GAAG;QAC7H;IACF;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2218, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axisPointer/SingleAxisPointer.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport BaseAxisPointer from './BaseAxisPointer.js';\nimport * as viewHelper from './viewHelper.js';\nimport * as singleAxisHelper from '../../coord/single/singleAxisHelper.js';\nvar XY = ['x', 'y'];\nvar WH = ['width', 'height'];\nvar SingleAxisPointer = /** @class */function (_super) {\n  __extends(SingleAxisPointer, _super);\n  function SingleAxisPointer() {\n    return _super !== null && _super.apply(this, arguments) || this;\n  }\n  /**\r\n   * @override\r\n   */\n  SingleAxisPointer.prototype.makeElOption = function (elOption, value, axisModel, axisPointerModel, api) {\n    var axis = axisModel.axis;\n    var coordSys = axis.coordinateSystem;\n    var otherExtent = getGlobalExtent(coordSys, 1 - getPointDimIndex(axis));\n    var pixelValue = coordSys.dataToPoint(value)[0];\n    var axisPointerType = axisPointerModel.get('type');\n    if (axisPointerType && axisPointerType !== 'none') {\n      var elStyle = viewHelper.buildElStyle(axisPointerModel);\n      var pointerOption = pointerShapeBuilder[axisPointerType](axis, pixelValue, otherExtent);\n      pointerOption.style = elStyle;\n      elOption.graphicKey = pointerOption.type;\n      elOption.pointer = pointerOption;\n    }\n    var layoutInfo = singleAxisHelper.layout(axisModel);\n    viewHelper.buildCartesianSingleLabelElOption(\n    // @ts-ignore\n    value, elOption, layoutInfo, axisModel, axisPointerModel, api);\n  };\n  /**\r\n   * @override\r\n   */\n  SingleAxisPointer.prototype.getHandleTransform = function (value, axisModel, axisPointerModel) {\n    var layoutInfo = singleAxisHelper.layout(axisModel, {\n      labelInside: false\n    });\n    // @ts-ignore\n    layoutInfo.labelMargin = axisPointerModel.get(['handle', 'margin']);\n    var position = viewHelper.getTransformedPosition(axisModel.axis, value, layoutInfo);\n    return {\n      x: position[0],\n      y: position[1],\n      rotation: layoutInfo.rotation + (layoutInfo.labelDirection < 0 ? Math.PI : 0)\n    };\n  };\n  /**\r\n   * @override\r\n   */\n  SingleAxisPointer.prototype.updateHandleTransform = function (transform, delta, axisModel, axisPointerModel) {\n    var axis = axisModel.axis;\n    var coordSys = axis.coordinateSystem;\n    var dimIndex = getPointDimIndex(axis);\n    var axisExtent = getGlobalExtent(coordSys, dimIndex);\n    var currPosition = [transform.x, transform.y];\n    currPosition[dimIndex] += delta[dimIndex];\n    currPosition[dimIndex] = Math.min(axisExtent[1], currPosition[dimIndex]);\n    currPosition[dimIndex] = Math.max(axisExtent[0], currPosition[dimIndex]);\n    var otherExtent = getGlobalExtent(coordSys, 1 - dimIndex);\n    var cursorOtherValue = (otherExtent[1] + otherExtent[0]) / 2;\n    var cursorPoint = [cursorOtherValue, cursorOtherValue];\n    cursorPoint[dimIndex] = currPosition[dimIndex];\n    return {\n      x: currPosition[0],\n      y: currPosition[1],\n      rotation: transform.rotation,\n      cursorPoint: cursorPoint,\n      tooltipOption: {\n        verticalAlign: 'middle'\n      }\n    };\n  };\n  return SingleAxisPointer;\n}(BaseAxisPointer);\nvar pointerShapeBuilder = {\n  line: function (axis, pixelValue, otherExtent) {\n    var targetShape = viewHelper.makeLineShape([pixelValue, otherExtent[0]], [pixelValue, otherExtent[1]], getPointDimIndex(axis));\n    return {\n      type: 'Line',\n      subPixelOptimize: true,\n      shape: targetShape\n    };\n  },\n  shadow: function (axis, pixelValue, otherExtent) {\n    var bandWidth = axis.getBandWidth();\n    var span = otherExtent[1] - otherExtent[0];\n    return {\n      type: 'Rect',\n      shape: viewHelper.makeRectShape([pixelValue - bandWidth / 2, otherExtent[0]], [bandWidth, span], getPointDimIndex(axis))\n    };\n  }\n};\nfunction getPointDimIndex(axis) {\n  return axis.isHorizontal() ? 0 : 1;\n}\nfunction getGlobalExtent(coordSys, dimIndex) {\n  var rect = coordSys.getRect();\n  return [rect[XY[dimIndex]], rect[XY[dimIndex]] + rect[WH[dimIndex]]];\n}\nexport default SingleAxisPointer;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;;;;;AACA,IAAI,KAAK;IAAC;IAAK;CAAI;AACnB,IAAI,KAAK;IAAC;IAAS;CAAS;AAC5B,IAAI,oBAAoB,WAAW,GAAE,SAAU,MAAM;IACnD,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE,mBAAmB;IAC7B,SAAS;QACP,OAAO,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;IACjE;IACA;;GAEC,GACD,kBAAkB,SAAS,CAAC,YAAY,GAAG,SAAU,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,gBAAgB,EAAE,GAAG;QACpG,IAAI,OAAO,UAAU,IAAI;QACzB,IAAI,WAAW,KAAK,gBAAgB;QACpC,IAAI,cAAc,gBAAgB,UAAU,IAAI,iBAAiB;QACjE,IAAI,aAAa,SAAS,WAAW,CAAC,MAAM,CAAC,EAAE;QAC/C,IAAI,kBAAkB,iBAAiB,GAAG,CAAC;QAC3C,IAAI,mBAAmB,oBAAoB,QAAQ;YACjD,IAAI,UAAU,CAAA,GAAA,wKAAA,CAAA,eAAuB,AAAD,EAAE;YACtC,IAAI,gBAAgB,mBAAmB,CAAC,gBAAgB,CAAC,MAAM,YAAY;YAC3E,cAAc,KAAK,GAAG;YACtB,SAAS,UAAU,GAAG,cAAc,IAAI;YACxC,SAAS,OAAO,GAAG;QACrB;QACA,IAAI,aAAa,CAAA,GAAA,qKAAA,CAAA,SAAuB,AAAD,EAAE;QACzC,CAAA,GAAA,wKAAA,CAAA,oCAA4C,AAAD,EAC3C,aAAa;QACb,OAAO,UAAU,YAAY,WAAW,kBAAkB;IAC5D;IACA;;GAEC,GACD,kBAAkB,SAAS,CAAC,kBAAkB,GAAG,SAAU,KAAK,EAAE,SAAS,EAAE,gBAAgB;QAC3F,IAAI,aAAa,CAAA,GAAA,qKAAA,CAAA,SAAuB,AAAD,EAAE,WAAW;YAClD,aAAa;QACf;QACA,aAAa;QACb,WAAW,WAAW,GAAG,iBAAiB,GAAG,CAAC;YAAC;YAAU;SAAS;QAClE,IAAI,WAAW,CAAA,GAAA,wKAAA,CAAA,yBAAiC,AAAD,EAAE,UAAU,IAAI,EAAE,OAAO;QACxE,OAAO;YACL,GAAG,QAAQ,CAAC,EAAE;YACd,GAAG,QAAQ,CAAC,EAAE;YACd,UAAU,WAAW,QAAQ,GAAG,CAAC,WAAW,cAAc,GAAG,IAAI,KAAK,EAAE,GAAG,CAAC;QAC9E;IACF;IACA;;GAEC,GACD,kBAAkB,SAAS,CAAC,qBAAqB,GAAG,SAAU,SAAS,EAAE,KAAK,EAAE,SAAS,EAAE,gBAAgB;QACzG,IAAI,OAAO,UAAU,IAAI;QACzB,IAAI,WAAW,KAAK,gBAAgB;QACpC,IAAI,WAAW,iBAAiB;QAChC,IAAI,aAAa,gBAAgB,UAAU;QAC3C,IAAI,eAAe;YAAC,UAAU,CAAC;YAAE,UAAU,CAAC;SAAC;QAC7C,YAAY,CAAC,SAAS,IAAI,KAAK,CAAC,SAAS;QACzC,YAAY,CAAC,SAAS,GAAG,KAAK,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,YAAY,CAAC,SAAS;QACvE,YAAY,CAAC,SAAS,GAAG,KAAK,GAAG,CAAC,UAAU,CAAC,EAAE,EAAE,YAAY,CAAC,SAAS;QACvE,IAAI,cAAc,gBAAgB,UAAU,IAAI;QAChD,IAAI,mBAAmB,CAAC,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,IAAI;QAC3D,IAAI,cAAc;YAAC;YAAkB;SAAiB;QACtD,WAAW,CAAC,SAAS,GAAG,YAAY,CAAC,SAAS;QAC9C,OAAO;YACL,GAAG,YAAY,CAAC,EAAE;YAClB,GAAG,YAAY,CAAC,EAAE;YAClB,UAAU,UAAU,QAAQ;YAC5B,aAAa;YACb,eAAe;gBACb,eAAe;YACjB;QACF;IACF;IACA,OAAO;AACT,EAAE,6KAAA,CAAA,UAAe;AACjB,IAAI,sBAAsB;IACxB,MAAM,SAAU,IAAI,EAAE,UAAU,EAAE,WAAW;QAC3C,IAAI,cAAc,CAAA,GAAA,wKAAA,CAAA,gBAAwB,AAAD,EAAE;YAAC;YAAY,WAAW,CAAC,EAAE;SAAC,EAAE;YAAC;YAAY,WAAW,CAAC,EAAE;SAAC,EAAE,iBAAiB;QACxH,OAAO;YACL,MAAM;YACN,kBAAkB;YAClB,OAAO;QACT;IACF;IACA,QAAQ,SAAU,IAAI,EAAE,UAAU,EAAE,WAAW;QAC7C,IAAI,YAAY,KAAK,YAAY;QACjC,IAAI,OAAO,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE;QAC1C,OAAO;YACL,MAAM;YACN,OAAO,CAAA,GAAA,wKAAA,CAAA,gBAAwB,AAAD,EAAE;gBAAC,aAAa,YAAY;gBAAG,WAAW,CAAC,EAAE;aAAC,EAAE;gBAAC;gBAAW;aAAK,EAAE,iBAAiB;QACpH;IACF;AACF;AACA,SAAS,iBAAiB,IAAI;IAC5B,OAAO,KAAK,YAAY,KAAK,IAAI;AACnC;AACA,SAAS,gBAAgB,QAAQ,EAAE,QAAQ;IACzC,IAAI,OAAO,SAAS,OAAO;IAC3B,OAAO;QAAC,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC;QAAE,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC,GAAG,IAAI,CAAC,EAAE,CAAC,SAAS,CAAC;KAAC;AACtE;uCACe", "ignoreList": [0], "debugId": null}}]}