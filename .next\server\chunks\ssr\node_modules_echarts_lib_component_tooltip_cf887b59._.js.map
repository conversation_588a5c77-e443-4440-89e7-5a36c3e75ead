{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/tooltip/tooltipMarkup.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { getTooltipMarker, encodeHTML, makeValueReadable, convertToColorString } from '../../util/format.js';\nimport { isString, each, hasOwn, isArray, map, assert, extend } from 'zrender/lib/core/util.js';\nimport { SortOrderComparator } from '../../data/helper/dataValueHelper.js';\nimport { getRandomIdBase } from '../../util/number.js';\nvar TOOLTIP_LINE_HEIGHT_CSS = 'line-height:1';\nfunction getTooltipLineHeight(textStyle) {\n  var lineHeight = textStyle.lineHeight;\n  if (lineHeight == null) {\n    return TOOLTIP_LINE_HEIGHT_CSS;\n  } else {\n    return \"line-height:\" + encodeHTML(lineHeight + '') + \"px\";\n  }\n}\n// TODO: more textStyle option\nfunction getTooltipTextStyle(textStyle, renderMode) {\n  var nameFontColor = textStyle.color || '#6e7079';\n  var nameFontSize = textStyle.fontSize || 12;\n  var nameFontWeight = textStyle.fontWeight || '400';\n  var valueFontColor = textStyle.color || '#464646';\n  var valueFontSize = textStyle.fontSize || 14;\n  var valueFontWeight = textStyle.fontWeight || '900';\n  if (renderMode === 'html') {\n    // `textStyle` is probably from user input, should be encoded to reduce security risk.\n    return {\n      // eslint-disable-next-line max-len\n      nameStyle: \"font-size:\" + encodeHTML(nameFontSize + '') + \"px;color:\" + encodeHTML(nameFontColor) + \";font-weight:\" + encodeHTML(nameFontWeight + ''),\n      // eslint-disable-next-line max-len\n      valueStyle: \"font-size:\" + encodeHTML(valueFontSize + '') + \"px;color:\" + encodeHTML(valueFontColor) + \";font-weight:\" + encodeHTML(valueFontWeight + '')\n    };\n  } else {\n    return {\n      nameStyle: {\n        fontSize: nameFontSize,\n        fill: nameFontColor,\n        fontWeight: nameFontWeight\n      },\n      valueStyle: {\n        fontSize: valueFontSize,\n        fill: valueFontColor,\n        fontWeight: valueFontWeight\n      }\n    };\n  }\n}\n// See `TooltipMarkupLayoutIntent['innerGapLevel']`.\n// (value from UI design)\nvar HTML_GAPS = [0, 10, 20, 30];\nvar RICH_TEXT_GAPS = ['', '\\n', '\\n\\n', '\\n\\n\\n'];\n// eslint-disable-next-line max-len\nexport function createTooltipMarkup(type, option) {\n  option.type = type;\n  return option;\n}\nfunction isSectionFragment(frag) {\n  return frag.type === 'section';\n}\nfunction getBuilder(frag) {\n  return isSectionFragment(frag) ? buildSection : buildNameValue;\n}\nfunction getBlockGapLevel(frag) {\n  if (isSectionFragment(frag)) {\n    var gapLevel_1 = 0;\n    var subBlockLen = frag.blocks.length;\n    var hasInnerGap_1 = subBlockLen > 1 || subBlockLen > 0 && !frag.noHeader;\n    each(frag.blocks, function (subBlock) {\n      var subGapLevel = getBlockGapLevel(subBlock);\n      // If the some of the sub-blocks have some gaps (like 10px) inside, this block\n      // should use a larger gap (like 20px) to distinguish those sub-blocks.\n      if (subGapLevel >= gapLevel_1) {\n        gapLevel_1 = subGapLevel + +(hasInnerGap_1 && (\n        // 0 always can not be readable gap level.\n        !subGapLevel\n        // If no header, always keep the sub gap level. Otherwise\n        // look weird in case `multipleSeries`.\n        || isSectionFragment(subBlock) && !subBlock.noHeader));\n      }\n    });\n    return gapLevel_1;\n  }\n  return 0;\n}\nfunction buildSection(ctx, fragment, topMarginForOuterGap, toolTipTextStyle) {\n  var noHeader = fragment.noHeader;\n  var gaps = getGap(getBlockGapLevel(fragment));\n  var subMarkupTextList = [];\n  var subBlocks = fragment.blocks || [];\n  assert(!subBlocks || isArray(subBlocks));\n  subBlocks = subBlocks || [];\n  var orderMode = ctx.orderMode;\n  if (fragment.sortBlocks && orderMode) {\n    subBlocks = subBlocks.slice();\n    var orderMap = {\n      valueAsc: 'asc',\n      valueDesc: 'desc'\n    };\n    if (hasOwn(orderMap, orderMode)) {\n      var comparator_1 = new SortOrderComparator(orderMap[orderMode], null);\n      subBlocks.sort(function (a, b) {\n        return comparator_1.evaluate(a.sortParam, b.sortParam);\n      });\n    }\n    // FIXME 'seriesDesc' necessary?\n    else if (orderMode === 'seriesDesc') {\n      subBlocks.reverse();\n    }\n  }\n  each(subBlocks, function (subBlock, idx) {\n    var valueFormatter = fragment.valueFormatter;\n    var subMarkupText = getBuilder(subBlock)(\n    // Inherit valueFormatter\n    valueFormatter ? extend(extend({}, ctx), {\n      valueFormatter: valueFormatter\n    }) : ctx, subBlock, idx > 0 ? gaps.html : 0, toolTipTextStyle);\n    subMarkupText != null && subMarkupTextList.push(subMarkupText);\n  });\n  var subMarkupText = ctx.renderMode === 'richText' ? subMarkupTextList.join(gaps.richText) : wrapBlockHTML(toolTipTextStyle, subMarkupTextList.join(''), noHeader ? topMarginForOuterGap : gaps.html);\n  if (noHeader) {\n    return subMarkupText;\n  }\n  var displayableHeader = makeValueReadable(fragment.header, 'ordinal', ctx.useUTC);\n  var nameStyle = getTooltipTextStyle(toolTipTextStyle, ctx.renderMode).nameStyle;\n  var tooltipLineHeight = getTooltipLineHeight(toolTipTextStyle);\n  if (ctx.renderMode === 'richText') {\n    return wrapInlineNameRichText(ctx, displayableHeader, nameStyle) + gaps.richText + subMarkupText;\n  } else {\n    return wrapBlockHTML(toolTipTextStyle, \"<div style=\\\"\" + nameStyle + \";\" + tooltipLineHeight + \";\\\">\" + encodeHTML(displayableHeader) + '</div>' + subMarkupText, topMarginForOuterGap);\n  }\n}\nfunction buildNameValue(ctx, fragment, topMarginForOuterGap, toolTipTextStyle) {\n  var renderMode = ctx.renderMode;\n  var noName = fragment.noName;\n  var noValue = fragment.noValue;\n  var noMarker = !fragment.markerType;\n  var name = fragment.name;\n  var useUTC = ctx.useUTC;\n  var valueFormatter = fragment.valueFormatter || ctx.valueFormatter || function (value) {\n    value = isArray(value) ? value : [value];\n    return map(value, function (val, idx) {\n      return makeValueReadable(val, isArray(valueTypeOption) ? valueTypeOption[idx] : valueTypeOption, useUTC);\n    });\n  };\n  if (noName && noValue) {\n    return;\n  }\n  var markerStr = noMarker ? '' : ctx.markupStyleCreator.makeTooltipMarker(fragment.markerType, fragment.markerColor || '#333', renderMode);\n  var readableName = noName ? '' : makeValueReadable(name, 'ordinal', useUTC);\n  var valueTypeOption = fragment.valueType;\n  var readableValueList = noValue ? [] : valueFormatter(fragment.value, fragment.dataIndex);\n  var valueAlignRight = !noMarker || !noName;\n  // It little weird if only value next to marker but far from marker.\n  var valueCloseToMarker = !noMarker && noName;\n  var _a = getTooltipTextStyle(toolTipTextStyle, renderMode),\n    nameStyle = _a.nameStyle,\n    valueStyle = _a.valueStyle;\n  return renderMode === 'richText' ? (noMarker ? '' : markerStr) + (noName ? '' : wrapInlineNameRichText(ctx, readableName, nameStyle))\n  // Value has commas inside, so use ' ' as delimiter for multiple values.\n  + (noValue ? '' : wrapInlineValueRichText(ctx, readableValueList, valueAlignRight, valueCloseToMarker, valueStyle)) : wrapBlockHTML(toolTipTextStyle, (noMarker ? '' : markerStr) + (noName ? '' : wrapInlineNameHTML(readableName, !noMarker, nameStyle)) + (noValue ? '' : wrapInlineValueHTML(readableValueList, valueAlignRight, valueCloseToMarker, valueStyle)), topMarginForOuterGap);\n}\n/**\r\n * @return markupText. null/undefined means no content.\r\n */\nexport function buildTooltipMarkup(fragment, markupStyleCreator, renderMode, orderMode, useUTC, toolTipTextStyle) {\n  if (!fragment) {\n    return;\n  }\n  var builder = getBuilder(fragment);\n  var ctx = {\n    useUTC: useUTC,\n    renderMode: renderMode,\n    orderMode: orderMode,\n    markupStyleCreator: markupStyleCreator,\n    valueFormatter: fragment.valueFormatter\n  };\n  return builder(ctx, fragment, 0, toolTipTextStyle);\n}\nfunction getGap(gapLevel) {\n  return {\n    html: HTML_GAPS[gapLevel],\n    richText: RICH_TEXT_GAPS[gapLevel]\n  };\n}\nfunction wrapBlockHTML(textStyle, encodedContent, topGap) {\n  var clearfix = '<div style=\"clear:both\"></div>';\n  var marginCSS = \"margin: \" + topGap + \"px 0 0\";\n  var tooltipLineHeight = getTooltipLineHeight(textStyle);\n  return \"<div style=\\\"\" + marginCSS + \";\" + tooltipLineHeight + \";\\\">\" + encodedContent + clearfix + '</div>';\n}\nfunction wrapInlineNameHTML(name, leftHasMarker, style) {\n  var marginCss = leftHasMarker ? 'margin-left:2px' : '';\n  return \"<span style=\\\"\" + style + \";\" + marginCss + \"\\\">\" + encodeHTML(name) + '</span>';\n}\nfunction wrapInlineValueHTML(valueList, alignRight, valueCloseToMarker, style) {\n  // Do not too close to marker, considering there are multiple values separated by spaces.\n  var paddingStr = valueCloseToMarker ? '10px' : '20px';\n  var alignCSS = alignRight ? \"float:right;margin-left:\" + paddingStr : '';\n  valueList = isArray(valueList) ? valueList : [valueList];\n  return \"<span style=\\\"\" + alignCSS + \";\" + style + \"\\\">\"\n  // Value has commas inside, so use '  ' as delimiter for multiple values.\n  + map(valueList, function (value) {\n    return encodeHTML(value);\n  }).join('&nbsp;&nbsp;') + '</span>';\n}\nfunction wrapInlineNameRichText(ctx, name, style) {\n  return ctx.markupStyleCreator.wrapRichTextStyle(name, style);\n}\nfunction wrapInlineValueRichText(ctx, values, alignRight, valueCloseToMarker, style) {\n  var styles = [style];\n  var paddingLeft = valueCloseToMarker ? 10 : 20;\n  alignRight && styles.push({\n    padding: [0, 0, 0, paddingLeft],\n    align: 'right'\n  });\n  // Value has commas inside, so use '  ' as delimiter for multiple values.\n  return ctx.markupStyleCreator.wrapRichTextStyle(isArray(values) ? values.join('  ') : values, styles);\n}\nexport function retrieveVisualColorForTooltipMarker(series, dataIndex) {\n  var style = series.getData().getItemVisual(dataIndex, 'style');\n  var color = style[series.visualDrawType];\n  return convertToColorString(color);\n}\nexport function getPaddingFromTooltipModel(model, renderMode) {\n  var padding = model.get('padding');\n  return padding != null ? padding\n  // We give slightly different to look pretty.\n  : renderMode === 'richText' ? [8, 10] : 10;\n}\n/**\r\n * The major feature is generate styles for `renderMode: 'richText'`.\r\n * But it also serves `renderMode: 'html'` to provide\r\n * \"renderMode-independent\" API.\r\n */\nvar TooltipMarkupStyleCreator = /** @class */function () {\n  function TooltipMarkupStyleCreator() {\n    this.richTextStyles = {};\n    // Notice that \"generate a style name\" usually happens repeatedly when mouse is moving and\n    // a tooltip is displayed. So we put the `_nextStyleNameId` as a member of each creator\n    // rather than static shared by all creators (which will cause it increase to fast).\n    this._nextStyleNameId = getRandomIdBase();\n  }\n  TooltipMarkupStyleCreator.prototype._generateStyleName = function () {\n    return '__EC_aUTo_' + this._nextStyleNameId++;\n  };\n  TooltipMarkupStyleCreator.prototype.makeTooltipMarker = function (markerType, colorStr, renderMode) {\n    var markerId = renderMode === 'richText' ? this._generateStyleName() : null;\n    var marker = getTooltipMarker({\n      color: colorStr,\n      type: markerType,\n      renderMode: renderMode,\n      markerId: markerId\n    });\n    if (isString(marker)) {\n      return marker;\n    } else {\n      if (process.env.NODE_ENV !== 'production') {\n        assert(markerId);\n      }\n      this.richTextStyles[markerId] = marker.style;\n      return marker.content;\n    }\n  };\n  /**\r\n   * @usage\r\n   * ```ts\r\n   * const styledText = markupStyleCreator.wrapRichTextStyle([\r\n   *     // The styles will be auto merged.\r\n   *     {\r\n   *         fontSize: 12,\r\n   *         color: 'blue'\r\n   *     },\r\n   *     {\r\n   *         padding: 20\r\n   *     }\r\n   * ]);\r\n   * ```\r\n   */\n  TooltipMarkupStyleCreator.prototype.wrapRichTextStyle = function (text, styles) {\n    var finalStl = {};\n    if (isArray(styles)) {\n      each(styles, function (stl) {\n        return extend(finalStl, stl);\n      });\n    } else {\n      extend(finalStl, styles);\n    }\n    var styleName = this._generateStyleName();\n    this.richTextStyles[styleName] = finalStl;\n    return \"{\" + styleName + \"|\" + text + \"}\";\n  };\n  return TooltipMarkupStyleCreator;\n}();\nexport { TooltipMarkupStyleCreator };"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;;;;AACA;AAAA;AACA;AACA;AACA;;;;;AACA,IAAI,0BAA0B;AAC9B,SAAS,qBAAqB,SAAS;IACrC,IAAI,aAAa,UAAU,UAAU;IACrC,IAAI,cAAc,MAAM;QACtB,OAAO;IACT,OAAO;QACL,OAAO,iBAAiB,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE,aAAa,MAAM;IACxD;AACF;AACA,8BAA8B;AAC9B,SAAS,oBAAoB,SAAS,EAAE,UAAU;IAChD,IAAI,gBAAgB,UAAU,KAAK,IAAI;IACvC,IAAI,eAAe,UAAU,QAAQ,IAAI;IACzC,IAAI,iBAAiB,UAAU,UAAU,IAAI;IAC7C,IAAI,iBAAiB,UAAU,KAAK,IAAI;IACxC,IAAI,gBAAgB,UAAU,QAAQ,IAAI;IAC1C,IAAI,kBAAkB,UAAU,UAAU,IAAI;IAC9C,IAAI,eAAe,QAAQ;QACzB,sFAAsF;QACtF,OAAO;YACL,mCAAmC;YACnC,WAAW,eAAe,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE,eAAe,MAAM,cAAc,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE,iBAAiB,kBAAkB,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE,iBAAiB;YAClJ,mCAAmC;YACnC,YAAY,eAAe,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE,gBAAgB,MAAM,cAAc,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE,kBAAkB,kBAAkB,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE,kBAAkB;QACxJ;IACF,OAAO;QACL,OAAO;YACL,WAAW;gBACT,UAAU;gBACV,MAAM;gBACN,YAAY;YACd;YACA,YAAY;gBACV,UAAU;gBACV,MAAM;gBACN,YAAY;YACd;QACF;IACF;AACF;AACA,oDAAoD;AACpD,yBAAyB;AACzB,IAAI,YAAY;IAAC;IAAG;IAAI;IAAI;CAAG;AAC/B,IAAI,iBAAiB;IAAC;IAAI;IAAM;IAAQ;CAAS;AAE1C,SAAS,oBAAoB,IAAI,EAAE,MAAM;IAC9C,OAAO,IAAI,GAAG;IACd,OAAO;AACT;AACA,SAAS,kBAAkB,IAAI;IAC7B,OAAO,KAAK,IAAI,KAAK;AACvB;AACA,SAAS,WAAW,IAAI;IACtB,OAAO,kBAAkB,QAAQ,eAAe;AAClD;AACA,SAAS,iBAAiB,IAAI;IAC5B,IAAI,kBAAkB,OAAO;QAC3B,IAAI,aAAa;QACjB,IAAI,cAAc,KAAK,MAAM,CAAC,MAAM;QACpC,IAAI,gBAAgB,cAAc,KAAK,cAAc,KAAK,CAAC,KAAK,QAAQ;QACxE,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,KAAK,MAAM,EAAE,SAAU,QAAQ;YAClC,IAAI,cAAc,iBAAiB;YACnC,8EAA8E;YAC9E,uEAAuE;YACvE,IAAI,eAAe,YAAY;gBAC7B,aAAa,cAAc,CAAC,CAAC,iBAAiB,CAC9C,0CAA0C;gBAC1C,CAAC,eAGE,kBAAkB,aAAa,CAAC,SAAS,QAAQ,CAAC;YACvD;QACF;QACA,OAAO;IACT;IACA,OAAO;AACT;AACA,SAAS,aAAa,GAAG,EAAE,QAAQ,EAAE,oBAAoB,EAAE,gBAAgB;IACzE,IAAI,WAAW,SAAS,QAAQ;IAChC,IAAI,OAAO,OAAO,iBAAiB;IACnC,IAAI,oBAAoB,EAAE;IAC1B,IAAI,YAAY,SAAS,MAAM,IAAI,EAAE;IACrC,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,CAAC,aAAa,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;IAC7B,YAAY,aAAa,EAAE;IAC3B,IAAI,YAAY,IAAI,SAAS;IAC7B,IAAI,SAAS,UAAU,IAAI,WAAW;QACpC,YAAY,UAAU,KAAK;QAC3B,IAAI,WAAW;YACb,UAAU;YACV,WAAW;QACb;QACA,IAAI,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,UAAU,YAAY;YAC/B,IAAI,eAAe,IAAI,mKAAA,CAAA,sBAAmB,CAAC,QAAQ,CAAC,UAAU,EAAE;YAChE,UAAU,IAAI,CAAC,SAAU,CAAC,EAAE,CAAC;gBAC3B,OAAO,aAAa,QAAQ,CAAC,EAAE,SAAS,EAAE,EAAE,SAAS;YACvD;QACF,OAEK,IAAI,cAAc,cAAc;YACnC,UAAU,OAAO;QACnB;IACF;IACA,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,WAAW,SAAU,QAAQ,EAAE,GAAG;QACrC,IAAI,iBAAiB,SAAS,cAAc;QAC5C,IAAI,gBAAgB,WAAW,UAC/B,yBAAyB;QACzB,iBAAiB,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,CAAC,GAAG,MAAM;YACvC,gBAAgB;QAClB,KAAK,KAAK,UAAU,MAAM,IAAI,KAAK,IAAI,GAAG,GAAG;QAC7C,iBAAiB,QAAQ,kBAAkB,IAAI,CAAC;IAClD;IACA,IAAI,gBAAgB,IAAI,UAAU,KAAK,aAAa,kBAAkB,IAAI,CAAC,KAAK,QAAQ,IAAI,cAAc,kBAAkB,kBAAkB,IAAI,CAAC,KAAK,WAAW,uBAAuB,KAAK,IAAI;IACnM,IAAI,UAAU;QACZ,OAAO;IACT;IACA,IAAI,oBAAoB,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,MAAM,EAAE,WAAW,IAAI,MAAM;IAChF,IAAI,YAAY,oBAAoB,kBAAkB,IAAI,UAAU,EAAE,SAAS;IAC/E,IAAI,oBAAoB,qBAAqB;IAC7C,IAAI,IAAI,UAAU,KAAK,YAAY;QACjC,OAAO,uBAAuB,KAAK,mBAAmB,aAAa,KAAK,QAAQ,GAAG;IACrF,OAAO;QACL,OAAO,cAAc,kBAAkB,kBAAkB,YAAY,MAAM,oBAAoB,SAAS,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE,qBAAqB,WAAW,eAAe;IACpK;AACF;AACA,SAAS,eAAe,GAAG,EAAE,QAAQ,EAAE,oBAAoB,EAAE,gBAAgB;IAC3E,IAAI,aAAa,IAAI,UAAU;IAC/B,IAAI,SAAS,SAAS,MAAM;IAC5B,IAAI,UAAU,SAAS,OAAO;IAC9B,IAAI,WAAW,CAAC,SAAS,UAAU;IACnC,IAAI,OAAO,SAAS,IAAI;IACxB,IAAI,SAAS,IAAI,MAAM;IACvB,IAAI,iBAAiB,SAAS,cAAc,IAAI,IAAI,cAAc,IAAI,SAAU,KAAK;QACnF,QAAQ,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,SAAS,QAAQ;YAAC;SAAM;QACxC,OAAO,CAAA,GAAA,8IAAA,CAAA,MAAG,AAAD,EAAE,OAAO,SAAU,GAAG,EAAE,GAAG;YAClC,OAAO,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,mBAAmB,eAAe,CAAC,IAAI,GAAG,iBAAiB;QACnG;IACF;IACA,IAAI,UAAU,SAAS;QACrB;IACF;IACA,IAAI,YAAY,WAAW,KAAK,IAAI,kBAAkB,CAAC,iBAAiB,CAAC,SAAS,UAAU,EAAE,SAAS,WAAW,IAAI,QAAQ;IAC9H,IAAI,eAAe,SAAS,KAAK,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,WAAW;IACpE,IAAI,kBAAkB,SAAS,SAAS;IACxC,IAAI,oBAAoB,UAAU,EAAE,GAAG,eAAe,SAAS,KAAK,EAAE,SAAS,SAAS;IACxF,IAAI,kBAAkB,CAAC,YAAY,CAAC;IACpC,oEAAoE;IACpE,IAAI,qBAAqB,CAAC,YAAY;IACtC,IAAI,KAAK,oBAAoB,kBAAkB,aAC7C,YAAY,GAAG,SAAS,EACxB,aAAa,GAAG,UAAU;IAC5B,OAAO,eAAe,aAAa,CAAC,WAAW,KAAK,SAAS,IAAI,CAAC,SAAS,KAAK,uBAAuB,KAAK,cAAc,UAAU,IAElI,CAAC,UAAU,KAAK,wBAAwB,KAAK,mBAAmB,iBAAiB,oBAAoB,WAAW,IAAI,cAAc,kBAAkB,CAAC,WAAW,KAAK,SAAS,IAAI,CAAC,SAAS,KAAK,mBAAmB,cAAc,CAAC,UAAU,UAAU,IAAI,CAAC,UAAU,KAAK,oBAAoB,mBAAmB,iBAAiB,oBAAoB,WAAW,GAAG;AACzW;AAIO,SAAS,mBAAmB,QAAQ,EAAE,kBAAkB,EAAE,UAAU,EAAE,SAAS,EAAE,MAAM,EAAE,gBAAgB;IAC9G,IAAI,CAAC,UAAU;QACb;IACF;IACA,IAAI,UAAU,WAAW;IACzB,IAAI,MAAM;QACR,QAAQ;QACR,YAAY;QACZ,WAAW;QACX,oBAAoB;QACpB,gBAAgB,SAAS,cAAc;IACzC;IACA,OAAO,QAAQ,KAAK,UAAU,GAAG;AACnC;AACA,SAAS,OAAO,QAAQ;IACtB,OAAO;QACL,MAAM,SAAS,CAAC,SAAS;QACzB,UAAU,cAAc,CAAC,SAAS;IACpC;AACF;AACA,SAAS,cAAc,SAAS,EAAE,cAAc,EAAE,MAAM;IACtD,IAAI,WAAW;IACf,IAAI,YAAY,aAAa,SAAS;IACtC,IAAI,oBAAoB,qBAAqB;IAC7C,OAAO,kBAAkB,YAAY,MAAM,oBAAoB,SAAS,iBAAiB,WAAW;AACtG;AACA,SAAS,mBAAmB,IAAI,EAAE,aAAa,EAAE,KAAK;IACpD,IAAI,YAAY,gBAAgB,oBAAoB;IACpD,OAAO,mBAAmB,QAAQ,MAAM,YAAY,QAAQ,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE,QAAQ;AACjF;AACA,SAAS,oBAAoB,SAAS,EAAE,UAAU,EAAE,kBAAkB,EAAE,KAAK;IAC3E,yFAAyF;IACzF,IAAI,aAAa,qBAAqB,SAAS;IAC/C,IAAI,WAAW,aAAa,6BAA6B,aAAa;IACtE,YAAY,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,aAAa,YAAY;QAAC;KAAU;IACxD,OAAO,mBAAmB,WAAW,MAAM,QAAQ,QAEjD,CAAA,GAAA,8IAAA,CAAA,MAAG,AAAD,EAAE,WAAW,SAAU,KAAK;QAC9B,OAAO,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE;IACpB,GAAG,IAAI,CAAC,kBAAkB;AAC5B;AACA,SAAS,uBAAuB,GAAG,EAAE,IAAI,EAAE,KAAK;IAC9C,OAAO,IAAI,kBAAkB,CAAC,iBAAiB,CAAC,MAAM;AACxD;AACA,SAAS,wBAAwB,GAAG,EAAE,MAAM,EAAE,UAAU,EAAE,kBAAkB,EAAE,KAAK;IACjF,IAAI,SAAS;QAAC;KAAM;IACpB,IAAI,cAAc,qBAAqB,KAAK;IAC5C,cAAc,OAAO,IAAI,CAAC;QACxB,SAAS;YAAC;YAAG;YAAG;YAAG;SAAY;QAC/B,OAAO;IACT;IACA,yEAAyE;IACzE,OAAO,IAAI,kBAAkB,CAAC,iBAAiB,CAAC,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,UAAU,OAAO,IAAI,CAAC,QAAQ,QAAQ;AAChG;AACO,SAAS,oCAAoC,MAAM,EAAE,SAAS;IACnE,IAAI,QAAQ,OAAO,OAAO,GAAG,aAAa,CAAC,WAAW;IACtD,IAAI,QAAQ,KAAK,CAAC,OAAO,cAAc,CAAC;IACxC,OAAO,CAAA,GAAA,gKAAA,CAAA,uBAAoB,AAAD,EAAE;AAC9B;AACO,SAAS,2BAA2B,KAAK,EAAE,UAAU;IAC1D,IAAI,UAAU,MAAM,GAAG,CAAC;IACxB,OAAO,WAAW,OAAO,UAEvB,eAAe,aAAa;QAAC;QAAG;KAAG,GAAG;AAC1C;AACA;;;;CAIC,GACD,IAAI,4BAA4B,WAAW,GAAE;IAC3C,SAAS;QACP,IAAI,CAAC,cAAc,GAAG,CAAC;QACvB,0FAA0F;QAC1F,uFAAuF;QACvF,oFAAoF;QACpF,IAAI,CAAC,gBAAgB,GAAG,CAAA,GAAA,gJAAA,CAAA,kBAAe,AAAD;IACxC;IACA,0BAA0B,SAAS,CAAC,kBAAkB,GAAG;QACvD,OAAO,eAAe,IAAI,CAAC,gBAAgB;IAC7C;IACA,0BAA0B,SAAS,CAAC,iBAAiB,GAAG,SAAU,UAAU,EAAE,QAAQ,EAAE,UAAU;QAChG,IAAI,WAAW,eAAe,aAAa,IAAI,CAAC,kBAAkB,KAAK;QACvE,IAAI,SAAS,CAAA,GAAA,gKAAA,CAAA,mBAAgB,AAAD,EAAE;YAC5B,OAAO;YACP,MAAM;YACN,YAAY;YACZ,UAAU;QACZ;QACA,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;YACpB,OAAO;QACT,OAAO;YACL,wCAA2C;gBACzC,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE;YACT;YACA,IAAI,CAAC,cAAc,CAAC,SAAS,GAAG,OAAO,KAAK;YAC5C,OAAO,OAAO,OAAO;QACvB;IACF;IACA;;;;;;;;;;;;;;GAcC,GACD,0BAA0B,SAAS,CAAC,iBAAiB,GAAG,SAAU,IAAI,EAAE,MAAM;QAC5E,IAAI,WAAW,CAAC;QAChB,IAAI,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,SAAS;YACnB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,SAAU,GAAG;gBACxB,OAAO,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,UAAU;YAC1B;QACF,OAAO;YACL,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,UAAU;QACnB;QACA,IAAI,YAAY,IAAI,CAAC,kBAAkB;QACvC,IAAI,CAAC,cAAc,CAAC,UAAU,GAAG;QACjC,OAAO,MAAM,YAAY,MAAM,OAAO;IACxC;IACA,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/tooltip/seriesFormatTooltip.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { trim, isArray, each, reduce } from 'zrender/lib/core/util.js';\nimport { retrieveVisualColorForTooltipMarker, createTooltipMarkup } from './tooltipMarkup.js';\nimport { retrieveRawValue } from '../../data/helper/dataProvider.js';\nimport { isNameSpecified } from '../../util/model.js';\nexport function defaultSeriesFormatTooltip(opt) {\n  var series = opt.series;\n  var dataIndex = opt.dataIndex;\n  var multipleSeries = opt.multipleSeries;\n  var data = series.getData();\n  var tooltipDims = data.mapDimensionsAll('defaultedTooltip');\n  var tooltipDimLen = tooltipDims.length;\n  var value = series.getRawValue(dataIndex);\n  var isValueArr = isArray(value);\n  var markerColor = retrieveVisualColorForTooltipMarker(series, dataIndex);\n  // Complicated rule for pretty tooltip.\n  var inlineValue;\n  var inlineValueType;\n  var subBlocks;\n  var sortParam;\n  if (tooltipDimLen > 1 || isValueArr && !tooltipDimLen) {\n    var formatArrResult = formatTooltipArrayValue(value, series, dataIndex, tooltipDims, markerColor);\n    inlineValue = formatArrResult.inlineValues;\n    inlineValueType = formatArrResult.inlineValueTypes;\n    subBlocks = formatArrResult.blocks;\n    // Only support tooltip sort by the first inline value. It's enough in most cases.\n    sortParam = formatArrResult.inlineValues[0];\n  } else if (tooltipDimLen) {\n    var dimInfo = data.getDimensionInfo(tooltipDims[0]);\n    sortParam = inlineValue = retrieveRawValue(data, dataIndex, tooltipDims[0]);\n    inlineValueType = dimInfo.type;\n  } else {\n    sortParam = inlineValue = isValueArr ? value[0] : value;\n  }\n  // Do not show generated series name. It might not be readable.\n  var seriesNameSpecified = isNameSpecified(series);\n  var seriesName = seriesNameSpecified && series.name || '';\n  var itemName = data.getName(dataIndex);\n  var inlineName = multipleSeries ? seriesName : itemName;\n  return createTooltipMarkup('section', {\n    header: seriesName,\n    // When series name is not specified, do not show a header line with only '-'.\n    // This case always happens in tooltip.trigger: 'item'.\n    noHeader: multipleSeries || !seriesNameSpecified,\n    sortParam: sortParam,\n    blocks: [createTooltipMarkup('nameValue', {\n      markerType: 'item',\n      markerColor: markerColor,\n      // Do not mix display seriesName and itemName in one tooltip,\n      // which might confuses users.\n      name: inlineName,\n      // name dimension might be auto assigned, where the name might\n      // be not readable. So we check trim here.\n      noName: !trim(inlineName),\n      value: inlineValue,\n      valueType: inlineValueType,\n      dataIndex: dataIndex\n    })].concat(subBlocks || [])\n  });\n}\nfunction formatTooltipArrayValue(value, series, dataIndex, tooltipDims, colorStr) {\n  // check: category-no-encode-has-axis-data in dataset.html\n  var data = series.getData();\n  var isValueMultipleLine = reduce(value, function (isValueMultipleLine, val, idx) {\n    var dimItem = data.getDimensionInfo(idx);\n    return isValueMultipleLine = isValueMultipleLine || dimItem && dimItem.tooltip !== false && dimItem.displayName != null;\n  }, false);\n  var inlineValues = [];\n  var inlineValueTypes = [];\n  var blocks = [];\n  tooltipDims.length ? each(tooltipDims, function (dim) {\n    setEachItem(retrieveRawValue(data, dataIndex, dim), dim);\n  })\n  // By default, all dims is used on tooltip.\n  : each(value, setEachItem);\n  function setEachItem(val, dim) {\n    var dimInfo = data.getDimensionInfo(dim);\n    // If `dimInfo.tooltip` is not set, show tooltip.\n    if (!dimInfo || dimInfo.otherDims.tooltip === false) {\n      return;\n    }\n    if (isValueMultipleLine) {\n      blocks.push(createTooltipMarkup('nameValue', {\n        markerType: 'subItem',\n        markerColor: colorStr,\n        name: dimInfo.displayName,\n        value: val,\n        valueType: dimInfo.type\n      }));\n    } else {\n      inlineValues.push(val);\n      inlineValueTypes.push(dimInfo.type);\n    }\n  }\n  return {\n    inlineValues: inlineValues,\n    inlineValueTypes: inlineValueTypes,\n    blocks: blocks\n  };\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;;;;;AACO,SAAS,2BAA2B,GAAG;IAC5C,IAAI,SAAS,IAAI,MAAM;IACvB,IAAI,YAAY,IAAI,SAAS;IAC7B,IAAI,iBAAiB,IAAI,cAAc;IACvC,IAAI,OAAO,OAAO,OAAO;IACzB,IAAI,cAAc,KAAK,gBAAgB,CAAC;IACxC,IAAI,gBAAgB,YAAY,MAAM;IACtC,IAAI,QAAQ,OAAO,WAAW,CAAC;IAC/B,IAAI,aAAa,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;IACzB,IAAI,cAAc,CAAA,GAAA,uKAAA,CAAA,sCAAmC,AAAD,EAAE,QAAQ;IAC9D,uCAAuC;IACvC,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI,gBAAgB,KAAK,cAAc,CAAC,eAAe;QACrD,IAAI,kBAAkB,wBAAwB,OAAO,QAAQ,WAAW,aAAa;QACrF,cAAc,gBAAgB,YAAY;QAC1C,kBAAkB,gBAAgB,gBAAgB;QAClD,YAAY,gBAAgB,MAAM;QAClC,kFAAkF;QAClF,YAAY,gBAAgB,YAAY,CAAC,EAAE;IAC7C,OAAO,IAAI,eAAe;QACxB,IAAI,UAAU,KAAK,gBAAgB,CAAC,WAAW,CAAC,EAAE;QAClD,YAAY,cAAc,CAAA,GAAA,gKAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,WAAW,WAAW,CAAC,EAAE;QAC1E,kBAAkB,QAAQ,IAAI;IAChC,OAAO;QACL,YAAY,cAAc,aAAa,KAAK,CAAC,EAAE,GAAG;IACpD;IACA,+DAA+D;IAC/D,IAAI,sBAAsB,CAAA,GAAA,+IAAA,CAAA,kBAAe,AAAD,EAAE;IAC1C,IAAI,aAAa,uBAAuB,OAAO,IAAI,IAAI;IACvD,IAAI,WAAW,KAAK,OAAO,CAAC;IAC5B,IAAI,aAAa,iBAAiB,aAAa;IAC/C,OAAO,CAAA,GAAA,uKAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW;QACpC,QAAQ;QACR,8EAA8E;QAC9E,uDAAuD;QACvD,UAAU,kBAAkB,CAAC;QAC7B,WAAW;QACX,QAAQ;YAAC,CAAA,GAAA,uKAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;gBACxC,YAAY;gBACZ,aAAa;gBACb,6DAA6D;gBAC7D,8BAA8B;gBAC9B,MAAM;gBACN,8DAA8D;gBAC9D,0CAA0C;gBAC1C,QAAQ,CAAC,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE;gBACd,OAAO;gBACP,WAAW;gBACX,WAAW;YACb;SAAG,CAAC,MAAM,CAAC,aAAa,EAAE;IAC5B;AACF;AACA,SAAS,wBAAwB,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,WAAW,EAAE,QAAQ;IAC9E,0DAA0D;IAC1D,IAAI,OAAO,OAAO,OAAO;IACzB,IAAI,sBAAsB,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE,OAAO,SAAU,mBAAmB,EAAE,GAAG,EAAE,GAAG;QAC7E,IAAI,UAAU,KAAK,gBAAgB,CAAC;QACpC,OAAO,sBAAsB,uBAAuB,WAAW,QAAQ,OAAO,KAAK,SAAS,QAAQ,WAAW,IAAI;IACrH,GAAG;IACH,IAAI,eAAe,EAAE;IACrB,IAAI,mBAAmB,EAAE;IACzB,IAAI,SAAS,EAAE;IACf,YAAY,MAAM,GAAG,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,aAAa,SAAU,GAAG;QAClD,YAAY,CAAA,GAAA,gKAAA,CAAA,mBAAgB,AAAD,EAAE,MAAM,WAAW,MAAM;IACtD,KAEE,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,OAAO;IACd,SAAS,YAAY,GAAG,EAAE,GAAG;QAC3B,IAAI,UAAU,KAAK,gBAAgB,CAAC;QACpC,iDAAiD;QACjD,IAAI,CAAC,WAAW,QAAQ,SAAS,CAAC,OAAO,KAAK,OAAO;YACnD;QACF;QACA,IAAI,qBAAqB;YACvB,OAAO,IAAI,CAAC,CAAA,GAAA,uKAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa;gBAC3C,YAAY;gBACZ,aAAa;gBACb,MAAM,QAAQ,WAAW;gBACzB,OAAO;gBACP,WAAW,QAAQ,IAAI;YACzB;QACF,OAAO;YACL,aAAa,IAAI,CAAC;YAClB,iBAAiB,IAAI,CAAC,QAAQ,IAAI;QACpC;IACF;IACA,OAAO;QACL,cAAc;QACd,kBAAkB;QAClB,QAAQ;IACV;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 503, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/tooltip/TooltipModel.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport ComponentModel from '../../model/Component.js';\nvar TooltipModel = /** @class */function (_super) {\n  __extends(TooltipModel, _super);\n  function TooltipModel() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TooltipModel.type;\n    return _this;\n  }\n  TooltipModel.type = 'tooltip';\n  TooltipModel.dependencies = ['axisPointer'];\n  TooltipModel.defaultOption = {\n    // zlevel: 0,\n    z: 60,\n    show: true,\n    // tooltip main content\n    showContent: true,\n    // 'trigger' only works on coordinate system.\n    // 'item' | 'axis' | 'none'\n    trigger: 'item',\n    // 'click' | 'mousemove' | 'none'\n    triggerOn: 'mousemove|click',\n    alwaysShowContent: false,\n    displayMode: 'single',\n    renderMode: 'auto',\n    // whether restraint content inside viewRect.\n    // If renderMode: 'richText', default true.\n    // If renderMode: 'html', defaut false (for backward compat).\n    confine: null,\n    showDelay: 0,\n    hideDelay: 100,\n    // Animation transition time, unit is second\n    transitionDuration: 0.4,\n    enterable: false,\n    backgroundColor: '#fff',\n    // box shadow\n    shadowBlur: 10,\n    shadowColor: 'rgba(0, 0, 0, .2)',\n    shadowOffsetX: 1,\n    shadowOffsetY: 2,\n    // tooltip border radius, unit is px, default is 4\n    borderRadius: 4,\n    // tooltip border width, unit is px, default is 0 (no border)\n    borderWidth: 1,\n    // Tooltip inside padding, default is 5 for all direction\n    // Array is allowed to set up, right, bottom, left, same with css\n    // The default value: See `tooltip/tooltipMarkup.ts#getPaddingFromTooltipModel`.\n    padding: null,\n    // Extra css text\n    extraCssText: '',\n    // axis indicator, trigger by axis\n    axisPointer: {\n      // default is line\n      // legal values: 'line' | 'shadow' | 'cross'\n      type: 'line',\n      // Valid when type is line, appoint tooltip line locate on which line. Optional\n      // legal values: 'x' | 'y' | 'angle' | 'radius' | 'auto'\n      // default is 'auto', chose the axis which type is category.\n      // for multiply y axis, cartesian coord chose x axis, polar chose angle axis\n      axis: 'auto',\n      animation: 'auto',\n      animationDurationUpdate: 200,\n      animationEasingUpdate: 'exponentialOut',\n      crossStyle: {\n        color: '#999',\n        width: 1,\n        type: 'dashed',\n        // TODO formatter\n        textStyle: {}\n      }\n      // lineStyle and shadowStyle should not be specified here,\n      // otherwise it will always override those styles on option.axisPointer.\n    },\n    textStyle: {\n      color: '#666',\n      fontSize: 14\n    }\n  };\n  return TooltipModel;\n}(ComponentModel);\nexport default TooltipModel;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;;;AACA,IAAI,eAAe,WAAW,GAAE,SAAU,MAAM;IAC9C,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE,cAAc;IACxB,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,aAAa,IAAI;QAC9B,OAAO;IACT;IACA,aAAa,IAAI,GAAG;IACpB,aAAa,YAAY,GAAG;QAAC;KAAc;IAC3C,aAAa,aAAa,GAAG;QAC3B,aAAa;QACb,GAAG;QACH,MAAM;QACN,uBAAuB;QACvB,aAAa;QACb,6CAA6C;QAC7C,2BAA2B;QAC3B,SAAS;QACT,iCAAiC;QACjC,WAAW;QACX,mBAAmB;QACnB,aAAa;QACb,YAAY;QACZ,6CAA6C;QAC7C,2CAA2C;QAC3C,6DAA6D;QAC7D,SAAS;QACT,WAAW;QACX,WAAW;QACX,4CAA4C;QAC5C,oBAAoB;QACpB,WAAW;QACX,iBAAiB;QACjB,aAAa;QACb,YAAY;QACZ,aAAa;QACb,eAAe;QACf,eAAe;QACf,kDAAkD;QAClD,cAAc;QACd,6DAA6D;QAC7D,aAAa;QACb,yDAAyD;QACzD,iEAAiE;QACjE,gFAAgF;QAChF,SAAS;QACT,iBAAiB;QACjB,cAAc;QACd,kCAAkC;QAClC,aAAa;YACX,kBAAkB;YAClB,4CAA4C;YAC5C,MAAM;YACN,+EAA+E;YAC/E,wDAAwD;YACxD,4DAA4D;YAC5D,4EAA4E;YAC5E,MAAM;YACN,WAAW;YACX,yBAAyB;YACzB,uBAAuB;YACvB,YAAY;gBACV,OAAO;gBACP,OAAO;gBACP,MAAM;gBACN,iBAAiB;gBACjB,WAAW,CAAC;YACd;QAGF;QACA,WAAW;YACT,OAAO;YACP,UAAU;QACZ;IACF;IACA,OAAO;AACT,EAAE,oJAAA,CAAA,UAAc;uCACD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 631, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/tooltip/helper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { toCamelCase } from '../../util/format.js';\nimport env from 'zrender/lib/core/env.js';\n/* global document */\nexport function shouldTooltipConfine(tooltipModel) {\n  var confineOption = tooltipModel.get('confine');\n  return confineOption != null ? !!confineOption\n  // In richText mode, the outside part can not be visible.\n  : tooltipModel.get('renderMode') === 'richText';\n}\nfunction testStyle(styleProps) {\n  if (!env.domSupported) {\n    return;\n  }\n  var style = document.documentElement.style;\n  for (var i = 0, len = styleProps.length; i < len; i++) {\n    if (styleProps[i] in style) {\n      return styleProps[i];\n    }\n  }\n}\nexport var TRANSFORM_VENDOR = testStyle(['transform', 'webkitTransform', 'OTransform', 'MozTransform', 'msTransform']);\nexport var TRANSITION_VENDOR = testStyle(['webkitTransition', 'transition', 'OTransition', 'MozTransition', 'msTransition']);\nexport function toCSSVendorPrefix(styleVendor, styleProp) {\n  if (!styleVendor) {\n    return styleProp;\n  }\n  styleProp = toCamelCase(styleProp, true);\n  var idx = styleVendor.indexOf(styleProp);\n  styleVendor = idx === -1 ? styleProp : \"-\" + styleVendor.slice(0, idx) + \"-\" + styleProp;\n  return styleVendor.toLowerCase();\n}\nexport function getComputedStyle(el, style) {\n  var stl = el.currentStyle || document.defaultView && document.defaultView.getComputedStyle(el);\n  return stl ? style ? stl[style] : stl : null;\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;;;;AACA;AACA;;;AAEO,SAAS,qBAAqB,YAAY;IAC/C,IAAI,gBAAgB,aAAa,GAAG,CAAC;IACrC,OAAO,iBAAiB,OAAO,CAAC,CAAC,gBAE/B,aAAa,GAAG,CAAC,kBAAkB;AACvC;AACA,SAAS,UAAU,UAAU;IAC3B,IAAI,CAAC,6IAAA,CAAA,UAAG,CAAC,YAAY,EAAE;QACrB;IACF;IACA,IAAI,QAAQ,SAAS,eAAe,CAAC,KAAK;IAC1C,IAAK,IAAI,IAAI,GAAG,MAAM,WAAW,MAAM,EAAE,IAAI,KAAK,IAAK;QACrD,IAAI,UAAU,CAAC,EAAE,IAAI,OAAO;YAC1B,OAAO,UAAU,CAAC,EAAE;QACtB;IACF;AACF;AACO,IAAI,mBAAmB,UAAU;IAAC;IAAa;IAAmB;IAAc;IAAgB;CAAc;AAC9G,IAAI,oBAAoB,UAAU;IAAC;IAAoB;IAAc;IAAe;IAAiB;CAAe;AACpH,SAAS,kBAAkB,WAAW,EAAE,SAAS;IACtD,IAAI,CAAC,aAAa;QAChB,OAAO;IACT;IACA,YAAY,CAAA,GAAA,gKAAA,CAAA,cAAW,AAAD,EAAE,WAAW;IACnC,IAAI,MAAM,YAAY,OAAO,CAAC;IAC9B,cAAc,QAAQ,CAAC,IAAI,YAAY,MAAM,YAAY,KAAK,CAAC,GAAG,OAAO,MAAM;IAC/E,OAAO,YAAY,WAAW;AAChC;AACO,SAAS,iBAAiB,EAAE,EAAE,KAAK;IACxC,IAAI,MAAM,GAAG,YAAY,IAAI,SAAS,WAAW,IAAI,SAAS,WAAW,CAAC,gBAAgB,CAAC;IAC3F,OAAO,MAAM,QAAQ,GAAG,CAAC,MAAM,GAAG,MAAM;AAC1C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 726, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/tooltip/TooltipHTMLContent.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { isString, indexOf, each, bind, isFunction, isArray, isDom, retrieve2 } from 'zrender/lib/core/util.js';\nimport { normalizeEvent } from 'zrender/lib/core/event.js';\nimport { transformLocalCoord } from 'zrender/lib/core/dom.js';\nimport env from 'zrender/lib/core/env.js';\nimport { convertToColorString, toCamelCase, normalizeCssArray } from '../../util/format.js';\nimport { shouldTooltipConfine, toCSSVendorPrefix, getComputedStyle, TRANSFORM_VENDOR, TRANSITION_VENDOR } from './helper.js';\nimport { getPaddingFromTooltipModel } from './tooltipMarkup.js';\n/* global document, window */\nvar CSS_TRANSITION_VENDOR = toCSSVendorPrefix(TRANSITION_VENDOR, 'transition');\nvar CSS_TRANSFORM_VENDOR = toCSSVendorPrefix(TRANSFORM_VENDOR, 'transform');\n// eslint-disable-next-line\nvar gCssText = \"position:absolute;display:block;border-style:solid;white-space:nowrap;z-index:9999999;\" + (env.transform3dSupported ? 'will-change:transform;' : '');\nfunction mirrorPos(pos) {\n  pos = pos === 'left' ? 'right' : pos === 'right' ? 'left' : pos === 'top' ? 'bottom' : 'top';\n  return pos;\n}\nfunction assembleArrow(tooltipModel, borderColor, arrowPosition) {\n  if (!isString(arrowPosition) || arrowPosition === 'inside') {\n    return '';\n  }\n  var backgroundColor = tooltipModel.get('backgroundColor');\n  var borderWidth = tooltipModel.get('borderWidth');\n  borderColor = convertToColorString(borderColor);\n  var arrowPos = mirrorPos(arrowPosition);\n  var arrowSize = Math.max(Math.round(borderWidth) * 1.5, 6);\n  var positionStyle = '';\n  var transformStyle = CSS_TRANSFORM_VENDOR + ':';\n  var rotateDeg;\n  if (indexOf(['left', 'right'], arrowPos) > -1) {\n    positionStyle += 'top:50%';\n    transformStyle += \"translateY(-50%) rotate(\" + (rotateDeg = arrowPos === 'left' ? -225 : -45) + \"deg)\";\n  } else {\n    positionStyle += 'left:50%';\n    transformStyle += \"translateX(-50%) rotate(\" + (rotateDeg = arrowPos === 'top' ? 225 : 45) + \"deg)\";\n  }\n  var rotateRadian = rotateDeg * Math.PI / 180;\n  var arrowWH = arrowSize + borderWidth;\n  var rotatedWH = arrowWH * Math.abs(Math.cos(rotateRadian)) + arrowWH * Math.abs(Math.sin(rotateRadian));\n  var arrowOffset = Math.round(((rotatedWH - Math.SQRT2 * borderWidth) / 2 + Math.SQRT2 * borderWidth - (rotatedWH - arrowWH) / 2) * 100) / 100;\n  positionStyle += \";\" + arrowPos + \":-\" + arrowOffset + \"px\";\n  var borderStyle = borderColor + \" solid \" + borderWidth + \"px;\";\n  var styleCss = [\"position:absolute;width:\" + arrowSize + \"px;height:\" + arrowSize + \"px;z-index:-1;\", positionStyle + \";\" + transformStyle + \";\", \"border-bottom:\" + borderStyle, \"border-right:\" + borderStyle, \"background-color:\" + backgroundColor + \";\"];\n  return \"<div style=\\\"\" + styleCss.join('') + \"\\\"></div>\";\n}\nfunction assembleTransition(duration, onlyFade) {\n  var transitionCurve = 'cubic-bezier(0.23,1,0.32,1)';\n  var transitionOption = \" \" + duration / 2 + \"s \" + transitionCurve;\n  var transitionText = \"opacity\" + transitionOption + \",visibility\" + transitionOption;\n  if (!onlyFade) {\n    transitionOption = \" \" + duration + \"s \" + transitionCurve;\n    transitionText += env.transformSupported ? \",\" + CSS_TRANSFORM_VENDOR + transitionOption : \",left\" + transitionOption + \",top\" + transitionOption;\n  }\n  return CSS_TRANSITION_VENDOR + ':' + transitionText;\n}\nfunction assembleTransform(x, y, toString) {\n  // If using float on style, the final width of the dom might\n  // keep changing slightly while mouse move. So `toFixed(0)` them.\n  var x0 = x.toFixed(0) + 'px';\n  var y0 = y.toFixed(0) + 'px';\n  // not support transform, use `left` and `top` instead.\n  if (!env.transformSupported) {\n    return toString ? \"top:\" + y0 + \";left:\" + x0 + \";\" : [['top', y0], ['left', x0]];\n  }\n  // support transform\n  var is3d = env.transform3dSupported;\n  var translate = \"translate\" + (is3d ? '3d' : '') + \"(\" + x0 + \",\" + y0 + (is3d ? ',0' : '') + \")\";\n  return toString ? 'top:0;left:0;' + CSS_TRANSFORM_VENDOR + ':' + translate + ';' : [['top', 0], ['left', 0], [TRANSFORM_VENDOR, translate]];\n}\n/**\r\n * @param {Object} textStyle\r\n * @return {string}\r\n * @inner\r\n */\nfunction assembleFont(textStyleModel) {\n  var cssText = [];\n  var fontSize = textStyleModel.get('fontSize');\n  var color = textStyleModel.getTextColor();\n  color && cssText.push('color:' + color);\n  cssText.push('font:' + textStyleModel.getFont());\n  // @ts-ignore, leave it to the tooltip refactor.\n  var lineHeight = retrieve2(textStyleModel.get('lineHeight'), Math.round(fontSize * 3 / 2));\n  fontSize && cssText.push('line-height:' + lineHeight + 'px');\n  var shadowColor = textStyleModel.get('textShadowColor');\n  var shadowBlur = textStyleModel.get('textShadowBlur') || 0;\n  var shadowOffsetX = textStyleModel.get('textShadowOffsetX') || 0;\n  var shadowOffsetY = textStyleModel.get('textShadowOffsetY') || 0;\n  shadowColor && shadowBlur && cssText.push('text-shadow:' + shadowOffsetX + 'px ' + shadowOffsetY + 'px ' + shadowBlur + 'px ' + shadowColor);\n  each(['decoration', 'align'], function (name) {\n    var val = textStyleModel.get(name);\n    val && cssText.push('text-' + name + ':' + val);\n  });\n  return cssText.join(';');\n}\nfunction assembleCssText(tooltipModel, enableTransition, onlyFade) {\n  var cssText = [];\n  var transitionDuration = tooltipModel.get('transitionDuration');\n  var backgroundColor = tooltipModel.get('backgroundColor');\n  var shadowBlur = tooltipModel.get('shadowBlur');\n  var shadowColor = tooltipModel.get('shadowColor');\n  var shadowOffsetX = tooltipModel.get('shadowOffsetX');\n  var shadowOffsetY = tooltipModel.get('shadowOffsetY');\n  var textStyleModel = tooltipModel.getModel('textStyle');\n  var padding = getPaddingFromTooltipModel(tooltipModel, 'html');\n  var boxShadow = shadowOffsetX + \"px \" + shadowOffsetY + \"px \" + shadowBlur + \"px \" + shadowColor;\n  cssText.push('box-shadow:' + boxShadow);\n  // Animation transition. Do not animate when transitionDuration is 0.\n  enableTransition && transitionDuration && cssText.push(assembleTransition(transitionDuration, onlyFade));\n  if (backgroundColor) {\n    cssText.push('background-color:' + backgroundColor);\n  }\n  // Border style\n  each(['width', 'color', 'radius'], function (name) {\n    var borderName = 'border-' + name;\n    var camelCase = toCamelCase(borderName);\n    var val = tooltipModel.get(camelCase);\n    val != null && cssText.push(borderName + ':' + val + (name === 'color' ? '' : 'px'));\n  });\n  // Text style\n  cssText.push(assembleFont(textStyleModel));\n  // Padding\n  if (padding != null) {\n    cssText.push('padding:' + normalizeCssArray(padding).join('px ') + 'px');\n  }\n  return cssText.join(';') + ';';\n}\n// If not able to make, do not modify the input `out`.\nfunction makeStyleCoord(out, zr, container, zrX, zrY) {\n  var zrPainter = zr && zr.painter;\n  if (container) {\n    var zrViewportRoot = zrPainter && zrPainter.getViewportRoot();\n    if (zrViewportRoot) {\n      // Some APPs might use scale on body, so we support CSS transform here.\n      transformLocalCoord(out, zrViewportRoot, container, zrX, zrY);\n    }\n  } else {\n    out[0] = zrX;\n    out[1] = zrY;\n    // xy should be based on canvas root. But tooltipContent is\n    // the sibling of canvas root. So padding of ec container\n    // should be considered here.\n    var viewportRootOffset = zrPainter && zrPainter.getViewportRootOffset();\n    if (viewportRootOffset) {\n      out[0] += viewportRootOffset.offsetLeft;\n      out[1] += viewportRootOffset.offsetTop;\n    }\n  }\n  out[2] = out[0] / zr.getWidth();\n  out[3] = out[1] / zr.getHeight();\n}\nvar TooltipHTMLContent = /** @class */function () {\n  function TooltipHTMLContent(api, opt) {\n    this._show = false;\n    this._styleCoord = [0, 0, 0, 0];\n    this._enterable = true;\n    this._alwaysShowContent = false;\n    this._firstShow = true;\n    this._longHide = true;\n    if (env.wxa) {\n      return null;\n    }\n    var el = document.createElement('div');\n    // TODO: TYPE\n    el.domBelongToZr = true;\n    this.el = el;\n    var zr = this._zr = api.getZr();\n    var appendTo = opt.appendTo;\n    var container = appendTo && (isString(appendTo) ? document.querySelector(appendTo) : isDom(appendTo) ? appendTo : isFunction(appendTo) && appendTo(api.getDom()));\n    makeStyleCoord(this._styleCoord, zr, container, api.getWidth() / 2, api.getHeight() / 2);\n    (container || api.getDom()).appendChild(el);\n    this._api = api;\n    this._container = container;\n    // FIXME\n    // Is it needed to trigger zr event manually if\n    // the browser do not support `pointer-events: none`.\n    var self = this;\n    el.onmouseenter = function () {\n      // clear the timeout in hideLater and keep showing tooltip\n      if (self._enterable) {\n        clearTimeout(self._hideTimeout);\n        self._show = true;\n      }\n      self._inContent = true;\n    };\n    el.onmousemove = function (e) {\n      e = e || window.event;\n      if (!self._enterable) {\n        // `pointer-events: none` is set to tooltip content div\n        // if `enterable` is set as `false`, and `el.onmousemove`\n        // can not be triggered. But in browser that do not\n        // support `pointer-events`, we need to do this:\n        // Try trigger zrender event to avoid mouse\n        // in and out shape too frequently\n        var handler = zr.handler;\n        var zrViewportRoot = zr.painter.getViewportRoot();\n        normalizeEvent(zrViewportRoot, e, true);\n        handler.dispatch('mousemove', e);\n      }\n    };\n    el.onmouseleave = function () {\n      // set `_inContent` to `false` before `hideLater`\n      self._inContent = false;\n      if (self._enterable) {\n        if (self._show) {\n          self.hideLater(self._hideDelay);\n        }\n      }\n    };\n  }\n  /**\r\n   * Update when tooltip is rendered\r\n   */\n  TooltipHTMLContent.prototype.update = function (tooltipModel) {\n    // FIXME\n    // Move this logic to ec main?\n    if (!this._container) {\n      var container = this._api.getDom();\n      var position = getComputedStyle(container, 'position');\n      var domStyle = container.style;\n      if (domStyle.position !== 'absolute' && position !== 'absolute') {\n        domStyle.position = 'relative';\n      }\n    }\n    // move tooltip if chart resized\n    var alwaysShowContent = tooltipModel.get('alwaysShowContent');\n    alwaysShowContent && this._moveIfResized();\n    // update alwaysShowContent\n    this._alwaysShowContent = alwaysShowContent;\n    // update className\n    this.el.className = tooltipModel.get('className') || '';\n    // Hide the tooltip\n    // PENDING\n    // this.hide();\n  };\n  TooltipHTMLContent.prototype.show = function (tooltipModel, nearPointColor) {\n    clearTimeout(this._hideTimeout);\n    clearTimeout(this._longHideTimeout);\n    var el = this.el;\n    var style = el.style;\n    var styleCoord = this._styleCoord;\n    if (!el.innerHTML) {\n      style.display = 'none';\n    } else {\n      style.cssText = gCssText + assembleCssText(tooltipModel, !this._firstShow, this._longHide)\n      // initial transform\n      + assembleTransform(styleCoord[0], styleCoord[1], true) + (\"border-color:\" + convertToColorString(nearPointColor) + \";\") + (tooltipModel.get('extraCssText') || '')\n      // If mouse occasionally move over the tooltip, a mouseout event will be\n      // triggered by canvas, and cause some unexpectable result like dragging\n      // stop, \"unfocusAdjacency\". Here `pointer-events: none` is used to solve\n      // it. Although it is not supported by IE8~IE10, fortunately it is a rare\n      // scenario.\n      + (\";pointer-events:\" + (this._enterable ? 'auto' : 'none'));\n    }\n    this._show = true;\n    this._firstShow = false;\n    this._longHide = false;\n  };\n  TooltipHTMLContent.prototype.setContent = function (content, markers, tooltipModel, borderColor, arrowPosition) {\n    var el = this.el;\n    if (content == null) {\n      el.innerHTML = '';\n      return;\n    }\n    var arrow = '';\n    if (isString(arrowPosition) && tooltipModel.get('trigger') === 'item' && !shouldTooltipConfine(tooltipModel)) {\n      arrow = assembleArrow(tooltipModel, borderColor, arrowPosition);\n    }\n    if (isString(content)) {\n      el.innerHTML = content + arrow;\n    } else if (content) {\n      // Clear previous\n      el.innerHTML = '';\n      if (!isArray(content)) {\n        content = [content];\n      }\n      for (var i = 0; i < content.length; i++) {\n        if (isDom(content[i]) && content[i].parentNode !== el) {\n          el.appendChild(content[i]);\n        }\n      }\n      // no arrow if empty\n      if (arrow && el.childNodes.length) {\n        // no need to create a new parent element, but it's not supported by IE 10 and older.\n        // const arrowEl = document.createRange().createContextualFragment(arrow);\n        var arrowEl = document.createElement('div');\n        arrowEl.innerHTML = arrow;\n        el.appendChild(arrowEl);\n      }\n    }\n  };\n  TooltipHTMLContent.prototype.setEnterable = function (enterable) {\n    this._enterable = enterable;\n  };\n  TooltipHTMLContent.prototype.getSize = function () {\n    var el = this.el;\n    return el ? [el.offsetWidth, el.offsetHeight] : [0, 0];\n  };\n  TooltipHTMLContent.prototype.moveTo = function (zrX, zrY) {\n    if (!this.el) {\n      return;\n    }\n    var styleCoord = this._styleCoord;\n    makeStyleCoord(styleCoord, this._zr, this._container, zrX, zrY);\n    if (styleCoord[0] != null && styleCoord[1] != null) {\n      var style_1 = this.el.style;\n      var transforms = assembleTransform(styleCoord[0], styleCoord[1]);\n      each(transforms, function (transform) {\n        style_1[transform[0]] = transform[1];\n      });\n    }\n  };\n  /**\r\n   * when `alwaysShowContent` is true,\r\n   * move the tooltip after chart resized\r\n   */\n  TooltipHTMLContent.prototype._moveIfResized = function () {\n    // The ratio of left to width\n    var ratioX = this._styleCoord[2];\n    // The ratio of top to height\n    var ratioY = this._styleCoord[3];\n    this.moveTo(ratioX * this._zr.getWidth(), ratioY * this._zr.getHeight());\n  };\n  TooltipHTMLContent.prototype.hide = function () {\n    var _this = this;\n    var style = this.el.style;\n    style.visibility = 'hidden';\n    style.opacity = '0';\n    env.transform3dSupported && (style.willChange = '');\n    this._show = false;\n    this._longHideTimeout = setTimeout(function () {\n      return _this._longHide = true;\n    }, 500);\n  };\n  TooltipHTMLContent.prototype.hideLater = function (time) {\n    if (this._show && !(this._inContent && this._enterable) && !this._alwaysShowContent) {\n      if (time) {\n        this._hideDelay = time;\n        // Set show false to avoid invoke hideLater multiple times\n        this._show = false;\n        this._hideTimeout = setTimeout(bind(this.hide, this), time);\n      } else {\n        this.hide();\n      }\n    }\n  };\n  TooltipHTMLContent.prototype.isShow = function () {\n    return this._show;\n  };\n  TooltipHTMLContent.prototype.dispose = function () {\n    clearTimeout(this._hideTimeout);\n    clearTimeout(this._longHideTimeout);\n    var parentNode = this.el.parentNode;\n    parentNode && parentNode.removeChild(this.el);\n    this.el = this._container = null;\n  };\n  return TooltipHTMLContent;\n}();\nexport default TooltipHTMLContent;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AACA,2BAA2B,GAC3B,IAAI,wBAAwB,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD,EAAE,gKAAA,CAAA,oBAAiB,EAAE;AACjE,IAAI,uBAAuB,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD,EAAE,gKAAA,CAAA,mBAAgB,EAAE;AAC/D,2BAA2B;AAC3B,IAAI,WAAW,2FAA2F,CAAC,6IAAA,CAAA,UAAG,CAAC,oBAAoB,GAAG,2BAA2B,EAAE;AACnK,SAAS,UAAU,GAAG;IACpB,MAAM,QAAQ,SAAS,UAAU,QAAQ,UAAU,SAAS,QAAQ,QAAQ,WAAW;IACvF,OAAO;AACT;AACA,SAAS,cAAc,YAAY,EAAE,WAAW,EAAE,aAAa;IAC7D,IAAI,CAAC,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,kBAAkB,kBAAkB,UAAU;QAC1D,OAAO;IACT;IACA,IAAI,kBAAkB,aAAa,GAAG,CAAC;IACvC,IAAI,cAAc,aAAa,GAAG,CAAC;IACnC,cAAc,CAAA,GAAA,gKAAA,CAAA,uBAAoB,AAAD,EAAE;IACnC,IAAI,WAAW,UAAU;IACzB,IAAI,YAAY,KAAK,GAAG,CAAC,KAAK,KAAK,CAAC,eAAe,KAAK;IACxD,IAAI,gBAAgB;IACpB,IAAI,iBAAiB,uBAAuB;IAC5C,IAAI;IACJ,IAAI,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE;QAAC;QAAQ;KAAQ,EAAE,YAAY,CAAC,GAAG;QAC7C,iBAAiB;QACjB,kBAAkB,6BAA6B,CAAC,YAAY,aAAa,SAAS,CAAC,MAAM,CAAC,EAAE,IAAI;IAClG,OAAO;QACL,iBAAiB;QACjB,kBAAkB,6BAA6B,CAAC,YAAY,aAAa,QAAQ,MAAM,EAAE,IAAI;IAC/F;IACA,IAAI,eAAe,YAAY,KAAK,EAAE,GAAG;IACzC,IAAI,UAAU,YAAY;IAC1B,IAAI,YAAY,UAAU,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,iBAAiB,UAAU,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC;IACzF,IAAI,cAAc,KAAK,KAAK,CAAC,CAAC,CAAC,YAAY,KAAK,KAAK,GAAG,WAAW,IAAI,IAAI,KAAK,KAAK,GAAG,cAAc,CAAC,YAAY,OAAO,IAAI,CAAC,IAAI,OAAO;IAC1I,iBAAiB,MAAM,WAAW,OAAO,cAAc;IACvD,IAAI,cAAc,cAAc,YAAY,cAAc;IAC1D,IAAI,WAAW;QAAC,6BAA6B,YAAY,eAAe,YAAY;QAAkB,gBAAgB,MAAM,iBAAiB;QAAK,mBAAmB;QAAa,kBAAkB;QAAa,sBAAsB,kBAAkB;KAAI;IAC7P,OAAO,kBAAkB,SAAS,IAAI,CAAC,MAAM;AAC/C;AACA,SAAS,mBAAmB,QAAQ,EAAE,QAAQ;IAC5C,IAAI,kBAAkB;IACtB,IAAI,mBAAmB,MAAM,WAAW,IAAI,OAAO;IACnD,IAAI,iBAAiB,YAAY,mBAAmB,gBAAgB;IACpE,IAAI,CAAC,UAAU;QACb,mBAAmB,MAAM,WAAW,OAAO;QAC3C,kBAAkB,6IAAA,CAAA,UAAG,CAAC,kBAAkB,GAAG,MAAM,uBAAuB,mBAAmB,UAAU,mBAAmB,SAAS;IACnI;IACA,OAAO,wBAAwB,MAAM;AACvC;AACA,SAAS,kBAAkB,CAAC,EAAE,CAAC,EAAE,QAAQ;IACvC,4DAA4D;IAC5D,iEAAiE;IACjE,IAAI,KAAK,EAAE,OAAO,CAAC,KAAK;IACxB,IAAI,KAAK,EAAE,OAAO,CAAC,KAAK;IACxB,uDAAuD;IACvD,IAAI,CAAC,6IAAA,CAAA,UAAG,CAAC,kBAAkB,EAAE;QAC3B,OAAO,WAAW,SAAS,KAAK,WAAW,KAAK,MAAM;YAAC;gBAAC;gBAAO;aAAG;YAAE;gBAAC;gBAAQ;aAAG;SAAC;IACnF;IACA,oBAAoB;IACpB,IAAI,OAAO,6IAAA,CAAA,UAAG,CAAC,oBAAoB;IACnC,IAAI,YAAY,cAAc,CAAC,OAAO,OAAO,EAAE,IAAI,MAAM,KAAK,MAAM,KAAK,CAAC,OAAO,OAAO,EAAE,IAAI;IAC9F,OAAO,WAAW,kBAAkB,uBAAuB,MAAM,YAAY,MAAM;QAAC;YAAC;YAAO;SAAE;QAAE;YAAC;YAAQ;SAAE;QAAE;YAAC,gKAAA,CAAA,mBAAgB;YAAE;SAAU;KAAC;AAC7I;AACA;;;;CAIC,GACD,SAAS,aAAa,cAAc;IAClC,IAAI,UAAU,EAAE;IAChB,IAAI,WAAW,eAAe,GAAG,CAAC;IAClC,IAAI,QAAQ,eAAe,YAAY;IACvC,SAAS,QAAQ,IAAI,CAAC,WAAW;IACjC,QAAQ,IAAI,CAAC,UAAU,eAAe,OAAO;IAC7C,gDAAgD;IAChD,IAAI,aAAa,CAAA,GAAA,8IAAA,CAAA,YAAS,AAAD,EAAE,eAAe,GAAG,CAAC,eAAe,KAAK,KAAK,CAAC,WAAW,IAAI;IACvF,YAAY,QAAQ,IAAI,CAAC,iBAAiB,aAAa;IACvD,IAAI,cAAc,eAAe,GAAG,CAAC;IACrC,IAAI,aAAa,eAAe,GAAG,CAAC,qBAAqB;IACzD,IAAI,gBAAgB,eAAe,GAAG,CAAC,wBAAwB;IAC/D,IAAI,gBAAgB,eAAe,GAAG,CAAC,wBAAwB;IAC/D,eAAe,cAAc,QAAQ,IAAI,CAAC,iBAAiB,gBAAgB,QAAQ,gBAAgB,QAAQ,aAAa,QAAQ;IAChI,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE;QAAC;QAAc;KAAQ,EAAE,SAAU,IAAI;QAC1C,IAAI,MAAM,eAAe,GAAG,CAAC;QAC7B,OAAO,QAAQ,IAAI,CAAC,UAAU,OAAO,MAAM;IAC7C;IACA,OAAO,QAAQ,IAAI,CAAC;AACtB;AACA,SAAS,gBAAgB,YAAY,EAAE,gBAAgB,EAAE,QAAQ;IAC/D,IAAI,UAAU,EAAE;IAChB,IAAI,qBAAqB,aAAa,GAAG,CAAC;IAC1C,IAAI,kBAAkB,aAAa,GAAG,CAAC;IACvC,IAAI,aAAa,aAAa,GAAG,CAAC;IAClC,IAAI,cAAc,aAAa,GAAG,CAAC;IACnC,IAAI,gBAAgB,aAAa,GAAG,CAAC;IACrC,IAAI,gBAAgB,aAAa,GAAG,CAAC;IACrC,IAAI,iBAAiB,aAAa,QAAQ,CAAC;IAC3C,IAAI,UAAU,CAAA,GAAA,uKAAA,CAAA,6BAA0B,AAAD,EAAE,cAAc;IACvD,IAAI,YAAY,gBAAgB,QAAQ,gBAAgB,QAAQ,aAAa,QAAQ;IACrF,QAAQ,IAAI,CAAC,gBAAgB;IAC7B,qEAAqE;IACrE,oBAAoB,sBAAsB,QAAQ,IAAI,CAAC,mBAAmB,oBAAoB;IAC9F,IAAI,iBAAiB;QACnB,QAAQ,IAAI,CAAC,sBAAsB;IACrC;IACA,eAAe;IACf,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE;QAAC;QAAS;QAAS;KAAS,EAAE,SAAU,IAAI;QAC/C,IAAI,aAAa,YAAY;QAC7B,IAAI,YAAY,CAAA,GAAA,gKAAA,CAAA,cAAW,AAAD,EAAE;QAC5B,IAAI,MAAM,aAAa,GAAG,CAAC;QAC3B,OAAO,QAAQ,QAAQ,IAAI,CAAC,aAAa,MAAM,MAAM,CAAC,SAAS,UAAU,KAAK,IAAI;IACpF;IACA,aAAa;IACb,QAAQ,IAAI,CAAC,aAAa;IAC1B,UAAU;IACV,IAAI,WAAW,MAAM;QACnB,QAAQ,IAAI,CAAC,aAAa,CAAA,GAAA,gKAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS,IAAI,CAAC,SAAS;IACrE;IACA,OAAO,QAAQ,IAAI,CAAC,OAAO;AAC7B;AACA,sDAAsD;AACtD,SAAS,eAAe,GAAG,EAAE,EAAE,EAAE,SAAS,EAAE,GAAG,EAAE,GAAG;IAClD,IAAI,YAAY,MAAM,GAAG,OAAO;IAChC,IAAI,WAAW;QACb,IAAI,iBAAiB,aAAa,UAAU,eAAe;QAC3D,IAAI,gBAAgB;YAClB,uEAAuE;YACvE,CAAA,GAAA,6IAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK,gBAAgB,WAAW,KAAK;QAC3D;IACF,OAAO;QACL,GAAG,CAAC,EAAE,GAAG;QACT,GAAG,CAAC,EAAE,GAAG;QACT,2DAA2D;QAC3D,yDAAyD;QACzD,6BAA6B;QAC7B,IAAI,qBAAqB,aAAa,UAAU,qBAAqB;QACrE,IAAI,oBAAoB;YACtB,GAAG,CAAC,EAAE,IAAI,mBAAmB,UAAU;YACvC,GAAG,CAAC,EAAE,IAAI,mBAAmB,SAAS;QACxC;IACF;IACA,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,QAAQ;IAC7B,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,SAAS;AAChC;AACA,IAAI,qBAAqB,WAAW,GAAE;IACpC,SAAS,mBAAmB,GAAG,EAAE,GAAG;QAClC,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,WAAW,GAAG;YAAC;YAAG;YAAG;YAAG;SAAE;QAC/B,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,6IAAA,CAAA,UAAG,CAAC,GAAG,EAAE;YACX,OAAO;QACT;QACA,IAAI,KAAK,SAAS,aAAa,CAAC;QAChC,aAAa;QACb,GAAG,aAAa,GAAG;QACnB,IAAI,CAAC,EAAE,GAAG;QACV,IAAI,KAAK,IAAI,CAAC,GAAG,GAAG,IAAI,KAAK;QAC7B,IAAI,WAAW,IAAI,QAAQ;QAC3B,IAAI,YAAY,YAAY,CAAC,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,YAAY,SAAS,aAAa,CAAC,YAAY,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE,YAAY,WAAW,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD,EAAE,aAAa,SAAS,IAAI,MAAM,GAAG;QAChK,eAAe,IAAI,CAAC,WAAW,EAAE,IAAI,WAAW,IAAI,QAAQ,KAAK,GAAG,IAAI,SAAS,KAAK;QACtF,CAAC,aAAa,IAAI,MAAM,EAAE,EAAE,WAAW,CAAC;QACxC,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,UAAU,GAAG;QAClB,QAAQ;QACR,+CAA+C;QAC/C,qDAAqD;QACrD,IAAI,OAAO,IAAI;QACf,GAAG,YAAY,GAAG;YAChB,0DAA0D;YAC1D,IAAI,KAAK,UAAU,EAAE;gBACnB,aAAa,KAAK,YAAY;gBAC9B,KAAK,KAAK,GAAG;YACf;YACA,KAAK,UAAU,GAAG;QACpB;QACA,GAAG,WAAW,GAAG,SAAU,CAAC;YAC1B,IAAI,KAAK,OAAO,KAAK;YACrB,IAAI,CAAC,KAAK,UAAU,EAAE;gBACpB,uDAAuD;gBACvD,yDAAyD;gBACzD,mDAAmD;gBACnD,gDAAgD;gBAChD,2CAA2C;gBAC3C,kCAAkC;gBAClC,IAAI,UAAU,GAAG,OAAO;gBACxB,IAAI,iBAAiB,GAAG,OAAO,CAAC,eAAe;gBAC/C,CAAA,GAAA,+JAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB,GAAG;gBAClC,QAAQ,QAAQ,CAAC,aAAa;YAChC;QACF;QACA,GAAG,YAAY,GAAG;YAChB,iDAAiD;YACjD,KAAK,UAAU,GAAG;YAClB,IAAI,KAAK,UAAU,EAAE;gBACnB,IAAI,KAAK,KAAK,EAAE;oBACd,KAAK,SAAS,CAAC,KAAK,UAAU;gBAChC;YACF;QACF;IACF;IACA;;GAEC,GACD,mBAAmB,SAAS,CAAC,MAAM,GAAG,SAAU,YAAY;QAC1D,QAAQ;QACR,8BAA8B;QAC9B,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YACpB,IAAI,YAAY,IAAI,CAAC,IAAI,CAAC,MAAM;YAChC,IAAI,WAAW,CAAA,GAAA,gKAAA,CAAA,mBAAgB,AAAD,EAAE,WAAW;YAC3C,IAAI,WAAW,UAAU,KAAK;YAC9B,IAAI,SAAS,QAAQ,KAAK,cAAc,aAAa,YAAY;gBAC/D,SAAS,QAAQ,GAAG;YACtB;QACF;QACA,gCAAgC;QAChC,IAAI,oBAAoB,aAAa,GAAG,CAAC;QACzC,qBAAqB,IAAI,CAAC,cAAc;QACxC,2BAA2B;QAC3B,IAAI,CAAC,kBAAkB,GAAG;QAC1B,mBAAmB;QACnB,IAAI,CAAC,EAAE,CAAC,SAAS,GAAG,aAAa,GAAG,CAAC,gBAAgB;IACrD,mBAAmB;IACnB,UAAU;IACV,eAAe;IACjB;IACA,mBAAmB,SAAS,CAAC,IAAI,GAAG,SAAU,YAAY,EAAE,cAAc;QACxE,aAAa,IAAI,CAAC,YAAY;QAC9B,aAAa,IAAI,CAAC,gBAAgB;QAClC,IAAI,KAAK,IAAI,CAAC,EAAE;QAChB,IAAI,QAAQ,GAAG,KAAK;QACpB,IAAI,aAAa,IAAI,CAAC,WAAW;QACjC,IAAI,CAAC,GAAG,SAAS,EAAE;YACjB,MAAM,OAAO,GAAG;QAClB,OAAO;YACL,MAAM,OAAO,GAAG,WAAW,gBAAgB,cAAc,CAAC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC,SAAS,IAEvF,kBAAkB,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,EAAE,EAAE,QAAQ,CAAC,kBAAkB,CAAA,GAAA,gKAAA,CAAA,uBAAoB,AAAD,EAAE,kBAAkB,GAAG,IAAI,CAAC,aAAa,GAAG,CAAC,mBAAmB,EAAE,IAMhK,CAAC,qBAAqB,CAAC,IAAI,CAAC,UAAU,GAAG,SAAS,MAAM,CAAC;QAC7D;QACA,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,SAAS,GAAG;IACnB;IACA,mBAAmB,SAAS,CAAC,UAAU,GAAG,SAAU,OAAO,EAAE,OAAO,EAAE,YAAY,EAAE,WAAW,EAAE,aAAa;QAC5G,IAAI,KAAK,IAAI,CAAC,EAAE;QAChB,IAAI,WAAW,MAAM;YACnB,GAAG,SAAS,GAAG;YACf;QACF;QACA,IAAI,QAAQ;QACZ,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,kBAAkB,aAAa,GAAG,CAAC,eAAe,UAAU,CAAC,CAAA,GAAA,gKAAA,CAAA,uBAAoB,AAAD,EAAE,eAAe;YAC5G,QAAQ,cAAc,cAAc,aAAa;QACnD;QACA,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,UAAU;YACrB,GAAG,SAAS,GAAG,UAAU;QAC3B,OAAO,IAAI,SAAS;YAClB,iBAAiB;YACjB,GAAG,SAAS,GAAG;YACf,IAAI,CAAC,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,UAAU;gBACrB,UAAU;oBAAC;iBAAQ;YACrB;YACA,IAAK,IAAI,IAAI,GAAG,IAAI,QAAQ,MAAM,EAAE,IAAK;gBACvC,IAAI,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE,OAAO,CAAC,EAAE,KAAK,OAAO,CAAC,EAAE,CAAC,UAAU,KAAK,IAAI;oBACrD,GAAG,WAAW,CAAC,OAAO,CAAC,EAAE;gBAC3B;YACF;YACA,oBAAoB;YACpB,IAAI,SAAS,GAAG,UAAU,CAAC,MAAM,EAAE;gBACjC,qFAAqF;gBACrF,0EAA0E;gBAC1E,IAAI,UAAU,SAAS,aAAa,CAAC;gBACrC,QAAQ,SAAS,GAAG;gBACpB,GAAG,WAAW,CAAC;YACjB;QACF;IACF;IACA,mBAAmB,SAAS,CAAC,YAAY,GAAG,SAAU,SAAS;QAC7D,IAAI,CAAC,UAAU,GAAG;IACpB;IACA,mBAAmB,SAAS,CAAC,OAAO,GAAG;QACrC,IAAI,KAAK,IAAI,CAAC,EAAE;QAChB,OAAO,KAAK;YAAC,GAAG,WAAW;YAAE,GAAG,YAAY;SAAC,GAAG;YAAC;YAAG;SAAE;IACxD;IACA,mBAAmB,SAAS,CAAC,MAAM,GAAG,SAAU,GAAG,EAAE,GAAG;QACtD,IAAI,CAAC,IAAI,CAAC,EAAE,EAAE;YACZ;QACF;QACA,IAAI,aAAa,IAAI,CAAC,WAAW;QACjC,eAAe,YAAY,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK;QAC3D,IAAI,UAAU,CAAC,EAAE,IAAI,QAAQ,UAAU,CAAC,EAAE,IAAI,MAAM;YAClD,IAAI,UAAU,IAAI,CAAC,EAAE,CAAC,KAAK;YAC3B,IAAI,aAAa,kBAAkB,UAAU,CAAC,EAAE,EAAE,UAAU,CAAC,EAAE;YAC/D,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,YAAY,SAAU,SAAS;gBAClC,OAAO,CAAC,SAAS,CAAC,EAAE,CAAC,GAAG,SAAS,CAAC,EAAE;YACtC;QACF;IACF;IACA;;;GAGC,GACD,mBAAmB,SAAS,CAAC,cAAc,GAAG;QAC5C,6BAA6B;QAC7B,IAAI,SAAS,IAAI,CAAC,WAAW,CAAC,EAAE;QAChC,6BAA6B;QAC7B,IAAI,SAAS,IAAI,CAAC,WAAW,CAAC,EAAE;QAChC,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,SAAS,IAAI,CAAC,GAAG,CAAC,SAAS;IACvE;IACA,mBAAmB,SAAS,CAAC,IAAI,GAAG;QAClC,IAAI,QAAQ,IAAI;QAChB,IAAI,QAAQ,IAAI,CAAC,EAAE,CAAC,KAAK;QACzB,MAAM,UAAU,GAAG;QACnB,MAAM,OAAO,GAAG;QAChB,6IAAA,CAAA,UAAG,CAAC,oBAAoB,IAAI,CAAC,MAAM,UAAU,GAAG,EAAE;QAClD,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,gBAAgB,GAAG,WAAW;YACjC,OAAO,MAAM,SAAS,GAAG;QAC3B,GAAG;IACL;IACA,mBAAmB,SAAS,CAAC,SAAS,GAAG,SAAU,IAAI;QACrD,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACnF,IAAI,MAAM;gBACR,IAAI,CAAC,UAAU,GAAG;gBAClB,0DAA0D;gBAC1D,IAAI,CAAC,KAAK,GAAG;gBACb,IAAI,CAAC,YAAY,GAAG,WAAW,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG;YACxD,OAAO;gBACL,IAAI,CAAC,IAAI;YACX;QACF;IACF;IACA,mBAAmB,SAAS,CAAC,MAAM,GAAG;QACpC,OAAO,IAAI,CAAC,KAAK;IACnB;IACA,mBAAmB,SAAS,CAAC,OAAO,GAAG;QACrC,aAAa,IAAI,CAAC,YAAY;QAC9B,aAAa,IAAI,CAAC,gBAAgB;QAClC,IAAI,aAAa,IAAI,CAAC,EAAE,CAAC,UAAU;QACnC,cAAc,WAAW,WAAW,CAAC,IAAI,CAAC,EAAE;QAC5C,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,UAAU,GAAG;IAC9B;IACA,OAAO;AACT;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1174, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/tooltip/TooltipRichContent.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport ZRText from 'zrender/lib/graphic/Text.js';\nimport { getPaddingFromTooltipModel } from './tooltipMarkup.js';\nimport { throwError } from '../../util/log.js';\nvar TooltipRichContent = /** @class */function () {\n  function TooltipRichContent(api) {\n    this._show = false;\n    this._styleCoord = [0, 0, 0, 0];\n    this._alwaysShowContent = false;\n    this._enterable = true;\n    this._zr = api.getZr();\n    makeStyleCoord(this._styleCoord, this._zr, api.getWidth() / 2, api.getHeight() / 2);\n  }\n  /**\r\n   * Update when tooltip is rendered\r\n   */\n  TooltipRichContent.prototype.update = function (tooltipModel) {\n    var alwaysShowContent = tooltipModel.get('alwaysShowContent');\n    alwaysShowContent && this._moveIfResized();\n    // update alwaysShowContent\n    this._alwaysShowContent = alwaysShowContent;\n  };\n  TooltipRichContent.prototype.show = function () {\n    if (this._hideTimeout) {\n      clearTimeout(this._hideTimeout);\n    }\n    this.el.show();\n    this._show = true;\n  };\n  /**\r\n   * Set tooltip content\r\n   */\n  TooltipRichContent.prototype.setContent = function (content, markupStyleCreator, tooltipModel, borderColor, arrowPosition) {\n    var _this = this;\n    if (zrUtil.isObject(content)) {\n      throwError(process.env.NODE_ENV !== 'production' ? 'Passing DOM nodes as content is not supported in richText tooltip!' : '');\n    }\n    if (this.el) {\n      this._zr.remove(this.el);\n    }\n    var textStyleModel = tooltipModel.getModel('textStyle');\n    this.el = new ZRText({\n      style: {\n        rich: markupStyleCreator.richTextStyles,\n        text: content,\n        lineHeight: 22,\n        borderWidth: 1,\n        borderColor: borderColor,\n        textShadowColor: textStyleModel.get('textShadowColor'),\n        fill: tooltipModel.get(['textStyle', 'color']),\n        padding: getPaddingFromTooltipModel(tooltipModel, 'richText'),\n        verticalAlign: 'top',\n        align: 'left'\n      },\n      z: tooltipModel.get('z')\n    });\n    zrUtil.each(['backgroundColor', 'borderRadius', 'shadowColor', 'shadowBlur', 'shadowOffsetX', 'shadowOffsetY'], function (propName) {\n      _this.el.style[propName] = tooltipModel.get(propName);\n    });\n    zrUtil.each(['textShadowBlur', 'textShadowOffsetX', 'textShadowOffsetY'], function (propName) {\n      _this.el.style[propName] = textStyleModel.get(propName) || 0;\n    });\n    this._zr.add(this.el);\n    var self = this;\n    this.el.on('mouseover', function () {\n      // clear the timeout in hideLater and keep showing tooltip\n      if (self._enterable) {\n        clearTimeout(self._hideTimeout);\n        self._show = true;\n      }\n      self._inContent = true;\n    });\n    this.el.on('mouseout', function () {\n      if (self._enterable) {\n        if (self._show) {\n          self.hideLater(self._hideDelay);\n        }\n      }\n      self._inContent = false;\n    });\n  };\n  TooltipRichContent.prototype.setEnterable = function (enterable) {\n    this._enterable = enterable;\n  };\n  TooltipRichContent.prototype.getSize = function () {\n    var el = this.el;\n    var bounding = this.el.getBoundingRect();\n    // bounding rect does not include shadow. For renderMode richText,\n    // if overflow, it will be cut. So calculate them accurately.\n    var shadowOuterSize = calcShadowOuterSize(el.style);\n    return [bounding.width + shadowOuterSize.left + shadowOuterSize.right, bounding.height + shadowOuterSize.top + shadowOuterSize.bottom];\n  };\n  TooltipRichContent.prototype.moveTo = function (x, y) {\n    var el = this.el;\n    if (el) {\n      var styleCoord = this._styleCoord;\n      makeStyleCoord(styleCoord, this._zr, x, y);\n      x = styleCoord[0];\n      y = styleCoord[1];\n      var style = el.style;\n      var borderWidth = mathMaxWith0(style.borderWidth || 0);\n      var shadowOuterSize = calcShadowOuterSize(style);\n      // rich text x, y do not include border.\n      el.x = x + borderWidth + shadowOuterSize.left;\n      el.y = y + borderWidth + shadowOuterSize.top;\n      el.markRedraw();\n    }\n  };\n  /**\r\n   * when `alwaysShowContent` is true,\r\n   * move the tooltip after chart resized\r\n   */\n  TooltipRichContent.prototype._moveIfResized = function () {\n    // The ratio of left to width\n    var ratioX = this._styleCoord[2];\n    // The ratio of top to height\n    var ratioY = this._styleCoord[3];\n    this.moveTo(ratioX * this._zr.getWidth(), ratioY * this._zr.getHeight());\n  };\n  TooltipRichContent.prototype.hide = function () {\n    if (this.el) {\n      this.el.hide();\n    }\n    this._show = false;\n  };\n  TooltipRichContent.prototype.hideLater = function (time) {\n    if (this._show && !(this._inContent && this._enterable) && !this._alwaysShowContent) {\n      if (time) {\n        this._hideDelay = time;\n        // Set show false to avoid invoke hideLater multiple times\n        this._show = false;\n        this._hideTimeout = setTimeout(zrUtil.bind(this.hide, this), time);\n      } else {\n        this.hide();\n      }\n    }\n  };\n  TooltipRichContent.prototype.isShow = function () {\n    return this._show;\n  };\n  TooltipRichContent.prototype.dispose = function () {\n    this._zr.remove(this.el);\n  };\n  return TooltipRichContent;\n}();\nfunction mathMaxWith0(val) {\n  return Math.max(0, val);\n}\nfunction calcShadowOuterSize(style) {\n  var shadowBlur = mathMaxWith0(style.shadowBlur || 0);\n  var shadowOffsetX = mathMaxWith0(style.shadowOffsetX || 0);\n  var shadowOffsetY = mathMaxWith0(style.shadowOffsetY || 0);\n  return {\n    left: mathMaxWith0(shadowBlur - shadowOffsetX),\n    right: mathMaxWith0(shadowBlur + shadowOffsetX),\n    top: mathMaxWith0(shadowBlur - shadowOffsetY),\n    bottom: mathMaxWith0(shadowBlur + shadowOffsetY)\n  };\n}\nfunction makeStyleCoord(out, zr, zrX, zrY) {\n  out[0] = zrX;\n  out[1] = zrY;\n  out[2] = out[0] / zr.getWidth();\n  out[3] = out[1] / zr.getHeight();\n}\nexport default TooltipRichContent;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;;;;;AACA,IAAI,qBAAqB,WAAW,GAAE;IACpC,SAAS,mBAAmB,GAAG;QAC7B,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,WAAW,GAAG;YAAC;YAAG;YAAG;YAAG;SAAE;QAC/B,IAAI,CAAC,kBAAkB,GAAG;QAC1B,IAAI,CAAC,UAAU,GAAG;QAClB,IAAI,CAAC,GAAG,GAAG,IAAI,KAAK;QACpB,eAAe,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,QAAQ,KAAK,GAAG,IAAI,SAAS,KAAK;IACnF;IACA;;GAEC,GACD,mBAAmB,SAAS,CAAC,MAAM,GAAG,SAAU,YAAY;QAC1D,IAAI,oBAAoB,aAAa,GAAG,CAAC;QACzC,qBAAqB,IAAI,CAAC,cAAc;QACxC,2BAA2B;QAC3B,IAAI,CAAC,kBAAkB,GAAG;IAC5B;IACA,mBAAmB,SAAS,CAAC,IAAI,GAAG;QAClC,IAAI,IAAI,CAAC,YAAY,EAAE;YACrB,aAAa,IAAI,CAAC,YAAY;QAChC;QACA,IAAI,CAAC,EAAE,CAAC,IAAI;QACZ,IAAI,CAAC,KAAK,GAAG;IACf;IACA;;GAEC,GACD,mBAAmB,SAAS,CAAC,UAAU,GAAG,SAAU,OAAO,EAAE,kBAAkB,EAAE,YAAY,EAAE,WAAW,EAAE,aAAa;QACvH,IAAI,QAAQ,IAAI;QAChB,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAe,AAAD,EAAE,UAAU;YAC5B,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE,uCAAwC;QACrD;QACA,IAAI,IAAI,CAAC,EAAE,EAAE;YACX,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;QACzB;QACA,IAAI,iBAAiB,aAAa,QAAQ,CAAC;QAC3C,IAAI,CAAC,EAAE,GAAG,IAAI,iJAAA,CAAA,UAAM,CAAC;YACnB,OAAO;gBACL,MAAM,mBAAmB,cAAc;gBACvC,MAAM;gBACN,YAAY;gBACZ,aAAa;gBACb,aAAa;gBACb,iBAAiB,eAAe,GAAG,CAAC;gBACpC,MAAM,aAAa,GAAG,CAAC;oBAAC;oBAAa;iBAAQ;gBAC7C,SAAS,CAAA,GAAA,uKAAA,CAAA,6BAA0B,AAAD,EAAE,cAAc;gBAClD,eAAe;gBACf,OAAO;YACT;YACA,GAAG,aAAa,GAAG,CAAC;QACtB;QACA,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE;YAAC;YAAmB;YAAgB;YAAe;YAAc;YAAiB;SAAgB,EAAE,SAAU,QAAQ;YAChI,MAAM,EAAE,CAAC,KAAK,CAAC,SAAS,GAAG,aAAa,GAAG,CAAC;QAC9C;QACA,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE;YAAC;YAAkB;YAAqB;SAAoB,EAAE,SAAU,QAAQ;YAC1F,MAAM,EAAE,CAAC,KAAK,CAAC,SAAS,GAAG,eAAe,GAAG,CAAC,aAAa;QAC7D;QACA,IAAI,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE;QACpB,IAAI,OAAO,IAAI;QACf,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,aAAa;YACtB,0DAA0D;YAC1D,IAAI,KAAK,UAAU,EAAE;gBACnB,aAAa,KAAK,YAAY;gBAC9B,KAAK,KAAK,GAAG;YACf;YACA,KAAK,UAAU,GAAG;QACpB;QACA,IAAI,CAAC,EAAE,CAAC,EAAE,CAAC,YAAY;YACrB,IAAI,KAAK,UAAU,EAAE;gBACnB,IAAI,KAAK,KAAK,EAAE;oBACd,KAAK,SAAS,CAAC,KAAK,UAAU;gBAChC;YACF;YACA,KAAK,UAAU,GAAG;QACpB;IACF;IACA,mBAAmB,SAAS,CAAC,YAAY,GAAG,SAAU,SAAS;QAC7D,IAAI,CAAC,UAAU,GAAG;IACpB;IACA,mBAAmB,SAAS,CAAC,OAAO,GAAG;QACrC,IAAI,KAAK,IAAI,CAAC,EAAE;QAChB,IAAI,WAAW,IAAI,CAAC,EAAE,CAAC,eAAe;QACtC,kEAAkE;QAClE,6DAA6D;QAC7D,IAAI,kBAAkB,oBAAoB,GAAG,KAAK;QAClD,OAAO;YAAC,SAAS,KAAK,GAAG,gBAAgB,IAAI,GAAG,gBAAgB,KAAK;YAAE,SAAS,MAAM,GAAG,gBAAgB,GAAG,GAAG,gBAAgB,MAAM;SAAC;IACxI;IACA,mBAAmB,SAAS,CAAC,MAAM,GAAG,SAAU,CAAC,EAAE,CAAC;QAClD,IAAI,KAAK,IAAI,CAAC,EAAE;QAChB,IAAI,IAAI;YACN,IAAI,aAAa,IAAI,CAAC,WAAW;YACjC,eAAe,YAAY,IAAI,CAAC,GAAG,EAAE,GAAG;YACxC,IAAI,UAAU,CAAC,EAAE;YACjB,IAAI,UAAU,CAAC,EAAE;YACjB,IAAI,QAAQ,GAAG,KAAK;YACpB,IAAI,cAAc,aAAa,MAAM,WAAW,IAAI;YACpD,IAAI,kBAAkB,oBAAoB;YAC1C,wCAAwC;YACxC,GAAG,CAAC,GAAG,IAAI,cAAc,gBAAgB,IAAI;YAC7C,GAAG,CAAC,GAAG,IAAI,cAAc,gBAAgB,GAAG;YAC5C,GAAG,UAAU;QACf;IACF;IACA;;;GAGC,GACD,mBAAmB,SAAS,CAAC,cAAc,GAAG;QAC5C,6BAA6B;QAC7B,IAAI,SAAS,IAAI,CAAC,WAAW,CAAC,EAAE;QAChC,6BAA6B;QAC7B,IAAI,SAAS,IAAI,CAAC,WAAW,CAAC,EAAE;QAChC,IAAI,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,GAAG,CAAC,QAAQ,IAAI,SAAS,IAAI,CAAC,GAAG,CAAC,SAAS;IACvE;IACA,mBAAmB,SAAS,CAAC,IAAI,GAAG;QAClC,IAAI,IAAI,CAAC,EAAE,EAAE;YACX,IAAI,CAAC,EAAE,CAAC,IAAI;QACd;QACA,IAAI,CAAC,KAAK,GAAG;IACf;IACA,mBAAmB,SAAS,CAAC,SAAS,GAAG,SAAU,IAAI;QACrD,IAAI,IAAI,CAAC,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,UAAU,IAAI,IAAI,CAAC,UAAU,KAAK,CAAC,IAAI,CAAC,kBAAkB,EAAE;YACnF,IAAI,MAAM;gBACR,IAAI,CAAC,UAAU,GAAG;gBAClB,0DAA0D;gBAC1D,IAAI,CAAC,KAAK,GAAG;gBACb,IAAI,CAAC,YAAY,GAAG,WAAW,CAAA,GAAA,8IAAA,CAAA,OAAW,AAAD,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,GAAG;YAC/D,OAAO;gBACL,IAAI,CAAC,IAAI;YACX;QACF;IACF;IACA,mBAAmB,SAAS,CAAC,MAAM,GAAG;QACpC,OAAO,IAAI,CAAC,KAAK;IACnB;IACA,mBAAmB,SAAS,CAAC,OAAO,GAAG;QACrC,IAAI,CAAC,GAAG,CAAC,MAAM,CAAC,IAAI,CAAC,EAAE;IACzB;IACA,OAAO;AACT;AACA,SAAS,aAAa,GAAG;IACvB,OAAO,KAAK,GAAG,CAAC,GAAG;AACrB;AACA,SAAS,oBAAoB,KAAK;IAChC,IAAI,aAAa,aAAa,MAAM,UAAU,IAAI;IAClD,IAAI,gBAAgB,aAAa,MAAM,aAAa,IAAI;IACxD,IAAI,gBAAgB,aAAa,MAAM,aAAa,IAAI;IACxD,OAAO;QACL,MAAM,aAAa,aAAa;QAChC,OAAO,aAAa,aAAa;QACjC,KAAK,aAAa,aAAa;QAC/B,QAAQ,aAAa,aAAa;IACpC;AACF;AACA,SAAS,eAAe,GAAG,EAAE,EAAE,EAAE,GAAG,EAAE,GAAG;IACvC,GAAG,CAAC,EAAE,GAAG;IACT,GAAG,CAAC,EAAE,GAAG;IACT,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,QAAQ;IAC7B,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,GAAG,GAAG,SAAS;AAChC;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1408, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/tooltip/TooltipView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\nimport { __extends } from \"tslib\";\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { bind, each, clone, trim, isString, isFunction, isArray, isObject, extend } from 'zrender/lib/core/util.js';\nimport env from 'zrender/lib/core/env.js';\nimport TooltipHTMLContent from './TooltipHTMLContent.js';\nimport TooltipRichContent from './TooltipRichContent.js';\nimport { convertToColorString, encodeHTML, formatTpl } from '../../util/format.js';\nimport { parsePercent } from '../../util/number.js';\nimport { Rect } from '../../util/graphic.js';\nimport findPointFromSeries from '../axisPointer/findPointFromSeries.js';\nimport { getLayoutRect } from '../../util/layout.js';\nimport Model from '../../model/Model.js';\nimport * as globalListener from '../axisPointer/globalListener.js';\nimport * as axisHelper from '../../coord/axisHelper.js';\nimport * as axisPointerViewHelper from '../axisPointer/viewHelper.js';\nimport { getTooltipRenderMode, preParseFinder, queryReferringComponents } from '../../util/model.js';\nimport ComponentView from '../../view/Component.js';\nimport { format as timeFormat } from '../../util/time.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { shouldTooltipConfine } from './helper.js';\nimport { normalizeTooltipFormatResult } from '../../model/mixin/dataFormat.js';\nimport { createTooltipMarkup, buildTooltipMarkup, TooltipMarkupStyleCreator } from './tooltipMarkup.js';\nimport { findEventDispatcher } from '../../util/event.js';\nimport { clear, createOrUpdate } from '../../util/throttle.js';\nvar proxyRect = new Rect({\n  shape: {\n    x: -1,\n    y: -1,\n    width: 2,\n    height: 2\n  }\n});\nvar TooltipView = /** @class */function (_super) {\n  __extends(TooltipView, _super);\n  function TooltipView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = TooltipView.type;\n    return _this;\n  }\n  TooltipView.prototype.init = function (ecModel, api) {\n    if (env.node || !api.getDom()) {\n      return;\n    }\n    var tooltipModel = ecModel.getComponent('tooltip');\n    var renderMode = this._renderMode = getTooltipRenderMode(tooltipModel.get('renderMode'));\n    this._tooltipContent = renderMode === 'richText' ? new TooltipRichContent(api) : new TooltipHTMLContent(api, {\n      appendTo: tooltipModel.get('appendToBody', true) ? 'body' : tooltipModel.get('appendTo', true)\n    });\n  };\n  TooltipView.prototype.render = function (tooltipModel, ecModel, api) {\n    if (env.node || !api.getDom()) {\n      return;\n    }\n    // Reset\n    this.group.removeAll();\n    this._tooltipModel = tooltipModel;\n    this._ecModel = ecModel;\n    this._api = api;\n    var tooltipContent = this._tooltipContent;\n    tooltipContent.update(tooltipModel);\n    tooltipContent.setEnterable(tooltipModel.get('enterable'));\n    this._initGlobalListener();\n    this._keepShow();\n    // PENDING\n    // `mousemove` event will be triggered very frequently when the mouse moves fast,\n    // which causes that the `updatePosition` function was also called frequently.\n    // In Chrome with devtools open and Firefox, tooltip looks laggy and shakes. See #14695 #16101\n    // To avoid frequent triggering,\n    // consider throttling it in 50ms when transition is enabled\n    if (this._renderMode !== 'richText' && tooltipModel.get('transitionDuration')) {\n      createOrUpdate(this, '_updatePosition', 50, 'fixRate');\n    } else {\n      clear(this, '_updatePosition');\n    }\n  };\n  TooltipView.prototype._initGlobalListener = function () {\n    var tooltipModel = this._tooltipModel;\n    var triggerOn = tooltipModel.get('triggerOn');\n    globalListener.register('itemTooltip', this._api, bind(function (currTrigger, e, dispatchAction) {\n      // If 'none', it is not controlled by mouse totally.\n      if (triggerOn !== 'none') {\n        if (triggerOn.indexOf(currTrigger) >= 0) {\n          this._tryShow(e, dispatchAction);\n        } else if (currTrigger === 'leave') {\n          this._hide(dispatchAction);\n        }\n      }\n    }, this));\n  };\n  TooltipView.prototype._keepShow = function () {\n    var tooltipModel = this._tooltipModel;\n    var ecModel = this._ecModel;\n    var api = this._api;\n    var triggerOn = tooltipModel.get('triggerOn');\n    // Try to keep the tooltip show when refreshing\n    if (this._lastX != null && this._lastY != null\n    // When user is willing to control tooltip totally using API,\n    // self.manuallyShowTip({x, y}) might cause tooltip hide,\n    // which is not expected.\n    && triggerOn !== 'none' && triggerOn !== 'click') {\n      var self_1 = this;\n      clearTimeout(this._refreshUpdateTimeout);\n      this._refreshUpdateTimeout = setTimeout(function () {\n        // Show tip next tick after other charts are rendered\n        // In case highlight action has wrong result\n        // FIXME\n        !api.isDisposed() && self_1.manuallyShowTip(tooltipModel, ecModel, api, {\n          x: self_1._lastX,\n          y: self_1._lastY,\n          dataByCoordSys: self_1._lastDataByCoordSys\n        });\n      });\n    }\n  };\n  /**\r\n   * Show tip manually by\r\n   * dispatchAction({\r\n   *     type: 'showTip',\r\n   *     x: 10,\r\n   *     y: 10\r\n   * });\r\n   * Or\r\n   * dispatchAction({\r\n   *      type: 'showTip',\r\n   *      seriesIndex: 0,\r\n   *      dataIndex or dataIndexInside or name\r\n   * });\r\n   *\r\n   *  TODO Batch\r\n   */\n  TooltipView.prototype.manuallyShowTip = function (tooltipModel, ecModel, api, payload) {\n    if (payload.from === this.uid || env.node || !api.getDom()) {\n      return;\n    }\n    var dispatchAction = makeDispatchAction(payload, api);\n    // Reset ticket\n    this._ticket = '';\n    // When triggered from axisPointer.\n    var dataByCoordSys = payload.dataByCoordSys;\n    var cmptRef = findComponentReference(payload, ecModel, api);\n    if (cmptRef) {\n      var rect = cmptRef.el.getBoundingRect().clone();\n      rect.applyTransform(cmptRef.el.transform);\n      this._tryShow({\n        offsetX: rect.x + rect.width / 2,\n        offsetY: rect.y + rect.height / 2,\n        target: cmptRef.el,\n        position: payload.position,\n        // When manully trigger, the mouse is not on the el, so we'd better to\n        // position tooltip on the bottom of the el and display arrow is possible.\n        positionDefault: 'bottom'\n      }, dispatchAction);\n    } else if (payload.tooltip && payload.x != null && payload.y != null) {\n      var el = proxyRect;\n      el.x = payload.x;\n      el.y = payload.y;\n      el.update();\n      getECData(el).tooltipConfig = {\n        name: null,\n        option: payload.tooltip\n      };\n      // Manually show tooltip while view is not using zrender elements.\n      this._tryShow({\n        offsetX: payload.x,\n        offsetY: payload.y,\n        target: el\n      }, dispatchAction);\n    } else if (dataByCoordSys) {\n      this._tryShow({\n        offsetX: payload.x,\n        offsetY: payload.y,\n        position: payload.position,\n        dataByCoordSys: dataByCoordSys,\n        tooltipOption: payload.tooltipOption\n      }, dispatchAction);\n    } else if (payload.seriesIndex != null) {\n      if (this._manuallyAxisShowTip(tooltipModel, ecModel, api, payload)) {\n        return;\n      }\n      var pointInfo = findPointFromSeries(payload, ecModel);\n      var cx = pointInfo.point[0];\n      var cy = pointInfo.point[1];\n      if (cx != null && cy != null) {\n        this._tryShow({\n          offsetX: cx,\n          offsetY: cy,\n          target: pointInfo.el,\n          position: payload.position,\n          // When manully trigger, the mouse is not on the el, so we'd better to\n          // position tooltip on the bottom of the el and display arrow is possible.\n          positionDefault: 'bottom'\n        }, dispatchAction);\n      }\n    } else if (payload.x != null && payload.y != null) {\n      // FIXME\n      // should wrap dispatchAction like `axisPointer/globalListener` ?\n      api.dispatchAction({\n        type: 'updateAxisPointer',\n        x: payload.x,\n        y: payload.y\n      });\n      this._tryShow({\n        offsetX: payload.x,\n        offsetY: payload.y,\n        position: payload.position,\n        target: api.getZr().findHover(payload.x, payload.y).target\n      }, dispatchAction);\n    }\n  };\n  TooltipView.prototype.manuallyHideTip = function (tooltipModel, ecModel, api, payload) {\n    var tooltipContent = this._tooltipContent;\n    if (this._tooltipModel) {\n      tooltipContent.hideLater(this._tooltipModel.get('hideDelay'));\n    }\n    this._lastX = this._lastY = this._lastDataByCoordSys = null;\n    if (payload.from !== this.uid) {\n      this._hide(makeDispatchAction(payload, api));\n    }\n  };\n  // Be compatible with previous design, that is, when tooltip.type is 'axis' and\n  // dispatchAction 'showTip' with seriesIndex and dataIndex will trigger axis pointer\n  // and tooltip.\n  TooltipView.prototype._manuallyAxisShowTip = function (tooltipModel, ecModel, api, payload) {\n    var seriesIndex = payload.seriesIndex;\n    var dataIndex = payload.dataIndex;\n    // @ts-ignore\n    var coordSysAxesInfo = ecModel.getComponent('axisPointer').coordSysAxesInfo;\n    if (seriesIndex == null || dataIndex == null || coordSysAxesInfo == null) {\n      return;\n    }\n    var seriesModel = ecModel.getSeriesByIndex(seriesIndex);\n    if (!seriesModel) {\n      return;\n    }\n    var data = seriesModel.getData();\n    var tooltipCascadedModel = buildTooltipModel([data.getItemModel(dataIndex), seriesModel, (seriesModel.coordinateSystem || {}).model], this._tooltipModel);\n    if (tooltipCascadedModel.get('trigger') !== 'axis') {\n      return;\n    }\n    api.dispatchAction({\n      type: 'updateAxisPointer',\n      seriesIndex: seriesIndex,\n      dataIndex: dataIndex,\n      position: payload.position\n    });\n    return true;\n  };\n  TooltipView.prototype._tryShow = function (e, dispatchAction) {\n    var el = e.target;\n    var tooltipModel = this._tooltipModel;\n    if (!tooltipModel) {\n      return;\n    }\n    // Save mouse x, mouse y. So we can try to keep showing the tip if chart is refreshed\n    this._lastX = e.offsetX;\n    this._lastY = e.offsetY;\n    var dataByCoordSys = e.dataByCoordSys;\n    if (dataByCoordSys && dataByCoordSys.length) {\n      this._showAxisTooltip(dataByCoordSys, e);\n    } else if (el) {\n      var ecData = getECData(el);\n      if (ecData.ssrType === 'legend') {\n        // Don't trigger tooltip for legend tooltip item\n        return;\n      }\n      this._lastDataByCoordSys = null;\n      var seriesDispatcher_1;\n      var cmptDispatcher_1;\n      findEventDispatcher(el, function (target) {\n        // Always show item tooltip if mouse is on the element with dataIndex\n        if (getECData(target).dataIndex != null) {\n          seriesDispatcher_1 = target;\n          return true;\n        }\n        // Tooltip provided directly. Like legend.\n        if (getECData(target).tooltipConfig != null) {\n          cmptDispatcher_1 = target;\n          return true;\n        }\n      }, true);\n      if (seriesDispatcher_1) {\n        this._showSeriesItemTooltip(e, seriesDispatcher_1, dispatchAction);\n      } else if (cmptDispatcher_1) {\n        this._showComponentItemTooltip(e, cmptDispatcher_1, dispatchAction);\n      } else {\n        this._hide(dispatchAction);\n      }\n    } else {\n      this._lastDataByCoordSys = null;\n      this._hide(dispatchAction);\n    }\n  };\n  TooltipView.prototype._showOrMove = function (tooltipModel, cb) {\n    // showDelay is used in this case: tooltip.enterable is set\n    // as true. User intent to move mouse into tooltip and click\n    // something. `showDelay` makes it easier to enter the content\n    // but tooltip do not move immediately.\n    var delay = tooltipModel.get('showDelay');\n    cb = bind(cb, this);\n    clearTimeout(this._showTimout);\n    delay > 0 ? this._showTimout = setTimeout(cb, delay) : cb();\n  };\n  TooltipView.prototype._showAxisTooltip = function (dataByCoordSys, e) {\n    var ecModel = this._ecModel;\n    var globalTooltipModel = this._tooltipModel;\n    var point = [e.offsetX, e.offsetY];\n    var singleTooltipModel = buildTooltipModel([e.tooltipOption], globalTooltipModel);\n    var renderMode = this._renderMode;\n    var cbParamsList = [];\n    var articleMarkup = createTooltipMarkup('section', {\n      blocks: [],\n      noHeader: true\n    });\n    // Only for legacy: `Serise['formatTooltip']` returns a string.\n    var markupTextArrLegacy = [];\n    var markupStyleCreator = new TooltipMarkupStyleCreator();\n    each(dataByCoordSys, function (itemCoordSys) {\n      each(itemCoordSys.dataByAxis, function (axisItem) {\n        var axisModel = ecModel.getComponent(axisItem.axisDim + 'Axis', axisItem.axisIndex);\n        var axisValue = axisItem.value;\n        if (!axisModel || axisValue == null) {\n          return;\n        }\n        var axisValueLabel = axisPointerViewHelper.getValueLabel(axisValue, axisModel.axis, ecModel, axisItem.seriesDataIndices, axisItem.valueLabelOpt);\n        var axisSectionMarkup = createTooltipMarkup('section', {\n          header: axisValueLabel,\n          noHeader: !trim(axisValueLabel),\n          sortBlocks: true,\n          blocks: []\n        });\n        articleMarkup.blocks.push(axisSectionMarkup);\n        each(axisItem.seriesDataIndices, function (idxItem) {\n          var series = ecModel.getSeriesByIndex(idxItem.seriesIndex);\n          var dataIndex = idxItem.dataIndexInside;\n          var cbParams = series.getDataParams(dataIndex);\n          // Can't find data.\n          if (cbParams.dataIndex < 0) {\n            return;\n          }\n          cbParams.axisDim = axisItem.axisDim;\n          cbParams.axisIndex = axisItem.axisIndex;\n          cbParams.axisType = axisItem.axisType;\n          cbParams.axisId = axisItem.axisId;\n          cbParams.axisValue = axisHelper.getAxisRawValue(axisModel.axis, {\n            value: axisValue\n          });\n          cbParams.axisValueLabel = axisValueLabel;\n          // Pre-create marker style for makers. Users can assemble richText\n          // text in `formatter` callback and use those markers style.\n          cbParams.marker = markupStyleCreator.makeTooltipMarker('item', convertToColorString(cbParams.color), renderMode);\n          var seriesTooltipResult = normalizeTooltipFormatResult(series.formatTooltip(dataIndex, true, null));\n          var frag = seriesTooltipResult.frag;\n          if (frag) {\n            var valueFormatter = buildTooltipModel([series], globalTooltipModel).get('valueFormatter');\n            axisSectionMarkup.blocks.push(valueFormatter ? extend({\n              valueFormatter: valueFormatter\n            }, frag) : frag);\n          }\n          if (seriesTooltipResult.text) {\n            markupTextArrLegacy.push(seriesTooltipResult.text);\n          }\n          cbParamsList.push(cbParams);\n        });\n      });\n    });\n    // In most cases, the second axis is displays upper on the first one.\n    // So we reverse it to look better.\n    articleMarkup.blocks.reverse();\n    markupTextArrLegacy.reverse();\n    var positionExpr = e.position;\n    var orderMode = singleTooltipModel.get('order');\n    var builtMarkupText = buildTooltipMarkup(articleMarkup, markupStyleCreator, renderMode, orderMode, ecModel.get('useUTC'), singleTooltipModel.get('textStyle'));\n    builtMarkupText && markupTextArrLegacy.unshift(builtMarkupText);\n    var blockBreak = renderMode === 'richText' ? '\\n\\n' : '<br/>';\n    var allMarkupText = markupTextArrLegacy.join(blockBreak);\n    this._showOrMove(singleTooltipModel, function () {\n      if (this._updateContentNotChangedOnAxis(dataByCoordSys, cbParamsList)) {\n        this._updatePosition(singleTooltipModel, positionExpr, point[0], point[1], this._tooltipContent, cbParamsList);\n      } else {\n        this._showTooltipContent(singleTooltipModel, allMarkupText, cbParamsList, Math.random() + '', point[0], point[1], positionExpr, null, markupStyleCreator);\n      }\n    });\n    // Do not trigger events here, because this branch only be entered\n    // from dispatchAction.\n  };\n  TooltipView.prototype._showSeriesItemTooltip = function (e, dispatcher, dispatchAction) {\n    var ecModel = this._ecModel;\n    var ecData = getECData(dispatcher);\n    // Use dataModel in element if possible\n    // Used when mouseover on a element like markPoint or edge\n    // In which case, the data is not main data in series.\n    var seriesIndex = ecData.seriesIndex;\n    var seriesModel = ecModel.getSeriesByIndex(seriesIndex);\n    // For example, graph link.\n    var dataModel = ecData.dataModel || seriesModel;\n    var dataIndex = ecData.dataIndex;\n    var dataType = ecData.dataType;\n    var data = dataModel.getData(dataType);\n    var renderMode = this._renderMode;\n    var positionDefault = e.positionDefault;\n    var tooltipModel = buildTooltipModel([data.getItemModel(dataIndex), dataModel, seriesModel && (seriesModel.coordinateSystem || {}).model], this._tooltipModel, positionDefault ? {\n      position: positionDefault\n    } : null);\n    var tooltipTrigger = tooltipModel.get('trigger');\n    if (tooltipTrigger != null && tooltipTrigger !== 'item') {\n      return;\n    }\n    var params = dataModel.getDataParams(dataIndex, dataType);\n    var markupStyleCreator = new TooltipMarkupStyleCreator();\n    // Pre-create marker style for makers. Users can assemble richText\n    // text in `formatter` callback and use those markers style.\n    params.marker = markupStyleCreator.makeTooltipMarker('item', convertToColorString(params.color), renderMode);\n    var seriesTooltipResult = normalizeTooltipFormatResult(dataModel.formatTooltip(dataIndex, false, dataType));\n    var orderMode = tooltipModel.get('order');\n    var valueFormatter = tooltipModel.get('valueFormatter');\n    var frag = seriesTooltipResult.frag;\n    var markupText = frag ? buildTooltipMarkup(valueFormatter ? extend({\n      valueFormatter: valueFormatter\n    }, frag) : frag, markupStyleCreator, renderMode, orderMode, ecModel.get('useUTC'), tooltipModel.get('textStyle')) : seriesTooltipResult.text;\n    var asyncTicket = 'item_' + dataModel.name + '_' + dataIndex;\n    this._showOrMove(tooltipModel, function () {\n      this._showTooltipContent(tooltipModel, markupText, params, asyncTicket, e.offsetX, e.offsetY, e.position, e.target, markupStyleCreator);\n    });\n    // FIXME\n    // duplicated showtip if manuallyShowTip is called from dispatchAction.\n    dispatchAction({\n      type: 'showTip',\n      dataIndexInside: dataIndex,\n      dataIndex: data.getRawIndex(dataIndex),\n      seriesIndex: seriesIndex,\n      from: this.uid\n    });\n  };\n  TooltipView.prototype._showComponentItemTooltip = function (e, el, dispatchAction) {\n    var isHTMLRenderMode = this._renderMode === 'html';\n    var ecData = getECData(el);\n    var tooltipConfig = ecData.tooltipConfig;\n    var tooltipOpt = tooltipConfig.option || {};\n    var encodeHTMLContent = tooltipOpt.encodeHTMLContent;\n    if (isString(tooltipOpt)) {\n      var content = tooltipOpt;\n      tooltipOpt = {\n        content: content,\n        // Fixed formatter\n        formatter: content\n      };\n      // when `tooltipConfig.option` is a string rather than an object,\n      // we can't know if the content needs to be encoded\n      // for the sake of security, encode it by default.\n      encodeHTMLContent = true;\n    }\n    if (encodeHTMLContent && isHTMLRenderMode && tooltipOpt.content) {\n      // clone might be unnecessary?\n      tooltipOpt = clone(tooltipOpt);\n      tooltipOpt.content = encodeHTML(tooltipOpt.content);\n    }\n    var tooltipModelCascade = [tooltipOpt];\n    var cmpt = this._ecModel.getComponent(ecData.componentMainType, ecData.componentIndex);\n    if (cmpt) {\n      tooltipModelCascade.push(cmpt);\n    }\n    // In most cases, component tooltip formatter has different params with series tooltip formatter,\n    // so that they cannot share the same formatter. Since the global tooltip formatter is used for series\n    // by convention, we do not use it as the default formatter for component.\n    tooltipModelCascade.push({\n      formatter: tooltipOpt.content\n    });\n    var positionDefault = e.positionDefault;\n    var subTooltipModel = buildTooltipModel(tooltipModelCascade, this._tooltipModel, positionDefault ? {\n      position: positionDefault\n    } : null);\n    var defaultHtml = subTooltipModel.get('content');\n    var asyncTicket = Math.random() + '';\n    // PENDING: this case do not support richText style yet.\n    var markupStyleCreator = new TooltipMarkupStyleCreator();\n    // Do not check whether `trigger` is 'none' here, because `trigger`\n    // only works on coordinate system. In fact, we have not found case\n    // that requires setting `trigger` nothing on component yet.\n    this._showOrMove(subTooltipModel, function () {\n      // Use formatterParams from element defined in component\n      // Avoid users modify it.\n      var formatterParams = clone(subTooltipModel.get('formatterParams') || {});\n      this._showTooltipContent(subTooltipModel, defaultHtml, formatterParams, asyncTicket, e.offsetX, e.offsetY, e.position, el, markupStyleCreator);\n    });\n    // If not dispatch showTip, tip may be hide triggered by axis.\n    dispatchAction({\n      type: 'showTip',\n      from: this.uid\n    });\n  };\n  TooltipView.prototype._showTooltipContent = function (\n  // Use Model<TooltipOption> insteadof TooltipModel because this model may be from series or other options.\n  // Instead of top level tooltip.\n  tooltipModel, defaultHtml, params, asyncTicket, x, y, positionExpr, el, markupStyleCreator) {\n    // Reset ticket\n    this._ticket = '';\n    if (!tooltipModel.get('showContent') || !tooltipModel.get('show')) {\n      return;\n    }\n    var tooltipContent = this._tooltipContent;\n    tooltipContent.setEnterable(tooltipModel.get('enterable'));\n    var formatter = tooltipModel.get('formatter');\n    positionExpr = positionExpr || tooltipModel.get('position');\n    var html = defaultHtml;\n    var nearPoint = this._getNearestPoint([x, y], params, tooltipModel.get('trigger'), tooltipModel.get('borderColor'));\n    var nearPointColor = nearPoint.color;\n    if (formatter) {\n      if (isString(formatter)) {\n        var useUTC = tooltipModel.ecModel.get('useUTC');\n        var params0 = isArray(params) ? params[0] : params;\n        var isTimeAxis = params0 && params0.axisType && params0.axisType.indexOf('time') >= 0;\n        html = formatter;\n        if (isTimeAxis) {\n          html = timeFormat(params0.axisValue, html, useUTC);\n        }\n        html = formatTpl(html, params, true);\n      } else if (isFunction(formatter)) {\n        var callback = bind(function (cbTicket, html) {\n          if (cbTicket === this._ticket) {\n            tooltipContent.setContent(html, markupStyleCreator, tooltipModel, nearPointColor, positionExpr);\n            this._updatePosition(tooltipModel, positionExpr, x, y, tooltipContent, params, el);\n          }\n        }, this);\n        this._ticket = asyncTicket;\n        html = formatter(params, asyncTicket, callback);\n      } else {\n        html = formatter;\n      }\n    }\n    tooltipContent.setContent(html, markupStyleCreator, tooltipModel, nearPointColor, positionExpr);\n    tooltipContent.show(tooltipModel, nearPointColor);\n    this._updatePosition(tooltipModel, positionExpr, x, y, tooltipContent, params, el);\n  };\n  TooltipView.prototype._getNearestPoint = function (point, tooltipDataParams, trigger, borderColor) {\n    if (trigger === 'axis' || isArray(tooltipDataParams)) {\n      return {\n        color: borderColor || (this._renderMode === 'html' ? '#fff' : 'none')\n      };\n    }\n    if (!isArray(tooltipDataParams)) {\n      return {\n        color: borderColor || tooltipDataParams.color || tooltipDataParams.borderColor\n      };\n    }\n  };\n  TooltipView.prototype._updatePosition = function (tooltipModel, positionExpr, x,\n  // Mouse x\n  y,\n  // Mouse y\n  content, params, el) {\n    var viewWidth = this._api.getWidth();\n    var viewHeight = this._api.getHeight();\n    positionExpr = positionExpr || tooltipModel.get('position');\n    var contentSize = content.getSize();\n    var align = tooltipModel.get('align');\n    var vAlign = tooltipModel.get('verticalAlign');\n    var rect = el && el.getBoundingRect().clone();\n    el && rect.applyTransform(el.transform);\n    if (isFunction(positionExpr)) {\n      // Callback of position can be an array or a string specify the position\n      positionExpr = positionExpr([x, y], params, content.el, rect, {\n        viewSize: [viewWidth, viewHeight],\n        contentSize: contentSize.slice()\n      });\n    }\n    if (isArray(positionExpr)) {\n      x = parsePercent(positionExpr[0], viewWidth);\n      y = parsePercent(positionExpr[1], viewHeight);\n    } else if (isObject(positionExpr)) {\n      var boxLayoutPosition = positionExpr;\n      boxLayoutPosition.width = contentSize[0];\n      boxLayoutPosition.height = contentSize[1];\n      var layoutRect = getLayoutRect(boxLayoutPosition, {\n        width: viewWidth,\n        height: viewHeight\n      });\n      x = layoutRect.x;\n      y = layoutRect.y;\n      align = null;\n      // When positionExpr is left/top/right/bottom,\n      // align and verticalAlign will not work.\n      vAlign = null;\n    }\n    // Specify tooltip position by string 'top' 'bottom' 'left' 'right' around graphic element\n    else if (isString(positionExpr) && el) {\n      var pos = calcTooltipPosition(positionExpr, rect, contentSize, tooltipModel.get('borderWidth'));\n      x = pos[0];\n      y = pos[1];\n    } else {\n      var pos = refixTooltipPosition(x, y, content, viewWidth, viewHeight, align ? null : 20, vAlign ? null : 20);\n      x = pos[0];\n      y = pos[1];\n    }\n    align && (x -= isCenterAlign(align) ? contentSize[0] / 2 : align === 'right' ? contentSize[0] : 0);\n    vAlign && (y -= isCenterAlign(vAlign) ? contentSize[1] / 2 : vAlign === 'bottom' ? contentSize[1] : 0);\n    if (shouldTooltipConfine(tooltipModel)) {\n      var pos = confineTooltipPosition(x, y, content, viewWidth, viewHeight);\n      x = pos[0];\n      y = pos[1];\n    }\n    content.moveTo(x, y);\n  };\n  // FIXME\n  // Should we remove this but leave this to user?\n  TooltipView.prototype._updateContentNotChangedOnAxis = function (dataByCoordSys, cbParamsList) {\n    var lastCoordSys = this._lastDataByCoordSys;\n    var lastCbParamsList = this._cbParamsList;\n    var contentNotChanged = !!lastCoordSys && lastCoordSys.length === dataByCoordSys.length;\n    contentNotChanged && each(lastCoordSys, function (lastItemCoordSys, indexCoordSys) {\n      var lastDataByAxis = lastItemCoordSys.dataByAxis || [];\n      var thisItemCoordSys = dataByCoordSys[indexCoordSys] || {};\n      var thisDataByAxis = thisItemCoordSys.dataByAxis || [];\n      contentNotChanged = contentNotChanged && lastDataByAxis.length === thisDataByAxis.length;\n      contentNotChanged && each(lastDataByAxis, function (lastItem, indexAxis) {\n        var thisItem = thisDataByAxis[indexAxis] || {};\n        var lastIndices = lastItem.seriesDataIndices || [];\n        var newIndices = thisItem.seriesDataIndices || [];\n        contentNotChanged = contentNotChanged && lastItem.value === thisItem.value && lastItem.axisType === thisItem.axisType && lastItem.axisId === thisItem.axisId && lastIndices.length === newIndices.length;\n        contentNotChanged && each(lastIndices, function (lastIdxItem, j) {\n          var newIdxItem = newIndices[j];\n          contentNotChanged = contentNotChanged && lastIdxItem.seriesIndex === newIdxItem.seriesIndex && lastIdxItem.dataIndex === newIdxItem.dataIndex;\n        });\n        // check is cbParams data value changed\n        lastCbParamsList && each(lastItem.seriesDataIndices, function (idxItem) {\n          var seriesIdx = idxItem.seriesIndex;\n          var cbParams = cbParamsList[seriesIdx];\n          var lastCbParams = lastCbParamsList[seriesIdx];\n          if (cbParams && lastCbParams && lastCbParams.data !== cbParams.data) {\n            contentNotChanged = false;\n          }\n        });\n      });\n    });\n    this._lastDataByCoordSys = dataByCoordSys;\n    this._cbParamsList = cbParamsList;\n    return !!contentNotChanged;\n  };\n  TooltipView.prototype._hide = function (dispatchAction) {\n    // Do not directly hideLater here, because this behavior may be prevented\n    // in dispatchAction when showTip is dispatched.\n    // FIXME\n    // duplicated hideTip if manuallyHideTip is called from dispatchAction.\n    this._lastDataByCoordSys = null;\n    dispatchAction({\n      type: 'hideTip',\n      from: this.uid\n    });\n  };\n  TooltipView.prototype.dispose = function (ecModel, api) {\n    if (env.node || !api.getDom()) {\n      return;\n    }\n    clear(this, '_updatePosition');\n    this._tooltipContent.dispose();\n    globalListener.unregister('itemTooltip', api);\n  };\n  TooltipView.type = 'tooltip';\n  return TooltipView;\n}(ComponentView);\n/**\r\n * From top to bottom. (the last one should be globalTooltipModel);\r\n */\nfunction buildTooltipModel(modelCascade, globalTooltipModel, defaultTooltipOption) {\n  // Last is always tooltip model.\n  var ecModel = globalTooltipModel.ecModel;\n  var resultModel;\n  if (defaultTooltipOption) {\n    resultModel = new Model(defaultTooltipOption, ecModel, ecModel);\n    resultModel = new Model(globalTooltipModel.option, resultModel, ecModel);\n  } else {\n    resultModel = globalTooltipModel;\n  }\n  for (var i = modelCascade.length - 1; i >= 0; i--) {\n    var tooltipOpt = modelCascade[i];\n    if (tooltipOpt) {\n      if (tooltipOpt instanceof Model) {\n        tooltipOpt = tooltipOpt.get('tooltip', true);\n      }\n      // In each data item tooltip can be simply write:\n      // {\n      //  value: 10,\n      //  tooltip: 'Something you need to know'\n      // }\n      if (isString(tooltipOpt)) {\n        tooltipOpt = {\n          formatter: tooltipOpt\n        };\n      }\n      if (tooltipOpt) {\n        resultModel = new Model(tooltipOpt, resultModel, ecModel);\n      }\n    }\n  }\n  return resultModel;\n}\nfunction makeDispatchAction(payload, api) {\n  return payload.dispatchAction || bind(api.dispatchAction, api);\n}\nfunction refixTooltipPosition(x, y, content, viewWidth, viewHeight, gapH, gapV) {\n  var size = content.getSize();\n  var width = size[0];\n  var height = size[1];\n  if (gapH != null) {\n    // Add extra 2 pixels for this case:\n    // At present the \"values\" in default tooltip are using CSS `float: right`.\n    // When the right edge of the tooltip box is on the right side of the\n    // viewport, the `float` layout might push the \"values\" to the second line.\n    if (x + width + gapH + 2 > viewWidth) {\n      x -= width + gapH;\n    } else {\n      x += gapH;\n    }\n  }\n  if (gapV != null) {\n    if (y + height + gapV > viewHeight) {\n      y -= height + gapV;\n    } else {\n      y += gapV;\n    }\n  }\n  return [x, y];\n}\nfunction confineTooltipPosition(x, y, content, viewWidth, viewHeight) {\n  var size = content.getSize();\n  var width = size[0];\n  var height = size[1];\n  x = Math.min(x + width, viewWidth) - width;\n  y = Math.min(y + height, viewHeight) - height;\n  x = Math.max(x, 0);\n  y = Math.max(y, 0);\n  return [x, y];\n}\nfunction calcTooltipPosition(position, rect, contentSize, borderWidth) {\n  var domWidth = contentSize[0];\n  var domHeight = contentSize[1];\n  var offset = Math.ceil(Math.SQRT2 * borderWidth) + 8;\n  var x = 0;\n  var y = 0;\n  var rectWidth = rect.width;\n  var rectHeight = rect.height;\n  switch (position) {\n    case 'inside':\n      x = rect.x + rectWidth / 2 - domWidth / 2;\n      y = rect.y + rectHeight / 2 - domHeight / 2;\n      break;\n    case 'top':\n      x = rect.x + rectWidth / 2 - domWidth / 2;\n      y = rect.y - domHeight - offset;\n      break;\n    case 'bottom':\n      x = rect.x + rectWidth / 2 - domWidth / 2;\n      y = rect.y + rectHeight + offset;\n      break;\n    case 'left':\n      x = rect.x - domWidth - offset;\n      y = rect.y + rectHeight / 2 - domHeight / 2;\n      break;\n    case 'right':\n      x = rect.x + rectWidth + offset;\n      y = rect.y + rectHeight / 2 - domHeight / 2;\n  }\n  return [x, y];\n}\nfunction isCenterAlign(align) {\n  return align === 'center' || align === 'middle';\n}\n/**\r\n * Find target component by payload like:\r\n * ```js\r\n * { legendId: 'some_id', name: 'xxx' }\r\n * { toolboxIndex: 1, name: 'xxx' }\r\n * { geoName: 'some_name', name: 'xxx' }\r\n * ```\r\n * PENDING: at present only\r\n *\r\n * If not found, return null/undefined.\r\n */\nfunction findComponentReference(payload, ecModel, api) {\n  var queryOptionMap = preParseFinder(payload).queryOptionMap;\n  var componentMainType = queryOptionMap.keys()[0];\n  if (!componentMainType || componentMainType === 'series') {\n    return;\n  }\n  var queryResult = queryReferringComponents(ecModel, componentMainType, queryOptionMap.get(componentMainType), {\n    useDefault: false,\n    enableAll: false,\n    enableNone: false\n  });\n  var model = queryResult.models[0];\n  if (!model) {\n    return;\n  }\n  var view = api.getViewOfComponentModel(model);\n  var el;\n  view.group.traverse(function (subEl) {\n    var tooltipConfig = getECData(subEl).tooltipConfig;\n    if (tooltipConfig && tooltipConfig.name === payload.name) {\n      el = subEl;\n      return true; // stop\n    }\n  });\n  if (el) {\n    return {\n      componentMainType: componentMainType,\n      componentIndex: model.componentIndex,\n      el: el\n    };\n  }\n}\nexport default TooltipView;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC;;;AAED;AACA;;;;;;;;;;;;;;;;;AAiBA,GACA;AACA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAI,YAAY,IAAI,6LAAA,CAAA,OAAI,CAAC;IACvB,OAAO;QACL,GAAG,CAAC;QACJ,GAAG,CAAC;QACJ,OAAO;QACP,QAAQ;IACV;AACF;AACA,IAAI,cAAc,WAAW,GAAE,SAAU,MAAM;IAC7C,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE,aAAa;IACvB,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,YAAY,IAAI;QAC7B,OAAO;IACT;IACA,YAAY,SAAS,CAAC,IAAI,GAAG,SAAU,OAAO,EAAE,GAAG;QACjD,IAAI,6IAAA,CAAA,UAAG,CAAC,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI;YAC7B;QACF;QACA,IAAI,eAAe,QAAQ,YAAY,CAAC;QACxC,IAAI,aAAa,IAAI,CAAC,WAAW,GAAG,CAAA,GAAA,+IAAA,CAAA,uBAAoB,AAAD,EAAE,aAAa,GAAG,CAAC;QAC1E,IAAI,CAAC,eAAe,GAAG,eAAe,aAAa,IAAI,4KAAA,CAAA,UAAkB,CAAC,OAAO,IAAI,4KAAA,CAAA,UAAkB,CAAC,KAAK;YAC3G,UAAU,aAAa,GAAG,CAAC,gBAAgB,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QAC3F;IACF;IACA,YAAY,SAAS,CAAC,MAAM,GAAG,SAAU,YAAY,EAAE,OAAO,EAAE,GAAG;QACjE,IAAI,6IAAA,CAAA,UAAG,CAAC,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI;YAC7B;QACF;QACA,QAAQ;QACR,IAAI,CAAC,KAAK,CAAC,SAAS;QACpB,IAAI,CAAC,aAAa,GAAG;QACrB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,iBAAiB,IAAI,CAAC,eAAe;QACzC,eAAe,MAAM,CAAC;QACtB,eAAe,YAAY,CAAC,aAAa,GAAG,CAAC;QAC7C,IAAI,CAAC,mBAAmB;QACxB,IAAI,CAAC,SAAS;QACd,UAAU;QACV,iFAAiF;QACjF,8EAA8E;QAC9E,8FAA8F;QAC9F,gCAAgC;QAChC,4DAA4D;QAC5D,IAAI,IAAI,CAAC,WAAW,KAAK,cAAc,aAAa,GAAG,CAAC,uBAAuB;YAC7E,CAAA,GAAA,kJAAA,CAAA,iBAAc,AAAD,EAAE,IAAI,EAAE,mBAAmB,IAAI;QAC9C,OAAO;YACL,CAAA,GAAA,kJAAA,CAAA,QAAK,AAAD,EAAE,IAAI,EAAE;QACd;IACF;IACA,YAAY,SAAS,CAAC,mBAAmB,GAAG;QAC1C,IAAI,eAAe,IAAI,CAAC,aAAa;QACrC,IAAI,YAAY,aAAa,GAAG,CAAC;QACjC,CAAA,GAAA,4KAAA,CAAA,WAAuB,AAAD,EAAE,eAAe,IAAI,CAAC,IAAI,EAAE,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,SAAU,WAAW,EAAE,CAAC,EAAE,cAAc;YAC7F,oDAAoD;YACpD,IAAI,cAAc,QAAQ;gBACxB,IAAI,UAAU,OAAO,CAAC,gBAAgB,GAAG;oBACvC,IAAI,CAAC,QAAQ,CAAC,GAAG;gBACnB,OAAO,IAAI,gBAAgB,SAAS;oBAClC,IAAI,CAAC,KAAK,CAAC;gBACb;YACF;QACF,GAAG,IAAI;IACT;IACA,YAAY,SAAS,CAAC,SAAS,GAAG;QAChC,IAAI,eAAe,IAAI,CAAC,aAAa;QACrC,IAAI,UAAU,IAAI,CAAC,QAAQ;QAC3B,IAAI,MAAM,IAAI,CAAC,IAAI;QACnB,IAAI,YAAY,aAAa,GAAG,CAAC;QACjC,+CAA+C;QAC/C,IAAI,IAAI,CAAC,MAAM,IAAI,QAAQ,IAAI,CAAC,MAAM,IAAI,QAIvC,cAAc,UAAU,cAAc,SAAS;YAChD,IAAI,SAAS,IAAI;YACjB,aAAa,IAAI,CAAC,qBAAqB;YACvC,IAAI,CAAC,qBAAqB,GAAG,WAAW;gBACtC,qDAAqD;gBACrD,4CAA4C;gBAC5C,QAAQ;gBACR,CAAC,IAAI,UAAU,MAAM,OAAO,eAAe,CAAC,cAAc,SAAS,KAAK;oBACtE,GAAG,OAAO,MAAM;oBAChB,GAAG,OAAO,MAAM;oBAChB,gBAAgB,OAAO,mBAAmB;gBAC5C;YACF;QACF;IACF;IACA;;;;;;;;;;;;;;;GAeC,GACD,YAAY,SAAS,CAAC,eAAe,GAAG,SAAU,YAAY,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO;QACnF,IAAI,QAAQ,IAAI,KAAK,IAAI,CAAC,GAAG,IAAI,6IAAA,CAAA,UAAG,CAAC,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI;YAC1D;QACF;QACA,IAAI,iBAAiB,mBAAmB,SAAS;QACjD,eAAe;QACf,IAAI,CAAC,OAAO,GAAG;QACf,mCAAmC;QACnC,IAAI,iBAAiB,QAAQ,cAAc;QAC3C,IAAI,UAAU,uBAAuB,SAAS,SAAS;QACvD,IAAI,SAAS;YACX,IAAI,OAAO,QAAQ,EAAE,CAAC,eAAe,GAAG,KAAK;YAC7C,KAAK,cAAc,CAAC,QAAQ,EAAE,CAAC,SAAS;YACxC,IAAI,CAAC,QAAQ,CAAC;gBACZ,SAAS,KAAK,CAAC,GAAG,KAAK,KAAK,GAAG;gBAC/B,SAAS,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG;gBAChC,QAAQ,QAAQ,EAAE;gBAClB,UAAU,QAAQ,QAAQ;gBAC1B,sEAAsE;gBACtE,0EAA0E;gBAC1E,iBAAiB;YACnB,GAAG;QACL,OAAO,IAAI,QAAQ,OAAO,IAAI,QAAQ,CAAC,IAAI,QAAQ,QAAQ,CAAC,IAAI,MAAM;YACpE,IAAI,KAAK;YACT,GAAG,CAAC,GAAG,QAAQ,CAAC;YAChB,GAAG,CAAC,GAAG,QAAQ,CAAC;YAChB,GAAG,MAAM;YACT,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,IAAI,aAAa,GAAG;gBAC5B,MAAM;gBACN,QAAQ,QAAQ,OAAO;YACzB;YACA,kEAAkE;YAClE,IAAI,CAAC,QAAQ,CAAC;gBACZ,SAAS,QAAQ,CAAC;gBAClB,SAAS,QAAQ,CAAC;gBAClB,QAAQ;YACV,GAAG;QACL,OAAO,IAAI,gBAAgB;YACzB,IAAI,CAAC,QAAQ,CAAC;gBACZ,SAAS,QAAQ,CAAC;gBAClB,SAAS,QAAQ,CAAC;gBAClB,UAAU,QAAQ,QAAQ;gBAC1B,gBAAgB;gBAChB,eAAe,QAAQ,aAAa;YACtC,GAAG;QACL,OAAO,IAAI,QAAQ,WAAW,IAAI,MAAM;YACtC,IAAI,IAAI,CAAC,oBAAoB,CAAC,cAAc,SAAS,KAAK,UAAU;gBAClE;YACF;YACA,IAAI,YAAY,CAAA,GAAA,iLAAA,CAAA,UAAmB,AAAD,EAAE,SAAS;YAC7C,IAAI,KAAK,UAAU,KAAK,CAAC,EAAE;YAC3B,IAAI,KAAK,UAAU,KAAK,CAAC,EAAE;YAC3B,IAAI,MAAM,QAAQ,MAAM,MAAM;gBAC5B,IAAI,CAAC,QAAQ,CAAC;oBACZ,SAAS;oBACT,SAAS;oBACT,QAAQ,UAAU,EAAE;oBACpB,UAAU,QAAQ,QAAQ;oBAC1B,sEAAsE;oBACtE,0EAA0E;oBAC1E,iBAAiB;gBACnB,GAAG;YACL;QACF,OAAO,IAAI,QAAQ,CAAC,IAAI,QAAQ,QAAQ,CAAC,IAAI,MAAM;YACjD,QAAQ;YACR,iEAAiE;YACjE,IAAI,cAAc,CAAC;gBACjB,MAAM;gBACN,GAAG,QAAQ,CAAC;gBACZ,GAAG,QAAQ,CAAC;YACd;YACA,IAAI,CAAC,QAAQ,CAAC;gBACZ,SAAS,QAAQ,CAAC;gBAClB,SAAS,QAAQ,CAAC;gBAClB,UAAU,QAAQ,QAAQ;gBAC1B,QAAQ,IAAI,KAAK,GAAG,SAAS,CAAC,QAAQ,CAAC,EAAE,QAAQ,CAAC,EAAE,MAAM;YAC5D,GAAG;QACL;IACF;IACA,YAAY,SAAS,CAAC,eAAe,GAAG,SAAU,YAAY,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO;QACnF,IAAI,iBAAiB,IAAI,CAAC,eAAe;QACzC,IAAI,IAAI,CAAC,aAAa,EAAE;YACtB,eAAe,SAAS,CAAC,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC;QAClD;QACA,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,MAAM,GAAG,IAAI,CAAC,mBAAmB,GAAG;QACvD,IAAI,QAAQ,IAAI,KAAK,IAAI,CAAC,GAAG,EAAE;YAC7B,IAAI,CAAC,KAAK,CAAC,mBAAmB,SAAS;QACzC;IACF;IACA,+EAA+E;IAC/E,oFAAoF;IACpF,eAAe;IACf,YAAY,SAAS,CAAC,oBAAoB,GAAG,SAAU,YAAY,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO;QACxF,IAAI,cAAc,QAAQ,WAAW;QACrC,IAAI,YAAY,QAAQ,SAAS;QACjC,aAAa;QACb,IAAI,mBAAmB,QAAQ,YAAY,CAAC,eAAe,gBAAgB;QAC3E,IAAI,eAAe,QAAQ,aAAa,QAAQ,oBAAoB,MAAM;YACxE;QACF;QACA,IAAI,cAAc,QAAQ,gBAAgB,CAAC;QAC3C,IAAI,CAAC,aAAa;YAChB;QACF;QACA,IAAI,OAAO,YAAY,OAAO;QAC9B,IAAI,uBAAuB,kBAAkB;YAAC,KAAK,YAAY,CAAC;YAAY;YAAa,CAAC,YAAY,gBAAgB,IAAI,CAAC,CAAC,EAAE,KAAK;SAAC,EAAE,IAAI,CAAC,aAAa;QACxJ,IAAI,qBAAqB,GAAG,CAAC,eAAe,QAAQ;YAClD;QACF;QACA,IAAI,cAAc,CAAC;YACjB,MAAM;YACN,aAAa;YACb,WAAW;YACX,UAAU,QAAQ,QAAQ;QAC5B;QACA,OAAO;IACT;IACA,YAAY,SAAS,CAAC,QAAQ,GAAG,SAAU,CAAC,EAAE,cAAc;QAC1D,IAAI,KAAK,EAAE,MAAM;QACjB,IAAI,eAAe,IAAI,CAAC,aAAa;QACrC,IAAI,CAAC,cAAc;YACjB;QACF;QACA,qFAAqF;QACrF,IAAI,CAAC,MAAM,GAAG,EAAE,OAAO;QACvB,IAAI,CAAC,MAAM,GAAG,EAAE,OAAO;QACvB,IAAI,iBAAiB,EAAE,cAAc;QACrC,IAAI,kBAAkB,eAAe,MAAM,EAAE;YAC3C,IAAI,CAAC,gBAAgB,CAAC,gBAAgB;QACxC,OAAO,IAAI,IAAI;YACb,IAAI,SAAS,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE;YACvB,IAAI,OAAO,OAAO,KAAK,UAAU;gBAC/B,gDAAgD;gBAChD;YACF;YACA,IAAI,CAAC,mBAAmB,GAAG;YAC3B,IAAI;YACJ,IAAI;YACJ,CAAA,GAAA,+IAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,SAAU,MAAM;gBACtC,qEAAqE;gBACrE,IAAI,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,SAAS,IAAI,MAAM;oBACvC,qBAAqB;oBACrB,OAAO;gBACT;gBACA,0CAA0C;gBAC1C,IAAI,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,aAAa,IAAI,MAAM;oBAC3C,mBAAmB;oBACnB,OAAO;gBACT;YACF,GAAG;YACH,IAAI,oBAAoB;gBACtB,IAAI,CAAC,sBAAsB,CAAC,GAAG,oBAAoB;YACrD,OAAO,IAAI,kBAAkB;gBAC3B,IAAI,CAAC,yBAAyB,CAAC,GAAG,kBAAkB;YACtD,OAAO;gBACL,IAAI,CAAC,KAAK,CAAC;YACb;QACF,OAAO;YACL,IAAI,CAAC,mBAAmB,GAAG;YAC3B,IAAI,CAAC,KAAK,CAAC;QACb;IACF;IACA,YAAY,SAAS,CAAC,WAAW,GAAG,SAAU,YAAY,EAAE,EAAE;QAC5D,2DAA2D;QAC3D,4DAA4D;QAC5D,8DAA8D;QAC9D,uCAAuC;QACvC,IAAI,QAAQ,aAAa,GAAG,CAAC;QAC7B,KAAK,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,IAAI,IAAI;QAClB,aAAa,IAAI,CAAC,WAAW;QAC7B,QAAQ,IAAI,IAAI,CAAC,WAAW,GAAG,WAAW,IAAI,SAAS;IACzD;IACA,YAAY,SAAS,CAAC,gBAAgB,GAAG,SAAU,cAAc,EAAE,CAAC;QAClE,IAAI,UAAU,IAAI,CAAC,QAAQ;QAC3B,IAAI,qBAAqB,IAAI,CAAC,aAAa;QAC3C,IAAI,QAAQ;YAAC,EAAE,OAAO;YAAE,EAAE,OAAO;SAAC;QAClC,IAAI,qBAAqB,kBAAkB;YAAC,EAAE,aAAa;SAAC,EAAE;QAC9D,IAAI,aAAa,IAAI,CAAC,WAAW;QACjC,IAAI,eAAe,EAAE;QACrB,IAAI,gBAAgB,CAAA,GAAA,uKAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW;YACjD,QAAQ,EAAE;YACV,UAAU;QACZ;QACA,+DAA+D;QAC/D,IAAI,sBAAsB,EAAE;QAC5B,IAAI,qBAAqB,IAAI,uKAAA,CAAA,4BAAyB;QACtD,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB,SAAU,YAAY;YACzC,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,aAAa,UAAU,EAAE,SAAU,QAAQ;gBAC9C,IAAI,YAAY,QAAQ,YAAY,CAAC,SAAS,OAAO,GAAG,QAAQ,SAAS,SAAS;gBAClF,IAAI,YAAY,SAAS,KAAK;gBAC9B,IAAI,CAAC,aAAa,aAAa,MAAM;oBACnC;gBACF;gBACA,IAAI,iBAAiB,CAAA,GAAA,wKAAA,CAAA,gBAAmC,AAAD,EAAE,WAAW,UAAU,IAAI,EAAE,SAAS,SAAS,iBAAiB,EAAE,SAAS,aAAa;gBAC/I,IAAI,oBAAoB,CAAA,GAAA,uKAAA,CAAA,sBAAmB,AAAD,EAAE,WAAW;oBACrD,QAAQ;oBACR,UAAU,CAAC,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE;oBAChB,YAAY;oBACZ,QAAQ,EAAE;gBACZ;gBACA,cAAc,MAAM,CAAC,IAAI,CAAC;gBAC1B,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,SAAS,iBAAiB,EAAE,SAAU,OAAO;oBAChD,IAAI,SAAS,QAAQ,gBAAgB,CAAC,QAAQ,WAAW;oBACzD,IAAI,YAAY,QAAQ,eAAe;oBACvC,IAAI,WAAW,OAAO,aAAa,CAAC;oBACpC,mBAAmB;oBACnB,IAAI,SAAS,SAAS,GAAG,GAAG;wBAC1B;oBACF;oBACA,SAAS,OAAO,GAAG,SAAS,OAAO;oBACnC,SAAS,SAAS,GAAG,SAAS,SAAS;oBACvC,SAAS,QAAQ,GAAG,SAAS,QAAQ;oBACrC,SAAS,MAAM,GAAG,SAAS,MAAM;oBACjC,SAAS,SAAS,GAAG,CAAA,GAAA,qJAAA,CAAA,kBAA0B,AAAD,EAAE,UAAU,IAAI,EAAE;wBAC9D,OAAO;oBACT;oBACA,SAAS,cAAc,GAAG;oBAC1B,kEAAkE;oBAClE,4DAA4D;oBAC5D,SAAS,MAAM,GAAG,mBAAmB,iBAAiB,CAAC,QAAQ,CAAA,GAAA,gKAAA,CAAA,uBAAoB,AAAD,EAAE,SAAS,KAAK,GAAG;oBACrG,IAAI,sBAAsB,CAAA,GAAA,8JAAA,CAAA,+BAA4B,AAAD,EAAE,OAAO,aAAa,CAAC,WAAW,MAAM;oBAC7F,IAAI,OAAO,oBAAoB,IAAI;oBACnC,IAAI,MAAM;wBACR,IAAI,iBAAiB,kBAAkB;4BAAC;yBAAO,EAAE,oBAAoB,GAAG,CAAC;wBACzE,kBAAkB,MAAM,CAAC,IAAI,CAAC,iBAAiB,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE;4BACpD,gBAAgB;wBAClB,GAAG,QAAQ;oBACb;oBACA,IAAI,oBAAoB,IAAI,EAAE;wBAC5B,oBAAoB,IAAI,CAAC,oBAAoB,IAAI;oBACnD;oBACA,aAAa,IAAI,CAAC;gBACpB;YACF;QACF;QACA,qEAAqE;QACrE,mCAAmC;QACnC,cAAc,MAAM,CAAC,OAAO;QAC5B,oBAAoB,OAAO;QAC3B,IAAI,eAAe,EAAE,QAAQ;QAC7B,IAAI,YAAY,mBAAmB,GAAG,CAAC;QACvC,IAAI,kBAAkB,CAAA,GAAA,uKAAA,CAAA,qBAAkB,AAAD,EAAE,eAAe,oBAAoB,YAAY,WAAW,QAAQ,GAAG,CAAC,WAAW,mBAAmB,GAAG,CAAC;QACjJ,mBAAmB,oBAAoB,OAAO,CAAC;QAC/C,IAAI,aAAa,eAAe,aAAa,SAAS;QACtD,IAAI,gBAAgB,oBAAoB,IAAI,CAAC;QAC7C,IAAI,CAAC,WAAW,CAAC,oBAAoB;YACnC,IAAI,IAAI,CAAC,8BAA8B,CAAC,gBAAgB,eAAe;gBACrE,IAAI,CAAC,eAAe,CAAC,oBAAoB,cAAc,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE,IAAI,CAAC,eAAe,EAAE;YACnG,OAAO;gBACL,IAAI,CAAC,mBAAmB,CAAC,oBAAoB,eAAe,cAAc,KAAK,MAAM,KAAK,IAAI,KAAK,CAAC,EAAE,EAAE,KAAK,CAAC,EAAE,EAAE,cAAc,MAAM;YACxI;QACF;IACA,kEAAkE;IAClE,uBAAuB;IACzB;IACA,YAAY,SAAS,CAAC,sBAAsB,GAAG,SAAU,CAAC,EAAE,UAAU,EAAE,cAAc;QACpF,IAAI,UAAU,IAAI,CAAC,QAAQ;QAC3B,IAAI,SAAS,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE;QACvB,uCAAuC;QACvC,0DAA0D;QAC1D,sDAAsD;QACtD,IAAI,cAAc,OAAO,WAAW;QACpC,IAAI,cAAc,QAAQ,gBAAgB,CAAC;QAC3C,2BAA2B;QAC3B,IAAI,YAAY,OAAO,SAAS,IAAI;QACpC,IAAI,YAAY,OAAO,SAAS;QAChC,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,OAAO,UAAU,OAAO,CAAC;QAC7B,IAAI,aAAa,IAAI,CAAC,WAAW;QACjC,IAAI,kBAAkB,EAAE,eAAe;QACvC,IAAI,eAAe,kBAAkB;YAAC,KAAK,YAAY,CAAC;YAAY;YAAW,eAAe,CAAC,YAAY,gBAAgB,IAAI,CAAC,CAAC,EAAE,KAAK;SAAC,EAAE,IAAI,CAAC,aAAa,EAAE,kBAAkB;YAC/K,UAAU;QACZ,IAAI;QACJ,IAAI,iBAAiB,aAAa,GAAG,CAAC;QACtC,IAAI,kBAAkB,QAAQ,mBAAmB,QAAQ;YACvD;QACF;QACA,IAAI,SAAS,UAAU,aAAa,CAAC,WAAW;QAChD,IAAI,qBAAqB,IAAI,uKAAA,CAAA,4BAAyB;QACtD,kEAAkE;QAClE,4DAA4D;QAC5D,OAAO,MAAM,GAAG,mBAAmB,iBAAiB,CAAC,QAAQ,CAAA,GAAA,gKAAA,CAAA,uBAAoB,AAAD,EAAE,OAAO,KAAK,GAAG;QACjG,IAAI,sBAAsB,CAAA,GAAA,8JAAA,CAAA,+BAA4B,AAAD,EAAE,UAAU,aAAa,CAAC,WAAW,OAAO;QACjG,IAAI,YAAY,aAAa,GAAG,CAAC;QACjC,IAAI,iBAAiB,aAAa,GAAG,CAAC;QACtC,IAAI,OAAO,oBAAoB,IAAI;QACnC,IAAI,aAAa,OAAO,CAAA,GAAA,uKAAA,CAAA,qBAAkB,AAAD,EAAE,iBAAiB,CAAA,GAAA,8IAAA,CAAA,SAAM,AAAD,EAAE;YACjE,gBAAgB;QAClB,GAAG,QAAQ,MAAM,oBAAoB,YAAY,WAAW,QAAQ,GAAG,CAAC,WAAW,aAAa,GAAG,CAAC,gBAAgB,oBAAoB,IAAI;QAC5I,IAAI,cAAc,UAAU,UAAU,IAAI,GAAG,MAAM;QACnD,IAAI,CAAC,WAAW,CAAC,cAAc;YAC7B,IAAI,CAAC,mBAAmB,CAAC,cAAc,YAAY,QAAQ,aAAa,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,EAAE,MAAM,EAAE;QACtH;QACA,QAAQ;QACR,uEAAuE;QACvE,eAAe;YACb,MAAM;YACN,iBAAiB;YACjB,WAAW,KAAK,WAAW,CAAC;YAC5B,aAAa;YACb,MAAM,IAAI,CAAC,GAAG;QAChB;IACF;IACA,YAAY,SAAS,CAAC,yBAAyB,GAAG,SAAU,CAAC,EAAE,EAAE,EAAE,cAAc;QAC/E,IAAI,mBAAmB,IAAI,CAAC,WAAW,KAAK;QAC5C,IAAI,SAAS,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE;QACvB,IAAI,gBAAgB,OAAO,aAAa;QACxC,IAAI,aAAa,cAAc,MAAM,IAAI,CAAC;QAC1C,IAAI,oBAAoB,WAAW,iBAAiB;QACpD,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;YACxB,IAAI,UAAU;YACd,aAAa;gBACX,SAAS;gBACT,kBAAkB;gBAClB,WAAW;YACb;YACA,iEAAiE;YACjE,mDAAmD;YACnD,kDAAkD;YAClD,oBAAoB;QACtB;QACA,IAAI,qBAAqB,oBAAoB,WAAW,OAAO,EAAE;YAC/D,8BAA8B;YAC9B,aAAa,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE;YACnB,WAAW,OAAO,GAAG,CAAA,GAAA,6IAAA,CAAA,aAAU,AAAD,EAAE,WAAW,OAAO;QACpD;QACA,IAAI,sBAAsB;YAAC;SAAW;QACtC,IAAI,OAAO,IAAI,CAAC,QAAQ,CAAC,YAAY,CAAC,OAAO,iBAAiB,EAAE,OAAO,cAAc;QACrF,IAAI,MAAM;YACR,oBAAoB,IAAI,CAAC;QAC3B;QACA,iGAAiG;QACjG,sGAAsG;QACtG,0EAA0E;QAC1E,oBAAoB,IAAI,CAAC;YACvB,WAAW,WAAW,OAAO;QAC/B;QACA,IAAI,kBAAkB,EAAE,eAAe;QACvC,IAAI,kBAAkB,kBAAkB,qBAAqB,IAAI,CAAC,aAAa,EAAE,kBAAkB;YACjG,UAAU;QACZ,IAAI;QACJ,IAAI,cAAc,gBAAgB,GAAG,CAAC;QACtC,IAAI,cAAc,KAAK,MAAM,KAAK;QAClC,wDAAwD;QACxD,IAAI,qBAAqB,IAAI,uKAAA,CAAA,4BAAyB;QACtD,mEAAmE;QACnE,mEAAmE;QACnE,4DAA4D;QAC5D,IAAI,CAAC,WAAW,CAAC,iBAAiB;YAChC,wDAAwD;YACxD,yBAAyB;YACzB,IAAI,kBAAkB,CAAA,GAAA,8IAAA,CAAA,QAAK,AAAD,EAAE,gBAAgB,GAAG,CAAC,sBAAsB,CAAC;YACvE,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,aAAa,iBAAiB,aAAa,EAAE,OAAO,EAAE,EAAE,OAAO,EAAE,EAAE,QAAQ,EAAE,IAAI;QAC7H;QACA,8DAA8D;QAC9D,eAAe;YACb,MAAM;YACN,MAAM,IAAI,CAAC,GAAG;QAChB;IACF;IACA,YAAY,SAAS,CAAC,mBAAmB,GAAG,SAC5C,0GAA0G;IAC1G,gCAAgC;IAChC,YAAY,EAAE,WAAW,EAAE,MAAM,EAAE,WAAW,EAAE,CAAC,EAAE,CAAC,EAAE,YAAY,EAAE,EAAE,EAAE,kBAAkB;QACxF,eAAe;QACf,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,aAAa,GAAG,CAAC,kBAAkB,CAAC,aAAa,GAAG,CAAC,SAAS;YACjE;QACF;QACA,IAAI,iBAAiB,IAAI,CAAC,eAAe;QACzC,eAAe,YAAY,CAAC,aAAa,GAAG,CAAC;QAC7C,IAAI,YAAY,aAAa,GAAG,CAAC;QACjC,eAAe,gBAAgB,aAAa,GAAG,CAAC;QAChD,IAAI,OAAO;QACX,IAAI,YAAY,IAAI,CAAC,gBAAgB,CAAC;YAAC;YAAG;SAAE,EAAE,QAAQ,aAAa,GAAG,CAAC,YAAY,aAAa,GAAG,CAAC;QACpG,IAAI,iBAAiB,UAAU,KAAK;QACpC,IAAI,WAAW;YACb,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,YAAY;gBACvB,IAAI,SAAS,aAAa,OAAO,CAAC,GAAG,CAAC;gBACtC,IAAI,UAAU,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,UAAU,MAAM,CAAC,EAAE,GAAG;gBAC5C,IAAI,aAAa,WAAW,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,OAAO,CAAC,WAAW;gBACpF,OAAO;gBACP,IAAI,YAAY;oBACd,OAAO,CAAA,GAAA,8IAAA,CAAA,SAAU,AAAD,EAAE,QAAQ,SAAS,EAAE,MAAM;gBAC7C;gBACA,OAAO,CAAA,GAAA,gKAAA,CAAA,YAAS,AAAD,EAAE,MAAM,QAAQ;YACjC,OAAO,IAAI,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD,EAAE,YAAY;gBAChC,IAAI,WAAW,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,SAAU,QAAQ,EAAE,IAAI;oBAC1C,IAAI,aAAa,IAAI,CAAC,OAAO,EAAE;wBAC7B,eAAe,UAAU,CAAC,MAAM,oBAAoB,cAAc,gBAAgB;wBAClF,IAAI,CAAC,eAAe,CAAC,cAAc,cAAc,GAAG,GAAG,gBAAgB,QAAQ;oBACjF;gBACF,GAAG,IAAI;gBACP,IAAI,CAAC,OAAO,GAAG;gBACf,OAAO,UAAU,QAAQ,aAAa;YACxC,OAAO;gBACL,OAAO;YACT;QACF;QACA,eAAe,UAAU,CAAC,MAAM,oBAAoB,cAAc,gBAAgB;QAClF,eAAe,IAAI,CAAC,cAAc;QAClC,IAAI,CAAC,eAAe,CAAC,cAAc,cAAc,GAAG,GAAG,gBAAgB,QAAQ;IACjF;IACA,YAAY,SAAS,CAAC,gBAAgB,GAAG,SAAU,KAAK,EAAE,iBAAiB,EAAE,OAAO,EAAE,WAAW;QAC/F,IAAI,YAAY,UAAU,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,oBAAoB;YACpD,OAAO;gBACL,OAAO,eAAe,CAAC,IAAI,CAAC,WAAW,KAAK,SAAS,SAAS,MAAM;YACtE;QACF;QACA,IAAI,CAAC,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,oBAAoB;YAC/B,OAAO;gBACL,OAAO,eAAe,kBAAkB,KAAK,IAAI,kBAAkB,WAAW;YAChF;QACF;IACF;IACA,YAAY,SAAS,CAAC,eAAe,GAAG,SAAU,YAAY,EAAE,YAAY,EAAE,CAAC,EAC/E,UAAU;IACV,CAAC,EACD,UAAU;IACV,OAAO,EAAE,MAAM,EAAE,EAAE;QACjB,IAAI,YAAY,IAAI,CAAC,IAAI,CAAC,QAAQ;QAClC,IAAI,aAAa,IAAI,CAAC,IAAI,CAAC,SAAS;QACpC,eAAe,gBAAgB,aAAa,GAAG,CAAC;QAChD,IAAI,cAAc,QAAQ,OAAO;QACjC,IAAI,QAAQ,aAAa,GAAG,CAAC;QAC7B,IAAI,SAAS,aAAa,GAAG,CAAC;QAC9B,IAAI,OAAO,MAAM,GAAG,eAAe,GAAG,KAAK;QAC3C,MAAM,KAAK,cAAc,CAAC,GAAG,SAAS;QACtC,IAAI,CAAA,GAAA,8IAAA,CAAA,aAAU,AAAD,EAAE,eAAe;YAC5B,wEAAwE;YACxE,eAAe,aAAa;gBAAC;gBAAG;aAAE,EAAE,QAAQ,QAAQ,EAAE,EAAE,MAAM;gBAC5D,UAAU;oBAAC;oBAAW;iBAAW;gBACjC,aAAa,YAAY,KAAK;YAChC;QACF;QACA,IAAI,CAAA,GAAA,8IAAA,CAAA,UAAO,AAAD,EAAE,eAAe;YACzB,IAAI,CAAA,GAAA,gJAAA,CAAA,eAAY,AAAD,EAAE,YAAY,CAAC,EAAE,EAAE;YAClC,IAAI,CAAA,GAAA,gJAAA,CAAA,eAAY,AAAD,EAAE,YAAY,CAAC,EAAE,EAAE;QACpC,OAAO,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,eAAe;YACjC,IAAI,oBAAoB;YACxB,kBAAkB,KAAK,GAAG,WAAW,CAAC,EAAE;YACxC,kBAAkB,MAAM,GAAG,WAAW,CAAC,EAAE;YACzC,IAAI,aAAa,CAAA,GAAA,gJAAA,CAAA,gBAAa,AAAD,EAAE,mBAAmB;gBAChD,OAAO;gBACP,QAAQ;YACV;YACA,IAAI,WAAW,CAAC;YAChB,IAAI,WAAW,CAAC;YAChB,QAAQ;YACR,8CAA8C;YAC9C,yCAAyC;YACzC,SAAS;QACX,OAEK,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,iBAAiB,IAAI;YACrC,IAAI,MAAM,oBAAoB,cAAc,MAAM,aAAa,aAAa,GAAG,CAAC;YAChF,IAAI,GAAG,CAAC,EAAE;YACV,IAAI,GAAG,CAAC,EAAE;QACZ,OAAO;YACL,IAAI,MAAM,qBAAqB,GAAG,GAAG,SAAS,WAAW,YAAY,QAAQ,OAAO,IAAI,SAAS,OAAO;YACxG,IAAI,GAAG,CAAC,EAAE;YACV,IAAI,GAAG,CAAC,EAAE;QACZ;QACA,SAAS,CAAC,KAAK,cAAc,SAAS,WAAW,CAAC,EAAE,GAAG,IAAI,UAAU,UAAU,WAAW,CAAC,EAAE,GAAG,CAAC;QACjG,UAAU,CAAC,KAAK,cAAc,UAAU,WAAW,CAAC,EAAE,GAAG,IAAI,WAAW,WAAW,WAAW,CAAC,EAAE,GAAG,CAAC;QACrG,IAAI,CAAA,GAAA,gKAAA,CAAA,uBAAoB,AAAD,EAAE,eAAe;YACtC,IAAI,MAAM,uBAAuB,GAAG,GAAG,SAAS,WAAW;YAC3D,IAAI,GAAG,CAAC,EAAE;YACV,IAAI,GAAG,CAAC,EAAE;QACZ;QACA,QAAQ,MAAM,CAAC,GAAG;IACpB;IACA,QAAQ;IACR,gDAAgD;IAChD,YAAY,SAAS,CAAC,8BAA8B,GAAG,SAAU,cAAc,EAAE,YAAY;QAC3F,IAAI,eAAe,IAAI,CAAC,mBAAmB;QAC3C,IAAI,mBAAmB,IAAI,CAAC,aAAa;QACzC,IAAI,oBAAoB,CAAC,CAAC,gBAAgB,aAAa,MAAM,KAAK,eAAe,MAAM;QACvF,qBAAqB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,cAAc,SAAU,gBAAgB,EAAE,aAAa;YAC/E,IAAI,iBAAiB,iBAAiB,UAAU,IAAI,EAAE;YACtD,IAAI,mBAAmB,cAAc,CAAC,cAAc,IAAI,CAAC;YACzD,IAAI,iBAAiB,iBAAiB,UAAU,IAAI,EAAE;YACtD,oBAAoB,qBAAqB,eAAe,MAAM,KAAK,eAAe,MAAM;YACxF,qBAAqB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,gBAAgB,SAAU,QAAQ,EAAE,SAAS;gBACrE,IAAI,WAAW,cAAc,CAAC,UAAU,IAAI,CAAC;gBAC7C,IAAI,cAAc,SAAS,iBAAiB,IAAI,EAAE;gBAClD,IAAI,aAAa,SAAS,iBAAiB,IAAI,EAAE;gBACjD,oBAAoB,qBAAqB,SAAS,KAAK,KAAK,SAAS,KAAK,IAAI,SAAS,QAAQ,KAAK,SAAS,QAAQ,IAAI,SAAS,MAAM,KAAK,SAAS,MAAM,IAAI,YAAY,MAAM,KAAK,WAAW,MAAM;gBACxM,qBAAqB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,aAAa,SAAU,WAAW,EAAE,CAAC;oBAC7D,IAAI,aAAa,UAAU,CAAC,EAAE;oBAC9B,oBAAoB,qBAAqB,YAAY,WAAW,KAAK,WAAW,WAAW,IAAI,YAAY,SAAS,KAAK,WAAW,SAAS;gBAC/I;gBACA,uCAAuC;gBACvC,oBAAoB,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,SAAS,iBAAiB,EAAE,SAAU,OAAO;oBACpE,IAAI,YAAY,QAAQ,WAAW;oBACnC,IAAI,WAAW,YAAY,CAAC,UAAU;oBACtC,IAAI,eAAe,gBAAgB,CAAC,UAAU;oBAC9C,IAAI,YAAY,gBAAgB,aAAa,IAAI,KAAK,SAAS,IAAI,EAAE;wBACnE,oBAAoB;oBACtB;gBACF;YACF;QACF;QACA,IAAI,CAAC,mBAAmB,GAAG;QAC3B,IAAI,CAAC,aAAa,GAAG;QACrB,OAAO,CAAC,CAAC;IACX;IACA,YAAY,SAAS,CAAC,KAAK,GAAG,SAAU,cAAc;QACpD,yEAAyE;QACzE,gDAAgD;QAChD,QAAQ;QACR,uEAAuE;QACvE,IAAI,CAAC,mBAAmB,GAAG;QAC3B,eAAe;YACb,MAAM;YACN,MAAM,IAAI,CAAC,GAAG;QAChB;IACF;IACA,YAAY,SAAS,CAAC,OAAO,GAAG,SAAU,OAAO,EAAE,GAAG;QACpD,IAAI,6IAAA,CAAA,UAAG,CAAC,IAAI,IAAI,CAAC,IAAI,MAAM,IAAI;YAC7B;QACF;QACA,CAAA,GAAA,kJAAA,CAAA,QAAK,AAAD,EAAE,IAAI,EAAE;QACZ,IAAI,CAAC,eAAe,CAAC,OAAO;QAC5B,CAAA,GAAA,4KAAA,CAAA,aAAyB,AAAD,EAAE,eAAe;IAC3C;IACA,YAAY,IAAI,GAAG;IACnB,OAAO;AACT,EAAE,mJAAA,CAAA,UAAa;AACf;;CAEC,GACD,SAAS,kBAAkB,YAAY,EAAE,kBAAkB,EAAE,oBAAoB;IAC/E,gCAAgC;IAChC,IAAI,UAAU,mBAAmB,OAAO;IACxC,IAAI;IACJ,IAAI,sBAAsB;QACxB,cAAc,IAAI,gJAAA,CAAA,UAAK,CAAC,sBAAsB,SAAS;QACvD,cAAc,IAAI,gJAAA,CAAA,UAAK,CAAC,mBAAmB,MAAM,EAAE,aAAa;IAClE,OAAO;QACL,cAAc;IAChB;IACA,IAAK,IAAI,IAAI,aAAa,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QACjD,IAAI,aAAa,YAAY,CAAC,EAAE;QAChC,IAAI,YAAY;YACd,IAAI,sBAAsB,gJAAA,CAAA,UAAK,EAAE;gBAC/B,aAAa,WAAW,GAAG,CAAC,WAAW;YACzC;YACA,iDAAiD;YACjD,IAAI;YACJ,cAAc;YACd,yCAAyC;YACzC,IAAI;YACJ,IAAI,CAAA,GAAA,8IAAA,CAAA,WAAQ,AAAD,EAAE,aAAa;gBACxB,aAAa;oBACX,WAAW;gBACb;YACF;YACA,IAAI,YAAY;gBACd,cAAc,IAAI,gJAAA,CAAA,UAAK,CAAC,YAAY,aAAa;YACnD;QACF;IACF;IACA,OAAO;AACT;AACA,SAAS,mBAAmB,OAAO,EAAE,GAAG;IACtC,OAAO,QAAQ,cAAc,IAAI,CAAA,GAAA,8IAAA,CAAA,OAAI,AAAD,EAAE,IAAI,cAAc,EAAE;AAC5D;AACA,SAAS,qBAAqB,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU,EAAE,IAAI,EAAE,IAAI;IAC5E,IAAI,OAAO,QAAQ,OAAO;IAC1B,IAAI,QAAQ,IAAI,CAAC,EAAE;IACnB,IAAI,SAAS,IAAI,CAAC,EAAE;IACpB,IAAI,QAAQ,MAAM;QAChB,oCAAoC;QACpC,2EAA2E;QAC3E,qEAAqE;QACrE,2EAA2E;QAC3E,IAAI,IAAI,QAAQ,OAAO,IAAI,WAAW;YACpC,KAAK,QAAQ;QACf,OAAO;YACL,KAAK;QACP;IACF;IACA,IAAI,QAAQ,MAAM;QAChB,IAAI,IAAI,SAAS,OAAO,YAAY;YAClC,KAAK,SAAS;QAChB,OAAO;YACL,KAAK;QACP;IACF;IACA,OAAO;QAAC;QAAG;KAAE;AACf;AACA,SAAS,uBAAuB,CAAC,EAAE,CAAC,EAAE,OAAO,EAAE,SAAS,EAAE,UAAU;IAClE,IAAI,OAAO,QAAQ,OAAO;IAC1B,IAAI,QAAQ,IAAI,CAAC,EAAE;IACnB,IAAI,SAAS,IAAI,CAAC,EAAE;IACpB,IAAI,KAAK,GAAG,CAAC,IAAI,OAAO,aAAa;IACrC,IAAI,KAAK,GAAG,CAAC,IAAI,QAAQ,cAAc;IACvC,IAAI,KAAK,GAAG,CAAC,GAAG;IAChB,IAAI,KAAK,GAAG,CAAC,GAAG;IAChB,OAAO;QAAC;QAAG;KAAE;AACf;AACA,SAAS,oBAAoB,QAAQ,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW;IACnE,IAAI,WAAW,WAAW,CAAC,EAAE;IAC7B,IAAI,YAAY,WAAW,CAAC,EAAE;IAC9B,IAAI,SAAS,KAAK,IAAI,CAAC,KAAK,KAAK,GAAG,eAAe;IACnD,IAAI,IAAI;IACR,IAAI,IAAI;IACR,IAAI,YAAY,KAAK,KAAK;IAC1B,IAAI,aAAa,KAAK,MAAM;IAC5B,OAAQ;QACN,KAAK;YACH,IAAI,KAAK,CAAC,GAAG,YAAY,IAAI,WAAW;YACxC,IAAI,KAAK,CAAC,GAAG,aAAa,IAAI,YAAY;YAC1C;QACF,KAAK;YACH,IAAI,KAAK,CAAC,GAAG,YAAY,IAAI,WAAW;YACxC,IAAI,KAAK,CAAC,GAAG,YAAY;YACzB;QACF,KAAK;YACH,IAAI,KAAK,CAAC,GAAG,YAAY,IAAI,WAAW;YACxC,IAAI,KAAK,CAAC,GAAG,aAAa;YAC1B;QACF,KAAK;YACH,IAAI,KAAK,CAAC,GAAG,WAAW;YACxB,IAAI,KAAK,CAAC,GAAG,aAAa,IAAI,YAAY;YAC1C;QACF,KAAK;YACH,IAAI,KAAK,CAAC,GAAG,YAAY;YACzB,IAAI,KAAK,CAAC,GAAG,aAAa,IAAI,YAAY;IAC9C;IACA,OAAO;QAAC;QAAG;KAAE;AACf;AACA,SAAS,cAAc,KAAK;IAC1B,OAAO,UAAU,YAAY,UAAU;AACzC;AACA;;;;;;;;;;CAUC,GACD,SAAS,uBAAuB,OAAO,EAAE,OAAO,EAAE,GAAG;IACnD,IAAI,iBAAiB,CAAA,GAAA,+IAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,cAAc;IAC3D,IAAI,oBAAoB,eAAe,IAAI,EAAE,CAAC,EAAE;IAChD,IAAI,CAAC,qBAAqB,sBAAsB,UAAU;QACxD;IACF;IACA,IAAI,cAAc,CAAA,GAAA,+IAAA,CAAA,2BAAwB,AAAD,EAAE,SAAS,mBAAmB,eAAe,GAAG,CAAC,oBAAoB;QAC5G,YAAY;QACZ,WAAW;QACX,YAAY;IACd;IACA,IAAI,QAAQ,YAAY,MAAM,CAAC,EAAE;IACjC,IAAI,CAAC,OAAO;QACV;IACF;IACA,IAAI,OAAO,IAAI,uBAAuB,CAAC;IACvC,IAAI;IACJ,KAAK,KAAK,CAAC,QAAQ,CAAC,SAAU,KAAK;QACjC,IAAI,gBAAgB,CAAA,GAAA,oJAAA,CAAA,YAAS,AAAD,EAAE,OAAO,aAAa;QAClD,IAAI,iBAAiB,cAAc,IAAI,KAAK,QAAQ,IAAI,EAAE;YACxD,KAAK;YACL,OAAO,MAAM,OAAO;QACtB;IACF;IACA,IAAI,IAAI;QACN,OAAO;YACL,mBAAmB;YACnB,gBAAgB,MAAM,cAAc;YACpC,IAAI;QACN;IACF;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/tooltip/install.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { install as installAxisPointer } from '../axisPointer/install.js';\nimport { use } from '../../extension.js';\nimport TooltipModel from './TooltipModel.js';\nimport TooltipView from './TooltipView.js';\nimport { noop } from 'zrender/lib/core/util.js';\nexport function install(registers) {\n  use(installAxisPointer);\n  registers.registerComponentModel(TooltipModel);\n  registers.registerComponentView(TooltipView);\n  /**\r\n   * @action\r\n   * @property {string} type\r\n   * @property {number} seriesIndex\r\n   * @property {number} dataIndex\r\n   * @property {number} [x]\r\n   * @property {number} [y]\r\n   */\n  registers.registerAction({\n    type: 'showTip',\n    event: 'showTip',\n    update: 'tooltip:manuallyShowTip'\n  }, noop);\n  registers.registerAction({\n    type: 'hideTip',\n    event: 'hideTip',\n    update: 'tooltip:manuallyHideTip'\n  }, noop);\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;;;;;;AACO,SAAS,QAAQ,SAAS;IAC/B,CAAA,GAAA,2IAAA,CAAA,MAAG,AAAD,EAAE,qKAAA,CAAA,UAAkB;IACtB,UAAU,sBAAsB,CAAC,sKAAA,CAAA,UAAY;IAC7C,UAAU,qBAAqB,CAAC,qKAAA,CAAA,UAAW;IAC3C;;;;;;;GAOC,GACD,UAAU,cAAc,CAAC;QACvB,MAAM;QACN,OAAO;QACP,QAAQ;IACV,GAAG,8IAAA,CAAA,OAAI;IACP,UAAU,cAAc,CAAC;QACvB,MAAM;QACN,OAAO;QACP,QAAQ;IACV,GAAG,8IAAA,CAAA,OAAI;AACT", "ignoreList": [0], "debugId": null}}]}