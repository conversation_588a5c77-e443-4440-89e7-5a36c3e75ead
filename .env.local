# Revantad Store Admin Dashboard - Environment Configuration
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
# Get these from your Supabase project dashboard: https://supabase.com/dashboard
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# =============================================================================
# CLOUDINARY CONFIGURATION
# =============================================================================
# Get these from your Cloudinary dashboard: https://cloudinary.com/console
NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# =============================================================================
# AUTHENTICATION CONFIGURATION
# =============================================================================
# Generate a secure random string for production
NEXTAUTH_SECRET=your_nextauth_secret_key_here
NEXTAUTH_URL=http://localhost:3000

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
# Optional: Set to 'production' for production builds
NODE_ENV=development

# Optional: Enable debug logging
DEBUG=false

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================
# 1. Copy this file to .env.local
# 2. Replace all placeholder values with your actual credentials
# 3. Never commit .env.local to version control
# 4. For production, set NEXTAUTH_URL to your actual domain
# 5. Run 'npm run db:setup' to set up your database schema
