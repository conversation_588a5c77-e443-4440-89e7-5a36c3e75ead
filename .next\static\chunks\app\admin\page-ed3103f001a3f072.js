(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[698],{283:(e,t,r)=>{"use strict";r.d(t,{A:()=>i,AuthProvider:()=>n});var a=r(5155),s=r(2115);let l=(0,s.createContext)(void 0);function i(){let e=(0,s.useContext)(l);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function n(e){let{children:t}=e,[r,i]=(0,s.useState)(null),[n,o]=(0,s.useState)(!0);(0,s.useEffect)(()=>{let e=localStorage.getItem("revantad_user");if(e)try{let t=JSON.parse(e);i(t)}catch(e){console.error("Error parsing saved user data:",e),localStorage.removeItem("revantad_user")}o(!1)},[]);let d=async(e,t)=>{o(!0);try{if(await new Promise(e=>setTimeout(e,1e3)),"<EMAIL>"!==e||"admin123"!==t)return o(!1),!1;{let e={id:"1",email:"<EMAIL>",name:"Admin User",role:"Store Owner"};return i(e),localStorage.setItem("revantad_user",JSON.stringify(e)),o(!1),!0}}catch(e){return console.error("Login error:",e),o(!1),!1}};return(0,a.jsx)(l.Provider,{value:{user:r,login:d,logout:()=>{i(null),localStorage.removeItem("revantad_user")},isLoading:n,isAuthenticated:!!r},children:t})}},3528:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>ek});var a=r(5155),s=r(2115),l=r(1362),i=r(7340),n=r(7108),o=r(7580),d=r(7213),c=r(7924),x=r(2098),m=r(3509),u=r(1007),g=r(4835),p=r(6874),b=r.n(p),h=r(283);function y(e){let{activeSection:t,setActiveSection:r}=e,[p,y]=(0,s.useState)(""),{setTheme:f,resolvedTheme:j}=(0,l.D)(),[v,N]=(0,s.useState)(!1),[k,w]=(0,s.useState)(!1),{user:S,logout:A}=(0,h.A)();(0,s.useEffect)(()=>{w(!0)},[]);let C=[{id:"dashboard",label:"Home Dashboard",icon:i.A,tooltip:"Dashboard Overview"},{id:"products",label:"Product Lists",icon:n.A,tooltip:"Manage Products"},{id:"debts",label:"Customer Debts",icon:o.A,tooltip:"Customer Debt Management"},{id:"family-gallery",label:"Family Gallery",icon:d.A,tooltip:"Family Photos & Memories"}];return(0,a.jsx)("header",{className:"fixed top-0 left-0 right-0 z-50 bg-white dark:bg-gray-900 border-b border-gray-200 dark:border-gray-700 shadow-sm transition-all duration-300",style:{backgroundColor:"dark"===j?"#111827":"#ffffff",borderColor:"dark"===j?"#374151":"#e5e7eb"},children:(0,a.jsxs)("div",{className:"grid grid-cols-3 items-center h-16 px-3 sm:px-4 lg:px-6 max-w-full overflow-hidden gap-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 w-auto",children:[(0,a.jsxs)(b(),{href:"/landing",className:"flex items-center space-x-2 hover:opacity-80 transition-opacity flex-shrink-0",title:"Return to Front Page",children:[(0,a.jsx)("div",{className:"w-10 h-10 hero-gradient rounded-full flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white font-bold text-lg",children:"R"})}),(0,a.jsx)("span",{className:"text-xl font-bold text-gradient hidden sm:block",children:"Revantad"})]}),(0,a.jsx)("form",{onSubmit:e=>{e.preventDefault(),console.log("Searching for:",p)},className:"w-32 sm:w-36 md:w-40 lg:w-44 xl:w-48",children:(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Search",value:p,onChange:e=>y(e.target.value),className:"w-full pl-10 pr-4 py-2 bg-gray-100 dark:bg-slate-700 border-0 rounded-full text-sm placeholder-gray-500 dark:placeholder-gray-400 focus:ring-2 focus:ring-green-500 focus:bg-white dark:focus:bg-slate-600 transition-all duration-200"})]})})]}),(0,a.jsx)("div",{className:"hidden sm:flex items-center justify-center",children:(0,a.jsx)("div",{className:"flex items-center space-x-3 md:space-x-4 lg:space-x-5",children:C.map(e=>{let s=e.icon,l=t===e.id;return(0,a.jsxs)("button",{onClick:()=>r(e.id),className:"relative p-3 md:p-3.5 lg:p-4 rounded-xl transition-all duration-200 group min-w-[48px] md:min-w-[52px] lg:min-w-[56px] hover:scale-[1.05]",style:{backgroundColor:l?"dark"===j?"rgba(34, 197, 94, 0.2)":"rgba(34, 197, 94, 0.1)":"transparent",color:l?"dark"===j?"#4ade80":"#16a34a":"dark"===j?"#cbd5e1":"#374151",boxShadow:l?"0 2px 8px rgba(34, 197, 94, 0.2)":"none"},title:e.tooltip,onMouseEnter:e=>{l||(e.currentTarget.style.backgroundColor="dark"===j?"rgba(71, 85, 105, 0.5)":"rgba(243, 244, 246, 0.8)",e.currentTarget.style.color="dark"===j?"#f1f5f9":"#111827")},onMouseLeave:e=>{l||(e.currentTarget.style.backgroundColor="transparent",e.currentTarget.style.color="dark"===j?"#cbd5e1":"#374151")},children:[(0,a.jsx)(s,{className:"h-5 w-5 md:h-6 md:w-6 mx-auto transition-all duration-200"}),l&&(0,a.jsx)("div",{className:"absolute -bottom-1 left-1/2 transform -translate-x-1/2 w-10 md:w-12 lg:w-14 h-1 bg-green-500 rounded-full"}),(0,a.jsxs)("div",{className:"absolute top-full mt-3 left-1/2 transform -translate-x-1/2 bg-gray-900 dark:bg-slate-700 text-white text-xs px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-all duration-200 whitespace-nowrap pointer-events-none shadow-lg z-10",children:[e.tooltip,(0,a.jsx)("div",{className:"absolute -top-1 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-gray-900 dark:bg-slate-700 rotate-45"})]})]},e.id)})})}),(0,a.jsx)("div",{className:"sm:hidden flex items-center justify-center",children:(0,a.jsx)("button",{onClick:()=>r("dashboard"===t?"products":"dashboard"),className:"p-2 rounded-lg text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors",title:"Toggle View",children:"dashboard"===t?(0,a.jsx)(n.A,{className:"h-5 w-5"}):(0,a.jsx)(i.A,{className:"h-5 w-5"})})}),(0,a.jsxs)("div",{className:"flex items-center justify-end space-x-3",children:[(0,a.jsx)("button",{onClick:()=>{if(!k)return;let e=document.documentElement;"dark"===j?(e.classList.remove("dark"),f("light")):(e.classList.add("dark"),f("dark"))},className:"p-2.5 rounded-xl text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-slate-700 hover:text-gray-800 dark:hover:text-gray-200 transition-all duration-200 flex-shrink-0",title:k?"Switch to ".concat("dark"===j?"light":"dark"," mode (Current: ").concat(j,")"):"Toggle theme",disabled:!k,children:k?"dark"===j?(0,a.jsx)(x.A,{className:"h-5 w-5"}):(0,a.jsx)(m.A,{className:"h-5 w-5"}):(0,a.jsx)("div",{className:"h-5 w-5 bg-gray-400 rounded-full animate-pulse"})}),(0,a.jsxs)("div",{className:"relative flex-shrink-0",children:[(0,a.jsxs)("button",{onClick:()=>N(!v),className:"flex items-center space-x-2 p-2 rounded-xl hover:bg-gray-100 dark:hover:bg-slate-700 transition-all duration-200 group",children:[(0,a.jsx)("div",{className:"w-8 h-8 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center shadow-sm group-hover:shadow-md transition-shadow",children:(0,a.jsx)(u.A,{className:"h-4 w-4 text-white"})}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300 hidden sm:block group-hover:text-gray-900 dark:group-hover:text-white transition-colors",children:(null==S?void 0:S.name)||"Admin"})]}),v&&(0,a.jsxs)("div",{className:"absolute right-0 mt-2 w-48 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-700 py-1",children:[(0,a.jsxs)("div",{className:"px-4 py-2 border-b border-gray-200 dark:border-slate-700",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:(null==S?void 0:S.name)||"Admin User"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:(null==S?void 0:S.email)||"<EMAIL>"})]}),(0,a.jsxs)("button",{onClick:()=>r("settings"),className:"w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center space-x-2",children:[(0,a.jsx)(u.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Settings"})]}),(0,a.jsxs)("button",{onClick:()=>{A(),window.location.href="/login"},className:"w-full text-left px-4 py-2 text-sm text-red-600 dark:text-red-400 hover:bg-gray-100 dark:hover:bg-slate-700 flex items-center space-x-2",children:[(0,a.jsx)(g.A,{className:"h-4 w-4"}),(0,a.jsx)("span",{children:"Logout"})]})]})]})]})]})})}var f=r(2713),j=r(9676),v=r(9074),N=r(381),k=r(3052),w=r(2355);function S(e){let{activeSection:t,setActiveSection:r}=e,{resolvedTheme:i}=(0,l.D)(),[n,o]=(0,s.useState)(!1),[d,c]=(0,s.useState)(!1);(0,s.useEffect)(()=>{c(!0);let e=localStorage.getItem("sidebar-collapsed");null!==e&&o(JSON.parse(e))},[]),(0,s.useEffect)(()=>{d&&localStorage.setItem("sidebar-collapsed",JSON.stringify(n))},[n,d]);let x=[{id:"api-graphing",label:"API Graphing & Visuals",icon:f.A},{id:"history",label:"History",icon:j.A},{id:"calendar",label:"Calendar",icon:v.A},{id:"settings",label:"Settings",icon:N.A}];return(0,a.jsx)("div",{className:"shadow-xl border-r sticky top-16 h-[calc(100vh-4rem)] overflow-hidden transition-all duration-300 ease-in-out ".concat(n?"w-20 min-w-20":"w-80 min-w-80"),style:{backgroundColor:"dark"===i?"#1e293b":"#ffffff",borderColor:"dark"===i?"#334155":"#e5e7eb",borderWidth:"1px",boxShadow:"dark"===i?"0 25px 50px -12px rgba(0, 0, 0, 0.5), 0 0 0 1px rgba(255, 255, 255, 0.05)":"0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)"},children:(0,a.jsxs)("div",{className:"flex flex-col h-full",children:[(0,a.jsxs)("div",{className:"sticky top-0 z-20 transition-all duration-300 backdrop-blur-md ".concat(n?"px-3 py-3":"px-6 py-4"),style:{background:"dark"===i?"linear-gradient(135deg, rgba(30, 41, 59, 0.98) 0%, rgba(51, 65, 85, 0.95) 100%)":"linear-gradient(135deg, rgba(255, 255, 255, 0.98) 0%, rgba(249, 250, 251, 0.95) 100%)",borderBottom:"dark"===i?"1px solid rgba(148, 163, 184, 0.2)":"1px solid rgba(229, 231, 235, 0.8)",boxShadow:"dark"===i?"0 4px 6px -1px rgba(0, 0, 0, 0.3), 0 2px 4px -1px rgba(0, 0, 0, 0.2)":"0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)"},children:[(0,a.jsxs)("div",{className:"flex items-center ".concat(n?"justify-center":"mb-3"),children:[(0,a.jsx)("div",{className:"rounded-lg flex items-center justify-center transition-all duration-300 ".concat(n?"w-10 h-10":"w-8 h-8 mr-3"),style:{background:"dark"===i?"linear-gradient(135deg, #22c55e 0%, #16a34a 100%)":"linear-gradient(135deg, #10b981 0%, #059669 100%)",boxShadow:"0 4px 8px rgba(34, 197, 94, 0.3)"},children:(0,a.jsx)("span",{className:"text-white font-bold ".concat(n?"text-base":"text-sm"),children:"⚡"})}),!n&&(0,a.jsx)("h2",{className:"text-lg font-bold transition-all duration-300 crisp-text",style:{color:"dark"===i?"#f8fafc":"#111827",textShadow:"dark"===i?"0 1px 2px rgba(0, 0, 0, 0.3)":"none"},children:"Additional Tools"})]}),!n&&(0,a.jsx)("p",{className:"text-xs font-medium transition-all duration-300 crisp-text",style:{color:"dark"===i?"#94a3b8":"#64748b",letterSpacing:"0.025em"},children:"Advanced features and utilities"})]}),(0,a.jsxs)("div",{className:"flex-1 relative",children:[(0,a.jsx)("button",{onClick:()=>{o(!n)},className:"absolute top-4 right-2 z-30 p-2 rounded-lg transition-all duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 group",style:{backgroundColor:"dark"===i?"rgba(34, 197, 94, 0.1)":"rgba(34, 197, 94, 0.08)",border:"dark"===i?"1px solid rgba(34, 197, 94, 0.3)":"1px solid rgba(34, 197, 94, 0.2)",boxShadow:"dark"===i?"0 2px 8px rgba(34, 197, 94, 0.2)":"0 2px 8px rgba(34, 197, 94, 0.15)"},title:n?"Expand Sidebar":"Collapse Sidebar",children:n?(0,a.jsx)(k.A,{className:"w-4 h-4 transition-all duration-200 group-hover:scale-105",style:{color:"dark"===i?"#4ade80":"#16a34a"}}):(0,a.jsx)(w.A,{className:"w-4 h-4 transition-all duration-200 group-hover:scale-105",style:{color:"dark"===i?"#4ade80":"#16a34a"}})}),(0,a.jsx)("div",{className:"absolute inset-0 overflow-hidden",children:(0,a.jsx)("nav",{className:"h-full pt-16 pb-6 overflow-y-auto sidebar-nav-scroll transition-all duration-300 space-y-1 ".concat(n?"px-2 pr-1":"px-4 pr-2"),style:{marginRight:n?"-4px":"-6px",paddingRight:n?"8px":"10px"},children:x.map(e=>{let s=e.icon,l=t===e.id;return(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)("button",{onClick:()=>r(e.id),className:"w-full flex items-center text-left transition-all duration-200 group sidebar-nav-item crisp-text relative overflow-hidden ".concat(n?"p-2.5 rounded-lg justify-center":"p-3 rounded-xl"),style:{background:l?"dark"===i?"linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%)":"linear-gradient(135deg, rgba(34, 197, 94, 0.1) 0%, rgba(16, 185, 129, 0.08) 100%)":"transparent",border:l?"dark"===i?"1px solid rgba(34, 197, 94, 0.4)":"1px solid rgba(34, 197, 94, 0.3)":"1px solid transparent",boxShadow:l?"dark"===i?"0 4px 12px rgba(34, 197, 94, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.1)":"0 4px 12px rgba(34, 197, 94, 0.15), inset 0 1px 0 rgba(255, 255, 255, 0.8)":"none"},onMouseEnter:e=>{l||(e.currentTarget.style.background="dark"===i?"rgba(71, 85, 105, 0.15)":"rgba(243, 244, 246, 0.6)",e.currentTarget.style.border="dark"===i?"1px solid rgba(148, 163, 184, 0.2)":"1px solid rgba(229, 231, 235, 0.6)")},onMouseLeave:e=>{l||(e.currentTarget.style.background="transparent",e.currentTarget.style.border="1px solid transparent")},children:n?(0,a.jsx)(s,{className:"h-5 w-5 transition-all duration-200 relative z-10",style:{color:l?"dark"===i?"#4ade80":"#16a34a":"dark"===i?"#e2e8f0":"#64748b",filter:l?"drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))":"none"}}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"transition-all duration-200 relative p-2 rounded-lg mr-3 overflow-hidden",style:{background:l?"dark"===i?"linear-gradient(135deg, rgba(34, 197, 94, 0.3) 0%, rgba(16, 185, 129, 0.25) 100%)":"linear-gradient(135deg, rgba(34, 197, 94, 0.2) 0%, rgba(16, 185, 129, 0.15) 100%)":"dark"===i?"linear-gradient(135deg, rgba(71, 85, 105, 0.5) 0%, rgba(51, 65, 85, 0.4) 100%)":"linear-gradient(135deg, rgba(243, 244, 246, 0.9) 0%, rgba(229, 231, 235, 0.7) 100%)",boxShadow:l?"dark"===i?"0 2px 8px rgba(34, 197, 94, 0.3), inset 0 1px 0 rgba(255, 255, 255, 0.1)":"0 2px 8px rgba(34, 197, 94, 0.2), inset 0 1px 0 rgba(255, 255, 255, 0.8)":"dark"===i?"inset 0 1px 0 rgba(255, 255, 255, 0.05)":"inset 0 1px 0 rgba(255, 255, 255, 0.9)"},children:(0,a.jsx)(s,{className:"h-4 w-4 transition-all duration-200 relative z-10",style:{color:l?"dark"===i?"#4ade80":"#16a34a":"dark"===i?"#e2e8f0":"#64748b",filter:l?"drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2))":"none"}})}),(0,a.jsx)("div",{className:"flex-1 sidebar-text",children:(0,a.jsx)("h3",{className:"font-medium text-sm transition-colors duration-200 leading-snug",style:{color:l?"dark"===i?"#4ade80":"#16a34a":"dark"===i?"#f8fafc":"#111827",fontWeight:l?"600":"500"},children:e.label})})]})}),n&&(0,a.jsxs)("div",{className:"absolute left-full ml-2 top-1/2 transform -translate-y-1/2 px-3 py-2 rounded-lg text-sm font-medium opacity-0 group-hover:opacity-100 transition-all duration-300 pointer-events-none z-50 whitespace-nowrap",style:{backgroundColor:"dark"===i?"#1e293b":"#ffffff",color:"dark"===i?"#f8fafc":"#111827",border:"dark"===i?"1px solid rgba(148, 163, 184, 0.3)":"1px solid rgba(229, 231, 235, 0.8)",boxShadow:"dark"===i?"0 4px 12px rgba(0, 0, 0, 0.3)":"0 4px 12px rgba(0, 0, 0, 0.15)"},children:[(0,a.jsx)("div",{className:"font-semibold",children:e.label}),(0,a.jsx)("div",{className:"absolute right-full top-1/2 transform -translate-y-1/2 w-0 h-0",style:{borderTop:"6px solid transparent",borderBottom:"6px solid transparent",borderRight:"6px solid ".concat("dark"===i?"#1e293b":"#ffffff")}})]})]},e.id)})})})]}),(0,a.jsx)("div",{className:"sticky bottom-0 z-20 transition-all duration-300 backdrop-blur-md ".concat(n?"px-3 py-3":"px-6 py-4"),style:{background:"dark"===i?"linear-gradient(135deg, rgba(15, 23, 42, 0.98) 0%, rgba(30, 41, 59, 0.95) 100%)":"linear-gradient(135deg, rgba(249, 250, 251, 0.98) 0%, rgba(255, 255, 255, 0.95) 100%)",borderTop:"dark"===i?"1px solid rgba(148, 163, 184, 0.2)":"1px solid rgba(229, 231, 235, 0.8)",boxShadow:"dark"===i?"0 -4px 6px -1px rgba(0, 0, 0, 0.3), 0 -2px 4px -1px rgba(0, 0, 0, 0.2)":"0 -4px 6px -1px rgba(0, 0, 0, 0.1), 0 -2px 4px -1px rgba(0, 0, 0, 0.06)"},children:(0,a.jsx)("div",{className:"text-sm transition-colors duration-300",style:{color:"dark"===i?"#94a3b8":"#64748b"},children:n?(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsxs)("div",{className:"rounded-xl flex items-center justify-center relative overflow-hidden w-10 h-10",style:{background:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",boxShadow:"0 4px 8px rgba(59, 130, 246, 0.3)"},children:[(0,a.jsx)("span",{className:"text-white font-bold relative z-10 text-base",children:"R"}),(0,a.jsx)("div",{className:"absolute inset-0 opacity-20",style:{background:"linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)"}})]})}):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"flex items-center mb-2 space-x-3",children:[(0,a.jsxs)("div",{className:"rounded-xl flex items-center justify-center relative overflow-hidden w-8 h-8",style:{background:"linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%)",boxShadow:"0 4px 8px rgba(59, 130, 246, 0.3)"},children:[(0,a.jsx)("span",{className:"text-white font-bold relative z-10 text-sm",children:"R"}),(0,a.jsx)("div",{className:"absolute inset-0 opacity-20",style:{background:"linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.5) 50%, transparent 70%)"}})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("span",{className:"font-bold text-sm transition-colors duration-300 block",style:{color:"dark"===i?"#f8fafc":"#1e293b",textShadow:"dark"===i?"0 1px 2px rgba(0, 0, 0, 0.3)":"none"},children:"Revantad Store"}),(0,a.jsx)("span",{className:"text-xs font-medium",style:{color:"dark"===i?"#64748b":"#94a3b8",letterSpacing:"0.025em"},children:"Professional Business Management"})]})]}),(0,a.jsx)("div",{className:"text-xs font-medium px-3 py-2 rounded-lg",style:{backgroundColor:"dark"===i?"rgba(71, 85, 105, 0.3)":"rgba(243, 244, 246, 0.8)",color:"dark"===i?"#cbd5e1":"#6b7280",border:"dark"===i?"1px solid rgba(148, 163, 184, 0.2)":"1px solid rgba(229, 231, 235, 0.6)"},children:"Admin Dashboard v2.0"})]})})})]})})}var A=r(5868),C=r(1243);function _(e){let{stats:t}=e,{resolvedTheme:r}=(0,l.D)(),s=[{title:"Products in List",value:t.totalProducts,icon:n.A,color:"bg-blue-500",textColor:"text-blue-600",bgColor:"bg-blue-50"},{title:"Customer Debts",value:t.totalDebts,icon:o.A,color:"bg-green-500",textColor:"text-green-600",bgColor:"bg-green-50"},{title:"Total Debt Amount",value:"₱".concat(t.totalDebtAmount.toFixed(2)),icon:A.A,color:"bg-yellow-500",textColor:"text-yellow-600",bgColor:"bg-yellow-50"},{title:"Low Stock Items",value:t.lowStockItems,icon:C.A,color:"bg-red-500",textColor:"text-red-600",bgColor:"bg-red-50"}];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:s.map((e,t)=>{let s=e.icon;return(0,a.jsx)("div",{className:"rounded-lg shadow-md p-6 transition-all duration-300 hover:shadow-lg",style:{backgroundColor:"dark"===r?"#1e293b":"#ffffff",border:"dark"===r?"1px solid #334155":"1px solid #e5e7eb"},children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"p-3 rounded-lg ".concat(e.bgColor," ").concat("dark"===r?"opacity-90":""),children:(0,a.jsx)(s,{className:"h-6 w-6 ".concat(e.textColor)})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium transition-colors duration-300",style:{color:"dark"===r?"#cbd5e1":"#6b7280"},children:e.title}),(0,a.jsx)("p",{className:"text-2xl font-semibold transition-colors duration-300",style:{color:"dark"===r?"#f8fafc":"#111827"},children:e.value})]})]})},t)})}),(0,a.jsxs)("div",{className:"rounded-lg shadow-md p-6 transition-all duration-300",style:{backgroundColor:"dark"===r?"#1e293b":"#ffffff",border:"dark"===r?"1px solid #334155":"1px solid #e5e7eb"},children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4 transition-colors duration-300",style:{color:"dark"===r?"#f8fafc":"#111827"},children:"Quick Actions"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("button",{className:"flex items-center p-4 rounded-lg transition-all duration-300 hover:scale-[1.02] hover:shadow-md",style:{border:"dark"===r?"1px solid #475569":"1px solid #d1d5db",backgroundColor:"dark"===r?"#334155":"#f9fafb"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="dark"===r?"#475569":"#f3f4f6"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="dark"===r?"#334155":"#f9fafb"},children:[(0,a.jsx)(n.A,{className:"h-8 w-8 text-blue-600 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("p",{className:"font-medium transition-colors duration-300",style:{color:"dark"===r?"#f8fafc":"#111827"},children:"Add to Product List"}),(0,a.jsx)("p",{className:"text-sm transition-colors duration-300",style:{color:"dark"===r?"#cbd5e1":"#6b7280"},children:"Add a new product to your list"})]})]}),(0,a.jsxs)("button",{className:"flex items-center p-4 rounded-lg transition-all duration-300 hover:scale-[1.02] hover:shadow-md",style:{border:"dark"===r?"1px solid #475569":"1px solid #d1d5db",backgroundColor:"dark"===r?"#334155":"#f9fafb"},onMouseEnter:e=>{e.currentTarget.style.backgroundColor="dark"===r?"#475569":"#f3f4f6"},onMouseLeave:e=>{e.currentTarget.style.backgroundColor="dark"===r?"#334155":"#f9fafb"},children:[(0,a.jsx)(o.A,{className:"h-8 w-8 text-green-600 mr-3"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("p",{className:"font-medium transition-colors duration-300",style:{color:"dark"===r?"#f8fafc":"#111827"},children:"Record New Debt"}),(0,a.jsx)("p",{className:"text-sm transition-colors duration-300",style:{color:"dark"===r?"#cbd5e1":"#6b7280"},children:"Add a new customer debt record"})]})]})]})]}),(0,a.jsxs)("div",{className:"rounded-lg shadow-md p-6 transition-all duration-300",style:{backgroundColor:"dark"===r?"#1e293b":"#ffffff",border:"dark"===r?"1px solid #334155":"1px solid #e5e7eb"},children:[(0,a.jsx)("h3",{className:"text-lg font-semibold mb-4 transition-colors duration-300",style:{color:"dark"===r?"#f8fafc":"#111827"},children:"Store Overview"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center py-2 transition-colors duration-300",style:{borderBottom:"dark"===r?"1px solid #475569":"1px solid #f3f4f6"},children:[(0,a.jsx)("span",{className:"transition-colors duration-300",style:{color:"dark"===r?"#cbd5e1":"#6b7280"},children:"Products in List"}),(0,a.jsx)("span",{className:"font-semibold transition-colors duration-300",style:{color:"dark"===r?"#f8fafc":"#111827"},children:t.totalProducts})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center py-2 transition-colors duration-300",style:{borderBottom:"dark"===r?"1px solid #475569":"1px solid #f3f4f6"},children:[(0,a.jsx)("span",{className:"transition-colors duration-300",style:{color:"dark"===r?"#cbd5e1":"#6b7280"},children:"Outstanding Debts"}),(0,a.jsx)("span",{className:"font-semibold transition-colors duration-300",style:{color:"dark"===r?"#f8fafc":"#111827"},children:t.totalDebts})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center py-2 transition-colors duration-300",style:{borderBottom:"dark"===r?"1px solid #475569":"1px solid #f3f4f6"},children:[(0,a.jsx)("span",{className:"transition-colors duration-300",style:{color:"dark"===r?"#cbd5e1":"#6b7280"},children:"Total Amount Owed"}),(0,a.jsxs)("span",{className:"font-semibold transition-colors duration-300",style:{color:"dark"===r?"#f8fafc":"#111827"},children:["₱",t.totalDebtAmount.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center py-2",children:[(0,a.jsx)("span",{className:"transition-colors duration-300",style:{color:"dark"===r?"#cbd5e1":"#6b7280"},children:"Items Need Restocking"}),(0,a.jsx)("span",{className:"font-semibold ".concat(t.lowStockItems>0?"text-red-600":"text-green-600"),children:t.lowStockItems})]})]})]})]})}var D=r(4792),E=r(3109);function L(e){let{stats:t}=e,[r,l]=(0,s.useState)([]),[i,d]=(0,s.useState)([]);(0,s.useEffect)(()=>{let e=[];for(let t=0;t<12;t++)e.push(Math.floor(5e4*Math.random())+2e4);l(e);let t=[];for(let e=0;e<7;e++)t.push(Math.floor(15e3*Math.random())+5e3);d(t)},[]);let c={title:{text:"Monthly Sales Revenue",textStyle:{fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"axis",formatter:e=>"".concat(e[0].name,"<br/>Revenue: ₱").concat(e[0].value.toLocaleString())},xAxis:{type:"category",data:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]},yAxis:{type:"value",axisLabel:{formatter:"₱{value}"}},series:[{data:r,type:"line",smooth:!0,lineStyle:{color:"#22c55e",width:3},itemStyle:{color:"#22c55e"},areaStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"rgba(34, 197, 94, 0.3)"},{offset:1,color:"rgba(34, 197, 94, 0.05)"}]}}}],grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0}},x={title:{text:"Weekly Customer Debt Trends",textStyle:{fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"axis",formatter:e=>"".concat(e[0].name,"<br/>Total Debt: ₱").concat(e[0].value.toLocaleString())},xAxis:{type:"category",data:["Mon","Tue","Wed","Thu","Fri","Sat","Sun"]},yAxis:{type:"value",axisLabel:{formatter:"₱{value}"}},series:[{data:i,type:"bar",itemStyle:{color:{type:"linear",x:0,y:0,x2:0,y2:1,colorStops:[{offset:0,color:"#facc15"},{offset:1,color:"#eab308"}]}},emphasis:{itemStyle:{color:"#f59e0b"}}}],grid:{left:"3%",right:"4%",bottom:"3%",containLabel:!0}},m={title:{text:"Product Categories Distribution",textStyle:{fontSize:16,fontWeight:"bold"}},tooltip:{trigger:"item",formatter:"{a} <br/>{b}: {c} ({d}%)"},series:[{name:"Categories",type:"pie",radius:["40%","70%"],avoidLabelOverlap:!1,itemStyle:{borderRadius:10,borderColor:"#fff",borderWidth:2},label:{show:!1,position:"center"},emphasis:{label:{show:!0,fontSize:20,fontWeight:"bold"}},labelLine:{show:!1},data:[{value:35,name:"Snacks",itemStyle:{color:"#22c55e"}},{value:25,name:"Beverages",itemStyle:{color:"#facc15"}},{value:20,name:"Canned Goods",itemStyle:{color:"#3b82f6"}},{value:12,name:"Personal Care",itemStyle:{color:"#f59e0b"}},{value:8,name:"Others",itemStyle:{color:"#8b5cf6"}}]}]},u=[{title:"Total Revenue",value:"₱"+r.reduce((e,t)=>e+t,0).toLocaleString(),icon:A.A,color:"text-green-600",bgColor:"bg-green-50",change:"+12.5%",changeColor:"text-green-600"},{title:"Products Listed",value:t.totalProducts.toString(),icon:n.A,color:"text-blue-600",bgColor:"bg-blue-50",change:"+5.2%",changeColor:"text-blue-600"},{title:"Active Customers",value:t.totalDebts.toString(),icon:o.A,color:"text-purple-600",bgColor:"bg-purple-50",change:"+8.1%",changeColor:"text-purple-600"},{title:"Outstanding Debt",value:"₱"+t.totalDebtAmount.toLocaleString(),icon:E.A,color:"text-yellow-600",bgColor:"bg-yellow-50",change:"-3.2%",changeColor:"text-red-600"}];return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:u.map((e,t)=>(0,a.jsx)("div",{className:"card p-6",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 dark:text-gray-400",children:e.title}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white mt-1",children:e.value}),(0,a.jsxs)("p",{className:"text-sm mt-1 ".concat(e.changeColor),children:[e.change," from last month"]})]}),(0,a.jsx)("div",{className:"p-3 rounded-lg ".concat(e.bgColor),children:(0,a.jsx)(e.icon,{className:"h-6 w-6 ".concat(e.color)})})]})},t))}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsx)("div",{className:"card p-6",children:(0,a.jsx)(D.A,{option:c,style:{height:"400px"}})}),(0,a.jsx)("div",{className:"card p-6",children:(0,a.jsx)(D.A,{option:x,style:{height:"400px"}})})]}),(0,a.jsx)("div",{className:"card p-6",children:(0,a.jsx)(D.A,{option:m,style:{height:"400px"}})}),(0,a.jsx)("div",{className:"card p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 bg-green-500 rounded-full animate-pulse"}),(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:"Real-time Data Updates"})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400",children:[(0,a.jsx)(v.A,{className:"h-4 w-4"}),(0,a.jsxs)("span",{children:["Last updated: ",new Date().toLocaleTimeString()]})]})]})})]})}var T=r(4616),P=r(3717),R=r(2525),U=r(4416),M=r(9869),O=r(5647),I=r(8663),q=r(4028),F=r(9509);let B=I.Ik({NODE_ENV:I.k5(["development","production","test"]).default("development"),NEXT_PUBLIC_SUPABASE_URL:I.Yj().optional(),NEXT_PUBLIC_SUPABASE_ANON_KEY:I.Yj().optional(),SUPABASE_SERVICE_ROLE_KEY:I.Yj().optional(),NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:I.Yj().optional(),CLOUDINARY_API_KEY:I.Yj().optional(),CLOUDINARY_API_SECRET:I.Yj().optional(),NEXTAUTH_SECRET:I.Yj().optional(),NEXTAUTH_URL:I.Yj().optional(),DEBUG:I.Yj().transform(e=>"true"===e).default("false")}),Y=function(){try{return B.parse(F.env)}catch(e){if(e instanceof q.G){let t=e.errors.map(e=>"".concat(e.path.join("."),": ").concat(e.message));throw Error("❌ Invalid environment variables:\n".concat(t.join("\n"),"\n\n")+"Please check your .env.local file and ensure all required variables are set.\nSee .env.example for reference.")}throw e}}(),z={isDevelopment:"development"===Y.NODE_ENV,isProduction:"production"===Y.NODE_ENV,isTest:"test"===Y.NODE_ENV,database:{url:Y.NEXT_PUBLIC_SUPABASE_URL||"https://placeholder.supabase.co",anonKey:Y.NEXT_PUBLIC_SUPABASE_ANON_KEY||"placeholder-key",serviceRoleKey:Y.SUPABASE_SERVICE_ROLE_KEY},cloudinary:{cloudName:Y.NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME||"placeholder",apiKey:Y.CLOUDINARY_API_KEY,apiSecret:Y.CLOUDINARY_API_SECRET},auth:{secret:Y.NEXTAUTH_SECRET,url:Y.NEXTAUTH_URL},debug:Y.DEBUG},{NODE_ENV:H,NEXT_PUBLIC_SUPABASE_URL:J,NEXT_PUBLIC_SUPABASE_ANON_KEY:G,SUPABASE_SERVICE_ROLE_KEY:W,NEXT_PUBLIC_CLOUDINARY_CLOUD_NAME:V,CLOUDINARY_API_KEY:X,CLOUDINARY_API_SECRET:K,NEXTAUTH_SECRET:Z,NEXTAUTH_URL:Q,DEBUG:$}=Y;(0,O.UU)(z.database.url,z.database.anonKey,{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"revantad-store@1.0.0"}}});let ee=["Snacks","Canned Goods","Beverages","Personal Care","Household Items","Condiments","Rice & Grains","Instant Foods","Dairy Products","Others"];function et(e){let{isOpen:t,onClose:r,product:l}=e,[i,o]=(0,s.useState)({name:"",net_weight:"",price:"",stock_quantity:"",category:"",image_url:""}),[d,c]=(0,s.useState)(null),[x,m]=(0,s.useState)(""),[u,g]=(0,s.useState)(!1),[p,b]=(0,s.useState)(!1);(0,s.useEffect)(()=>{l?(o({name:l.name,net_weight:l.net_weight,price:l.price.toString(),stock_quantity:l.stock_quantity.toString(),category:l.category,image_url:l.image_url||""}),m(l.image_url||"")):(o({name:"",net_weight:"",price:"",stock_quantity:"",category:"",image_url:""}),m("")),c(null)},[l,t]);let h=async()=>{if(!d)return i.image_url;b(!0);try{let e=new FormData;e.append("file",d);let t=await fetch("/api/upload",{method:"POST",body:e});return(await t.json()).url}catch(e){return console.error("Error uploading image:",e),i.image_url}finally{b(!1)}},y=async e=>{e.preventDefault(),g(!0);try{let e=await h(),t={...i,image_url:e,price:parseFloat(i.price),stock_quantity:parseInt(i.stock_quantity)},a=l?"/api/products/".concat(l.id):"/api/products",s=l?"PUT":"POST";(await fetch(a,{method:s,headers:{"Content-Type":"application/json"},body:JSON.stringify(t)})).ok?r():console.error("Error saving product")}catch(e){console.error("Error saving product:",e)}finally{g(!1)}};return t?(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold",children:l?"Edit Product in List":"Add Product to List"}),(0,a.jsx)("button",{onClick:r,className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(U.A,{className:"h-6 w-6"})})]}),(0,a.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Product Image"}),(0,a.jsxs)("div",{className:"flex flex-col items-center",children:[(0,a.jsx)("div",{className:"w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center mb-2",children:x?(0,a.jsx)("img",{src:x,alt:"Preview",className:"w-full h-full object-cover rounded-lg"}):(0,a.jsx)(n.A,{className:"h-12 w-12 text-gray-400"})}),(0,a.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var t;let r=null==(t=e.target.files)?void 0:t[0];if(r){c(r);let e=new FileReader;e.onloadend=()=>{m(e.result)},e.readAsDataURL(r)}},className:"hidden",id:"image-upload"}),(0,a.jsxs)("label",{htmlFor:"image-upload",className:"flex items-center px-3 py-2 border border-gray-300 rounded-md cursor-pointer hover:bg-gray-50",children:[(0,a.jsx)(M.A,{className:"h-4 w-4 mr-2"}),"Choose Image"]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Name *"}),(0,a.jsx)("input",{type:"text",required:!0,value:i.name,onChange:e=>o({...i,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Net Weight *"}),(0,a.jsx)("input",{type:"text",required:!0,placeholder:"e.g., 100g, 1L, 250ml",value:i.net_weight,onChange:e=>o({...i,net_weight:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Price (₱) *"}),(0,a.jsx)("input",{type:"number",step:"0.01",min:"0",required:!0,value:i.price,onChange:e=>o({...i,price:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Stock Quantity *"}),(0,a.jsx)("input",{type:"number",min:"0",required:!0,value:i.stock_quantity,onChange:e=>o({...i,stock_quantity:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Category *"}),(0,a.jsxs)("select",{required:!0,value:i.category,onChange:e=>o({...i,category:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",children:[(0,a.jsx)("option",{value:"",children:"Select Category"}),ee.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]})]}),(0,a.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,a.jsx)("button",{type:"button",onClick:r,className:"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:u||p,className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50",children:u||p?"Saving...":l?"Update in List":"Add to List"})]})]})]})}):null}function er(e){let{onStatsUpdate:t}=e,{resolvedTheme:r}=(0,l.D)(),[i,o]=(0,s.useState)([]),[d,x]=(0,s.useState)(!0),[m,u]=(0,s.useState)(""),[g,p]=(0,s.useState)(""),[b,h]=(0,s.useState)(!1),[y,f]=(0,s.useState)(null);(0,s.useEffect)(()=>{j()},[]);let j=async()=>{try{let e=await fetch("/api/products"),t=await e.json();o(t.products||[])}catch(e){console.error("Error fetching products:",e)}finally{x(!1)}},v=async e=>{if(confirm("Are you sure you want to delete this product?"))try{(await fetch("/api/products/".concat(e),{method:"DELETE"})).ok&&(o(i.filter(t=>t.id!==e)),t())}catch(e){console.error("Error deleting product:",e)}},N=e=>{f(e),h(!0)},k=i.filter(e=>{let t=e.name.toLowerCase().includes(m.toLowerCase()),r=""===g||e.category===g;return t&&r});return d?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-300",style:{color:"dark"===r?"#9ca3af":"#6b7280"}}),(0,a.jsx)("input",{type:"text",placeholder:"Search products...",value:m,onChange:e=>u(e.target.value),className:"pl-10 pr-4 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===r?"#374151":"#ffffff",border:"dark"===r?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===r?"#f9fafb":"#111827"}})]}),(0,a.jsxs)("select",{value:g,onChange:e=>p(e.target.value),className:"px-4 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===r?"#374151":"#ffffff",border:"dark"===r?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===r?"#f9fafb":"#111827"},children:[(0,a.jsx)("option",{value:"",children:"All Categories"}),ee.map(e=>(0,a.jsx)("option",{value:e,children:e},e))]})]}),(0,a.jsxs)("button",{onClick:()=>h(!0),className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg",children:[(0,a.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"Add to Product List"]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:k.map(e=>(0,a.jsxs)("div",{className:"rounded-lg shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg hover:scale-[1.02]",style:{backgroundColor:"dark"===r?"#1e293b":"#ffffff",border:"dark"===r?"1px solid #334155":"1px solid #e5e7eb"},children:[(0,a.jsx)("div",{className:"aspect-square flex items-center justify-center transition-colors duration-300",style:{backgroundColor:"dark"===r?"#374151":"#f3f4f6"},children:e.image_url?(0,a.jsx)("img",{src:e.image_url,alt:e.name,className:"w-full h-full object-cover"}):(0,a.jsx)(n.A,{className:"h-16 w-16 transition-colors duration-300",style:{color:"dark"===r?"#9ca3af":"#6b7280"}})}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("h3",{className:"font-semibold mb-1 transition-colors duration-300",style:{color:"dark"===r?"#f8fafc":"#111827"},children:e.name}),(0,a.jsx)("p",{className:"text-sm mb-2 transition-colors duration-300",style:{color:"dark"===r?"#cbd5e1":"#6b7280"},children:e.category}),(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["₱",e.price]}),(0,a.jsx)("span",{className:"text-sm transition-colors duration-300",style:{color:"dark"===r?"#9ca3af":"#6b7280"},children:e.net_weight})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsxs)("span",{className:"text-sm ".concat(e.stock_quantity<10?"text-red-600":""),style:{color:e.stock_quantity>=10?"dark"===r?"#cbd5e1":"#6b7280":"#dc2626"},children:["Stock: ",e.stock_quantity]}),e.stock_quantity<10&&(0,a.jsx)("span",{className:"text-xs bg-red-100 text-red-800 px-2 py-1 rounded",children:"Low Stock"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>N(e),className:"flex-1 flex items-center justify-center px-3 py-2 bg-blue-100 text-blue-700 rounded hover:bg-blue-200 transition-colors",children:[(0,a.jsx)(P.A,{className:"h-4 w-4 mr-1"}),"Edit"]}),(0,a.jsxs)("button",{onClick:()=>v(e.id),className:"flex-1 flex items-center justify-center px-3 py-2 bg-red-100 text-red-700 rounded hover:bg-red-200 transition-colors",children:[(0,a.jsx)(R.A,{className:"h-4 w-4 mr-1"}),"Delete"]})]})]})]},e.id))}),0===k.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(n.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No products in list"}),(0,a.jsx)("p",{className:"text-gray-600",children:m||g?"Try adjusting your search or filter criteria":"Get started by adding your first product to the list"})]}),(0,a.jsx)(et,{isOpen:b,onClose:()=>{h(!1),f(null),j(),t()},product:y})]})}function ea(e){let{isOpen:t,onClose:r,debt:l}=e,[i,n]=(0,s.useState)({customer_name:"",customer_family_name:"",product_name:"",product_price:"",quantity:"",debt_date:""}),[o,d]=(0,s.useState)(!1);(0,s.useEffect)(()=>{l?n({customer_name:l.customer_name,customer_family_name:l.customer_family_name,product_name:l.product_name,product_price:l.product_price.toString(),quantity:l.quantity.toString(),debt_date:l.debt_date}):n({customer_name:"",customer_family_name:"",product_name:"",product_price:"",quantity:"",debt_date:new Date().toISOString().split("T")[0]||""})},[l,t]);let c=async e=>{e.preventDefault(),d(!0);try{let e={...i,product_price:parseFloat(i.product_price),quantity:parseInt(i.quantity)},t=l?"/api/debts/".concat(l.id):"/api/debts",a=l?"PUT":"POST";(await fetch(t,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok?r():console.error("Error saving debt record")}catch(e){console.error("Error saving debt record:",e)}finally{d(!1)}};if(!t)return null;let x=i.product_price&&i.quantity?(parseFloat(i.product_price)*parseInt(i.quantity)).toFixed(2):"0.00";return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold",children:l?"Edit Debt Record":"Add New Debt Record"}),(0,a.jsx)("button",{onClick:r,className:"text-gray-400 hover:text-gray-600",children:(0,a.jsx)(U.A,{className:"h-6 w-6"})})]}),(0,a.jsxs)("form",{onSubmit:c,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Customer First Name *"}),(0,a.jsx)("input",{type:"text",required:!0,value:i.customer_name,onChange:e=>n({...i,customer_name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"e.g., Juan"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Customer Family Name *"}),(0,a.jsx)("input",{type:"text",required:!0,value:i.customer_family_name,onChange:e=>n({...i,customer_family_name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"e.g., Dela Cruz"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Name *"}),(0,a.jsx)("input",{type:"text",required:!0,value:i.product_name,onChange:e=>n({...i,product_name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"e.g., Lucky Me Pancit Canton"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Product Price (₱) *"}),(0,a.jsx)("input",{type:"number",step:"0.01",min:"0",required:!0,value:i.product_price,onChange:e=>n({...i,product_price:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"0.00"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Quantity *"}),(0,a.jsx)("input",{type:"number",min:"1",required:!0,value:i.quantity,onChange:e=>n({...i,quantity:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"1"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Debt Date *"}),(0,a.jsx)("input",{type:"date",required:!0,value:i.debt_date,onChange:e=>n({...i,debt_date:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]}),(0,a.jsx)("div",{className:"bg-gray-50 p-3 rounded-md",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Total Amount:"}),(0,a.jsxs)("span",{className:"text-lg font-bold text-green-600",children:["₱",x]})]})}),(0,a.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,a.jsx)("button",{type:"button",onClick:r,className:"flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:o,className:"flex-1 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50",children:o?"Saving...":l?"Update":"Add Record"})]})]})]})})}var es=r(3319);function el(e){let{onStatsUpdate:t}=e,{resolvedTheme:r}=(0,l.D)(),[i,n]=(0,s.useState)([]),[d,x]=(0,s.useState)(!0),[m,u]=(0,s.useState)(""),[g,p]=(0,s.useState)(!1),[b,h]=(0,s.useState)(null);(0,s.useEffect)(()=>{y()},[]);let y=async()=>{try{let e=await fetch("/api/debts"),t=await e.json();n(t.debts||[])}catch(e){console.error("Error fetching debts:",e)}finally{x(!1)}},f=async e=>{if(confirm("Are you sure you want to delete this debt record?"))try{(await fetch("/api/debts/".concat(e),{method:"DELETE"})).ok&&(n(i.filter(t=>t.id!==e)),t())}catch(e){console.error("Error deleting debt:",e)}},j=e=>{h(e),p(!0)},N=i.filter(e=>{let t="".concat(e.customer_name," ").concat(e.customer_family_name).toLowerCase(),r=e.product_name.toLowerCase(),a=m.toLowerCase();return t.includes(a)||r.includes(a)}),k=N.reduce((e,t)=>{let r="".concat(t.customer_name," ").concat(t.customer_family_name);return e[r]||(e[r]=[]),e[r].push(t),e},{});return d?(0,a.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"})}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 transition-colors duration-300",style:{color:"dark"===r?"#9ca3af":"#6b7280"}}),(0,a.jsx)("input",{type:"text",placeholder:"Search by customer or product...",value:m,onChange:e=>u(e.target.value),className:"pl-10 pr-4 py-2 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all duration-300",style:{backgroundColor:"dark"===r?"#374151":"#ffffff",border:"dark"===r?"1px solid #6b7280":"1px solid #d1d5db",color:"dark"===r?"#f9fafb":"#111827"}})]}),(0,a.jsxs)("button",{onClick:()=>p(!0),className:"flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-all duration-300 hover:scale-[1.02] shadow-md hover:shadow-lg",children:[(0,a.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"Add Debt Record"]})]}),(0,a.jsx)("div",{className:"space-y-6",children:Object.entries(k).map(e=>{let[t,r]=e,s=r.reduce((e,t)=>e+t.total_amount,0);return(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[(0,a.jsx)("div",{className:"bg-gray-50 px-6 py-4 border-b border-gray-200",children:(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(o.A,{className:"h-5 w-5 text-gray-400 mr-2"}),(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:t})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[r.length," item(s)"]}),(0,a.jsxs)("p",{className:"text-lg font-bold text-red-600",children:["₱",s.toFixed(2)]})]})]})}),(0,a.jsx)("div",{className:"divide-y divide-gray-200",children:r.map(e=>(0,a.jsx)("div",{className:"px-6 py-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900",children:e.product_name}),(0,a.jsxs)("div",{className:"mt-1 text-sm text-gray-600 space-y-1",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("span",{children:["Quantity: ",e.quantity]}),(0,a.jsx)("span",{className:"mx-2",children:"•"}),(0,a.jsxs)("span",{children:["Unit Price: ₱",e.product_price.toFixed(2)]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(v.A,{className:"h-4 w-4 mr-1"}),(0,a.jsxs)("span",{children:["Date: ",(0,es.GP)(new Date(e.debt_date),"MMM dd, yyyy")]})]})]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 ml-4",children:[(0,a.jsx)("div",{className:"text-right",children:(0,a.jsxs)("p",{className:"font-semibold text-gray-900",children:["₱",e.total_amount.toFixed(2)]})}),(0,a.jsx)("button",{onClick:()=>j(e),className:"p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors",children:(0,a.jsx)(P.A,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>f(e.id),className:"p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors",children:(0,a.jsx)(R.A,{className:"h-4 w-4"})})]})]})},e.id))})]},t)})}),0===N.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(o.A,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"No debt records found"}),(0,a.jsx)("p",{className:"text-gray-600",children:m?"Try adjusting your search criteria":"Get started by adding your first debt record"})]}),(0,a.jsx)(ea,{isOpen:g,onClose:()=>{p(!1),h(null),y(),t()},debt:b})]})}var ei=r(1976),en=r(6516),eo=r(1788);function ed(){let[e,t]=(0,s.useState)([{id:"1",url:"/api/placeholder/400/300",title:"Family Store Opening",description:"Grand opening of our Revantad Store with the whole family",date:"2024-01-15",likes:12,isLiked:!0},{id:"2",url:"/api/placeholder/400/300",title:"Store Anniversary",description:"Celebrating our first year in business",date:"2024-02-20",likes:8,isLiked:!1},{id:"3",url:"/api/placeholder/400/300",title:"Community Event",description:"Participating in the local community festival",date:"2024-03-10",likes:15,isLiked:!0}]),[r,l]=(0,s.useState)(!1),[i,n]=(0,s.useState)(null),[o,c]=(0,s.useState)({title:"",description:"",file:null}),x=r=>{t(e.map(e=>e.id===r?{...e,likes:e.isLiked?e.likes-1:e.likes+1,isLiked:!e.isLiked}:e))},m=r=>{confirm("Are you sure you want to delete this photo?")&&t(e.filter(e=>e.id!==r))};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Family Gallery"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Preserve your family memories and store moments"})]}),(0,a.jsxs)("button",{onClick:()=>l(!0),className:"btn-primary flex items-center",children:[(0,a.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"Add Photo"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"card p-6 text-center",children:[(0,a.jsx)(d.A,{className:"h-8 w-8 text-green-500 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.length}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Total Photos"})]}),(0,a.jsxs)("div",{className:"card p-6 text-center",children:[(0,a.jsx)(ei.A,{className:"h-8 w-8 text-red-500 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.reduce((e,t)=>e+t.likes,0)}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Total Likes"})]}),(0,a.jsxs)("div",{className:"card p-6 text-center",children:[(0,a.jsx)(M.A,{className:"h-8 w-8 text-blue-500 mx-auto mb-2"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:new Date().getFullYear()}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:"Year Started"})]})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:e.map(e=>(0,a.jsxs)("div",{className:"card overflow-hidden group",children:[(0,a.jsxs)("div",{className:"relative aspect-video bg-gray-200 dark:bg-gray-700",children:[(0,a.jsx)("div",{className:"w-full h-full bg-gradient-to-br from-green-100 to-yellow-100 dark:from-green-900 dark:to-yellow-900 flex items-center justify-center",children:(0,a.jsx)(d.A,{className:"h-16 w-16 text-gray-400"})}),(0,a.jsx)("div",{className:"absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-200 flex items-center justify-center opacity-0 group-hover:opacity-100",children:(0,a.jsx)("button",{onClick:()=>n(e),className:"bg-white text-gray-900 px-4 py-2 rounded-lg font-medium",children:"View Details"})})]}),(0,a.jsxs)("div",{className:"p-4",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 dark:text-white mb-1",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-3 line-clamp-2",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:new Date(e.date).toLocaleDateString()}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsxs)("button",{onClick:()=>x(e.id),className:"flex items-center space-x-1 text-sm ".concat(e.isLiked?"text-red-500":"text-gray-500 dark:text-gray-400"),children:[(0,a.jsx)(ei.A,{className:"h-4 w-4 ".concat(e.isLiked?"fill-current":"")}),(0,a.jsx)("span",{children:e.likes})]}),(0,a.jsx)("button",{className:"text-gray-500 dark:text-gray-400 hover:text-blue-500",children:(0,a.jsx)(en.A,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>m(e.id),className:"text-gray-500 dark:text-gray-400 hover:text-red-500",children:(0,a.jsx)(R.A,{className:"h-4 w-4"})})]})]})]})]},e.id))}),r&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-lg p-6 w-full max-w-md",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Add New Photo"}),(0,a.jsxs)("form",{onSubmit:r=>{r.preventDefault(),o.file&&o.title&&(t([{id:Date.now().toString(),url:URL.createObjectURL(o.file),title:o.title,description:o.description,date:new Date().toISOString().split("T")[0]||"",likes:0,isLiked:!1},...e]),c({title:"",description:"",file:null}),l(!1))},className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Photo File"}),(0,a.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var t;let r=null==(t=e.target.files)?void 0:t[0];r&&c({...o,file:r})},className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Title"}),(0,a.jsx)("input",{type:"text",value:o.title,onChange:e=>c({...o,title:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Description"}),(0,a.jsx)("textarea",{value:o.description,onChange:e=>c({...o,description:e.target.value}),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"})]}),(0,a.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,a.jsx)("button",{type:"button",onClick:()=>l(!1),className:"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",className:"flex-1 btn-primary",children:"Upload Photo"})]})]})]})}),i&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-75 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-lg p-6 w-full max-w-2xl",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:i.title}),(0,a.jsx)("button",{onClick:()=>n(null),className:"text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200",children:"✕"})]}),(0,a.jsx)("div",{className:"aspect-video bg-gradient-to-br from-green-100 to-yellow-100 dark:from-green-900 dark:to-yellow-900 rounded-lg mb-4 flex items-center justify-center",children:(0,a.jsx)(d.A,{className:"h-24 w-24 text-gray-400"})}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mb-4",children:i.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:new Date(i.date).toLocaleDateString()}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsxs)("button",{onClick:()=>x(i.id),className:"flex items-center space-x-1 ".concat(i.isLiked?"text-red-500":"text-gray-500 dark:text-gray-400"),children:[(0,a.jsx)(ei.A,{className:"h-5 w-5 ".concat(i.isLiked?"fill-current":"")}),(0,a.jsx)("span",{children:i.likes})]}),(0,a.jsx)("button",{className:"text-gray-500 dark:text-gray-400 hover:text-blue-500",children:(0,a.jsx)(eo.A,{className:"h-5 w-5"})})]})]})]})})]})}var ec=r(4186),ex=r(4516);function em(){let[e,t]=(0,s.useState)(new Date),[r,l]=(0,s.useState)(null),[i,n]=(0,s.useState)(!1),[d,c]=(0,s.useState)([{id:"1",title:"Supplier Delivery",description:"Weekly grocery delivery from main supplier",date:"2024-01-22",time:"09:00",type:"delivery",location:"Store Front"},{id:"2",title:"Monthly Inventory Check",description:"Complete inventory count and stock verification",date:"2024-01-25",time:"14:00",type:"reminder"},{id:"3",title:"Community Meeting",description:"Barangay business owners meeting",date:"2024-01-28",time:"16:00",type:"meeting",location:"Barangay Hall",attendees:["Maria Santos","Juan Dela Cruz","Ana Reyes"]},{id:"4",title:"New Year Holiday",description:"Store closed for New Year celebration",date:"2024-01-01",time:"00:00",type:"holiday"}]),[x,m]=(0,s.useState)({title:"",description:"",date:"",time:"",type:"reminder",location:""}),u=e=>{let t=e.toISOString().split("T")[0];return d.filter(e=>e.date===t)},g=e=>{switch(e){case"delivery":return"bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400";case"meeting":return"bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-400";case"reminder":return"bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400";case"holiday":return"bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400";case"personal":return"bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400";default:return"bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-400"}},p=e=>{t(t=>{let r=new Date(t);return"prev"===e?r.setMonth(t.getMonth()-1):r.setMonth(t.getMonth()+1),r})},b=(e=>{let t=e.getFullYear(),r=e.getMonth(),a=new Date(t,r,1),s=new Date(t,r+1,0).getDate(),l=a.getDay(),i=[];for(let e=0;e<l;e++)i.push(null);for(let e=1;e<=s;e++)i.push(new Date(t,r,e));return i})(e),h=new Date;return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Calendar"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Manage your store events and schedules"})]}),(0,a.jsxs)("button",{onClick:()=>n(!0),className:"btn-primary flex items-center",children:[(0,a.jsx)(T.A,{className:"h-4 w-4 mr-2"}),"Add Event"]})]}),(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:[["January","February","March","April","May","June","July","August","September","October","November","December"][e.getMonth()]," ",e.getFullYear()]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>p("prev"),className:"p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-slate-700",children:(0,a.jsx)(w.A,{className:"h-4 w-4"})}),(0,a.jsx)("button",{onClick:()=>t(new Date),className:"px-4 py-2 text-sm bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400 rounded-lg hover:bg-green-200 dark:hover:bg-green-900/50",children:"Today"}),(0,a.jsx)("button",{onClick:()=>p("next"),className:"p-2 rounded-lg border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-slate-700",children:(0,a.jsx)(k.A,{className:"h-4 w-4"})})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-7 gap-1",children:[["Sun","Mon","Tue","Wed","Thu","Fri","Sat"].map(e=>(0,a.jsx)("div",{className:"p-3 text-center text-sm font-medium text-gray-500 dark:text-gray-400",children:e},e)),b.map((e,t)=>{if(!e)return(0,a.jsx)("div",{className:"p-3 h-24"},t);let s=u(e),i=e.toDateString()===h.toDateString(),n=(null==r?void 0:r.toDateString())===e.toDateString();return(0,a.jsxs)("div",{onClick:()=>l(e),className:"p-2 h-24 border border-gray-200 dark:border-gray-700 cursor-pointer hover:bg-gray-50 dark:hover:bg-slate-700 ".concat(i?"bg-green-50 dark:bg-green-900/20":""," ").concat(n?"ring-2 ring-green-500":""),children:[(0,a.jsx)("div",{className:"text-sm font-medium mb-1 ".concat(i?"text-green-600 dark:text-green-400":"text-gray-900 dark:text-white"),children:e.getDate()}),(0,a.jsxs)("div",{className:"space-y-1",children:[s.slice(0,2).map(e=>(0,a.jsx)("div",{className:"text-xs px-1 py-0.5 rounded truncate ".concat(g(e.type)),children:e.title},e.id)),s.length>2&&(0,a.jsxs)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:["+",s.length-2," more"]})]})]},t)})]})]}),(0,a.jsxs)("div",{className:"card p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Upcoming Events"}),(0,a.jsx)("div",{className:"space-y-3",children:d.filter(e=>new Date(e.date)>=h).sort((e,t)=>new Date(e.date).getTime()-new Date(t.date).getTime()).slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-start space-x-3 p-3 rounded-lg border border-gray-200 dark:border-gray-700",children:[(0,a.jsx)("div",{className:"p-2 rounded-lg ".concat(g(e.type)),children:(0,a.jsx)(v.A,{className:"h-4 w-4"})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center space-x-4 mt-2 text-xs text-gray-500 dark:text-gray-400",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(ec.A,{className:"h-3 w-3"}),(0,a.jsxs)("span",{children:[new Date(e.date).toLocaleDateString()," at ",e.time]})]}),e.location&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(ex.A,{className:"h-3 w-3"}),(0,a.jsx)("span",{children:e.location})]}),e.attendees&&(0,a.jsxs)("div",{className:"flex items-center space-x-1",children:[(0,a.jsx)(o.A,{className:"h-3 w-3"}),(0,a.jsxs)("span",{children:[e.attendees.length," attendees"]})]})]})]})]},e.id))})]}),i&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:(0,a.jsxs)("div",{className:"bg-white dark:bg-slate-800 rounded-lg p-6 w-full max-w-md",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white mb-4",children:"Add New Event"}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),x.title&&x.date&&x.time&&(c([...d,{id:Date.now().toString(),...x}]),m({title:"",description:"",date:"",time:"",type:"reminder",location:""}),n(!1))},className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Event Title"}),(0,a.jsx)("input",{type:"text",value:x.title,onChange:e=>m({...x,title:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Description"}),(0,a.jsx)("textarea",{value:x.description,onChange:e=>m({...x,description:e.target.value}),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Date"}),(0,a.jsx)("input",{type:"date",value:x.date,onChange:e=>m({...x,date:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Time"}),(0,a.jsx)("input",{type:"time",value:x.time,onChange:e=>m({...x,time:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700",required:!0})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Event Type"}),(0,a.jsxs)("select",{value:x.type,onChange:e=>m({...x,type:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700",children:[(0,a.jsx)("option",{value:"reminder",children:"Reminder"}),(0,a.jsx)("option",{value:"delivery",children:"Delivery"}),(0,a.jsx)("option",{value:"meeting",children:"Meeting"}),(0,a.jsx)("option",{value:"holiday",children:"Holiday"}),(0,a.jsx)("option",{value:"personal",children:"Personal"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Location (Optional)"}),(0,a.jsx)("input",{type:"text",value:x.location,onChange:e=>m({...x,location:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"})]}),(0,a.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,a.jsx)("button",{type:"button",onClick:()=>n(!1),className:"flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",className:"flex-1 btn-primary",children:"Add Event"})]})]})]})})]})}function eu(){let[e,t]=(0,s.useState)(""),[r,l]=(0,s.useState)("all"),[i,o]=(0,s.useState)("7days"),d=[{id:"1",type:"product",action:"Product Added",description:'Added "Lucky Me Pancit Canton" to product list',user:"Admin",timestamp:"2024-01-20T10:30:00Z",details:{productName:"Lucky Me Pancit Canton",price:15}},{id:"2",type:"debt",action:"Debt Recorded",description:"New debt record for Juan Dela Cruz",user:"Admin",timestamp:"2024-01-20T09:15:00Z",details:{customer:"Juan Dela Cruz",amount:45}},{id:"3",type:"payment",action:"Payment Received",description:"Payment received from Maria Santos",user:"Admin",timestamp:"2024-01-19T16:45:00Z",details:{customer:"Maria Santos",amount:120}},{id:"4",type:"product",action:"Stock Updated",description:"Updated stock quantity for Coca-Cola",user:"Admin",timestamp:"2024-01-19T14:20:00Z",details:{productName:"Coca-Cola",oldStock:25,newStock:50}},{id:"5",type:"login",action:"User Login",description:"Admin user logged into the system",user:"Admin",timestamp:"2024-01-19T08:00:00Z",details:{ipAddress:"*************"}},{id:"6",type:"system",action:"Backup Created",description:"Automatic database backup completed",user:"System",timestamp:"2024-01-19T02:00:00Z",details:{backupSize:"2.5MB"}},{id:"7",type:"debt",action:"Debt Updated",description:"Updated debt record for Ana Reyes",user:"Admin",timestamp:"2024-01-18T15:30:00Z",details:{customer:"Ana Reyes",oldAmount:75,newAmount:100}},{id:"8",type:"product",action:"Product Deleted",description:'Removed "Expired Milk" from product list',user:"Admin",timestamp:"2024-01-18T11:10:00Z",details:{productName:"Expired Milk"}}],x=e=>{switch(e){case"product":return(0,a.jsx)(n.A,{className:"h-4 w-4"});case"debt":case"payment":return(0,a.jsx)(A.A,{className:"h-4 w-4"});case"login":return(0,a.jsx)(u.A,{className:"h-4 w-4"});default:return(0,a.jsx)(ec.A,{className:"h-4 w-4"})}},m=e=>{switch(e){case"product":return"bg-blue-100 text-blue-600 dark:bg-blue-900/30 dark:text-blue-400";case"debt":return"bg-red-100 text-red-600 dark:bg-red-900/30 dark:text-red-400";case"payment":return"bg-green-100 text-green-600 dark:bg-green-900/30 dark:text-green-400";case"login":return"bg-purple-100 text-purple-600 dark:bg-purple-900/30 dark:text-purple-400";default:return"bg-gray-100 text-gray-600 dark:bg-gray-900/30 dark:text-gray-400"}},g=d.filter(t=>{let a=t.description.toLowerCase().includes(e.toLowerCase())||t.action.toLowerCase().includes(e.toLowerCase()),s="all"===r||t.type===r;return a&&s}),p=e=>{let t=new Date(e),r=Math.floor((new Date().getTime()-t.getTime())/36e5);return r<1?"Just now":r<24?"".concat(r," hours ago"):t.toLocaleDateString()+" "+t.toLocaleTimeString()};return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Activity History"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Track all activities and changes in your store"})]}),(0,a.jsxs)("button",{onClick:()=>{let e=new Blob([[["Timestamp","Type","Action","Description","User"],...g.map(e=>[e.timestamp,e.type,e.action,e.description,e.user])].map(e=>e.join(",")).join("\n")],{type:"text/csv"}),t=window.URL.createObjectURL(e),r=document.createElement("a");r.href=t,r.download="revantad-store-history-".concat(new Date().toISOString().split("T")[0],".csv"),r.click(),window.URL.revokeObjectURL(t)},className:"btn-primary flex items-center",children:[(0,a.jsx)(eo.A,{className:"h-4 w-4 mr-2"}),"Export History"]})]}),(0,a.jsx)("div",{className:"card p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4"}),(0,a.jsx)("input",{type:"text",placeholder:"Search activities...",value:e,onChange:e=>t(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"})]}),(0,a.jsxs)("select",{value:r,onChange:e=>l(e.target.value),className:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700",children:[(0,a.jsx)("option",{value:"all",children:"All Types"}),(0,a.jsx)("option",{value:"product",children:"Product Activities"}),(0,a.jsx)("option",{value:"debt",children:"Debt Activities"}),(0,a.jsx)("option",{value:"payment",children:"Payment Activities"}),(0,a.jsx)("option",{value:"login",children:"Login Activities"}),(0,a.jsx)("option",{value:"system",children:"System Activities"})]}),(0,a.jsxs)("select",{value:i,onChange:e=>o(e.target.value),className:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700",children:[(0,a.jsx)("option",{value:"7days",children:"Last 7 days"}),(0,a.jsx)("option",{value:"30days",children:"Last 30 days"}),(0,a.jsx)("option",{value:"90days",children:"Last 90 days"}),(0,a.jsx)("option",{value:"all",children:"All time"})]})]})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-5 gap-4",children:[{type:"product",label:"Product Activities",count:d.filter(e=>"product"===e.type).length},{type:"debt",label:"Debt Activities",count:d.filter(e=>"debt"===e.type).length},{type:"payment",label:"Payment Activities",count:d.filter(e=>"payment"===e.type).length},{type:"login",label:"Login Activities",count:d.filter(e=>"login"===e.type).length},{type:"system",label:"System Activities",count:d.filter(e=>"system"===e.type).length}].map(e=>(0,a.jsxs)("div",{className:"card p-4 text-center",children:[(0,a.jsx)("div",{className:"inline-flex p-2 rounded-lg mb-2 ".concat(m(e.type)),children:x(e.type)}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:e.count}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:e.label})]},e.type))}),(0,a.jsxs)("div",{className:"card",children:[(0,a.jsx)("div",{className:"p-6 border-b border-gray-200 dark:border-gray-700",children:(0,a.jsxs)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:["Recent Activities (",g.length,")"]})}),(0,a.jsx)("div",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:g.map(e=>(0,a.jsx)("div",{className:"p-6 hover:bg-gray-50 dark:hover:bg-slate-700/50 transition-colors",children:(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[(0,a.jsx)("div",{className:"p-2 rounded-lg ".concat(m(e.type)),children:x(e.type)}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.action}),(0,a.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:p(e.timestamp)})]}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mt-1",children:e.description}),(0,a.jsxs)("div",{className:"flex items-center justify-between mt-2",children:[(0,a.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["by ",e.user]}),e.details&&(0,a.jsx)("button",{className:"text-xs text-green-600 dark:text-green-400 hover:underline",children:"View Details"})]})]})]})},e.id))}),0===g.length&&(0,a.jsxs)("div",{className:"p-12 text-center",children:[(0,a.jsx)(ec.A,{className:"h-12 w-12 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No activities found"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Try adjusting your search or filter criteria"})]})]})]})}var eg=r(5937),ep=r(3861),eb=r(5525),eh=r(3127),ey=r(4213),ef=r(4229);function ej(){let[e,t]=(0,s.useState)("store"),[r,l]=(0,s.useState)({store:{name:"Revantad Store",address:"123 Barangay Street, Manila, Philippines",phone:"+63 ************",email:"<EMAIL>",currency:"PHP",timezone:"Asia/Manila",businessHours:{open:"06:00",close:"22:00"}},profile:{firstName:"Admin",lastName:"User",email:"<EMAIL>",phone:"+63 ************",role:"Store Owner"},notifications:{lowStock:!0,newDebt:!0,paymentReceived:!0,dailyReport:!1,weeklyReport:!0,emailNotifications:!0,smsNotifications:!1},security:{twoFactorAuth:!1,sessionTimeout:"30",passwordExpiry:"90",loginAttempts:"5"},appearance:{theme:"light",language:"en",dateFormat:"MM/DD/YYYY",numberFormat:"en-US"},backup:{autoBackup:!0,backupFrequency:"daily",retentionDays:"30",lastBackup:"2024-01-20T10:30:00Z"}}),i=[{id:"store",label:"Store Info",icon:eg.A},{id:"profile",label:"Profile",icon:u.A},{id:"notifications",label:"Notifications",icon:ep.A},{id:"security",label:"Security",icon:eb.A},{id:"appearance",label:"Appearance",icon:eh.A},{id:"backup",label:"Backup",icon:ey.A}],n=()=>{console.log("Exporting data..."),alert("Data export started. You will receive an email when ready.")},o=()=>{console.log("Importing data..."),alert("Please select a backup file to import.")},d=()=>(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Store Name"}),(0,a.jsx)("input",{type:"text",value:r.store.name,onChange:e=>l({...r,store:{...r.store,name:e.target.value}}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Phone Number"}),(0,a.jsx)("input",{type:"tel",value:r.store.phone,onChange:e=>l({...r,store:{...r.store,phone:e.target.value}}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Address"}),(0,a.jsx)("textarea",{value:r.store.address,onChange:e=>l({...r,store:{...r.store,address:e.target.value}}),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Opening Time"}),(0,a.jsx)("input",{type:"time",value:r.store.businessHours.open,onChange:e=>l({...r,store:{...r.store,businessHours:{...r.store.businessHours,open:e.target.value}}}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Closing Time"}),(0,a.jsx)("input",{type:"time",value:r.store.businessHours.close,onChange:e=>l({...r,store:{...r.store,businessHours:{...r.store.businessHours,close:e.target.value}}}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"})]})]})]}),c=()=>(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Alert Preferences"}),(0,a.jsx)("div",{className:"space-y-4",children:Object.entries(r.notifications).map(e=>{let[t,s]=e;return(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"text-sm font-medium text-gray-700 dark:text-gray-300",children:t.replace(/([A-Z])/g," $1").replace(/^./,e=>e.toUpperCase())}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 dark:text-gray-400",children:["lowStock"===t&&"Get notified when products are running low","newDebt"===t&&"Alert when new customer debt is recorded","paymentReceived"===t&&"Notification for debt payments","dailyReport"===t&&"Daily business summary report","weeklyReport"===t&&"Weekly business analytics report","emailNotifications"===t&&"Receive notifications via email","smsNotifications"===t&&"Receive notifications via SMS"]})]}),(0,a.jsxs)("label",{className:"relative inline-flex items-center cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",checked:s,onChange:e=>l({...r,notifications:{...r.notifications,[t]:e.target.checked}}),className:"sr-only peer"}),(0,a.jsx)("div",{className:"w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-green-300 dark:peer-focus:ring-green-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-green-600"})]})]},t)})})]})}),x=()=>(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Backup Configuration"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Backup Frequency"}),(0,a.jsxs)("select",{value:r.backup.backupFrequency,onChange:e=>l({...r,backup:{...r.backup,backupFrequency:e.target.value}}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700",children:[(0,a.jsx)("option",{value:"daily",children:"Daily"}),(0,a.jsx)("option",{value:"weekly",children:"Weekly"}),(0,a.jsx)("option",{value:"monthly",children:"Monthly"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Retention Period (Days)"}),(0,a.jsx)("input",{type:"number",value:r.backup.retentionDays,onChange:e=>l({...r,backup:{...r.backup,retentionDays:e.target.value}}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent dark:bg-slate-700"})]})]})]}),(0,a.jsxs)("div",{className:"border-t border-gray-200 dark:border-gray-700 pt-6",children:[(0,a.jsx)("h4",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Data Management"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("button",{onClick:n,className:"flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700",children:[(0,a.jsx)(eo.A,{className:"h-5 w-5 mr-2"}),"Export All Data"]}),(0,a.jsxs)("button",{onClick:o,className:"flex items-center justify-center px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-slate-700",children:[(0,a.jsx)(M.A,{className:"h-5 w-5 mr-2"}),"Import Data"]})]}),(0,a.jsx)("div",{className:"mt-4 p-4 bg-gray-50 dark:bg-slate-700 rounded-md",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:[(0,a.jsx)("strong",{children:"Last Backup:"})," ",new Date(r.backup.lastBackup).toLocaleString()]})})]})]});return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Settings"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400 mt-1",children:"Configure your store preferences and system settings"})]}),(0,a.jsxs)("button",{onClick:()=>{console.log("Settings saved:",r),alert("Settings saved successfully!")},className:"btn-primary flex items-center",children:[(0,a.jsx)(ef.A,{className:"h-4 w-4 mr-2"}),"Save Changes"]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsx)("div",{className:"card p-4",children:(0,a.jsx)("nav",{className:"space-y-1",children:i.map(r=>{let s=r.icon;return(0,a.jsxs)("button",{onClick:()=>t(r.id),className:"w-full flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ".concat(e===r.id?"bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400":"text-gray-600 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-slate-700"),children:[(0,a.jsx)(s,{className:"h-4 w-4 mr-3"}),r.label]},r.id)})})})}),(0,a.jsx)("div",{className:"lg:col-span-3",children:(0,a.jsx)("div",{className:"card p-6",children:(()=>{switch(e){case"store":return d();case"notifications":return c();case"backup":return x();default:return(0,a.jsx)("div",{className:"text-center py-12",children:(0,a.jsxs)("p",{className:"text-gray-500 dark:text-gray-400",children:[e.charAt(0).toUpperCase()+e.slice(1)," settings coming soon..."]})})}})()})})]})]})}var ev=r(5695);function eN(e){let{children:t}=e,{isAuthenticated:r,isLoading:l}=(0,h.A)(),i=(0,ev.useRouter)();return((0,s.useEffect)(()=>{l||r||i.push("/login")},[r,l,i]),l)?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-slate-900",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 hero-gradient rounded-full flex items-center justify-center mx-auto mb-4 animate-pulse",children:(0,a.jsx)("span",{className:"text-white font-bold text-2xl",children:"R"})}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"Loading Revantad Store"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Please wait while we prepare your dashboard..."})]})}):r?(0,a.jsx)(a.Fragment,{children:t}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50 dark:bg-slate-900",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-red-100 dark:bg-red-900/30 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)("span",{className:"text-red-600 dark:text-red-400 font-bold text-2xl",children:"!"})}),(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 dark:text-white mb-2",children:"Access Denied"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-400",children:"Redirecting to login page..."})]})})}function ek(){let[e,t]=(0,s.useState)("dashboard"),{resolvedTheme:r}=(0,l.D)(),[i,n]=(0,s.useState)({totalProducts:0,totalDebts:0,totalDebtAmount:0,lowStockItems:0,recentProducts:[],recentDebts:[]});(0,s.useEffect)(()=>{o()},[]);let o=async()=>{try{let e=await fetch("/api/products"),t=(await e.json()).products||[],r=await fetch("/api/debts"),a=(await r.json()).debts||[],s=a.reduce((e,t)=>e+t.total_amount,0),l=t.filter(e=>e.stock_quantity<10).length;n({totalProducts:t.length,totalDebts:a.length,totalDebtAmount:s,lowStockItems:l,recentProducts:t.slice(0,5),recentDebts:a.slice(0,5)})}catch(e){console.error("Error fetching stats:",e)}};return(0,a.jsx)(eN,{children:(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-slate-900 transition-colors duration-300",style:{backgroundColor:"dark"===r?"#0f172a":"#f9fafb"},children:[(0,a.jsx)(y,{activeSection:e,setActiveSection:t}),(0,a.jsxs)("div",{className:"flex pt-16",children:[(0,a.jsx)(S,{activeSection:e,setActiveSection:t}),(0,a.jsx)("main",{className:"flex-1 overflow-auto transition-colors duration-300",style:{backgroundColor:"dark"===r?"#0f172a":"#ffffff"},children:(0,a.jsxs)("div",{className:"p-8",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold transition-colors duration-300",style:{color:"dark"===r?"#f8fafc":"#1f2937"},children:(()=>{switch(e){case"dashboard":default:return"Dashboard";case"products":return"Product Lists";case"debts":return"Customer Debt Management";case"family-gallery":return"Family Gallery";case"api-graphing":return"API Graphing & Visuals";case"history":return"History";case"calendar":return"Calendar";case"settings":return"Settings"}})()}),(0,a.jsx)("p",{className:"mt-2 transition-colors duration-300",style:{color:"dark"===r?"#cbd5e1":"#6b7280"},children:(()=>{switch(e){case"dashboard":default:return"Overview of your Revantad Store";case"products":return"Manage your product lists with CRUD operations";case"debts":return"Track customer debt and payments";case"family-gallery":return"Manage family photos and memories";case"api-graphing":return"Visual analytics and business insights";case"history":return"View transaction and activity history";case"calendar":return"Manage events and schedules";case"settings":return"Configure your store settings"}})()})]}),(()=>{switch(e){case"products":return(0,a.jsx)(er,{onStatsUpdate:o});case"debts":return(0,a.jsx)(el,{onStatsUpdate:o});case"family-gallery":return(0,a.jsx)(ed,{});case"api-graphing":return(0,a.jsx)(L,{stats:i});case"history":return(0,a.jsx)(eu,{});case"calendar":return(0,a.jsx)(em,{});case"settings":return(0,a.jsx)(ej,{});default:return(0,a.jsx)(_,{stats:i})}})()]})})]})]})})}r(7280)},7280:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>l});var a=r(5155),s=r(1362);function l(e){let{children:t,...r}=e;return(0,a.jsx)(s.N,{...r,children:t})}},8606:(e,t,r)=>{Promise.resolve().then(r.bind(r,3528))}},e=>{var t=t=>e(e.s=t);e.O(0,[95,377,441,684,358],()=>t(8606)),_N_E=e.O()}]);