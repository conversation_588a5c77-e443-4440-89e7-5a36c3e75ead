{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axis/AxisBuilder.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { retrieve, defaults, extend, each, isObject, map, isString, isNumber, isFunction, retrieve2 } from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { getECData } from '../../util/innerStore.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport Model from '../../model/Model.js';\nimport { isRadianAroundZero, remRadian } from '../../util/number.js';\nimport { createSymbol, normalizeSymbolOffset } from '../../util/symbol.js';\nimport * as matrixUtil from 'zrender/lib/core/matrix.js';\nimport { applyTransform as v2ApplyTransform } from 'zrender/lib/core/vector.js';\nimport { shouldShowAllLabels } from '../../coord/axisHelper.js';\nimport { prepareLayoutList, hideOverlap } from '../../label/labelLayoutHelper.js';\nvar PI = Math.PI;\n/**\r\n * A final axis is translated and rotated from a \"standard axis\".\r\n * So opt.position and opt.rotation is required.\r\n *\r\n * A standard axis is and axis from [0, 0] to [0, axisExtent[1]],\r\n * for example: (0, 0) ------------> (0, 50)\r\n *\r\n * nameDirection or tickDirection or labelDirection is 1 means tick\r\n * or label is below the standard axis, whereas is -1 means above\r\n * the standard axis. labelOffset means offset between label and axis,\r\n * which is useful when 'onZero', where axisLabel is in the grid and\r\n * label in outside grid.\r\n *\r\n * Tips: like always,\r\n * positive rotation represents anticlockwise, and negative rotation\r\n * represents clockwise.\r\n * The direction of position coordinate is the same as the direction\r\n * of screen coordinate.\r\n *\r\n * Do not need to consider axis 'inverse', which is auto processed by\r\n * axis extent.\r\n */\nvar AxisBuilder = /** @class */function () {\n  function AxisBuilder(axisModel, opt) {\n    this.group = new graphic.Group();\n    this.opt = opt;\n    this.axisModel = axisModel;\n    // Default value\n    defaults(opt, {\n      labelOffset: 0,\n      nameDirection: 1,\n      tickDirection: 1,\n      labelDirection: 1,\n      silent: true,\n      handleAutoShown: function () {\n        return true;\n      }\n    });\n    // FIXME Not use a separate text group?\n    var transformGroup = new graphic.Group({\n      x: opt.position[0],\n      y: opt.position[1],\n      rotation: opt.rotation\n    });\n    // this.group.add(transformGroup);\n    // this._transformGroup = transformGroup;\n    transformGroup.updateTransform();\n    this._transformGroup = transformGroup;\n  }\n  AxisBuilder.prototype.hasBuilder = function (name) {\n    return !!builders[name];\n  };\n  AxisBuilder.prototype.add = function (name) {\n    builders[name](this.opt, this.axisModel, this.group, this._transformGroup);\n  };\n  AxisBuilder.prototype.getGroup = function () {\n    return this.group;\n  };\n  AxisBuilder.innerTextLayout = function (axisRotation, textRotation, direction) {\n    var rotationDiff = remRadian(textRotation - axisRotation);\n    var textAlign;\n    var textVerticalAlign;\n    if (isRadianAroundZero(rotationDiff)) {\n      // Label is parallel with axis line.\n      textVerticalAlign = direction > 0 ? 'top' : 'bottom';\n      textAlign = 'center';\n    } else if (isRadianAroundZero(rotationDiff - PI)) {\n      // Label is inverse parallel with axis line.\n      textVerticalAlign = direction > 0 ? 'bottom' : 'top';\n      textAlign = 'center';\n    } else {\n      textVerticalAlign = 'middle';\n      if (rotationDiff > 0 && rotationDiff < PI) {\n        textAlign = direction > 0 ? 'right' : 'left';\n      } else {\n        textAlign = direction > 0 ? 'left' : 'right';\n      }\n    }\n    return {\n      rotation: rotationDiff,\n      textAlign: textAlign,\n      textVerticalAlign: textVerticalAlign\n    };\n  };\n  AxisBuilder.makeAxisEventDataBase = function (axisModel) {\n    var eventData = {\n      componentType: axisModel.mainType,\n      componentIndex: axisModel.componentIndex\n    };\n    eventData[axisModel.mainType + 'Index'] = axisModel.componentIndex;\n    return eventData;\n  };\n  AxisBuilder.isLabelSilent = function (axisModel) {\n    var tooltipOpt = axisModel.get('tooltip');\n    return axisModel.get('silent')\n    // Consider mouse cursor, add these restrictions.\n    || !(axisModel.get('triggerEvent') || tooltipOpt && tooltipOpt.show);\n  };\n  return AxisBuilder;\n}();\n;\nvar builders = {\n  axisLine: function (opt, axisModel, group, transformGroup) {\n    var shown = axisModel.get(['axisLine', 'show']);\n    if (shown === 'auto' && opt.handleAutoShown) {\n      shown = opt.handleAutoShown('axisLine');\n    }\n    if (!shown) {\n      return;\n    }\n    var extent = axisModel.axis.getExtent();\n    var matrix = transformGroup.transform;\n    var pt1 = [extent[0], 0];\n    var pt2 = [extent[1], 0];\n    var inverse = pt1[0] > pt2[0];\n    if (matrix) {\n      v2ApplyTransform(pt1, pt1, matrix);\n      v2ApplyTransform(pt2, pt2, matrix);\n    }\n    var lineStyle = extend({\n      lineCap: 'round'\n    }, axisModel.getModel(['axisLine', 'lineStyle']).getLineStyle());\n    var line = new graphic.Line({\n      shape: {\n        x1: pt1[0],\n        y1: pt1[1],\n        x2: pt2[0],\n        y2: pt2[1]\n      },\n      style: lineStyle,\n      strokeContainThreshold: opt.strokeContainThreshold || 5,\n      silent: true,\n      z2: 1\n    });\n    graphic.subPixelOptimizeLine(line.shape, line.style.lineWidth);\n    line.anid = 'line';\n    group.add(line);\n    var arrows = axisModel.get(['axisLine', 'symbol']);\n    if (arrows != null) {\n      var arrowSize = axisModel.get(['axisLine', 'symbolSize']);\n      if (isString(arrows)) {\n        // Use the same arrow for start and end point\n        arrows = [arrows, arrows];\n      }\n      if (isString(arrowSize) || isNumber(arrowSize)) {\n        // Use the same size for width and height\n        arrowSize = [arrowSize, arrowSize];\n      }\n      var arrowOffset = normalizeSymbolOffset(axisModel.get(['axisLine', 'symbolOffset']) || 0, arrowSize);\n      var symbolWidth_1 = arrowSize[0];\n      var symbolHeight_1 = arrowSize[1];\n      each([{\n        rotate: opt.rotation + Math.PI / 2,\n        offset: arrowOffset[0],\n        r: 0\n      }, {\n        rotate: opt.rotation - Math.PI / 2,\n        offset: arrowOffset[1],\n        r: Math.sqrt((pt1[0] - pt2[0]) * (pt1[0] - pt2[0]) + (pt1[1] - pt2[1]) * (pt1[1] - pt2[1]))\n      }], function (point, index) {\n        if (arrows[index] !== 'none' && arrows[index] != null) {\n          var symbol = createSymbol(arrows[index], -symbolWidth_1 / 2, -symbolHeight_1 / 2, symbolWidth_1, symbolHeight_1, lineStyle.stroke, true);\n          // Calculate arrow position with offset\n          var r = point.r + point.offset;\n          var pt = inverse ? pt2 : pt1;\n          symbol.attr({\n            rotation: point.rotate,\n            x: pt[0] + r * Math.cos(opt.rotation),\n            y: pt[1] - r * Math.sin(opt.rotation),\n            silent: true,\n            z2: 11\n          });\n          group.add(symbol);\n        }\n      });\n    }\n  },\n  axisTickLabel: function (opt, axisModel, group, transformGroup) {\n    var ticksEls = buildAxisMajorTicks(group, transformGroup, axisModel, opt);\n    var labelEls = buildAxisLabel(group, transformGroup, axisModel, opt);\n    fixMinMaxLabelShow(axisModel, labelEls, ticksEls);\n    buildAxisMinorTicks(group, transformGroup, axisModel, opt.tickDirection);\n    // This bit fixes the label overlap issue for the time chart.\n    // See https://github.com/apache/echarts/issues/14266 for more.\n    if (axisModel.get(['axisLabel', 'hideOverlap'])) {\n      var labelList = prepareLayoutList(map(labelEls, function (label) {\n        return {\n          label: label,\n          priority: label.z2,\n          defaultAttr: {\n            ignore: label.ignore\n          }\n        };\n      }));\n      hideOverlap(labelList);\n    }\n  },\n  axisName: function (opt, axisModel, group, transformGroup) {\n    var name = retrieve(opt.axisName, axisModel.get('name'));\n    if (!name) {\n      return;\n    }\n    var nameLocation = axisModel.get('nameLocation');\n    var nameDirection = opt.nameDirection;\n    var textStyleModel = axisModel.getModel('nameTextStyle');\n    var gap = axisModel.get('nameGap') || 0;\n    var extent = axisModel.axis.getExtent();\n    var gapSignal = extent[0] > extent[1] ? -1 : 1;\n    var pos = [nameLocation === 'start' ? extent[0] - gapSignal * gap : nameLocation === 'end' ? extent[1] + gapSignal * gap : (extent[0] + extent[1]) / 2,\n    // Reuse labelOffset.\n    isNameLocationCenter(nameLocation) ? opt.labelOffset + nameDirection * gap : 0];\n    var labelLayout;\n    var nameRotation = axisModel.get('nameRotate');\n    if (nameRotation != null) {\n      nameRotation = nameRotation * PI / 180; // To radian.\n    }\n    var axisNameAvailableWidth;\n    if (isNameLocationCenter(nameLocation)) {\n      labelLayout = AxisBuilder.innerTextLayout(opt.rotation, nameRotation != null ? nameRotation : opt.rotation,\n      // Adapt to axis.\n      nameDirection);\n    } else {\n      labelLayout = endTextLayout(opt.rotation, nameLocation, nameRotation || 0, extent);\n      axisNameAvailableWidth = opt.axisNameAvailableWidth;\n      if (axisNameAvailableWidth != null) {\n        axisNameAvailableWidth = Math.abs(axisNameAvailableWidth / Math.sin(labelLayout.rotation));\n        !isFinite(axisNameAvailableWidth) && (axisNameAvailableWidth = null);\n      }\n    }\n    var textFont = textStyleModel.getFont();\n    var truncateOpt = axisModel.get('nameTruncate', true) || {};\n    var ellipsis = truncateOpt.ellipsis;\n    var maxWidth = retrieve(opt.nameTruncateMaxWidth, truncateOpt.maxWidth, axisNameAvailableWidth);\n    var textEl = new graphic.Text({\n      x: pos[0],\n      y: pos[1],\n      rotation: labelLayout.rotation,\n      silent: AxisBuilder.isLabelSilent(axisModel),\n      style: createTextStyle(textStyleModel, {\n        text: name,\n        font: textFont,\n        overflow: 'truncate',\n        width: maxWidth,\n        ellipsis: ellipsis,\n        fill: textStyleModel.getTextColor() || axisModel.get(['axisLine', 'lineStyle', 'color']),\n        align: textStyleModel.get('align') || labelLayout.textAlign,\n        verticalAlign: textStyleModel.get('verticalAlign') || labelLayout.textVerticalAlign\n      }),\n      z2: 1\n    });\n    graphic.setTooltipConfig({\n      el: textEl,\n      componentModel: axisModel,\n      itemName: name\n    });\n    textEl.__fullText = name;\n    // Id for animation\n    textEl.anid = 'name';\n    if (axisModel.get('triggerEvent')) {\n      var eventData = AxisBuilder.makeAxisEventDataBase(axisModel);\n      eventData.targetType = 'axisName';\n      eventData.name = name;\n      getECData(textEl).eventData = eventData;\n    }\n    // FIXME\n    transformGroup.add(textEl);\n    textEl.updateTransform();\n    group.add(textEl);\n    textEl.decomposeTransform();\n  }\n};\nfunction endTextLayout(rotation, textPosition, textRotate, extent) {\n  var rotationDiff = remRadian(textRotate - rotation);\n  var textAlign;\n  var textVerticalAlign;\n  var inverse = extent[0] > extent[1];\n  var onLeft = textPosition === 'start' && !inverse || textPosition !== 'start' && inverse;\n  if (isRadianAroundZero(rotationDiff - PI / 2)) {\n    textVerticalAlign = onLeft ? 'bottom' : 'top';\n    textAlign = 'center';\n  } else if (isRadianAroundZero(rotationDiff - PI * 1.5)) {\n    textVerticalAlign = onLeft ? 'top' : 'bottom';\n    textAlign = 'center';\n  } else {\n    textVerticalAlign = 'middle';\n    if (rotationDiff < PI * 1.5 && rotationDiff > PI / 2) {\n      textAlign = onLeft ? 'left' : 'right';\n    } else {\n      textAlign = onLeft ? 'right' : 'left';\n    }\n  }\n  return {\n    rotation: rotationDiff,\n    textAlign: textAlign,\n    textVerticalAlign: textVerticalAlign\n  };\n}\nfunction fixMinMaxLabelShow(axisModel, labelEls, tickEls) {\n  if (shouldShowAllLabels(axisModel.axis)) {\n    return;\n  }\n  // If min or max are user set, we need to check\n  // If the tick on min(max) are overlap on their neighbour tick\n  // If they are overlapped, we need to hide the min(max) tick label\n  var showMinLabel = axisModel.get(['axisLabel', 'showMinLabel']);\n  var showMaxLabel = axisModel.get(['axisLabel', 'showMaxLabel']);\n  // FIXME\n  // Have not consider onBand yet, where tick els is more than label els.\n  labelEls = labelEls || [];\n  tickEls = tickEls || [];\n  var firstLabel = labelEls[0];\n  var nextLabel = labelEls[1];\n  var lastLabel = labelEls[labelEls.length - 1];\n  var prevLabel = labelEls[labelEls.length - 2];\n  var firstTick = tickEls[0];\n  var nextTick = tickEls[1];\n  var lastTick = tickEls[tickEls.length - 1];\n  var prevTick = tickEls[tickEls.length - 2];\n  if (showMinLabel === false) {\n    ignoreEl(firstLabel);\n    ignoreEl(firstTick);\n  } else if (isTwoLabelOverlapped(firstLabel, nextLabel)) {\n    if (showMinLabel) {\n      ignoreEl(nextLabel);\n      ignoreEl(nextTick);\n    } else {\n      ignoreEl(firstLabel);\n      ignoreEl(firstTick);\n    }\n  }\n  if (showMaxLabel === false) {\n    ignoreEl(lastLabel);\n    ignoreEl(lastTick);\n  } else if (isTwoLabelOverlapped(prevLabel, lastLabel)) {\n    if (showMaxLabel) {\n      ignoreEl(prevLabel);\n      ignoreEl(prevTick);\n    } else {\n      ignoreEl(lastLabel);\n      ignoreEl(lastTick);\n    }\n  }\n}\nfunction ignoreEl(el) {\n  el && (el.ignore = true);\n}\nfunction isTwoLabelOverlapped(current, next) {\n  // current and next has the same rotation.\n  var firstRect = current && current.getBoundingRect().clone();\n  var nextRect = next && next.getBoundingRect().clone();\n  if (!firstRect || !nextRect) {\n    return;\n  }\n  // When checking intersect of two rotated labels, we use mRotationBack\n  // to avoid that boundingRect is enlarge when using `boundingRect.applyTransform`.\n  var mRotationBack = matrixUtil.identity([]);\n  matrixUtil.rotate(mRotationBack, mRotationBack, -current.rotation);\n  firstRect.applyTransform(matrixUtil.mul([], mRotationBack, current.getLocalTransform()));\n  nextRect.applyTransform(matrixUtil.mul([], mRotationBack, next.getLocalTransform()));\n  return firstRect.intersect(nextRect);\n}\nfunction isNameLocationCenter(nameLocation) {\n  return nameLocation === 'middle' || nameLocation === 'center';\n}\nfunction createTicks(ticksCoords, tickTransform, tickEndCoord, tickLineStyle, anidPrefix) {\n  var tickEls = [];\n  var pt1 = [];\n  var pt2 = [];\n  for (var i = 0; i < ticksCoords.length; i++) {\n    var tickCoord = ticksCoords[i].coord;\n    pt1[0] = tickCoord;\n    pt1[1] = 0;\n    pt2[0] = tickCoord;\n    pt2[1] = tickEndCoord;\n    if (tickTransform) {\n      v2ApplyTransform(pt1, pt1, tickTransform);\n      v2ApplyTransform(pt2, pt2, tickTransform);\n    }\n    // Tick line, Not use group transform to have better line draw\n    var tickEl = new graphic.Line({\n      shape: {\n        x1: pt1[0],\n        y1: pt1[1],\n        x2: pt2[0],\n        y2: pt2[1]\n      },\n      style: tickLineStyle,\n      z2: 2,\n      autoBatch: true,\n      silent: true\n    });\n    graphic.subPixelOptimizeLine(tickEl.shape, tickEl.style.lineWidth);\n    tickEl.anid = anidPrefix + '_' + ticksCoords[i].tickValue;\n    tickEls.push(tickEl);\n  }\n  return tickEls;\n}\nfunction buildAxisMajorTicks(group, transformGroup, axisModel, opt) {\n  var axis = axisModel.axis;\n  var tickModel = axisModel.getModel('axisTick');\n  var shown = tickModel.get('show');\n  if (shown === 'auto' && opt.handleAutoShown) {\n    shown = opt.handleAutoShown('axisTick');\n  }\n  if (!shown || axis.scale.isBlank()) {\n    return;\n  }\n  var lineStyleModel = tickModel.getModel('lineStyle');\n  var tickEndCoord = opt.tickDirection * tickModel.get('length');\n  var ticksCoords = axis.getTicksCoords();\n  var ticksEls = createTicks(ticksCoords, transformGroup.transform, tickEndCoord, defaults(lineStyleModel.getLineStyle(), {\n    stroke: axisModel.get(['axisLine', 'lineStyle', 'color'])\n  }), 'ticks');\n  for (var i = 0; i < ticksEls.length; i++) {\n    group.add(ticksEls[i]);\n  }\n  return ticksEls;\n}\nfunction buildAxisMinorTicks(group, transformGroup, axisModel, tickDirection) {\n  var axis = axisModel.axis;\n  var minorTickModel = axisModel.getModel('minorTick');\n  if (!minorTickModel.get('show') || axis.scale.isBlank()) {\n    return;\n  }\n  var minorTicksCoords = axis.getMinorTicksCoords();\n  if (!minorTicksCoords.length) {\n    return;\n  }\n  var lineStyleModel = minorTickModel.getModel('lineStyle');\n  var tickEndCoord = tickDirection * minorTickModel.get('length');\n  var minorTickLineStyle = defaults(lineStyleModel.getLineStyle(), defaults(axisModel.getModel('axisTick').getLineStyle(), {\n    stroke: axisModel.get(['axisLine', 'lineStyle', 'color'])\n  }));\n  for (var i = 0; i < minorTicksCoords.length; i++) {\n    var minorTicksEls = createTicks(minorTicksCoords[i], transformGroup.transform, tickEndCoord, minorTickLineStyle, 'minorticks_' + i);\n    for (var k = 0; k < minorTicksEls.length; k++) {\n      group.add(minorTicksEls[k]);\n    }\n  }\n}\nfunction buildAxisLabel(group, transformGroup, axisModel, opt) {\n  var axis = axisModel.axis;\n  var show = retrieve(opt.axisLabelShow, axisModel.get(['axisLabel', 'show']));\n  if (!show || axis.scale.isBlank()) {\n    return;\n  }\n  var labelModel = axisModel.getModel('axisLabel');\n  var labelMargin = labelModel.get('margin');\n  var labels = axis.getViewLabels();\n  // Special label rotate.\n  var labelRotation = (retrieve(opt.labelRotate, labelModel.get('rotate')) || 0) * PI / 180;\n  var labelLayout = AxisBuilder.innerTextLayout(opt.rotation, labelRotation, opt.labelDirection);\n  var rawCategoryData = axisModel.getCategories && axisModel.getCategories(true);\n  var labelEls = [];\n  var silent = AxisBuilder.isLabelSilent(axisModel);\n  var triggerEvent = axisModel.get('triggerEvent');\n  each(labels, function (labelItem, index) {\n    var tickValue = axis.scale.type === 'ordinal' ? axis.scale.getRawOrdinalNumber(labelItem.tickValue) : labelItem.tickValue;\n    var formattedLabel = labelItem.formattedLabel;\n    var rawLabel = labelItem.rawLabel;\n    var itemLabelModel = labelModel;\n    if (rawCategoryData && rawCategoryData[tickValue]) {\n      var rawCategoryItem = rawCategoryData[tickValue];\n      if (isObject(rawCategoryItem) && rawCategoryItem.textStyle) {\n        itemLabelModel = new Model(rawCategoryItem.textStyle, labelModel, axisModel.ecModel);\n      }\n    }\n    var textColor = itemLabelModel.getTextColor() || axisModel.get(['axisLine', 'lineStyle', 'color']);\n    var tickCoord = axis.dataToCoord(tickValue);\n    var align = itemLabelModel.getShallow('align', true) || labelLayout.textAlign;\n    var alignMin = retrieve2(itemLabelModel.getShallow('alignMinLabel', true), align);\n    var alignMax = retrieve2(itemLabelModel.getShallow('alignMaxLabel', true), align);\n    var verticalAlign = itemLabelModel.getShallow('verticalAlign', true) || itemLabelModel.getShallow('baseline', true) || labelLayout.textVerticalAlign;\n    var verticalAlignMin = retrieve2(itemLabelModel.getShallow('verticalAlignMinLabel', true), verticalAlign);\n    var verticalAlignMax = retrieve2(itemLabelModel.getShallow('verticalAlignMaxLabel', true), verticalAlign);\n    var textEl = new graphic.Text({\n      x: tickCoord,\n      y: opt.labelOffset + opt.labelDirection * labelMargin,\n      rotation: labelLayout.rotation,\n      silent: silent,\n      z2: 10 + (labelItem.level || 0),\n      style: createTextStyle(itemLabelModel, {\n        text: formattedLabel,\n        align: index === 0 ? alignMin : index === labels.length - 1 ? alignMax : align,\n        verticalAlign: index === 0 ? verticalAlignMin : index === labels.length - 1 ? verticalAlignMax : verticalAlign,\n        fill: isFunction(textColor) ? textColor(\n        // (1) In category axis with data zoom, tick is not the original\n        // index of axis.data. So tick should not be exposed to user\n        // in category axis.\n        // (2) Compatible with previous version, which always use formatted label as\n        // input. But in interval scale the formatted label is like '223,445', which\n        // maked user replace ','. So we modify it to return original val but remain\n        // it as 'string' to avoid error in replacing.\n        axis.type === 'category' ? rawLabel : axis.type === 'value' ? tickValue + '' : tickValue, index) : textColor\n      })\n    });\n    textEl.anid = 'label_' + tickValue;\n    graphic.setTooltipConfig({\n      el: textEl,\n      componentModel: axisModel,\n      itemName: formattedLabel,\n      formatterParamsExtra: {\n        isTruncated: function () {\n          return textEl.isTruncated;\n        },\n        value: rawLabel,\n        tickIndex: index\n      }\n    });\n    // Pack data for mouse event\n    if (triggerEvent) {\n      var eventData = AxisBuilder.makeAxisEventDataBase(axisModel);\n      eventData.targetType = 'axisLabel';\n      eventData.value = rawLabel;\n      eventData.tickIndex = index;\n      if (axis.type === 'category') {\n        eventData.dataIndex = tickValue;\n      }\n      getECData(textEl).eventData = eventData;\n    }\n    // FIXME\n    transformGroup.add(textEl);\n    textEl.updateTransform();\n    labelEls.push(textEl);\n    group.add(textEl);\n    textEl.decomposeTransform();\n  });\n  return labelEls;\n}\nexport default AxisBuilder;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;AACA,IAAI,KAAK,KAAK,EAAE;AAChB;;;;;;;;;;;;;;;;;;;;;CAqBC,GACD,IAAI,cAAc,WAAW,GAAE;IAC7B,SAAS,YAAY,SAAS,EAAE,GAAG;QACjC,IAAI,CAAC,KAAK,GAAG,IAAI,yLAAA,CAAA,QAAa;QAC9B,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,SAAS,GAAG;QACjB,gBAAgB;QAChB,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,KAAK;YACZ,aAAa;YACb,eAAe;YACf,eAAe;YACf,gBAAgB;YAChB,QAAQ;YACR,iBAAiB;gBACf,OAAO;YACT;QACF;QACA,uCAAuC;QACvC,IAAI,iBAAiB,IAAI,yLAAA,CAAA,QAAa,CAAC;YACrC,GAAG,IAAI,QAAQ,CAAC,EAAE;YAClB,GAAG,IAAI,QAAQ,CAAC,EAAE;YAClB,UAAU,IAAI,QAAQ;QACxB;QACA,kCAAkC;QAClC,yCAAyC;QACzC,eAAe,eAAe;QAC9B,IAAI,CAAC,eAAe,GAAG;IACzB;IACA,YAAY,SAAS,CAAC,UAAU,GAAG,SAAU,IAAI;QAC/C,OAAO,CAAC,CAAC,QAAQ,CAAC,KAAK;IACzB;IACA,YAAY,SAAS,CAAC,GAAG,GAAG,SAAU,IAAI;QACxC,QAAQ,CAAC,KAAK,CAAC,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,eAAe;IAC3E;IACA,YAAY,SAAS,CAAC,QAAQ,GAAG;QAC/B,OAAO,IAAI,CAAC,KAAK;IACnB;IACA,YAAY,eAAe,GAAG,SAAU,YAAY,EAAE,YAAY,EAAE,SAAS;QAC3E,IAAI,eAAe,CAAA,GAAA,mJAAA,CAAA,YAAS,AAAD,EAAE,eAAe;QAC5C,IAAI;QACJ,IAAI;QACJ,IAAI,CAAA,GAAA,mJAAA,CAAA,qBAAkB,AAAD,EAAE,eAAe;YACpC,oCAAoC;YACpC,oBAAoB,YAAY,IAAI,QAAQ;YAC5C,YAAY;QACd,OAAO,IAAI,CAAA,GAAA,mJAAA,CAAA,qBAAkB,AAAD,EAAE,eAAe,KAAK;YAChD,4CAA4C;YAC5C,oBAAoB,YAAY,IAAI,WAAW;YAC/C,YAAY;QACd,OAAO;YACL,oBAAoB;YACpB,IAAI,eAAe,KAAK,eAAe,IAAI;gBACzC,YAAY,YAAY,IAAI,UAAU;YACxC,OAAO;gBACL,YAAY,YAAY,IAAI,SAAS;YACvC;QACF;QACA,OAAO;YACL,UAAU;YACV,WAAW;YACX,mBAAmB;QACrB;IACF;IACA,YAAY,qBAAqB,GAAG,SAAU,SAAS;QACrD,IAAI,YAAY;YACd,eAAe,UAAU,QAAQ;YACjC,gBAAgB,UAAU,cAAc;QAC1C;QACA,SAAS,CAAC,UAAU,QAAQ,GAAG,QAAQ,GAAG,UAAU,cAAc;QAClE,OAAO;IACT;IACA,YAAY,aAAa,GAAG,SAAU,SAAS;QAC7C,IAAI,aAAa,UAAU,GAAG,CAAC;QAC/B,OAAO,UAAU,GAAG,CAAC,aAElB,CAAC,CAAC,UAAU,GAAG,CAAC,mBAAmB,cAAc,WAAW,IAAI;IACrE;IACA,OAAO;AACT;;AAEA,IAAI,WAAW;IACb,UAAU,SAAU,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc;QACvD,IAAI,QAAQ,UAAU,GAAG,CAAC;YAAC;YAAY;SAAO;QAC9C,IAAI,UAAU,UAAU,IAAI,eAAe,EAAE;YAC3C,QAAQ,IAAI,eAAe,CAAC;QAC9B;QACA,IAAI,CAAC,OAAO;YACV;QACF;QACA,IAAI,SAAS,UAAU,IAAI,CAAC,SAAS;QACrC,IAAI,SAAS,eAAe,SAAS;QACrC,IAAI,MAAM;YAAC,MAAM,CAAC,EAAE;YAAE;SAAE;QACxB,IAAI,MAAM;YAAC,MAAM,CAAC,EAAE;YAAE;SAAE;QACxB,IAAI,UAAU,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;QAC7B,IAAI,QAAQ;YACV,CAAA,GAAA,mJAAA,CAAA,iBAAgB,AAAD,EAAE,KAAK,KAAK;YAC3B,CAAA,GAAA,mJAAA,CAAA,iBAAgB,AAAD,EAAE,KAAK,KAAK;QAC7B;QACA,IAAI,YAAY,CAAA,GAAA,iJAAA,CAAA,SAAM,AAAD,EAAE;YACrB,SAAS;QACX,GAAG,UAAU,QAAQ,CAAC;YAAC;YAAY;SAAY,EAAE,YAAY;QAC7D,IAAI,OAAO,IAAI,gMAAA,CAAA,OAAY,CAAC;YAC1B,OAAO;gBACL,IAAI,GAAG,CAAC,EAAE;gBACV,IAAI,GAAG,CAAC,EAAE;gBACV,IAAI,GAAG,CAAC,EAAE;gBACV,IAAI,GAAG,CAAC,EAAE;YACZ;YACA,OAAO;YACP,wBAAwB,IAAI,sBAAsB,IAAI;YACtD,QAAQ;YACR,IAAI;QACN;QACA,CAAA,GAAA,oKAAA,CAAA,uBAA4B,AAAD,EAAE,KAAK,KAAK,EAAE,KAAK,KAAK,CAAC,SAAS;QAC7D,KAAK,IAAI,GAAG;QACZ,MAAM,GAAG,CAAC;QACV,IAAI,SAAS,UAAU,GAAG,CAAC;YAAC;YAAY;SAAS;QACjD,IAAI,UAAU,MAAM;YAClB,IAAI,YAAY,UAAU,GAAG,CAAC;gBAAC;gBAAY;aAAa;YACxD,IAAI,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,SAAS;gBACpB,6CAA6C;gBAC7C,SAAS;oBAAC;oBAAQ;iBAAO;YAC3B;YACA,IAAI,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,cAAc,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,YAAY;gBAC9C,yCAAyC;gBACzC,YAAY;oBAAC;oBAAW;iBAAU;YACpC;YACA,IAAI,cAAc,CAAA,GAAA,mJAAA,CAAA,wBAAqB,AAAD,EAAE,UAAU,GAAG,CAAC;gBAAC;gBAAY;aAAe,KAAK,GAAG;YAC1F,IAAI,gBAAgB,SAAS,CAAC,EAAE;YAChC,IAAI,iBAAiB,SAAS,CAAC,EAAE;YACjC,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE;gBAAC;oBACJ,QAAQ,IAAI,QAAQ,GAAG,KAAK,EAAE,GAAG;oBACjC,QAAQ,WAAW,CAAC,EAAE;oBACtB,GAAG;gBACL;gBAAG;oBACD,QAAQ,IAAI,QAAQ,GAAG,KAAK,EAAE,GAAG;oBACjC,QAAQ,WAAW,CAAC,EAAE;oBACtB,GAAG,KAAK,IAAI,CAAC,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE,IAAI,CAAC,GAAG,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;gBAC3F;aAAE,EAAE,SAAU,KAAK,EAAE,KAAK;gBACxB,IAAI,MAAM,CAAC,MAAM,KAAK,UAAU,MAAM,CAAC,MAAM,IAAI,MAAM;oBACrD,IAAI,SAAS,CAAA,GAAA,mJAAA,CAAA,eAAY,AAAD,EAAE,MAAM,CAAC,MAAM,EAAE,CAAC,gBAAgB,GAAG,CAAC,iBAAiB,GAAG,eAAe,gBAAgB,UAAU,MAAM,EAAE;oBACnI,uCAAuC;oBACvC,IAAI,IAAI,MAAM,CAAC,GAAG,MAAM,MAAM;oBAC9B,IAAI,KAAK,UAAU,MAAM;oBACzB,OAAO,IAAI,CAAC;wBACV,UAAU,MAAM,MAAM;wBACtB,GAAG,EAAE,CAAC,EAAE,GAAG,IAAI,KAAK,GAAG,CAAC,IAAI,QAAQ;wBACpC,GAAG,EAAE,CAAC,EAAE,GAAG,IAAI,KAAK,GAAG,CAAC,IAAI,QAAQ;wBACpC,QAAQ;wBACR,IAAI;oBACN;oBACA,MAAM,GAAG,CAAC;gBACZ;YACF;QACF;IACF;IACA,eAAe,SAAU,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc;QAC5D,IAAI,WAAW,oBAAoB,OAAO,gBAAgB,WAAW;QACrE,IAAI,WAAW,eAAe,OAAO,gBAAgB,WAAW;QAChE,mBAAmB,WAAW,UAAU;QACxC,oBAAoB,OAAO,gBAAgB,WAAW,IAAI,aAAa;QACvE,6DAA6D;QAC7D,+DAA+D;QAC/D,IAAI,UAAU,GAAG,CAAC;YAAC;YAAa;SAAc,GAAG;YAC/C,IAAI,YAAY,CAAA,GAAA,+JAAA,CAAA,oBAAiB,AAAD,EAAE,CAAA,GAAA,iJAAA,CAAA,MAAG,AAAD,EAAE,UAAU,SAAU,KAAK;gBAC7D,OAAO;oBACL,OAAO;oBACP,UAAU,MAAM,EAAE;oBAClB,aAAa;wBACX,QAAQ,MAAM,MAAM;oBACtB;gBACF;YACF;YACA,CAAA,GAAA,+JAAA,CAAA,cAAW,AAAD,EAAE;QACd;IACF;IACA,UAAU,SAAU,GAAG,EAAE,SAAS,EAAE,KAAK,EAAE,cAAc;QACvD,IAAI,OAAO,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,QAAQ,EAAE,UAAU,GAAG,CAAC;QAChD,IAAI,CAAC,MAAM;YACT;QACF;QACA,IAAI,eAAe,UAAU,GAAG,CAAC;QACjC,IAAI,gBAAgB,IAAI,aAAa;QACrC,IAAI,iBAAiB,UAAU,QAAQ,CAAC;QACxC,IAAI,MAAM,UAAU,GAAG,CAAC,cAAc;QACtC,IAAI,SAAS,UAAU,IAAI,CAAC,SAAS;QACrC,IAAI,YAAY,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,GAAG,CAAC,IAAI;QAC7C,IAAI,MAAM;YAAC,iBAAiB,UAAU,MAAM,CAAC,EAAE,GAAG,YAAY,MAAM,iBAAiB,QAAQ,MAAM,CAAC,EAAE,GAAG,YAAY,MAAM,CAAC,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE,IAAI;YACrJ,qBAAqB;YACrB,qBAAqB,gBAAgB,IAAI,WAAW,GAAG,gBAAgB,MAAM;SAAE;QAC/E,IAAI;QACJ,IAAI,eAAe,UAAU,GAAG,CAAC;QACjC,IAAI,gBAAgB,MAAM;YACxB,eAAe,eAAe,KAAK,KAAK,aAAa;QACvD;QACA,IAAI;QACJ,IAAI,qBAAqB,eAAe;YACtC,cAAc,YAAY,eAAe,CAAC,IAAI,QAAQ,EAAE,gBAAgB,OAAO,eAAe,IAAI,QAAQ,EAC1G,iBAAiB;YACjB;QACF,OAAO;YACL,cAAc,cAAc,IAAI,QAAQ,EAAE,cAAc,gBAAgB,GAAG;YAC3E,yBAAyB,IAAI,sBAAsB;YACnD,IAAI,0BAA0B,MAAM;gBAClC,yBAAyB,KAAK,GAAG,CAAC,yBAAyB,KAAK,GAAG,CAAC,YAAY,QAAQ;gBACxF,CAAC,SAAS,2BAA2B,CAAC,yBAAyB,IAAI;YACrE;QACF;QACA,IAAI,WAAW,eAAe,OAAO;QACrC,IAAI,cAAc,UAAU,GAAG,CAAC,gBAAgB,SAAS,CAAC;QAC1D,IAAI,WAAW,YAAY,QAAQ;QACnC,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,oBAAoB,EAAE,YAAY,QAAQ,EAAE;QACxE,IAAI,SAAS,IAAI,uLAAA,CAAA,OAAY,CAAC;YAC5B,GAAG,GAAG,CAAC,EAAE;YACT,GAAG,GAAG,CAAC,EAAE;YACT,UAAU,YAAY,QAAQ;YAC9B,QAAQ,YAAY,aAAa,CAAC;YAClC,OAAO,CAAA,GAAA,wJAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB;gBACrC,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,OAAO;gBACP,UAAU;gBACV,MAAM,eAAe,YAAY,MAAM,UAAU,GAAG,CAAC;oBAAC;oBAAY;oBAAa;iBAAQ;gBACvF,OAAO,eAAe,GAAG,CAAC,YAAY,YAAY,SAAS;gBAC3D,eAAe,eAAe,GAAG,CAAC,oBAAoB,YAAY,iBAAiB;YACrF;YACA,IAAI;QACN;QACA,CAAA,GAAA,oKAAA,CAAA,mBAAwB,AAAD,EAAE;YACvB,IAAI;YACJ,gBAAgB;YAChB,UAAU;QACZ;QACA,OAAO,UAAU,GAAG;QACpB,mBAAmB;QACnB,OAAO,IAAI,GAAG;QACd,IAAI,UAAU,GAAG,CAAC,iBAAiB;YACjC,IAAI,YAAY,YAAY,qBAAqB,CAAC;YAClD,UAAU,UAAU,GAAG;YACvB,UAAU,IAAI,GAAG;YACjB,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,SAAS,GAAG;QAChC;QACA,QAAQ;QACR,eAAe,GAAG,CAAC;QACnB,OAAO,eAAe;QACtB,MAAM,GAAG,CAAC;QACV,OAAO,kBAAkB;IAC3B;AACF;AACA,SAAS,cAAc,QAAQ,EAAE,YAAY,EAAE,UAAU,EAAE,MAAM;IAC/D,IAAI,eAAe,CAAA,GAAA,mJAAA,CAAA,YAAS,AAAD,EAAE,aAAa;IAC1C,IAAI;IACJ,IAAI;IACJ,IAAI,UAAU,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;IACnC,IAAI,SAAS,iBAAiB,WAAW,CAAC,WAAW,iBAAiB,WAAW;IACjF,IAAI,CAAA,GAAA,mJAAA,CAAA,qBAAkB,AAAD,EAAE,eAAe,KAAK,IAAI;QAC7C,oBAAoB,SAAS,WAAW;QACxC,YAAY;IACd,OAAO,IAAI,CAAA,GAAA,mJAAA,CAAA,qBAAkB,AAAD,EAAE,eAAe,KAAK,MAAM;QACtD,oBAAoB,SAAS,QAAQ;QACrC,YAAY;IACd,OAAO;QACL,oBAAoB;QACpB,IAAI,eAAe,KAAK,OAAO,eAAe,KAAK,GAAG;YACpD,YAAY,SAAS,SAAS;QAChC,OAAO;YACL,YAAY,SAAS,UAAU;QACjC;IACF;IACA,OAAO;QACL,UAAU;QACV,WAAW;QACX,mBAAmB;IACrB;AACF;AACA,SAAS,mBAAmB,SAAS,EAAE,QAAQ,EAAE,OAAO;IACtD,IAAI,CAAA,GAAA,wJAAA,CAAA,sBAAmB,AAAD,EAAE,UAAU,IAAI,GAAG;QACvC;IACF;IACA,+CAA+C;IAC/C,8DAA8D;IAC9D,kEAAkE;IAClE,IAAI,eAAe,UAAU,GAAG,CAAC;QAAC;QAAa;KAAe;IAC9D,IAAI,eAAe,UAAU,GAAG,CAAC;QAAC;QAAa;KAAe;IAC9D,QAAQ;IACR,uEAAuE;IACvE,WAAW,YAAY,EAAE;IACzB,UAAU,WAAW,EAAE;IACvB,IAAI,aAAa,QAAQ,CAAC,EAAE;IAC5B,IAAI,YAAY,QAAQ,CAAC,EAAE;IAC3B,IAAI,YAAY,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;IAC7C,IAAI,YAAY,QAAQ,CAAC,SAAS,MAAM,GAAG,EAAE;IAC7C,IAAI,YAAY,OAAO,CAAC,EAAE;IAC1B,IAAI,WAAW,OAAO,CAAC,EAAE;IACzB,IAAI,WAAW,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;IAC1C,IAAI,WAAW,OAAO,CAAC,QAAQ,MAAM,GAAG,EAAE;IAC1C,IAAI,iBAAiB,OAAO;QAC1B,SAAS;QACT,SAAS;IACX,OAAO,IAAI,qBAAqB,YAAY,YAAY;QACtD,IAAI,cAAc;YAChB,SAAS;YACT,SAAS;QACX,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;IACA,IAAI,iBAAiB,OAAO;QAC1B,SAAS;QACT,SAAS;IACX,OAAO,IAAI,qBAAqB,WAAW,YAAY;QACrD,IAAI,cAAc;YAChB,SAAS;YACT,SAAS;QACX,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF;AACF;AACA,SAAS,SAAS,EAAE;IAClB,MAAM,CAAC,GAAG,MAAM,GAAG,IAAI;AACzB;AACA,SAAS,qBAAqB,OAAO,EAAE,IAAI;IACzC,0CAA0C;IAC1C,IAAI,YAAY,WAAW,QAAQ,eAAe,GAAG,KAAK;IAC1D,IAAI,WAAW,QAAQ,KAAK,eAAe,GAAG,KAAK;IACnD,IAAI,CAAC,aAAa,CAAC,UAAU;QAC3B;IACF;IACA,sEAAsE;IACtE,kFAAkF;IAClF,IAAI,gBAAgB,CAAA,GAAA,mJAAA,CAAA,WAAmB,AAAD,EAAE,EAAE;IAC1C,CAAA,GAAA,mJAAA,CAAA,SAAiB,AAAD,EAAE,eAAe,eAAe,CAAC,QAAQ,QAAQ;IACjE,UAAU,cAAc,CAAC,CAAA,GAAA,mJAAA,CAAA,MAAc,AAAD,EAAE,EAAE,EAAE,eAAe,QAAQ,iBAAiB;IACpF,SAAS,cAAc,CAAC,CAAA,GAAA,mJAAA,CAAA,MAAc,AAAD,EAAE,EAAE,EAAE,eAAe,KAAK,iBAAiB;IAChF,OAAO,UAAU,SAAS,CAAC;AAC7B;AACA,SAAS,qBAAqB,YAAY;IACxC,OAAO,iBAAiB,YAAY,iBAAiB;AACvD;AACA,SAAS,YAAY,WAAW,EAAE,aAAa,EAAE,YAAY,EAAE,aAAa,EAAE,UAAU;IACtF,IAAI,UAAU,EAAE;IAChB,IAAI,MAAM,EAAE;IACZ,IAAI,MAAM,EAAE;IACZ,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;QAC3C,IAAI,YAAY,WAAW,CAAC,EAAE,CAAC,KAAK;QACpC,GAAG,CAAC,EAAE,GAAG;QACT,GAAG,CAAC,EAAE,GAAG;QACT,GAAG,CAAC,EAAE,GAAG;QACT,GAAG,CAAC,EAAE,GAAG;QACT,IAAI,eAAe;YACjB,CAAA,GAAA,mJAAA,CAAA,iBAAgB,AAAD,EAAE,KAAK,KAAK;YAC3B,CAAA,GAAA,mJAAA,CAAA,iBAAgB,AAAD,EAAE,KAAK,KAAK;QAC7B;QACA,8DAA8D;QAC9D,IAAI,SAAS,IAAI,gMAAA,CAAA,OAAY,CAAC;YAC5B,OAAO;gBACL,IAAI,GAAG,CAAC,EAAE;gBACV,IAAI,GAAG,CAAC,EAAE;gBACV,IAAI,GAAG,CAAC,EAAE;gBACV,IAAI,GAAG,CAAC,EAAE;YACZ;YACA,OAAO;YACP,IAAI;YACJ,WAAW;YACX,QAAQ;QACV;QACA,CAAA,GAAA,oKAAA,CAAA,uBAA4B,AAAD,EAAE,OAAO,KAAK,EAAE,OAAO,KAAK,CAAC,SAAS;QACjE,OAAO,IAAI,GAAG,aAAa,MAAM,WAAW,CAAC,EAAE,CAAC,SAAS;QACzD,QAAQ,IAAI,CAAC;IACf;IACA,OAAO;AACT;AACA,SAAS,oBAAoB,KAAK,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG;IAChE,IAAI,OAAO,UAAU,IAAI;IACzB,IAAI,YAAY,UAAU,QAAQ,CAAC;IACnC,IAAI,QAAQ,UAAU,GAAG,CAAC;IAC1B,IAAI,UAAU,UAAU,IAAI,eAAe,EAAE;QAC3C,QAAQ,IAAI,eAAe,CAAC;IAC9B;IACA,IAAI,CAAC,SAAS,KAAK,KAAK,CAAC,OAAO,IAAI;QAClC;IACF;IACA,IAAI,iBAAiB,UAAU,QAAQ,CAAC;IACxC,IAAI,eAAe,IAAI,aAAa,GAAG,UAAU,GAAG,CAAC;IACrD,IAAI,cAAc,KAAK,cAAc;IACrC,IAAI,WAAW,YAAY,aAAa,eAAe,SAAS,EAAE,cAAc,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,YAAY,IAAI;QACtH,QAAQ,UAAU,GAAG,CAAC;YAAC;YAAY;YAAa;SAAQ;IAC1D,IAAI;IACJ,IAAK,IAAI,IAAI,GAAG,IAAI,SAAS,MAAM,EAAE,IAAK;QACxC,MAAM,GAAG,CAAC,QAAQ,CAAC,EAAE;IACvB;IACA,OAAO;AACT;AACA,SAAS,oBAAoB,KAAK,EAAE,cAAc,EAAE,SAAS,EAAE,aAAa;IAC1E,IAAI,OAAO,UAAU,IAAI;IACzB,IAAI,iBAAiB,UAAU,QAAQ,CAAC;IACxC,IAAI,CAAC,eAAe,GAAG,CAAC,WAAW,KAAK,KAAK,CAAC,OAAO,IAAI;QACvD;IACF;IACA,IAAI,mBAAmB,KAAK,mBAAmB;IAC/C,IAAI,CAAC,iBAAiB,MAAM,EAAE;QAC5B;IACF;IACA,IAAI,iBAAiB,eAAe,QAAQ,CAAC;IAC7C,IAAI,eAAe,gBAAgB,eAAe,GAAG,CAAC;IACtD,IAAI,qBAAqB,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,eAAe,YAAY,IAAI,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,UAAU,QAAQ,CAAC,YAAY,YAAY,IAAI;QACvH,QAAQ,UAAU,GAAG,CAAC;YAAC;YAAY;YAAa;SAAQ;IAC1D;IACA,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;QAChD,IAAI,gBAAgB,YAAY,gBAAgB,CAAC,EAAE,EAAE,eAAe,SAAS,EAAE,cAAc,oBAAoB,gBAAgB;QACjI,IAAK,IAAI,IAAI,GAAG,IAAI,cAAc,MAAM,EAAE,IAAK;YAC7C,MAAM,GAAG,CAAC,aAAa,CAAC,EAAE;QAC5B;IACF;AACF;AACA,SAAS,eAAe,KAAK,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG;IAC3D,IAAI,OAAO,UAAU,IAAI;IACzB,IAAI,OAAO,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,aAAa,EAAE,UAAU,GAAG,CAAC;QAAC;QAAa;KAAO;IAC1E,IAAI,CAAC,QAAQ,KAAK,KAAK,CAAC,OAAO,IAAI;QACjC;IACF;IACA,IAAI,aAAa,UAAU,QAAQ,CAAC;IACpC,IAAI,cAAc,WAAW,GAAG,CAAC;IACjC,IAAI,SAAS,KAAK,aAAa;IAC/B,wBAAwB;IACxB,IAAI,gBAAgB,CAAC,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,IAAI,WAAW,EAAE,WAAW,GAAG,CAAC,cAAc,CAAC,IAAI,KAAK;IACtF,IAAI,cAAc,YAAY,eAAe,CAAC,IAAI,QAAQ,EAAE,eAAe,IAAI,cAAc;IAC7F,IAAI,kBAAkB,UAAU,aAAa,IAAI,UAAU,aAAa,CAAC;IACzE,IAAI,WAAW,EAAE;IACjB,IAAI,SAAS,YAAY,aAAa,CAAC;IACvC,IAAI,eAAe,UAAU,GAAG,CAAC;IACjC,CAAA,GAAA,iJAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,SAAU,SAAS,EAAE,KAAK;QACrC,IAAI,YAAY,KAAK,KAAK,CAAC,IAAI,KAAK,YAAY,KAAK,KAAK,CAAC,mBAAmB,CAAC,UAAU,SAAS,IAAI,UAAU,SAAS;QACzH,IAAI,iBAAiB,UAAU,cAAc;QAC7C,IAAI,WAAW,UAAU,QAAQ;QACjC,IAAI,iBAAiB;QACrB,IAAI,mBAAmB,eAAe,CAAC,UAAU,EAAE;YACjD,IAAI,kBAAkB,eAAe,CAAC,UAAU;YAChD,IAAI,CAAA,GAAA,iJAAA,CAAA,WAAQ,AAAD,EAAE,oBAAoB,gBAAgB,SAAS,EAAE;gBAC1D,iBAAiB,IAAI,mJAAA,CAAA,UAAK,CAAC,gBAAgB,SAAS,EAAE,YAAY,UAAU,OAAO;YACrF;QACF;QACA,IAAI,YAAY,eAAe,YAAY,MAAM,UAAU,GAAG,CAAC;YAAC;YAAY;YAAa;SAAQ;QACjG,IAAI,YAAY,KAAK,WAAW,CAAC;QACjC,IAAI,QAAQ,eAAe,UAAU,CAAC,SAAS,SAAS,YAAY,SAAS;QAC7E,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE,eAAe,UAAU,CAAC,iBAAiB,OAAO;QAC3E,IAAI,WAAW,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE,eAAe,UAAU,CAAC,iBAAiB,OAAO;QAC3E,IAAI,gBAAgB,eAAe,UAAU,CAAC,iBAAiB,SAAS,eAAe,UAAU,CAAC,YAAY,SAAS,YAAY,iBAAiB;QACpJ,IAAI,mBAAmB,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE,eAAe,UAAU,CAAC,yBAAyB,OAAO;QAC3F,IAAI,mBAAmB,CAAA,GAAA,iJAAA,CAAA,YAAS,AAAD,EAAE,eAAe,UAAU,CAAC,yBAAyB,OAAO;QAC3F,IAAI,SAAS,IAAI,uLAAA,CAAA,OAAY,CAAC;YAC5B,GAAG;YACH,GAAG,IAAI,WAAW,GAAG,IAAI,cAAc,GAAG;YAC1C,UAAU,YAAY,QAAQ;YAC9B,QAAQ;YACR,IAAI,KAAK,CAAC,UAAU,KAAK,IAAI,CAAC;YAC9B,OAAO,CAAA,GAAA,wJAAA,CAAA,kBAAe,AAAD,EAAE,gBAAgB;gBACrC,MAAM;gBACN,OAAO,UAAU,IAAI,WAAW,UAAU,OAAO,MAAM,GAAG,IAAI,WAAW;gBACzE,eAAe,UAAU,IAAI,mBAAmB,UAAU,OAAO,MAAM,GAAG,IAAI,mBAAmB;gBACjG,MAAM,CAAA,GAAA,iJAAA,CAAA,aAAU,AAAD,EAAE,aAAa,UAC9B,gEAAgE;gBAChE,4DAA4D;gBAC5D,oBAAoB;gBACpB,4EAA4E;gBAC5E,4EAA4E;gBAC5E,4EAA4E;gBAC5E,8CAA8C;gBAC9C,KAAK,IAAI,KAAK,aAAa,WAAW,KAAK,IAAI,KAAK,UAAU,YAAY,KAAK,WAAW,SAAS;YACrG;QACF;QACA,OAAO,IAAI,GAAG,WAAW;QACzB,CAAA,GAAA,oKAAA,CAAA,mBAAwB,AAAD,EAAE;YACvB,IAAI;YACJ,gBAAgB;YAChB,UAAU;YACV,sBAAsB;gBACpB,aAAa;oBACX,OAAO,OAAO,WAAW;gBAC3B;gBACA,OAAO;gBACP,WAAW;YACb;QACF;QACA,4BAA4B;QAC5B,IAAI,cAAc;YAChB,IAAI,YAAY,YAAY,qBAAqB,CAAC;YAClD,UAAU,UAAU,GAAG;YACvB,UAAU,KAAK,GAAG;YAClB,UAAU,SAAS,GAAG;YACtB,IAAI,KAAK,IAAI,KAAK,YAAY;gBAC5B,UAAU,SAAS,GAAG;YACxB;YACA,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,SAAS,GAAG;QAChC;QACA,QAAQ;QACR,eAAe,GAAG,CAAC;QACnB,OAAO,eAAe;QACtB,SAAS,IAAI,CAAC;QACd,MAAM,GAAG,CAAC;QACV,OAAO,kBAAkB;IAC3B;IACA,OAAO;AACT;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 663, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axis/AxisView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as axisPointerModelHelper from '../axisPointer/modelHelper.js';\nimport ComponentView from '../../view/Component.js';\nvar axisPointerClazz = {};\n/**\r\n * Base class of AxisView.\r\n */\nvar AxisView = /** @class */function (_super) {\n  __extends(AxisView, _super);\n  function AxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = AxisView.type;\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  AxisView.prototype.render = function (axisModel, ecModel, api, payload) {\n    // FIXME\n    // This process should proformed after coordinate systems updated\n    // (axis scale updated), and should be performed each time update.\n    // So put it here temporarily, although it is not appropriate to\n    // put a model-writing procedure in `view`.\n    this.axisPointerClass && axisPointerModelHelper.fixValue(axisModel);\n    _super.prototype.render.apply(this, arguments);\n    this._doUpdateAxisPointerClass(axisModel, api, true);\n  };\n  /**\r\n   * Action handler.\r\n   */\n  AxisView.prototype.updateAxisPointer = function (axisModel, ecModel, api, payload) {\n    this._doUpdateAxisPointerClass(axisModel, api, false);\n  };\n  /**\r\n   * @override\r\n   */\n  AxisView.prototype.remove = function (ecModel, api) {\n    var axisPointer = this._axisPointer;\n    axisPointer && axisPointer.remove(api);\n  };\n  /**\r\n   * @override\r\n   */\n  AxisView.prototype.dispose = function (ecModel, api) {\n    this._disposeAxisPointer(api);\n    _super.prototype.dispose.apply(this, arguments);\n  };\n  AxisView.prototype._doUpdateAxisPointerClass = function (axisModel, api, forceRender) {\n    var Clazz = AxisView.getAxisPointerClass(this.axisPointerClass);\n    if (!Clazz) {\n      return;\n    }\n    var axisPointerModel = axisPointerModelHelper.getAxisPointerModel(axisModel);\n    axisPointerModel ? (this._axisPointer || (this._axisPointer = new Clazz())).render(axisModel, axisPointerModel, api, forceRender) : this._disposeAxisPointer(api);\n  };\n  AxisView.prototype._disposeAxisPointer = function (api) {\n    this._axisPointer && this._axisPointer.dispose(api);\n    this._axisPointer = null;\n  };\n  AxisView.registerAxisPointerClass = function (type, clazz) {\n    if (process.env.NODE_ENV !== 'production') {\n      if (axisPointerClazz[type]) {\n        throw new Error('axisPointer ' + type + ' exists');\n      }\n    }\n    axisPointerClazz[type] = clazz;\n  };\n  ;\n  AxisView.getAxisPointerClass = function (type) {\n    return type && axisPointerClazz[type];\n  };\n  ;\n  AxisView.type = 'axis';\n  return AxisView;\n}(ComponentView);\nexport default AxisView;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AA6DQ;AA5DR;AACA;AACA;;;;AACA,IAAI,mBAAmB,CAAC;AACxB;;CAEC,GACD,IAAI,WAAW,WAAW,GAAE,SAAU,MAAM;IAC1C,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,UAAU;IACpB,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,SAAS,IAAI;QAC1B,OAAO;IACT;IACA;;GAEC,GACD,SAAS,SAAS,CAAC,MAAM,GAAG,SAAU,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO;QACpE,QAAQ;QACR,iEAAiE;QACjE,kEAAkE;QAClE,gEAAgE;QAChE,2CAA2C;QAC3C,IAAI,CAAC,gBAAgB,IAAI,CAAA,GAAA,4KAAA,CAAA,WAA+B,AAAD,EAAE;QACzD,OAAO,SAAS,CAAC,MAAM,CAAC,KAAK,CAAC,IAAI,EAAE;QACpC,IAAI,CAAC,yBAAyB,CAAC,WAAW,KAAK;IACjD;IACA;;GAEC,GACD,SAAS,SAAS,CAAC,iBAAiB,GAAG,SAAU,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO;QAC/E,IAAI,CAAC,yBAAyB,CAAC,WAAW,KAAK;IACjD;IACA;;GAEC,GACD,SAAS,SAAS,CAAC,MAAM,GAAG,SAAU,OAAO,EAAE,GAAG;QAChD,IAAI,cAAc,IAAI,CAAC,YAAY;QACnC,eAAe,YAAY,MAAM,CAAC;IACpC;IACA;;GAEC,GACD,SAAS,SAAS,CAAC,OAAO,GAAG,SAAU,OAAO,EAAE,GAAG;QACjD,IAAI,CAAC,mBAAmB,CAAC;QACzB,OAAO,SAAS,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,EAAE;IACvC;IACA,SAAS,SAAS,CAAC,yBAAyB,GAAG,SAAU,SAAS,EAAE,GAAG,EAAE,WAAW;QAClF,IAAI,QAAQ,SAAS,mBAAmB,CAAC,IAAI,CAAC,gBAAgB;QAC9D,IAAI,CAAC,OAAO;YACV;QACF;QACA,IAAI,mBAAmB,CAAA,GAAA,4KAAA,CAAA,sBAA0C,AAAD,EAAE;QAClE,mBAAmB,CAAC,IAAI,CAAC,YAAY,IAAI,CAAC,IAAI,CAAC,YAAY,GAAG,IAAI,OAAO,CAAC,EAAE,MAAM,CAAC,WAAW,kBAAkB,KAAK,eAAe,IAAI,CAAC,mBAAmB,CAAC;IAC/J;IACA,SAAS,SAAS,CAAC,mBAAmB,GAAG,SAAU,GAAG;QACpD,IAAI,CAAC,YAAY,IAAI,IAAI,CAAC,YAAY,CAAC,OAAO,CAAC;QAC/C,IAAI,CAAC,YAAY,GAAG;IACtB;IACA,SAAS,wBAAwB,GAAG,SAAU,IAAI,EAAE,KAAK;QACvD,wCAA2C;YACzC,IAAI,gBAAgB,CAAC,KAAK,EAAE;gBAC1B,MAAM,IAAI,MAAM,iBAAiB,OAAO;YAC1C;QACF;QACA,gBAAgB,CAAC,KAAK,GAAG;IAC3B;;IAEA,SAAS,mBAAmB,GAAG,SAAU,IAAI;QAC3C,OAAO,QAAQ,gBAAgB,CAAC,KAAK;IACvC;;IAEA,SAAS,IAAI,GAAG;IAChB,OAAO;AACT,EAAE,sJAAA,CAAA,UAAa;uCACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 783, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axis/axisSplitHelper.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { makeInner } from '../../util/model.js';\nvar inner = makeInner();\nexport function rectCoordAxisBuildSplitArea(axisView, axisGroup, axisModel, gridModel) {\n  var axis = axisModel.axis;\n  if (axis.scale.isBlank()) {\n    return;\n  }\n  // TODO: TYPE\n  var splitAreaModel = axisModel.getModel('splitArea');\n  var areaStyleModel = splitAreaModel.getModel('areaStyle');\n  var areaColors = areaStyleModel.get('color');\n  var gridRect = gridModel.coordinateSystem.getRect();\n  var ticksCoords = axis.getTicksCoords({\n    tickModel: splitAreaModel,\n    clamp: true\n  });\n  if (!ticksCoords.length) {\n    return;\n  }\n  // For Making appropriate splitArea animation, the color and anid\n  // should be corresponding to previous one if possible.\n  var areaColorsLen = areaColors.length;\n  var lastSplitAreaColors = inner(axisView).splitAreaColors;\n  var newSplitAreaColors = zrUtil.createHashMap();\n  var colorIndex = 0;\n  if (lastSplitAreaColors) {\n    for (var i = 0; i < ticksCoords.length; i++) {\n      var cIndex = lastSplitAreaColors.get(ticksCoords[i].tickValue);\n      if (cIndex != null) {\n        colorIndex = (cIndex + (areaColorsLen - 1) * i) % areaColorsLen;\n        break;\n      }\n    }\n  }\n  var prev = axis.toGlobalCoord(ticksCoords[0].coord);\n  var areaStyle = areaStyleModel.getAreaStyle();\n  areaColors = zrUtil.isArray(areaColors) ? areaColors : [areaColors];\n  for (var i = 1; i < ticksCoords.length; i++) {\n    var tickCoord = axis.toGlobalCoord(ticksCoords[i].coord);\n    var x = void 0;\n    var y = void 0;\n    var width = void 0;\n    var height = void 0;\n    if (axis.isHorizontal()) {\n      x = prev;\n      y = gridRect.y;\n      width = tickCoord - x;\n      height = gridRect.height;\n      prev = x + width;\n    } else {\n      x = gridRect.x;\n      y = prev;\n      width = gridRect.width;\n      height = tickCoord - y;\n      prev = y + height;\n    }\n    var tickValue = ticksCoords[i - 1].tickValue;\n    tickValue != null && newSplitAreaColors.set(tickValue, colorIndex);\n    axisGroup.add(new graphic.Rect({\n      anid: tickValue != null ? 'area_' + tickValue : null,\n      shape: {\n        x: x,\n        y: y,\n        width: width,\n        height: height\n      },\n      style: zrUtil.defaults({\n        fill: areaColors[colorIndex]\n      }, areaStyle),\n      autoBatch: true,\n      silent: true\n    }));\n    colorIndex = (colorIndex + 1) % areaColorsLen;\n  }\n  inner(axisView).splitAreaColors = newSplitAreaColors;\n}\nexport function rectCoordAxisHandleRemove(axisView) {\n  inner(axisView).splitAreaColors = null;\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;AACA;AACA;AACA;;;;AACA,IAAI,QAAQ,CAAA,GAAA,kJAAA,CAAA,YAAS,AAAD;AACb,SAAS,4BAA4B,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;IACnF,IAAI,OAAO,UAAU,IAAI;IACzB,IAAI,KAAK,KAAK,CAAC,OAAO,IAAI;QACxB;IACF;IACA,aAAa;IACb,IAAI,iBAAiB,UAAU,QAAQ,CAAC;IACxC,IAAI,iBAAiB,eAAe,QAAQ,CAAC;IAC7C,IAAI,aAAa,eAAe,GAAG,CAAC;IACpC,IAAI,WAAW,UAAU,gBAAgB,CAAC,OAAO;IACjD,IAAI,cAAc,KAAK,cAAc,CAAC;QACpC,WAAW;QACX,OAAO;IACT;IACA,IAAI,CAAC,YAAY,MAAM,EAAE;QACvB;IACF;IACA,iEAAiE;IACjE,uDAAuD;IACvD,IAAI,gBAAgB,WAAW,MAAM;IACrC,IAAI,sBAAsB,MAAM,UAAU,eAAe;IACzD,IAAI,qBAAqB,CAAA,GAAA,iJAAA,CAAA,gBAAoB,AAAD;IAC5C,IAAI,aAAa;IACjB,IAAI,qBAAqB;QACvB,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;YAC3C,IAAI,SAAS,oBAAoB,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,SAAS;YAC7D,IAAI,UAAU,MAAM;gBAClB,aAAa,CAAC,SAAS,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI;gBAClD;YACF;QACF;IACF;IACA,IAAI,OAAO,KAAK,aAAa,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK;IAClD,IAAI,YAAY,eAAe,YAAY;IAC3C,aAAa,CAAA,GAAA,iJAAA,CAAA,UAAc,AAAD,EAAE,cAAc,aAAa;QAAC;KAAW;IACnE,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;QAC3C,IAAI,YAAY,KAAK,aAAa,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK;QACvD,IAAI,IAAI,KAAK;QACb,IAAI,IAAI,KAAK;QACb,IAAI,QAAQ,KAAK;QACjB,IAAI,SAAS,KAAK;QAClB,IAAI,KAAK,YAAY,IAAI;YACvB,IAAI;YACJ,IAAI,SAAS,CAAC;YACd,QAAQ,YAAY;YACpB,SAAS,SAAS,MAAM;YACxB,OAAO,IAAI;QACb,OAAO;YACL,IAAI,SAAS,CAAC;YACd,IAAI;YACJ,QAAQ,SAAS,KAAK;YACtB,SAAS,YAAY;YACrB,OAAO,IAAI;QACb;QACA,IAAI,YAAY,WAAW,CAAC,IAAI,EAAE,CAAC,SAAS;QAC5C,aAAa,QAAQ,mBAAmB,GAAG,CAAC,WAAW;QACvD,UAAU,GAAG,CAAC,IAAI,gMAAA,CAAA,OAAY,CAAC;YAC7B,MAAM,aAAa,OAAO,UAAU,YAAY;YAChD,OAAO;gBACL,GAAG;gBACH,GAAG;gBACH,OAAO;gBACP,QAAQ;YACV;YACA,OAAO,CAAA,GAAA,iJAAA,CAAA,WAAe,AAAD,EAAE;gBACrB,MAAM,UAAU,CAAC,WAAW;YAC9B,GAAG;YACH,WAAW;YACX,QAAQ;QACV;QACA,aAAa,CAAC,aAAa,CAAC,IAAI;IAClC;IACA,MAAM,UAAU,eAAe,GAAG;AACpC;AACO,SAAS,0BAA0B,QAAQ;IAChD,MAAM,UAAU,eAAe,GAAG;AACpC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 915, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axis/CartesianAxisView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport AxisBuilder from './AxisBuilder.js';\nimport AxisView from './AxisView.js';\nimport * as cartesianAxisHelper from '../../coord/cartesian/cartesianAxisHelper.js';\nimport { rectCoordAxisBuildSplitArea, rectCoordAxisHandleRemove } from './axisSplitHelper.js';\nimport { isIntervalOrLogScale } from '../../scale/helper.js';\nvar axisBuilderAttrs = ['axisLine', 'axisTickLabel', 'axisName'];\nvar selfBuilderAttrs = ['splitArea', 'splitLine', 'minorSplitLine'];\nvar CartesianAxisView = /** @class */function (_super) {\n  __extends(CartesianAxisView, _super);\n  function CartesianAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CartesianAxisView.type;\n    _this.axisPointerClass = 'CartesianAxisPointer';\n    return _this;\n  }\n  /**\r\n   * @override\r\n   */\n  CartesianAxisView.prototype.render = function (axisModel, ecModel, api, payload) {\n    this.group.removeAll();\n    var oldAxisGroup = this._axisGroup;\n    this._axisGroup = new graphic.Group();\n    this.group.add(this._axisGroup);\n    if (!axisModel.get('show')) {\n      return;\n    }\n    var gridModel = axisModel.getCoordSysModel();\n    var layout = cartesianAxisHelper.layout(gridModel, axisModel);\n    var axisBuilder = new AxisBuilder(axisModel, zrUtil.extend({\n      handleAutoShown: function (elementType) {\n        var cartesians = gridModel.coordinateSystem.getCartesians();\n        for (var i = 0; i < cartesians.length; i++) {\n          if (isIntervalOrLogScale(cartesians[i].getOtherAxis(axisModel.axis).scale)) {\n            // Still show axis tick or axisLine if other axis is value / log\n            return true;\n          }\n        }\n        // Not show axisTick or axisLine if other axis is category / time\n        return false;\n      }\n    }, layout));\n    zrUtil.each(axisBuilderAttrs, axisBuilder.add, axisBuilder);\n    this._axisGroup.add(axisBuilder.getGroup());\n    zrUtil.each(selfBuilderAttrs, function (name) {\n      if (axisModel.get([name, 'show'])) {\n        axisElementBuilders[name](this, this._axisGroup, axisModel, gridModel);\n      }\n    }, this);\n    // THIS is a special case for bar racing chart.\n    // Update the axis label from the natural initial layout to\n    // sorted layout should has no animation.\n    var isInitialSortFromBarRacing = payload && payload.type === 'changeAxisOrder' && payload.isInitSort;\n    if (!isInitialSortFromBarRacing) {\n      graphic.groupTransition(oldAxisGroup, this._axisGroup, axisModel);\n    }\n    _super.prototype.render.call(this, axisModel, ecModel, api, payload);\n  };\n  CartesianAxisView.prototype.remove = function () {\n    rectCoordAxisHandleRemove(this);\n  };\n  CartesianAxisView.type = 'cartesianAxis';\n  return CartesianAxisView;\n}(AxisView);\nvar axisElementBuilders = {\n  splitLine: function (axisView, axisGroup, axisModel, gridModel) {\n    var axis = axisModel.axis;\n    if (axis.scale.isBlank()) {\n      return;\n    }\n    var splitLineModel = axisModel.getModel('splitLine');\n    var lineStyleModel = splitLineModel.getModel('lineStyle');\n    var lineColors = lineStyleModel.get('color');\n    var showMinLine = splitLineModel.get('showMinLine') !== false;\n    var showMaxLine = splitLineModel.get('showMaxLine') !== false;\n    lineColors = zrUtil.isArray(lineColors) ? lineColors : [lineColors];\n    var gridRect = gridModel.coordinateSystem.getRect();\n    var isHorizontal = axis.isHorizontal();\n    var lineCount = 0;\n    var ticksCoords = axis.getTicksCoords({\n      tickModel: splitLineModel\n    });\n    var p1 = [];\n    var p2 = [];\n    var lineStyle = lineStyleModel.getLineStyle();\n    for (var i = 0; i < ticksCoords.length; i++) {\n      var tickCoord = axis.toGlobalCoord(ticksCoords[i].coord);\n      if (i === 0 && !showMinLine || i === ticksCoords.length - 1 && !showMaxLine) {\n        continue;\n      }\n      var tickValue = ticksCoords[i].tickValue;\n      if (isHorizontal) {\n        p1[0] = tickCoord;\n        p1[1] = gridRect.y;\n        p2[0] = tickCoord;\n        p2[1] = gridRect.y + gridRect.height;\n      } else {\n        p1[0] = gridRect.x;\n        p1[1] = tickCoord;\n        p2[0] = gridRect.x + gridRect.width;\n        p2[1] = tickCoord;\n      }\n      var colorIndex = lineCount++ % lineColors.length;\n      var line = new graphic.Line({\n        anid: tickValue != null ? 'line_' + tickValue : null,\n        autoBatch: true,\n        shape: {\n          x1: p1[0],\n          y1: p1[1],\n          x2: p2[0],\n          y2: p2[1]\n        },\n        style: zrUtil.defaults({\n          stroke: lineColors[colorIndex]\n        }, lineStyle),\n        silent: true\n      });\n      graphic.subPixelOptimizeLine(line.shape, lineStyle.lineWidth);\n      axisGroup.add(line);\n    }\n  },\n  minorSplitLine: function (axisView, axisGroup, axisModel, gridModel) {\n    var axis = axisModel.axis;\n    var minorSplitLineModel = axisModel.getModel('minorSplitLine');\n    var lineStyleModel = minorSplitLineModel.getModel('lineStyle');\n    var gridRect = gridModel.coordinateSystem.getRect();\n    var isHorizontal = axis.isHorizontal();\n    var minorTicksCoords = axis.getMinorTicksCoords();\n    if (!minorTicksCoords.length) {\n      return;\n    }\n    var p1 = [];\n    var p2 = [];\n    var lineStyle = lineStyleModel.getLineStyle();\n    for (var i = 0; i < minorTicksCoords.length; i++) {\n      for (var k = 0; k < minorTicksCoords[i].length; k++) {\n        var tickCoord = axis.toGlobalCoord(minorTicksCoords[i][k].coord);\n        if (isHorizontal) {\n          p1[0] = tickCoord;\n          p1[1] = gridRect.y;\n          p2[0] = tickCoord;\n          p2[1] = gridRect.y + gridRect.height;\n        } else {\n          p1[0] = gridRect.x;\n          p1[1] = tickCoord;\n          p2[0] = gridRect.x + gridRect.width;\n          p2[1] = tickCoord;\n        }\n        var line = new graphic.Line({\n          anid: 'minor_line_' + minorTicksCoords[i][k].tickValue,\n          autoBatch: true,\n          shape: {\n            x1: p1[0],\n            y1: p1[1],\n            x2: p2[0],\n            y2: p2[1]\n          },\n          style: lineStyle,\n          silent: true\n        });\n        graphic.subPixelOptimizeLine(line.shape, lineStyle.lineWidth);\n        axisGroup.add(line);\n      }\n    }\n  },\n  splitArea: function (axisView, axisGroup, axisModel, gridModel) {\n    rectCoordAxisBuildSplitArea(axisView, axisGroup, axisModel, gridModel);\n  }\n};\nvar CartesianXAxisView = /** @class */function (_super) {\n  __extends(CartesianXAxisView, _super);\n  function CartesianXAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CartesianXAxisView.type;\n    return _this;\n  }\n  CartesianXAxisView.type = 'xAxis';\n  return CartesianXAxisView;\n}(CartesianAxisView);\nexport { CartesianXAxisView };\nvar CartesianYAxisView = /** @class */function (_super) {\n  __extends(CartesianYAxisView, _super);\n  function CartesianYAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = CartesianXAxisView.type;\n    return _this;\n  }\n  CartesianYAxisView.type = 'yAxis';\n  return CartesianYAxisView;\n}(CartesianAxisView);\nexport { CartesianYAxisView };\nexport default CartesianAxisView;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;;;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACA,IAAI,mBAAmB;IAAC;IAAY;IAAiB;CAAW;AAChE,IAAI,mBAAmB;IAAC;IAAa;IAAa;CAAiB;AACnE,IAAI,oBAAoB,WAAW,GAAE,SAAU,MAAM;IACnD,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,mBAAmB;IAC7B,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,kBAAkB,IAAI;QACnC,MAAM,gBAAgB,GAAG;QACzB,OAAO;IACT;IACA;;GAEC,GACD,kBAAkB,SAAS,CAAC,MAAM,GAAG,SAAU,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO;QAC7E,IAAI,CAAC,KAAK,CAAC,SAAS;QACpB,IAAI,eAAe,IAAI,CAAC,UAAU;QAClC,IAAI,CAAC,UAAU,GAAG,IAAI,yLAAA,CAAA,QAAa;QACnC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU;QAC9B,IAAI,CAAC,UAAU,GAAG,CAAC,SAAS;YAC1B;QACF;QACA,IAAI,YAAY,UAAU,gBAAgB;QAC1C,IAAI,SAAS,CAAA,GAAA,8KAAA,CAAA,SAA0B,AAAD,EAAE,WAAW;QACnD,IAAI,cAAc,IAAI,qKAAA,CAAA,UAAW,CAAC,WAAW,CAAA,GAAA,iJAAA,CAAA,SAAa,AAAD,EAAE;YACzD,iBAAiB,SAAU,WAAW;gBACpC,IAAI,aAAa,UAAU,gBAAgB,CAAC,aAAa;gBACzD,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;oBAC1C,IAAI,CAAA,GAAA,oJAAA,CAAA,uBAAoB,AAAD,EAAE,UAAU,CAAC,EAAE,CAAC,YAAY,CAAC,UAAU,IAAI,EAAE,KAAK,GAAG;wBAC1E,gEAAgE;wBAChE,OAAO;oBACT;gBACF;gBACA,iEAAiE;gBACjE,OAAO;YACT;QACF,GAAG;QACH,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,kBAAkB,YAAY,GAAG,EAAE;QAC/C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,QAAQ;QACxC,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,kBAAkB,SAAU,IAAI;YAC1C,IAAI,UAAU,GAAG,CAAC;gBAAC;gBAAM;aAAO,GAAG;gBACjC,mBAAmB,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,UAAU,EAAE,WAAW;YAC9D;QACF,GAAG,IAAI;QACP,+CAA+C;QAC/C,2DAA2D;QAC3D,yCAAyC;QACzC,IAAI,6BAA6B,WAAW,QAAQ,IAAI,KAAK,qBAAqB,QAAQ,UAAU;QACpG,IAAI,CAAC,4BAA4B;YAC/B,CAAA,GAAA,oKAAA,CAAA,kBAAuB,AAAD,EAAE,cAAc,IAAI,CAAC,UAAU,EAAE;QACzD;QACA,OAAO,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,SAAS,KAAK;IAC9D;IACA,kBAAkB,SAAS,CAAC,MAAM,GAAG;QACnC,CAAA,GAAA,yKAAA,CAAA,4BAAyB,AAAD,EAAE,IAAI;IAChC;IACA,kBAAkB,IAAI,GAAG;IACzB,OAAO;AACT,EAAE,kKAAA,CAAA,UAAQ;AACV,IAAI,sBAAsB;IACxB,WAAW,SAAU,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;QAC5D,IAAI,OAAO,UAAU,IAAI;QACzB,IAAI,KAAK,KAAK,CAAC,OAAO,IAAI;YACxB;QACF;QACA,IAAI,iBAAiB,UAAU,QAAQ,CAAC;QACxC,IAAI,iBAAiB,eAAe,QAAQ,CAAC;QAC7C,IAAI,aAAa,eAAe,GAAG,CAAC;QACpC,IAAI,cAAc,eAAe,GAAG,CAAC,mBAAmB;QACxD,IAAI,cAAc,eAAe,GAAG,CAAC,mBAAmB;QACxD,aAAa,CAAA,GAAA,iJAAA,CAAA,UAAc,AAAD,EAAE,cAAc,aAAa;YAAC;SAAW;QACnE,IAAI,WAAW,UAAU,gBAAgB,CAAC,OAAO;QACjD,IAAI,eAAe,KAAK,YAAY;QACpC,IAAI,YAAY;QAChB,IAAI,cAAc,KAAK,cAAc,CAAC;YACpC,WAAW;QACb;QACA,IAAI,KAAK,EAAE;QACX,IAAI,KAAK,EAAE;QACX,IAAI,YAAY,eAAe,YAAY;QAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;YAC3C,IAAI,YAAY,KAAK,aAAa,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK;YACvD,IAAI,MAAM,KAAK,CAAC,eAAe,MAAM,YAAY,MAAM,GAAG,KAAK,CAAC,aAAa;gBAC3E;YACF;YACA,IAAI,YAAY,WAAW,CAAC,EAAE,CAAC,SAAS;YACxC,IAAI,cAAc;gBAChB,EAAE,CAAC,EAAE,GAAG;gBACR,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC;gBAClB,EAAE,CAAC,EAAE,GAAG;gBACR,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,GAAG,SAAS,MAAM;YACtC,OAAO;gBACL,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC;gBAClB,EAAE,CAAC,EAAE,GAAG;gBACR,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,GAAG,SAAS,KAAK;gBACnC,EAAE,CAAC,EAAE,GAAG;YACV;YACA,IAAI,aAAa,cAAc,WAAW,MAAM;YAChD,IAAI,OAAO,IAAI,gMAAA,CAAA,OAAY,CAAC;gBAC1B,MAAM,aAAa,OAAO,UAAU,YAAY;gBAChD,WAAW;gBACX,OAAO;oBACL,IAAI,EAAE,CAAC,EAAE;oBACT,IAAI,EAAE,CAAC,EAAE;oBACT,IAAI,EAAE,CAAC,EAAE;oBACT,IAAI,EAAE,CAAC,EAAE;gBACX;gBACA,OAAO,CAAA,GAAA,iJAAA,CAAA,WAAe,AAAD,EAAE;oBACrB,QAAQ,UAAU,CAAC,WAAW;gBAChC,GAAG;gBACH,QAAQ;YACV;YACA,CAAA,GAAA,oKAAA,CAAA,uBAA4B,AAAD,EAAE,KAAK,KAAK,EAAE,UAAU,SAAS;YAC5D,UAAU,GAAG,CAAC;QAChB;IACF;IACA,gBAAgB,SAAU,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;QACjE,IAAI,OAAO,UAAU,IAAI;QACzB,IAAI,sBAAsB,UAAU,QAAQ,CAAC;QAC7C,IAAI,iBAAiB,oBAAoB,QAAQ,CAAC;QAClD,IAAI,WAAW,UAAU,gBAAgB,CAAC,OAAO;QACjD,IAAI,eAAe,KAAK,YAAY;QACpC,IAAI,mBAAmB,KAAK,mBAAmB;QAC/C,IAAI,CAAC,iBAAiB,MAAM,EAAE;YAC5B;QACF;QACA,IAAI,KAAK,EAAE;QACX,IAAI,KAAK,EAAE;QACX,IAAI,YAAY,eAAe,YAAY;QAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;YAChD,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,CAAC,EAAE,CAAC,MAAM,EAAE,IAAK;gBACnD,IAAI,YAAY,KAAK,aAAa,CAAC,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK;gBAC/D,IAAI,cAAc;oBAChB,EAAE,CAAC,EAAE,GAAG;oBACR,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC;oBAClB,EAAE,CAAC,EAAE,GAAG;oBACR,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,GAAG,SAAS,MAAM;gBACtC,OAAO;oBACL,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC;oBAClB,EAAE,CAAC,EAAE,GAAG;oBACR,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,GAAG,SAAS,KAAK;oBACnC,EAAE,CAAC,EAAE,GAAG;gBACV;gBACA,IAAI,OAAO,IAAI,gMAAA,CAAA,OAAY,CAAC;oBAC1B,MAAM,gBAAgB,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC,SAAS;oBACtD,WAAW;oBACX,OAAO;wBACL,IAAI,EAAE,CAAC,EAAE;wBACT,IAAI,EAAE,CAAC,EAAE;wBACT,IAAI,EAAE,CAAC,EAAE;wBACT,IAAI,EAAE,CAAC,EAAE;oBACX;oBACA,OAAO;oBACP,QAAQ;gBACV;gBACA,CAAA,GAAA,oKAAA,CAAA,uBAA4B,AAAD,EAAE,KAAK,KAAK,EAAE,UAAU,SAAS;gBAC5D,UAAU,GAAG,CAAC;YAChB;QACF;IACF;IACA,WAAW,SAAU,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS;QAC5D,CAAA,GAAA,yKAAA,CAAA,8BAA2B,AAAD,EAAE,UAAU,WAAW,WAAW;IAC9D;AACF;AACA,IAAI,qBAAqB,WAAW,GAAE,SAAU,MAAM;IACpD,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,oBAAoB;IAC9B,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,mBAAmB,IAAI;QACpC,OAAO;IACT;IACA,mBAAmB,IAAI,GAAG;IAC1B,OAAO;AACT,EAAE;;AAEF,IAAI,qBAAqB,WAAW,GAAE,SAAU,MAAM;IACpD,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,oBAAoB;IAC9B,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,mBAAmB,IAAI;QACpC,OAAO;IACT;IACA,mBAAmB,IAAI,GAAG;IAC1B,OAAO;AACT,EAAE;;uCAEa", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1178, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axis/ParallelAxisView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport AxisBuilder from './AxisBuilder.js';\nimport BrushController from '../helper/BrushController.js';\nimport * as brushHelper from '../helper/brushHelper.js';\nimport * as graphic from '../../util/graphic.js';\nimport ComponentView from '../../view/Component.js';\nvar elementList = ['axisLine', 'axisTickLabel', 'axisName'];\nvar ParallelAxisView = /** @class */function (_super) {\n  __extends(ParallelAxisView, _super);\n  function ParallelAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = ParallelAxisView.type;\n    return _this;\n  }\n  ParallelAxisView.prototype.init = function (ecModel, api) {\n    _super.prototype.init.apply(this, arguments);\n    (this._brushController = new BrushController(api.getZr())).on('brush', zrUtil.bind(this._onBrush, this));\n  };\n  ParallelAxisView.prototype.render = function (axisModel, ecModel, api, payload) {\n    if (fromAxisAreaSelect(axisModel, ecModel, payload)) {\n      return;\n    }\n    this.axisModel = axisModel;\n    this.api = api;\n    this.group.removeAll();\n    var oldAxisGroup = this._axisGroup;\n    this._axisGroup = new graphic.Group();\n    this.group.add(this._axisGroup);\n    if (!axisModel.get('show')) {\n      return;\n    }\n    var coordSysModel = getCoordSysModel(axisModel, ecModel);\n    var coordSys = coordSysModel.coordinateSystem;\n    var areaSelectStyle = axisModel.getAreaSelectStyle();\n    var areaWidth = areaSelectStyle.width;\n    var dim = axisModel.axis.dim;\n    var axisLayout = coordSys.getAxisLayout(dim);\n    var builderOpt = zrUtil.extend({\n      strokeContainThreshold: areaWidth\n    }, axisLayout);\n    var axisBuilder = new AxisBuilder(axisModel, builderOpt);\n    zrUtil.each(elementList, axisBuilder.add, axisBuilder);\n    this._axisGroup.add(axisBuilder.getGroup());\n    this._refreshBrushController(builderOpt, areaSelectStyle, axisModel, coordSysModel, areaWidth, api);\n    graphic.groupTransition(oldAxisGroup, this._axisGroup, axisModel);\n  };\n  // /**\n  //  * @override\n  //  */\n  // updateVisual(axisModel, ecModel, api, payload) {\n  //     this._brushController && this._brushController\n  //         .updateCovers(getCoverInfoList(axisModel));\n  // }\n  ParallelAxisView.prototype._refreshBrushController = function (builderOpt, areaSelectStyle, axisModel, coordSysModel, areaWidth, api) {\n    // After filtering, axis may change, select area needs to be update.\n    var extent = axisModel.axis.getExtent();\n    var extentLen = extent[1] - extent[0];\n    var extra = Math.min(30, Math.abs(extentLen) * 0.1); // Arbitrary value.\n    // width/height might be negative, which will be\n    // normalized in BoundingRect.\n    var rect = graphic.BoundingRect.create({\n      x: extent[0],\n      y: -areaWidth / 2,\n      width: extentLen,\n      height: areaWidth\n    });\n    rect.x -= extra;\n    rect.width += 2 * extra;\n    this._brushController.mount({\n      enableGlobalPan: true,\n      rotation: builderOpt.rotation,\n      x: builderOpt.position[0],\n      y: builderOpt.position[1]\n    }).setPanels([{\n      panelId: 'pl',\n      clipPath: brushHelper.makeRectPanelClipPath(rect),\n      isTargetByCursor: brushHelper.makeRectIsTargetByCursor(rect, api, coordSysModel),\n      getLinearBrushOtherExtent: brushHelper.makeLinearBrushOtherExtent(rect, 0)\n    }]).enableBrush({\n      brushType: 'lineX',\n      brushStyle: areaSelectStyle,\n      removeOnClick: true\n    }).updateCovers(getCoverInfoList(axisModel));\n  };\n  ParallelAxisView.prototype._onBrush = function (eventParam) {\n    var coverInfoList = eventParam.areas;\n    // Do not cache these object, because the mey be changed.\n    var axisModel = this.axisModel;\n    var axis = axisModel.axis;\n    var intervals = zrUtil.map(coverInfoList, function (coverInfo) {\n      return [axis.coordToData(coverInfo.range[0], true), axis.coordToData(coverInfo.range[1], true)];\n    });\n    // If realtime is true, action is not dispatched on drag end, because\n    // the drag end emits the same params with the last drag move event,\n    // and may have some delay when using touch pad.\n    if (!axisModel.option.realtime === eventParam.isEnd || eventParam.removeOnClick) {\n      // jshint ignore:line\n      this.api.dispatchAction({\n        type: 'axisAreaSelect',\n        parallelAxisId: axisModel.id,\n        intervals: intervals\n      });\n    }\n  };\n  ParallelAxisView.prototype.dispose = function () {\n    this._brushController.dispose();\n  };\n  ParallelAxisView.type = 'parallelAxis';\n  return ParallelAxisView;\n}(ComponentView);\nfunction fromAxisAreaSelect(axisModel, ecModel, payload) {\n  return payload && payload.type === 'axisAreaSelect' && ecModel.findComponents({\n    mainType: 'parallelAxis',\n    query: payload\n  })[0] === axisModel;\n}\nfunction getCoverInfoList(axisModel) {\n  var axis = axisModel.axis;\n  return zrUtil.map(axisModel.activeIntervals, function (interval) {\n    return {\n      brushType: 'lineX',\n      panelId: 'pl',\n      range: [axis.dataToCoord(interval[0], true), axis.dataToCoord(interval[1], true)]\n    };\n  });\n}\nfunction getCoordSysModel(axisModel, ecModel) {\n  return ecModel.getComponent('parallel', axisModel.get('parallelIndex'));\n}\nexport default ParallelAxisView;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;;;;;;;;AACA,IAAI,cAAc;IAAC;IAAY;IAAiB;CAAW;AAC3D,IAAI,mBAAmB,WAAW,GAAE,SAAU,MAAM;IAClD,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,kBAAkB;IAC5B,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,iBAAiB,IAAI;QAClC,OAAO;IACT;IACA,iBAAiB,SAAS,CAAC,IAAI,GAAG,SAAU,OAAO,EAAE,GAAG;QACtD,OAAO,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;QAClC,CAAC,IAAI,CAAC,gBAAgB,GAAG,IAAI,2KAAA,CAAA,UAAe,CAAC,IAAI,KAAK,GAAG,EAAE,EAAE,CAAC,SAAS,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI;IACxG;IACA,iBAAiB,SAAS,CAAC,MAAM,GAAG,SAAU,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO;QAC5E,IAAI,mBAAmB,WAAW,SAAS,UAAU;YACnD;QACF;QACA,IAAI,CAAC,SAAS,GAAG;QACjB,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,KAAK,CAAC,SAAS;QACpB,IAAI,eAAe,IAAI,CAAC,UAAU;QAClC,IAAI,CAAC,UAAU,GAAG,IAAI,yLAAA,CAAA,QAAa;QACnC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU;QAC9B,IAAI,CAAC,UAAU,GAAG,CAAC,SAAS;YAC1B;QACF;QACA,IAAI,gBAAgB,iBAAiB,WAAW;QAChD,IAAI,WAAW,cAAc,gBAAgB;QAC7C,IAAI,kBAAkB,UAAU,kBAAkB;QAClD,IAAI,YAAY,gBAAgB,KAAK;QACrC,IAAI,MAAM,UAAU,IAAI,CAAC,GAAG;QAC5B,IAAI,aAAa,SAAS,aAAa,CAAC;QACxC,IAAI,aAAa,CAAA,GAAA,iJAAA,CAAA,SAAa,AAAD,EAAE;YAC7B,wBAAwB;QAC1B,GAAG;QACH,IAAI,cAAc,IAAI,qKAAA,CAAA,UAAW,CAAC,WAAW;QAC7C,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,aAAa,YAAY,GAAG,EAAE;QAC1C,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,YAAY,QAAQ;QACxC,IAAI,CAAC,uBAAuB,CAAC,YAAY,iBAAiB,WAAW,eAAe,WAAW;QAC/F,CAAA,GAAA,oKAAA,CAAA,kBAAuB,AAAD,EAAE,cAAc,IAAI,CAAC,UAAU,EAAE;IACzD;IACA,MAAM;IACN,eAAe;IACf,MAAM;IACN,mDAAmD;IACnD,qDAAqD;IACrD,sDAAsD;IACtD,IAAI;IACJ,iBAAiB,SAAS,CAAC,uBAAuB,GAAG,SAAU,UAAU,EAAE,eAAe,EAAE,SAAS,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG;QAClI,oEAAoE;QACpE,IAAI,SAAS,UAAU,IAAI,CAAC,SAAS;QACrC,IAAI,YAAY,MAAM,CAAC,EAAE,GAAG,MAAM,CAAC,EAAE;QACrC,IAAI,QAAQ,KAAK,GAAG,CAAC,IAAI,KAAK,GAAG,CAAC,aAAa,MAAM,mBAAmB;QACxE,gDAAgD;QAChD,8BAA8B;QAC9B,IAAI,OAAO,oMAAA,CAAA,eAAoB,CAAC,MAAM,CAAC;YACrC,GAAG,MAAM,CAAC,EAAE;YACZ,GAAG,CAAC,YAAY;YAChB,OAAO;YACP,QAAQ;QACV;QACA,KAAK,CAAC,IAAI;QACV,KAAK,KAAK,IAAI,IAAI;QAClB,IAAI,CAAC,gBAAgB,CAAC,KAAK,CAAC;YAC1B,iBAAiB;YACjB,UAAU,WAAW,QAAQ;YAC7B,GAAG,WAAW,QAAQ,CAAC,EAAE;YACzB,GAAG,WAAW,QAAQ,CAAC,EAAE;QAC3B,GAAG,SAAS,CAAC;YAAC;gBACZ,SAAS;gBACT,UAAU,CAAA,GAAA,uKAAA,CAAA,wBAAiC,AAAD,EAAE;gBAC5C,kBAAkB,CAAA,GAAA,uKAAA,CAAA,2BAAoC,AAAD,EAAE,MAAM,KAAK;gBAClE,2BAA2B,CAAA,GAAA,uKAAA,CAAA,6BAAsC,AAAD,EAAE,MAAM;YAC1E;SAAE,EAAE,WAAW,CAAC;YACd,WAAW;YACX,YAAY;YACZ,eAAe;QACjB,GAAG,YAAY,CAAC,iBAAiB;IACnC;IACA,iBAAiB,SAAS,CAAC,QAAQ,GAAG,SAAU,UAAU;QACxD,IAAI,gBAAgB,WAAW,KAAK;QACpC,yDAAyD;QACzD,IAAI,YAAY,IAAI,CAAC,SAAS;QAC9B,IAAI,OAAO,UAAU,IAAI;QACzB,IAAI,YAAY,CAAA,GAAA,iJAAA,CAAA,MAAU,AAAD,EAAE,eAAe,SAAU,SAAS;YAC3D,OAAO;gBAAC,KAAK,WAAW,CAAC,UAAU,KAAK,CAAC,EAAE,EAAE;gBAAO,KAAK,WAAW,CAAC,UAAU,KAAK,CAAC,EAAE,EAAE;aAAM;QACjG;QACA,qEAAqE;QACrE,oEAAoE;QACpE,gDAAgD;QAChD,IAAI,CAAC,UAAU,MAAM,CAAC,QAAQ,KAAK,WAAW,KAAK,IAAI,WAAW,aAAa,EAAE;YAC/E,qBAAqB;YACrB,IAAI,CAAC,GAAG,CAAC,cAAc,CAAC;gBACtB,MAAM;gBACN,gBAAgB,UAAU,EAAE;gBAC5B,WAAW;YACb;QACF;IACF;IACA,iBAAiB,SAAS,CAAC,OAAO,GAAG;QACnC,IAAI,CAAC,gBAAgB,CAAC,OAAO;IAC/B;IACA,iBAAiB,IAAI,GAAG;IACxB,OAAO;AACT,EAAE,sJAAA,CAAA,UAAa;AACf,SAAS,mBAAmB,SAAS,EAAE,OAAO,EAAE,OAAO;IACrD,OAAO,WAAW,QAAQ,IAAI,KAAK,oBAAoB,QAAQ,cAAc,CAAC;QAC5E,UAAU;QACV,OAAO;IACT,EAAE,CAAC,EAAE,KAAK;AACZ;AACA,SAAS,iBAAiB,SAAS;IACjC,IAAI,OAAO,UAAU,IAAI;IACzB,OAAO,CAAA,GAAA,iJAAA,CAAA,MAAU,AAAD,EAAE,UAAU,eAAe,EAAE,SAAU,QAAQ;QAC7D,OAAO;YACL,WAAW;YACX,SAAS;YACT,OAAO;gBAAC,KAAK,WAAW,CAAC,QAAQ,CAAC,EAAE,EAAE;gBAAO,KAAK,WAAW,CAAC,QAAQ,CAAC,EAAE,EAAE;aAAM;QACnF;IACF;AACF;AACA,SAAS,iBAAiB,SAAS,EAAE,OAAO;IAC1C,OAAO,QAAQ,YAAY,CAAC,YAAY,UAAU,GAAG,CAAC;AACxD;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1375, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axis/parallelAxisAction.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nvar actionInfo = {\n  type: 'axisAreaSelect',\n  event: 'axisAreaSelected'\n  // update: 'updateVisual'\n};\nexport function installParallelActions(registers) {\n  registers.registerAction(actionInfo, function (payload, ecModel) {\n    ecModel.eachComponent({\n      mainType: 'parallelAxis',\n      query: payload\n    }, function (parallelAxisModel) {\n      parallelAxisModel.axis.model.setActiveIntervals(payload.intervals);\n    });\n  });\n  /**\r\n   * @payload\r\n   */\n  registers.registerAction('parallelAxisExpand', function (payload, ecModel) {\n    ecModel.eachComponent({\n      mainType: 'parallel',\n      query: payload\n    }, function (parallelModel) {\n      parallelModel.setAxisExpand(payload);\n    });\n  });\n}"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA,IAAI,aAAa;IACf,MAAM;IACN,OAAO;AAET;AACO,SAAS,uBAAuB,SAAS;IAC9C,UAAU,cAAc,CAAC,YAAY,SAAU,OAAO,EAAE,OAAO;QAC7D,QAAQ,aAAa,CAAC;YACpB,UAAU;YACV,OAAO;QACT,GAAG,SAAU,iBAAiB;YAC5B,kBAAkB,IAAI,CAAC,KAAK,CAAC,kBAAkB,CAAC,QAAQ,SAAS;QACnE;IACF;IACA;;GAEC,GACD,UAAU,cAAc,CAAC,sBAAsB,SAAU,OAAO,EAAE,OAAO;QACvE,QAAQ,aAAa,CAAC;YACpB,UAAU;YACV,OAAO;QACT,GAAG,SAAU,aAAa;YACxB,cAAc,aAAa,CAAC;QAC9B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1444, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axis/AngleAxisView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport { createTextStyle } from '../../label/labelStyle.js';\nimport Model from '../../model/Model.js';\nimport AxisView from './AxisView.js';\nimport AxisBuilder from './AxisBuilder.js';\nimport { getECData } from '../../util/innerStore.js';\nvar elementList = ['axisLine', 'axisLabel', 'axisTick', 'minorTick', 'splitLine', 'minorSplitLine', 'splitArea'];\nfunction getAxisLineShape(polar, rExtent, angle) {\n  rExtent[1] > rExtent[0] && (rExtent = rExtent.slice().reverse());\n  var start = polar.coordToPoint([rExtent[0], angle]);\n  var end = polar.coordToPoint([rExtent[1], angle]);\n  return {\n    x1: start[0],\n    y1: start[1],\n    x2: end[0],\n    y2: end[1]\n  };\n}\nfunction getRadiusIdx(polar) {\n  var radiusAxis = polar.getRadiusAxis();\n  return radiusAxis.inverse ? 0 : 1;\n}\n// Remove the last tick which will overlap the first tick\nfunction fixAngleOverlap(list) {\n  var firstItem = list[0];\n  var lastItem = list[list.length - 1];\n  if (firstItem && lastItem && Math.abs(Math.abs(firstItem.coord - lastItem.coord) - 360) < 1e-4) {\n    list.pop();\n  }\n}\nvar AngleAxisView = /** @class */function (_super) {\n  __extends(AngleAxisView, _super);\n  function AngleAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = AngleAxisView.type;\n    _this.axisPointerClass = 'PolarAxisPointer';\n    return _this;\n  }\n  AngleAxisView.prototype.render = function (angleAxisModel, ecModel) {\n    this.group.removeAll();\n    if (!angleAxisModel.get('show')) {\n      return;\n    }\n    var angleAxis = angleAxisModel.axis;\n    var polar = angleAxis.polar;\n    var radiusExtent = polar.getRadiusAxis().getExtent();\n    var ticksAngles = angleAxis.getTicksCoords();\n    var minorTickAngles = angleAxis.getMinorTicksCoords();\n    var labels = zrUtil.map(angleAxis.getViewLabels(), function (labelItem) {\n      labelItem = zrUtil.clone(labelItem);\n      var scale = angleAxis.scale;\n      var tickValue = scale.type === 'ordinal' ? scale.getRawOrdinalNumber(labelItem.tickValue) : labelItem.tickValue;\n      labelItem.coord = angleAxis.dataToCoord(tickValue);\n      return labelItem;\n    });\n    fixAngleOverlap(labels);\n    fixAngleOverlap(ticksAngles);\n    zrUtil.each(elementList, function (name) {\n      if (angleAxisModel.get([name, 'show']) && (!angleAxis.scale.isBlank() || name === 'axisLine')) {\n        angelAxisElementsBuilders[name](this.group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent, labels);\n      }\n    }, this);\n  };\n  AngleAxisView.type = 'angleAxis';\n  return AngleAxisView;\n}(AxisView);\nvar angelAxisElementsBuilders = {\n  axisLine: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    var lineStyleModel = angleAxisModel.getModel(['axisLine', 'lineStyle']);\n    var angleAxis = polar.getAngleAxis();\n    var RADIAN = Math.PI / 180;\n    var angleExtent = angleAxis.getExtent();\n    // extent id of the axis radius (r0 and r)\n    var rId = getRadiusIdx(polar);\n    var r0Id = rId ? 0 : 1;\n    var shape;\n    var shapeType = Math.abs(angleExtent[1] - angleExtent[0]) === 360 ? 'Circle' : 'Arc';\n    if (radiusExtent[r0Id] === 0) {\n      shape = new graphic[shapeType]({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          r: radiusExtent[rId],\n          startAngle: -angleExtent[0] * RADIAN,\n          endAngle: -angleExtent[1] * RADIAN,\n          clockwise: angleAxis.inverse\n        },\n        style: lineStyleModel.getLineStyle(),\n        z2: 1,\n        silent: true\n      });\n    } else {\n      shape = new graphic.Ring({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          r: radiusExtent[rId],\n          r0: radiusExtent[r0Id]\n        },\n        style: lineStyleModel.getLineStyle(),\n        z2: 1,\n        silent: true\n      });\n    }\n    shape.style.fill = null;\n    group.add(shape);\n  },\n  axisTick: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    var tickModel = angleAxisModel.getModel('axisTick');\n    var tickLen = (tickModel.get('inside') ? -1 : 1) * tickModel.get('length');\n    var radius = radiusExtent[getRadiusIdx(polar)];\n    var lines = zrUtil.map(ticksAngles, function (tickAngleItem) {\n      return new graphic.Line({\n        shape: getAxisLineShape(polar, [radius, radius + tickLen], tickAngleItem.coord)\n      });\n    });\n    group.add(graphic.mergePath(lines, {\n      style: zrUtil.defaults(tickModel.getModel('lineStyle').getLineStyle(), {\n        stroke: angleAxisModel.get(['axisLine', 'lineStyle', 'color'])\n      })\n    }));\n  },\n  minorTick: function (group, angleAxisModel, polar, tickAngles, minorTickAngles, radiusExtent) {\n    if (!minorTickAngles.length) {\n      return;\n    }\n    var tickModel = angleAxisModel.getModel('axisTick');\n    var minorTickModel = angleAxisModel.getModel('minorTick');\n    var tickLen = (tickModel.get('inside') ? -1 : 1) * minorTickModel.get('length');\n    var radius = radiusExtent[getRadiusIdx(polar)];\n    var lines = [];\n    for (var i = 0; i < minorTickAngles.length; i++) {\n      for (var k = 0; k < minorTickAngles[i].length; k++) {\n        lines.push(new graphic.Line({\n          shape: getAxisLineShape(polar, [radius, radius + tickLen], minorTickAngles[i][k].coord)\n        }));\n      }\n    }\n    group.add(graphic.mergePath(lines, {\n      style: zrUtil.defaults(minorTickModel.getModel('lineStyle').getLineStyle(), zrUtil.defaults(tickModel.getLineStyle(), {\n        stroke: angleAxisModel.get(['axisLine', 'lineStyle', 'color'])\n      }))\n    }));\n  },\n  axisLabel: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent, labels) {\n    var rawCategoryData = angleAxisModel.getCategories(true);\n    var commonLabelModel = angleAxisModel.getModel('axisLabel');\n    var labelMargin = commonLabelModel.get('margin');\n    var triggerEvent = angleAxisModel.get('triggerEvent');\n    // Use length of ticksAngles because it may remove the last tick to avoid overlapping\n    zrUtil.each(labels, function (labelItem, idx) {\n      var labelModel = commonLabelModel;\n      var tickValue = labelItem.tickValue;\n      var r = radiusExtent[getRadiusIdx(polar)];\n      var p = polar.coordToPoint([r + labelMargin, labelItem.coord]);\n      var cx = polar.cx;\n      var cy = polar.cy;\n      var labelTextAlign = Math.abs(p[0] - cx) / r < 0.3 ? 'center' : p[0] > cx ? 'left' : 'right';\n      var labelTextVerticalAlign = Math.abs(p[1] - cy) / r < 0.3 ? 'middle' : p[1] > cy ? 'top' : 'bottom';\n      if (rawCategoryData && rawCategoryData[tickValue]) {\n        var rawCategoryItem = rawCategoryData[tickValue];\n        if (zrUtil.isObject(rawCategoryItem) && rawCategoryItem.textStyle) {\n          labelModel = new Model(rawCategoryItem.textStyle, commonLabelModel, commonLabelModel.ecModel);\n        }\n      }\n      var textEl = new graphic.Text({\n        silent: AxisBuilder.isLabelSilent(angleAxisModel),\n        style: createTextStyle(labelModel, {\n          x: p[0],\n          y: p[1],\n          fill: labelModel.getTextColor() || angleAxisModel.get(['axisLine', 'lineStyle', 'color']),\n          text: labelItem.formattedLabel,\n          align: labelTextAlign,\n          verticalAlign: labelTextVerticalAlign\n        })\n      });\n      group.add(textEl);\n      // Pack data for mouse event\n      if (triggerEvent) {\n        var eventData = AxisBuilder.makeAxisEventDataBase(angleAxisModel);\n        eventData.targetType = 'axisLabel';\n        eventData.value = labelItem.rawLabel;\n        getECData(textEl).eventData = eventData;\n      }\n    }, this);\n  },\n  splitLine: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    var splitLineModel = angleAxisModel.getModel('splitLine');\n    var lineStyleModel = splitLineModel.getModel('lineStyle');\n    var lineColors = lineStyleModel.get('color');\n    var lineCount = 0;\n    lineColors = lineColors instanceof Array ? lineColors : [lineColors];\n    var splitLines = [];\n    for (var i = 0; i < ticksAngles.length; i++) {\n      var colorIndex = lineCount++ % lineColors.length;\n      splitLines[colorIndex] = splitLines[colorIndex] || [];\n      splitLines[colorIndex].push(new graphic.Line({\n        shape: getAxisLineShape(polar, radiusExtent, ticksAngles[i].coord)\n      }));\n    }\n    // Simple optimization\n    // Batching the lines if color are the same\n    for (var i = 0; i < splitLines.length; i++) {\n      group.add(graphic.mergePath(splitLines[i], {\n        style: zrUtil.defaults({\n          stroke: lineColors[i % lineColors.length]\n        }, lineStyleModel.getLineStyle()),\n        silent: true,\n        z: angleAxisModel.get('z')\n      }));\n    }\n  },\n  minorSplitLine: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    if (!minorTickAngles.length) {\n      return;\n    }\n    var minorSplitLineModel = angleAxisModel.getModel('minorSplitLine');\n    var lineStyleModel = minorSplitLineModel.getModel('lineStyle');\n    var lines = [];\n    for (var i = 0; i < minorTickAngles.length; i++) {\n      for (var k = 0; k < minorTickAngles[i].length; k++) {\n        lines.push(new graphic.Line({\n          shape: getAxisLineShape(polar, radiusExtent, minorTickAngles[i][k].coord)\n        }));\n      }\n    }\n    group.add(graphic.mergePath(lines, {\n      style: lineStyleModel.getLineStyle(),\n      silent: true,\n      z: angleAxisModel.get('z')\n    }));\n  },\n  splitArea: function (group, angleAxisModel, polar, ticksAngles, minorTickAngles, radiusExtent) {\n    if (!ticksAngles.length) {\n      return;\n    }\n    var splitAreaModel = angleAxisModel.getModel('splitArea');\n    var areaStyleModel = splitAreaModel.getModel('areaStyle');\n    var areaColors = areaStyleModel.get('color');\n    var lineCount = 0;\n    areaColors = areaColors instanceof Array ? areaColors : [areaColors];\n    var splitAreas = [];\n    var RADIAN = Math.PI / 180;\n    var prevAngle = -ticksAngles[0].coord * RADIAN;\n    var r0 = Math.min(radiusExtent[0], radiusExtent[1]);\n    var r1 = Math.max(radiusExtent[0], radiusExtent[1]);\n    var clockwise = angleAxisModel.get('clockwise');\n    for (var i = 1, len = ticksAngles.length; i <= len; i++) {\n      var coord = i === len ? ticksAngles[0].coord : ticksAngles[i].coord;\n      var colorIndex = lineCount++ % areaColors.length;\n      splitAreas[colorIndex] = splitAreas[colorIndex] || [];\n      splitAreas[colorIndex].push(new graphic.Sector({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          r0: r0,\n          r: r1,\n          startAngle: prevAngle,\n          endAngle: -coord * RADIAN,\n          clockwise: clockwise\n        },\n        silent: true\n      }));\n      prevAngle = -coord * RADIAN;\n    }\n    // Simple optimization\n    // Batching the lines if color are the same\n    for (var i = 0; i < splitAreas.length; i++) {\n      group.add(graphic.mergePath(splitAreas[i], {\n        style: zrUtil.defaults({\n          fill: areaColors[i % areaColors.length]\n        }, areaStyleModel.getAreaStyle()),\n        silent: true\n      }));\n    }\n  }\n};\nexport default AngleAxisView;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACA,IAAI,cAAc;IAAC;IAAY;IAAa;IAAY;IAAa;IAAa;IAAkB;CAAY;AAChH,SAAS,iBAAiB,KAAK,EAAE,OAAO,EAAE,KAAK;IAC7C,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE,IAAI,CAAC,UAAU,QAAQ,KAAK,GAAG,OAAO,EAAE;IAC/D,IAAI,QAAQ,MAAM,YAAY,CAAC;QAAC,OAAO,CAAC,EAAE;QAAE;KAAM;IAClD,IAAI,MAAM,MAAM,YAAY,CAAC;QAAC,OAAO,CAAC,EAAE;QAAE;KAAM;IAChD,OAAO;QACL,IAAI,KAAK,CAAC,EAAE;QACZ,IAAI,KAAK,CAAC,EAAE;QACZ,IAAI,GAAG,CAAC,EAAE;QACV,IAAI,GAAG,CAAC,EAAE;IACZ;AACF;AACA,SAAS,aAAa,KAAK;IACzB,IAAI,aAAa,MAAM,aAAa;IACpC,OAAO,WAAW,OAAO,GAAG,IAAI;AAClC;AACA,yDAAyD;AACzD,SAAS,gBAAgB,IAAI;IAC3B,IAAI,YAAY,IAAI,CAAC,EAAE;IACvB,IAAI,WAAW,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;IACpC,IAAI,aAAa,YAAY,KAAK,GAAG,CAAC,KAAK,GAAG,CAAC,UAAU,KAAK,GAAG,SAAS,KAAK,IAAI,OAAO,MAAM;QAC9F,KAAK,GAAG;IACV;AACF;AACA,IAAI,gBAAgB,WAAW,GAAE,SAAU,MAAM;IAC/C,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,eAAe;IACzB,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,cAAc,IAAI;QAC/B,MAAM,gBAAgB,GAAG;QACzB,OAAO;IACT;IACA,cAAc,SAAS,CAAC,MAAM,GAAG,SAAU,cAAc,EAAE,OAAO;QAChE,IAAI,CAAC,KAAK,CAAC,SAAS;QACpB,IAAI,CAAC,eAAe,GAAG,CAAC,SAAS;YAC/B;QACF;QACA,IAAI,YAAY,eAAe,IAAI;QACnC,IAAI,QAAQ,UAAU,KAAK;QAC3B,IAAI,eAAe,MAAM,aAAa,GAAG,SAAS;QAClD,IAAI,cAAc,UAAU,cAAc;QAC1C,IAAI,kBAAkB,UAAU,mBAAmB;QACnD,IAAI,SAAS,CAAA,GAAA,iJAAA,CAAA,MAAU,AAAD,EAAE,UAAU,aAAa,IAAI,SAAU,SAAS;YACpE,YAAY,CAAA,GAAA,iJAAA,CAAA,QAAY,AAAD,EAAE;YACzB,IAAI,QAAQ,UAAU,KAAK;YAC3B,IAAI,YAAY,MAAM,IAAI,KAAK,YAAY,MAAM,mBAAmB,CAAC,UAAU,SAAS,IAAI,UAAU,SAAS;YAC/G,UAAU,KAAK,GAAG,UAAU,WAAW,CAAC;YACxC,OAAO;QACT;QACA,gBAAgB;QAChB,gBAAgB;QAChB,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,aAAa,SAAU,IAAI;YACrC,IAAI,eAAe,GAAG,CAAC;gBAAC;gBAAM;aAAO,KAAK,CAAC,CAAC,UAAU,KAAK,CAAC,OAAO,MAAM,SAAS,UAAU,GAAG;gBAC7F,yBAAyB,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,gBAAgB,OAAO,aAAa,iBAAiB,cAAc;YACjH;QACF,GAAG,IAAI;IACT;IACA,cAAc,IAAI,GAAG;IACrB,OAAO;AACT,EAAE,kKAAA,CAAA,UAAQ;AACV,IAAI,4BAA4B;IAC9B,UAAU,SAAU,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,YAAY;QAC1F,IAAI,iBAAiB,eAAe,QAAQ,CAAC;YAAC;YAAY;SAAY;QACtE,IAAI,YAAY,MAAM,YAAY;QAClC,IAAI,SAAS,KAAK,EAAE,GAAG;QACvB,IAAI,cAAc,UAAU,SAAS;QACrC,0CAA0C;QAC1C,IAAI,MAAM,aAAa;QACvB,IAAI,OAAO,MAAM,IAAI;QACrB,IAAI;QACJ,IAAI,YAAY,KAAK,GAAG,CAAC,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,MAAM,MAAM,WAAW;QAC/E,IAAI,YAAY,CAAC,KAAK,KAAK,GAAG;YAC5B,QAAQ,IAAI,oJAAO,CAAC,UAAU,CAAC;gBAC7B,OAAO;oBACL,IAAI,MAAM,EAAE;oBACZ,IAAI,MAAM,EAAE;oBACZ,GAAG,YAAY,CAAC,IAAI;oBACpB,YAAY,CAAC,WAAW,CAAC,EAAE,GAAG;oBAC9B,UAAU,CAAC,WAAW,CAAC,EAAE,GAAG;oBAC5B,WAAW,UAAU,OAAO;gBAC9B;gBACA,OAAO,eAAe,YAAY;gBAClC,IAAI;gBACJ,QAAQ;YACV;QACF,OAAO;YACL,QAAQ,IAAI,qJAAQ,IAAI,CAAC;gBACvB,OAAO;oBACL,IAAI,MAAM,EAAE;oBACZ,IAAI,MAAM,EAAE;oBACZ,GAAG,YAAY,CAAC,IAAI;oBACpB,IAAI,YAAY,CAAC,KAAK;gBACxB;gBACA,OAAO,eAAe,YAAY;gBAClC,IAAI;gBACJ,QAAQ;YACV;QACF;QACA,MAAM,KAAK,CAAC,IAAI,GAAG;QACnB,MAAM,GAAG,CAAC;IACZ;IACA,UAAU,SAAU,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,YAAY;QAC1F,IAAI,YAAY,eAAe,QAAQ,CAAC;QACxC,IAAI,UAAU,CAAC,UAAU,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,UAAU,GAAG,CAAC;QACjE,IAAI,SAAS,YAAY,CAAC,aAAa,OAAO;QAC9C,IAAI,QAAQ,CAAA,GAAA,iJAAA,CAAA,MAAU,AAAD,EAAE,aAAa,SAAU,aAAa;YACzD,OAAO,IAAI,qJAAQ,IAAI,CAAC;gBACtB,OAAO,iBAAiB,OAAO;oBAAC;oBAAQ,SAAS;iBAAQ,EAAE,cAAc,KAAK;YAChF;QACF;QACA,MAAM,GAAG,CAAC,qJAAQ,SAAS,CAAC,OAAO;YACjC,OAAO,CAAA,GAAA,iJAAA,CAAA,WAAe,AAAD,EAAE,UAAU,QAAQ,CAAC,aAAa,YAAY,IAAI;gBACrE,QAAQ,eAAe,GAAG,CAAC;oBAAC;oBAAY;oBAAa;iBAAQ;YAC/D;QACF;IACF;IACA,WAAW,SAAU,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,UAAU,EAAE,eAAe,EAAE,YAAY;QAC1F,IAAI,CAAC,gBAAgB,MAAM,EAAE;YAC3B;QACF;QACA,IAAI,YAAY,eAAe,QAAQ,CAAC;QACxC,IAAI,iBAAiB,eAAe,QAAQ,CAAC;QAC7C,IAAI,UAAU,CAAC,UAAU,GAAG,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,eAAe,GAAG,CAAC;QACtE,IAAI,SAAS,YAAY,CAAC,aAAa,OAAO;QAC9C,IAAI,QAAQ,EAAE;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAK;YAC/C,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,IAAK;gBAClD,MAAM,IAAI,CAAC,IAAI,qJAAQ,IAAI,CAAC;oBAC1B,OAAO,iBAAiB,OAAO;wBAAC;wBAAQ,SAAS;qBAAQ,EAAE,eAAe,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK;gBACxF;YACF;QACF;QACA,MAAM,GAAG,CAAC,qJAAQ,SAAS,CAAC,OAAO;YACjC,OAAO,CAAA,GAAA,iJAAA,CAAA,WAAe,AAAD,EAAE,eAAe,QAAQ,CAAC,aAAa,YAAY,IAAI,CAAA,GAAA,iJAAA,CAAA,WAAe,AAAD,EAAE,UAAU,YAAY,IAAI;gBACpH,QAAQ,eAAe,GAAG,CAAC;oBAAC;oBAAY;oBAAa;iBAAQ;YAC/D;QACF;IACF;IACA,WAAW,SAAU,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,YAAY,EAAE,MAAM;QACnG,IAAI,kBAAkB,eAAe,aAAa,CAAC;QACnD,IAAI,mBAAmB,eAAe,QAAQ,CAAC;QAC/C,IAAI,cAAc,iBAAiB,GAAG,CAAC;QACvC,IAAI,eAAe,eAAe,GAAG,CAAC;QACtC,qFAAqF;QACrF,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,QAAQ,SAAU,SAAS,EAAE,GAAG;YAC1C,IAAI,aAAa;YACjB,IAAI,YAAY,UAAU,SAAS;YACnC,IAAI,IAAI,YAAY,CAAC,aAAa,OAAO;YACzC,IAAI,IAAI,MAAM,YAAY,CAAC;gBAAC,IAAI;gBAAa,UAAU,KAAK;aAAC;YAC7D,IAAI,KAAK,MAAM,EAAE;YACjB,IAAI,KAAK,MAAM,EAAE;YACjB,IAAI,iBAAiB,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,IAAI,MAAM,WAAW,CAAC,CAAC,EAAE,GAAG,KAAK,SAAS;YACrF,IAAI,yBAAyB,KAAK,GAAG,CAAC,CAAC,CAAC,EAAE,GAAG,MAAM,IAAI,MAAM,WAAW,CAAC,CAAC,EAAE,GAAG,KAAK,QAAQ;YAC5F,IAAI,mBAAmB,eAAe,CAAC,UAAU,EAAE;gBACjD,IAAI,kBAAkB,eAAe,CAAC,UAAU;gBAChD,IAAI,CAAA,GAAA,iJAAA,CAAA,WAAe,AAAD,EAAE,oBAAoB,gBAAgB,SAAS,EAAE;oBACjE,aAAa,IAAI,mJAAA,CAAA,UAAK,CAAC,gBAAgB,SAAS,EAAE,kBAAkB,iBAAiB,OAAO;gBAC9F;YACF;YACA,IAAI,SAAS,IAAI,qJAAQ,IAAI,CAAC;gBAC5B,QAAQ,qKAAA,CAAA,UAAW,CAAC,aAAa,CAAC;gBAClC,OAAO,CAAA,GAAA,wJAAA,CAAA,kBAAe,AAAD,EAAE,YAAY;oBACjC,GAAG,CAAC,CAAC,EAAE;oBACP,GAAG,CAAC,CAAC,EAAE;oBACP,MAAM,WAAW,YAAY,MAAM,eAAe,GAAG,CAAC;wBAAC;wBAAY;wBAAa;qBAAQ;oBACxF,MAAM,UAAU,cAAc;oBAC9B,OAAO;oBACP,eAAe;gBACjB;YACF;YACA,MAAM,GAAG,CAAC;YACV,4BAA4B;YAC5B,IAAI,cAAc;gBAChB,IAAI,YAAY,qKAAA,CAAA,UAAW,CAAC,qBAAqB,CAAC;gBAClD,UAAU,UAAU,GAAG;gBACvB,UAAU,KAAK,GAAG,UAAU,QAAQ;gBACpC,CAAA,GAAA,uJAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,SAAS,GAAG;YAChC;QACF,GAAG,IAAI;IACT;IACA,WAAW,SAAU,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,YAAY;QAC3F,IAAI,iBAAiB,eAAe,QAAQ,CAAC;QAC7C,IAAI,iBAAiB,eAAe,QAAQ,CAAC;QAC7C,IAAI,aAAa,eAAe,GAAG,CAAC;QACpC,IAAI,YAAY;QAChB,aAAa,sBAAsB,QAAQ,aAAa;YAAC;SAAW;QACpE,IAAI,aAAa,EAAE;QACnB,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;YAC3C,IAAI,aAAa,cAAc,WAAW,MAAM;YAChD,UAAU,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,IAAI,EAAE;YACrD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,qJAAQ,IAAI,CAAC;gBAC3C,OAAO,iBAAiB,OAAO,cAAc,WAAW,CAAC,EAAE,CAAC,KAAK;YACnE;QACF;QACA,sBAAsB;QACtB,2CAA2C;QAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YAC1C,MAAM,GAAG,CAAC,qJAAQ,SAAS,CAAC,UAAU,CAAC,EAAE,EAAE;gBACzC,OAAO,CAAA,GAAA,iJAAA,CAAA,WAAe,AAAD,EAAE;oBACrB,QAAQ,UAAU,CAAC,IAAI,WAAW,MAAM,CAAC;gBAC3C,GAAG,eAAe,YAAY;gBAC9B,QAAQ;gBACR,GAAG,eAAe,GAAG,CAAC;YACxB;QACF;IACF;IACA,gBAAgB,SAAU,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,YAAY;QAChG,IAAI,CAAC,gBAAgB,MAAM,EAAE;YAC3B;QACF;QACA,IAAI,sBAAsB,eAAe,QAAQ,CAAC;QAClD,IAAI,iBAAiB,oBAAoB,QAAQ,CAAC;QAClD,IAAI,QAAQ,EAAE;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,MAAM,EAAE,IAAK;YAC/C,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,CAAC,EAAE,CAAC,MAAM,EAAE,IAAK;gBAClD,MAAM,IAAI,CAAC,IAAI,qJAAQ,IAAI,CAAC;oBAC1B,OAAO,iBAAiB,OAAO,cAAc,eAAe,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK;gBAC1E;YACF;QACF;QACA,MAAM,GAAG,CAAC,qJAAQ,SAAS,CAAC,OAAO;YACjC,OAAO,eAAe,YAAY;YAClC,QAAQ;YACR,GAAG,eAAe,GAAG,CAAC;QACxB;IACF;IACA,WAAW,SAAU,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,WAAW,EAAE,eAAe,EAAE,YAAY;QAC3F,IAAI,CAAC,YAAY,MAAM,EAAE;YACvB;QACF;QACA,IAAI,iBAAiB,eAAe,QAAQ,CAAC;QAC7C,IAAI,iBAAiB,eAAe,QAAQ,CAAC;QAC7C,IAAI,aAAa,eAAe,GAAG,CAAC;QACpC,IAAI,YAAY;QAChB,aAAa,sBAAsB,QAAQ,aAAa;YAAC;SAAW;QACpE,IAAI,aAAa,EAAE;QACnB,IAAI,SAAS,KAAK,EAAE,GAAG;QACvB,IAAI,YAAY,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK,GAAG;QACxC,IAAI,KAAK,KAAK,GAAG,CAAC,YAAY,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE;QAClD,IAAI,KAAK,KAAK,GAAG,CAAC,YAAY,CAAC,EAAE,EAAE,YAAY,CAAC,EAAE;QAClD,IAAI,YAAY,eAAe,GAAG,CAAC;QACnC,IAAK,IAAI,IAAI,GAAG,MAAM,YAAY,MAAM,EAAE,KAAK,KAAK,IAAK;YACvD,IAAI,QAAQ,MAAM,MAAM,WAAW,CAAC,EAAE,CAAC,KAAK,GAAG,WAAW,CAAC,EAAE,CAAC,KAAK;YACnE,IAAI,aAAa,cAAc,WAAW,MAAM;YAChD,UAAU,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,IAAI,EAAE;YACrD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,qJAAQ,MAAM,CAAC;gBAC7C,OAAO;oBACL,IAAI,MAAM,EAAE;oBACZ,IAAI,MAAM,EAAE;oBACZ,IAAI;oBACJ,GAAG;oBACH,YAAY;oBACZ,UAAU,CAAC,QAAQ;oBACnB,WAAW;gBACb;gBACA,QAAQ;YACV;YACA,YAAY,CAAC,QAAQ;QACvB;QACA,sBAAsB;QACtB,2CAA2C;QAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YAC1C,MAAM,GAAG,CAAC,qJAAQ,SAAS,CAAC,UAAU,CAAC,EAAE,EAAE;gBACzC,OAAO,CAAA,GAAA,iJAAA,CAAA,WAAe,AAAD,EAAE;oBACrB,MAAM,UAAU,CAAC,IAAI,WAAW,MAAM,CAAC;gBACzC,GAAG,eAAe,YAAY;gBAC9B,QAAQ;YACV;QACF;IACF;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1822, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axis/RadiusAxisView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport * as graphic from '../../util/graphic.js';\nimport AxisBuilder from './AxisBuilder.js';\nimport AxisView from './AxisView.js';\nvar axisBuilderAttrs = ['axisLine', 'axisTickLabel', 'axisName'];\nvar selfBuilderAttrs = ['splitLine', 'splitArea', 'minorSplitLine'];\nvar RadiusAxisView = /** @class */function (_super) {\n  __extends(RadiusAxisView, _super);\n  function RadiusAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = RadiusAxisView.type;\n    _this.axisPointerClass = 'PolarAxisPointer';\n    return _this;\n  }\n  RadiusAxisView.prototype.render = function (radiusAxisModel, ecModel) {\n    this.group.removeAll();\n    if (!radiusAxisModel.get('show')) {\n      return;\n    }\n    var oldAxisGroup = this._axisGroup;\n    var newAxisGroup = this._axisGroup = new graphic.Group();\n    this.group.add(newAxisGroup);\n    var radiusAxis = radiusAxisModel.axis;\n    var polar = radiusAxis.polar;\n    var angleAxis = polar.getAngleAxis();\n    var ticksCoords = radiusAxis.getTicksCoords();\n    var minorTicksCoords = radiusAxis.getMinorTicksCoords();\n    var axisAngle = angleAxis.getExtent()[0];\n    var radiusExtent = radiusAxis.getExtent();\n    var layout = layoutAxis(polar, radiusAxisModel, axisAngle);\n    var axisBuilder = new AxisBuilder(radiusAxisModel, layout);\n    zrUtil.each(axisBuilderAttrs, axisBuilder.add, axisBuilder);\n    newAxisGroup.add(axisBuilder.getGroup());\n    graphic.groupTransition(oldAxisGroup, newAxisGroup, radiusAxisModel);\n    zrUtil.each(selfBuilderAttrs, function (name) {\n      if (radiusAxisModel.get([name, 'show']) && !radiusAxis.scale.isBlank()) {\n        axisElementBuilders[name](this.group, radiusAxisModel, polar, axisAngle, radiusExtent, ticksCoords, minorTicksCoords);\n      }\n    }, this);\n  };\n  RadiusAxisView.type = 'radiusAxis';\n  return RadiusAxisView;\n}(AxisView);\nvar axisElementBuilders = {\n  splitLine: function (group, radiusAxisModel, polar, axisAngle, radiusExtent, ticksCoords) {\n    var splitLineModel = radiusAxisModel.getModel('splitLine');\n    var lineStyleModel = splitLineModel.getModel('lineStyle');\n    var lineColors = lineStyleModel.get('color');\n    var lineCount = 0;\n    var angleAxis = polar.getAngleAxis();\n    var RADIAN = Math.PI / 180;\n    var angleExtent = angleAxis.getExtent();\n    var shapeType = Math.abs(angleExtent[1] - angleExtent[0]) === 360 ? 'Circle' : 'Arc';\n    lineColors = lineColors instanceof Array ? lineColors : [lineColors];\n    var splitLines = [];\n    for (var i = 0; i < ticksCoords.length; i++) {\n      var colorIndex = lineCount++ % lineColors.length;\n      splitLines[colorIndex] = splitLines[colorIndex] || [];\n      splitLines[colorIndex].push(new graphic[shapeType]({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          // ensure circle radius >= 0\n          r: Math.max(ticksCoords[i].coord, 0),\n          startAngle: -angleExtent[0] * RADIAN,\n          endAngle: -angleExtent[1] * RADIAN,\n          clockwise: angleAxis.inverse\n        }\n      }));\n    }\n    // Simple optimization\n    // Batching the lines if color are the same\n    for (var i = 0; i < splitLines.length; i++) {\n      group.add(graphic.mergePath(splitLines[i], {\n        style: zrUtil.defaults({\n          stroke: lineColors[i % lineColors.length],\n          fill: null\n        }, lineStyleModel.getLineStyle()),\n        silent: true\n      }));\n    }\n  },\n  minorSplitLine: function (group, radiusAxisModel, polar, axisAngle, radiusExtent, ticksCoords, minorTicksCoords) {\n    if (!minorTicksCoords.length) {\n      return;\n    }\n    var minorSplitLineModel = radiusAxisModel.getModel('minorSplitLine');\n    var lineStyleModel = minorSplitLineModel.getModel('lineStyle');\n    var lines = [];\n    for (var i = 0; i < minorTicksCoords.length; i++) {\n      for (var k = 0; k < minorTicksCoords[i].length; k++) {\n        lines.push(new graphic.Circle({\n          shape: {\n            cx: polar.cx,\n            cy: polar.cy,\n            r: minorTicksCoords[i][k].coord\n          }\n        }));\n      }\n    }\n    group.add(graphic.mergePath(lines, {\n      style: zrUtil.defaults({\n        fill: null\n      }, lineStyleModel.getLineStyle()),\n      silent: true\n    }));\n  },\n  splitArea: function (group, radiusAxisModel, polar, axisAngle, radiusExtent, ticksCoords) {\n    if (!ticksCoords.length) {\n      return;\n    }\n    var splitAreaModel = radiusAxisModel.getModel('splitArea');\n    var areaStyleModel = splitAreaModel.getModel('areaStyle');\n    var areaColors = areaStyleModel.get('color');\n    var lineCount = 0;\n    areaColors = areaColors instanceof Array ? areaColors : [areaColors];\n    var splitAreas = [];\n    var prevRadius = ticksCoords[0].coord;\n    for (var i = 1; i < ticksCoords.length; i++) {\n      var colorIndex = lineCount++ % areaColors.length;\n      splitAreas[colorIndex] = splitAreas[colorIndex] || [];\n      splitAreas[colorIndex].push(new graphic.Sector({\n        shape: {\n          cx: polar.cx,\n          cy: polar.cy,\n          r0: prevRadius,\n          r: ticksCoords[i].coord,\n          startAngle: 0,\n          endAngle: Math.PI * 2\n        },\n        silent: true\n      }));\n      prevRadius = ticksCoords[i].coord;\n    }\n    // Simple optimization\n    // Batching the lines if color are the same\n    for (var i = 0; i < splitAreas.length; i++) {\n      group.add(graphic.mergePath(splitAreas[i], {\n        style: zrUtil.defaults({\n          fill: areaColors[i % areaColors.length]\n        }, areaStyleModel.getAreaStyle()),\n        silent: true\n      }));\n    }\n  }\n};\n/**\r\n * @inner\r\n */\nfunction layoutAxis(polar, radiusAxisModel, axisAngle) {\n  return {\n    position: [polar.cx, polar.cy],\n    rotation: axisAngle / 180 * Math.PI,\n    labelDirection: -1,\n    tickDirection: -1,\n    nameDirection: 1,\n    labelRotate: radiusAxisModel.getModel('axisLabel').get('rotate'),\n    // Over splitLine and splitArea\n    z2: 1\n  };\n}\nexport default RadiusAxisView;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AACA;;;;;;AACA,IAAI,mBAAmB;IAAC;IAAY;IAAiB;CAAW;AAChE,IAAI,mBAAmB;IAAC;IAAa;IAAa;CAAiB;AACnE,IAAI,iBAAiB,WAAW,GAAE,SAAU,MAAM;IAChD,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;IAC1B,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,eAAe,IAAI;QAChC,MAAM,gBAAgB,GAAG;QACzB,OAAO;IACT;IACA,eAAe,SAAS,CAAC,MAAM,GAAG,SAAU,eAAe,EAAE,OAAO;QAClE,IAAI,CAAC,KAAK,CAAC,SAAS;QACpB,IAAI,CAAC,gBAAgB,GAAG,CAAC,SAAS;YAChC;QACF;QACA,IAAI,eAAe,IAAI,CAAC,UAAU;QAClC,IAAI,eAAe,IAAI,CAAC,UAAU,GAAG,IAAI,qJAAQ,KAAK;QACtD,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC;QACf,IAAI,aAAa,gBAAgB,IAAI;QACrC,IAAI,QAAQ,WAAW,KAAK;QAC5B,IAAI,YAAY,MAAM,YAAY;QAClC,IAAI,cAAc,WAAW,cAAc;QAC3C,IAAI,mBAAmB,WAAW,mBAAmB;QACrD,IAAI,YAAY,UAAU,SAAS,EAAE,CAAC,EAAE;QACxC,IAAI,eAAe,WAAW,SAAS;QACvC,IAAI,SAAS,WAAW,OAAO,iBAAiB;QAChD,IAAI,cAAc,IAAI,qKAAA,CAAA,UAAW,CAAC,iBAAiB;QACnD,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,kBAAkB,YAAY,GAAG,EAAE;QAC/C,aAAa,GAAG,CAAC,YAAY,QAAQ;QACrC,qJAAQ,eAAe,CAAC,cAAc,cAAc;QACpD,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,kBAAkB,SAAU,IAAI;YAC1C,IAAI,gBAAgB,GAAG,CAAC;gBAAC;gBAAM;aAAO,KAAK,CAAC,WAAW,KAAK,CAAC,OAAO,IAAI;gBACtE,mBAAmB,CAAC,KAAK,CAAC,IAAI,CAAC,KAAK,EAAE,iBAAiB,OAAO,WAAW,cAAc,aAAa;YACtG;QACF,GAAG,IAAI;IACT;IACA,eAAe,IAAI,GAAG;IACtB,OAAO;AACT,EAAE,kKAAA,CAAA,UAAQ;AACV,IAAI,sBAAsB;IACxB,WAAW,SAAU,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,SAAS,EAAE,YAAY,EAAE,WAAW;QACtF,IAAI,iBAAiB,gBAAgB,QAAQ,CAAC;QAC9C,IAAI,iBAAiB,eAAe,QAAQ,CAAC;QAC7C,IAAI,aAAa,eAAe,GAAG,CAAC;QACpC,IAAI,YAAY;QAChB,IAAI,YAAY,MAAM,YAAY;QAClC,IAAI,SAAS,KAAK,EAAE,GAAG;QACvB,IAAI,cAAc,UAAU,SAAS;QACrC,IAAI,YAAY,KAAK,GAAG,CAAC,WAAW,CAAC,EAAE,GAAG,WAAW,CAAC,EAAE,MAAM,MAAM,WAAW;QAC/E,aAAa,sBAAsB,QAAQ,aAAa;YAAC;SAAW;QACpE,IAAI,aAAa,EAAE;QACnB,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;YAC3C,IAAI,aAAa,cAAc,WAAW,MAAM;YAChD,UAAU,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,IAAI,EAAE;YACrD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,oJAAO,CAAC,UAAU,CAAC;gBACjD,OAAO;oBACL,IAAI,MAAM,EAAE;oBACZ,IAAI,MAAM,EAAE;oBACZ,4BAA4B;oBAC5B,GAAG,KAAK,GAAG,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK,EAAE;oBAClC,YAAY,CAAC,WAAW,CAAC,EAAE,GAAG;oBAC9B,UAAU,CAAC,WAAW,CAAC,EAAE,GAAG;oBAC5B,WAAW,UAAU,OAAO;gBAC9B;YACF;QACF;QACA,sBAAsB;QACtB,2CAA2C;QAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YAC1C,MAAM,GAAG,CAAC,qJAAQ,SAAS,CAAC,UAAU,CAAC,EAAE,EAAE;gBACzC,OAAO,CAAA,GAAA,iJAAA,CAAA,WAAe,AAAD,EAAE;oBACrB,QAAQ,UAAU,CAAC,IAAI,WAAW,MAAM,CAAC;oBACzC,MAAM;gBACR,GAAG,eAAe,YAAY;gBAC9B,QAAQ;YACV;QACF;IACF;IACA,gBAAgB,SAAU,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,SAAS,EAAE,YAAY,EAAE,WAAW,EAAE,gBAAgB;QAC7G,IAAI,CAAC,iBAAiB,MAAM,EAAE;YAC5B;QACF;QACA,IAAI,sBAAsB,gBAAgB,QAAQ,CAAC;QACnD,IAAI,iBAAiB,oBAAoB,QAAQ,CAAC;QAClD,IAAI,QAAQ,EAAE;QACd,IAAK,IAAI,IAAI,GAAG,IAAI,iBAAiB,MAAM,EAAE,IAAK;YAChD,IAAK,IAAI,IAAI,GAAG,IAAI,gBAAgB,CAAC,EAAE,CAAC,MAAM,EAAE,IAAK;gBACnD,MAAM,IAAI,CAAC,IAAI,qJAAQ,MAAM,CAAC;oBAC5B,OAAO;wBACL,IAAI,MAAM,EAAE;wBACZ,IAAI,MAAM,EAAE;wBACZ,GAAG,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC,KAAK;oBACjC;gBACF;YACF;QACF;QACA,MAAM,GAAG,CAAC,qJAAQ,SAAS,CAAC,OAAO;YACjC,OAAO,CAAA,GAAA,iJAAA,CAAA,WAAe,AAAD,EAAE;gBACrB,MAAM;YACR,GAAG,eAAe,YAAY;YAC9B,QAAQ;QACV;IACF;IACA,WAAW,SAAU,KAAK,EAAE,eAAe,EAAE,KAAK,EAAE,SAAS,EAAE,YAAY,EAAE,WAAW;QACtF,IAAI,CAAC,YAAY,MAAM,EAAE;YACvB;QACF;QACA,IAAI,iBAAiB,gBAAgB,QAAQ,CAAC;QAC9C,IAAI,iBAAiB,eAAe,QAAQ,CAAC;QAC7C,IAAI,aAAa,eAAe,GAAG,CAAC;QACpC,IAAI,YAAY;QAChB,aAAa,sBAAsB,QAAQ,aAAa;YAAC;SAAW;QACpE,IAAI,aAAa,EAAE;QACnB,IAAI,aAAa,WAAW,CAAC,EAAE,CAAC,KAAK;QACrC,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,IAAK;YAC3C,IAAI,aAAa,cAAc,WAAW,MAAM;YAChD,UAAU,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,IAAI,EAAE;YACrD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC,IAAI,qJAAQ,MAAM,CAAC;gBAC7C,OAAO;oBACL,IAAI,MAAM,EAAE;oBACZ,IAAI,MAAM,EAAE;oBACZ,IAAI;oBACJ,GAAG,WAAW,CAAC,EAAE,CAAC,KAAK;oBACvB,YAAY;oBACZ,UAAU,KAAK,EAAE,GAAG;gBACtB;gBACA,QAAQ;YACV;YACA,aAAa,WAAW,CAAC,EAAE,CAAC,KAAK;QACnC;QACA,sBAAsB;QACtB,2CAA2C;QAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,IAAK;YAC1C,MAAM,GAAG,CAAC,qJAAQ,SAAS,CAAC,UAAU,CAAC,EAAE,EAAE;gBACzC,OAAO,CAAA,GAAA,iJAAA,CAAA,WAAe,AAAD,EAAE;oBACrB,MAAM,UAAU,CAAC,IAAI,WAAW,MAAM,CAAC;gBACzC,GAAG,eAAe,YAAY;gBAC9B,QAAQ;YACV;QACF;IACF;AACF;AACA;;CAEC,GACD,SAAS,WAAW,KAAK,EAAE,eAAe,EAAE,SAAS;IACnD,OAAO;QACL,UAAU;YAAC,MAAM,EAAE;YAAE,MAAM,EAAE;SAAC;QAC9B,UAAU,YAAY,MAAM,KAAK,EAAE;QACnC,gBAAgB,CAAC;QACjB,eAAe,CAAC;QAChB,eAAe;QACf,aAAa,gBAAgB,QAAQ,CAAC,aAAa,GAAG,CAAC;QACvD,+BAA+B;QAC/B,IAAI;IACN;AACF;uCACe", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2052, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/OneDrive/Desktop/tindahan/node_modules/echarts/lib/component/axis/SingleAxisView.js"], "sourcesContent": ["\n/*\n* Licensed to the Apache Software Foundation (ASF) under one\n* or more contributor license agreements.  See the NOTICE file\n* distributed with this work for additional information\n* regarding copyright ownership.  The ASF licenses this file\n* to you under the Apache License, Version 2.0 (the\n* \"License\"); you may not use this file except in compliance\n* with the License.  You may obtain a copy of the License at\n*\n*   http://www.apache.org/licenses/LICENSE-2.0\n*\n* Unless required by applicable law or agreed to in writing,\n* software distributed under the License is distributed on an\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\n* KIND, either express or implied.  See the License for the\n* specific language governing permissions and limitations\n* under the License.\n*/\n\n\n/**\n * AUTO-GENERATED FILE. DO NOT MODIFY.\n */\n\n/*\r\n* Licensed to the Apache Software Foundation (ASF) under one\r\n* or more contributor license agreements.  See the NOTICE file\r\n* distributed with this work for additional information\r\n* regarding copyright ownership.  The ASF licenses this file\r\n* to you under the Apache License, Version 2.0 (the\r\n* \"License\"); you may not use this file except in compliance\r\n* with the License.  You may obtain a copy of the License at\r\n*\r\n*   http://www.apache.org/licenses/LICENSE-2.0\r\n*\r\n* Unless required by applicable law or agreed to in writing,\r\n* software distributed under the License is distributed on an\r\n* \"AS IS\" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY\r\n* KIND, either express or implied.  See the License for the\r\n* specific language governing permissions and limitations\r\n* under the License.\r\n*/\nimport { __extends } from \"tslib\";\nimport * as zrUtil from 'zrender/lib/core/util.js';\nimport AxisBuilder from './AxisBuilder.js';\nimport * as graphic from '../../util/graphic.js';\nimport * as singleAxisHelper from '../../coord/single/singleAxisHelper.js';\nimport AxisView from './AxisView.js';\nimport { rectCoordAxisBuildSplitArea, rectCoordAxisHandleRemove } from './axisSplitHelper.js';\nvar axisBuilderAttrs = ['axisLine', 'axisTickLabel', 'axisName'];\nvar selfBuilderAttrs = ['splitArea', 'splitLine'];\nvar SingleAxisView = /** @class */function (_super) {\n  __extends(SingleAxisView, _super);\n  function SingleAxisView() {\n    var _this = _super !== null && _super.apply(this, arguments) || this;\n    _this.type = SingleAxisView.type;\n    _this.axisPointerClass = 'SingleAxisPointer';\n    return _this;\n  }\n  SingleAxisView.prototype.render = function (axisModel, ecModel, api, payload) {\n    var group = this.group;\n    group.removeAll();\n    var oldAxisGroup = this._axisGroup;\n    this._axisGroup = new graphic.Group();\n    var layout = singleAxisHelper.layout(axisModel);\n    var axisBuilder = new AxisBuilder(axisModel, layout);\n    zrUtil.each(axisBuilderAttrs, axisBuilder.add, axisBuilder);\n    group.add(this._axisGroup);\n    group.add(axisBuilder.getGroup());\n    zrUtil.each(selfBuilderAttrs, function (name) {\n      if (axisModel.get([name, 'show'])) {\n        axisElementBuilders[name](this, this.group, this._axisGroup, axisModel);\n      }\n    }, this);\n    graphic.groupTransition(oldAxisGroup, this._axisGroup, axisModel);\n    _super.prototype.render.call(this, axisModel, ecModel, api, payload);\n  };\n  SingleAxisView.prototype.remove = function () {\n    rectCoordAxisHandleRemove(this);\n  };\n  SingleAxisView.type = 'singleAxis';\n  return SingleAxisView;\n}(AxisView);\nvar axisElementBuilders = {\n  splitLine: function (axisView, group, axisGroup, axisModel) {\n    var axis = axisModel.axis;\n    if (axis.scale.isBlank()) {\n      return;\n    }\n    var splitLineModel = axisModel.getModel('splitLine');\n    var lineStyleModel = splitLineModel.getModel('lineStyle');\n    var lineColors = lineStyleModel.get('color');\n    lineColors = lineColors instanceof Array ? lineColors : [lineColors];\n    var lineWidth = lineStyleModel.get('width');\n    var gridRect = axisModel.coordinateSystem.getRect();\n    var isHorizontal = axis.isHorizontal();\n    var splitLines = [];\n    var lineCount = 0;\n    var ticksCoords = axis.getTicksCoords({\n      tickModel: splitLineModel\n    });\n    var p1 = [];\n    var p2 = [];\n    for (var i = 0; i < ticksCoords.length; ++i) {\n      var tickCoord = axis.toGlobalCoord(ticksCoords[i].coord);\n      if (isHorizontal) {\n        p1[0] = tickCoord;\n        p1[1] = gridRect.y;\n        p2[0] = tickCoord;\n        p2[1] = gridRect.y + gridRect.height;\n      } else {\n        p1[0] = gridRect.x;\n        p1[1] = tickCoord;\n        p2[0] = gridRect.x + gridRect.width;\n        p2[1] = tickCoord;\n      }\n      var line = new graphic.Line({\n        shape: {\n          x1: p1[0],\n          y1: p1[1],\n          x2: p2[0],\n          y2: p2[1]\n        },\n        silent: true\n      });\n      graphic.subPixelOptimizeLine(line.shape, lineWidth);\n      var colorIndex = lineCount++ % lineColors.length;\n      splitLines[colorIndex] = splitLines[colorIndex] || [];\n      splitLines[colorIndex].push(line);\n    }\n    var lineStyle = lineStyleModel.getLineStyle(['color']);\n    for (var i = 0; i < splitLines.length; ++i) {\n      group.add(graphic.mergePath(splitLines[i], {\n        style: zrUtil.defaults({\n          stroke: lineColors[i % lineColors.length]\n        }, lineStyle),\n        silent: true\n      }));\n    }\n  },\n  splitArea: function (axisView, group, axisGroup, axisModel) {\n    rectCoordAxisBuildSplitArea(axisView, axisGroup, axisModel, axisModel);\n  }\n};\nexport default SingleAxisView;"], "names": [], "mappings": "AACA;;;;;;;;;;;;;;;;;AAiBA,GAGA;;CAEC,GAED;;;;;;;;;;;;;;;;;AAiBA;;;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AACA;AACA;;;;;;;;AACA,IAAI,mBAAmB;IAAC;IAAY;IAAiB;CAAW;AAChE,IAAI,mBAAmB;IAAC;IAAa;CAAY;AACjD,IAAI,iBAAiB,WAAW,GAAE,SAAU,MAAM;IAChD,CAAA,GAAA,mKAAA,CAAA,YAAS,AAAD,EAAE,gBAAgB;IAC1B,SAAS;QACP,IAAI,QAAQ,WAAW,QAAQ,OAAO,KAAK,CAAC,IAAI,EAAE,cAAc,IAAI;QACpE,MAAM,IAAI,GAAG,eAAe,IAAI;QAChC,MAAM,gBAAgB,GAAG;QACzB,OAAO;IACT;IACA,eAAe,SAAS,CAAC,MAAM,GAAG,SAAU,SAAS,EAAE,OAAO,EAAE,GAAG,EAAE,OAAO;QAC1E,IAAI,QAAQ,IAAI,CAAC,KAAK;QACtB,MAAM,SAAS;QACf,IAAI,eAAe,IAAI,CAAC,UAAU;QAClC,IAAI,CAAC,UAAU,GAAG,IAAI,yLAAA,CAAA,QAAa;QACnC,IAAI,SAAS,CAAA,GAAA,wKAAA,CAAA,SAAuB,AAAD,EAAE;QACrC,IAAI,cAAc,IAAI,qKAAA,CAAA,UAAW,CAAC,WAAW;QAC7C,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,kBAAkB,YAAY,GAAG,EAAE;QAC/C,MAAM,GAAG,CAAC,IAAI,CAAC,UAAU;QACzB,MAAM,GAAG,CAAC,YAAY,QAAQ;QAC9B,CAAA,GAAA,iJAAA,CAAA,OAAW,AAAD,EAAE,kBAAkB,SAAU,IAAI;YAC1C,IAAI,UAAU,GAAG,CAAC;gBAAC;gBAAM;aAAO,GAAG;gBACjC,mBAAmB,CAAC,KAAK,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,UAAU,EAAE;YAC/D;QACF,GAAG,IAAI;QACP,CAAA,GAAA,oKAAA,CAAA,kBAAuB,AAAD,EAAE,cAAc,IAAI,CAAC,UAAU,EAAE;QACvD,OAAO,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,EAAE,WAAW,SAAS,KAAK;IAC9D;IACA,eAAe,SAAS,CAAC,MAAM,GAAG;QAChC,CAAA,GAAA,yKAAA,CAAA,4BAAyB,AAAD,EAAE,IAAI;IAChC;IACA,eAAe,IAAI,GAAG;IACtB,OAAO;AACT,EAAE,kKAAA,CAAA,UAAQ;AACV,IAAI,sBAAsB;IACxB,WAAW,SAAU,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS;QACxD,IAAI,OAAO,UAAU,IAAI;QACzB,IAAI,KAAK,KAAK,CAAC,OAAO,IAAI;YACxB;QACF;QACA,IAAI,iBAAiB,UAAU,QAAQ,CAAC;QACxC,IAAI,iBAAiB,eAAe,QAAQ,CAAC;QAC7C,IAAI,aAAa,eAAe,GAAG,CAAC;QACpC,aAAa,sBAAsB,QAAQ,aAAa;YAAC;SAAW;QACpE,IAAI,YAAY,eAAe,GAAG,CAAC;QACnC,IAAI,WAAW,UAAU,gBAAgB,CAAC,OAAO;QACjD,IAAI,eAAe,KAAK,YAAY;QACpC,IAAI,aAAa,EAAE;QACnB,IAAI,YAAY;QAChB,IAAI,cAAc,KAAK,cAAc,CAAC;YACpC,WAAW;QACb;QACA,IAAI,KAAK,EAAE;QACX,IAAI,KAAK,EAAE;QACX,IAAK,IAAI,IAAI,GAAG,IAAI,YAAY,MAAM,EAAE,EAAE,EAAG;YAC3C,IAAI,YAAY,KAAK,aAAa,CAAC,WAAW,CAAC,EAAE,CAAC,KAAK;YACvD,IAAI,cAAc;gBAChB,EAAE,CAAC,EAAE,GAAG;gBACR,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC;gBAClB,EAAE,CAAC,EAAE,GAAG;gBACR,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,GAAG,SAAS,MAAM;YACtC,OAAO;gBACL,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC;gBAClB,EAAE,CAAC,EAAE,GAAG;gBACR,EAAE,CAAC,EAAE,GAAG,SAAS,CAAC,GAAG,SAAS,KAAK;gBACnC,EAAE,CAAC,EAAE,GAAG;YACV;YACA,IAAI,OAAO,IAAI,gMAAA,CAAA,OAAY,CAAC;gBAC1B,OAAO;oBACL,IAAI,EAAE,CAAC,EAAE;oBACT,IAAI,EAAE,CAAC,EAAE;oBACT,IAAI,EAAE,CAAC,EAAE;oBACT,IAAI,EAAE,CAAC,EAAE;gBACX;gBACA,QAAQ;YACV;YACA,CAAA,GAAA,oKAAA,CAAA,uBAA4B,AAAD,EAAE,KAAK,KAAK,EAAE;YACzC,IAAI,aAAa,cAAc,WAAW,MAAM;YAChD,UAAU,CAAC,WAAW,GAAG,UAAU,CAAC,WAAW,IAAI,EAAE;YACrD,UAAU,CAAC,WAAW,CAAC,IAAI,CAAC;QAC9B;QACA,IAAI,YAAY,eAAe,YAAY,CAAC;YAAC;SAAQ;QACrD,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,MAAM,EAAE,EAAE,EAAG;YAC1C,MAAM,GAAG,CAAC,CAAA,GAAA,oKAAA,CAAA,YAAiB,AAAD,EAAE,UAAU,CAAC,EAAE,EAAE;gBACzC,OAAO,CAAA,GAAA,iJAAA,CAAA,WAAe,AAAD,EAAE;oBACrB,QAAQ,UAAU,CAAC,IAAI,WAAW,MAAM,CAAC;gBAC3C,GAAG;gBACH,QAAQ;YACV;QACF;IACF;IACA,WAAW,SAAU,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,SAAS;QACxD,CAAA,GAAA,yKAAA,CAAA,8BAA2B,AAAD,EAAE,UAAU,WAAW,WAAW;IAC9D;AACF;uCACe", "ignoreList": [0], "debugId": null}}]}